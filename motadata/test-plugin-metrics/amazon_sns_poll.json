{"127.0.0.1": {"_type": "1", "cloud.access.id": "********************", "cloud.secret.key": "pu6mxO0hJxJHQ3/ee5d4rNmErG/sKvlfVoYh+uvv", "cloud.type": "AWS Cloud", "credential.profile.name": "AWS-Cloud-Test1655203749779", "credential.profile.protocol": "Cloud", "discovery.category": "Cloud", "discovery.context": {}, "discovery.credential.profiles": [***************], "discovery.discovered.objects": 41, "discovery.event.processors": [176993700471165], "discovery.failed.objects": 0, "discovery.groups": [**************], "discovery.name": "AWS-Cloud-Test1655203749780", "discovery.object.type": "AWS Cloud", "discovery.progress": 100, "discovery.status": "Last ran at 04:19:50.876 pm 14/06/2022", "discovery.total.objects": 41, "errors": [], "event.id": 1769***********, "event.timestamp": **********, "event.topic": "remote.event.processor ", "event.type": "metric.poll", "id": ***************, "metric.category": "Cloud", "metric.credential.profile": ***************, "metric.credential.profile.protocol": "Cloud", "metric.discovery.method": "REMOTE", "metric.name": "Amazon SNS", "metric.object": ***************, "metric.plugin": "amazonsns", "metric.polling.min.time": 300, "metric.polling.time": 300, "metric.state": "ENABLE", "metric.type": "Amazon SNS", "object.account.id": "************", "object.business.hour.profile": **************, "object.category": "Cloud", "object.creation.time": "04:20:28.521 pm 14/06/2022", "object.discovery.method": "REMOTE", "object.event.processors": [], "object.groups": [**************, **************, ***************, ***************], "object.id": 38, "object.name": "testsns(ap-south-1)", "object.region": "ap-south-1", "object.state": "ENABLE", "object.target": "testsns(ap-south-1)", "object.type": "Amazon SNS", "object.vendor": "AWS Cloud", "plugin.engine": "go", "plugin.id": 12, "remote.event.processor.uuid": "4066de21-18b0-49df-b20d-fe59231e2bd4", "result": {"aws.sns.topic": [{"aws.sns.region": "ap-south-1", "aws.sns.subscriptions": 3, "aws.sns.topic": "MyTopic"}, {"aws.sns.region": "ap-south-1", "aws.sns.subscriptions": 0, "aws.sns.topic": "TestTopic2"}, {"aws.sns.region": "ap-south-1", "aws.sns.subscriptions": 2, "aws.sns.topic": "dynamodb"}, {"12": {"metadata.fields": {"types": "sns"}}, "aws.sns.region": "ap-south-1", "aws.sns.subscriptions": 2, "aws.sns.topic": "testsns"}]}, "status": "succeed", "timeout": 60}}