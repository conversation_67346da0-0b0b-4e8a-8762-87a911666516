{"************": {"result": {"correlation.metrics": ["mssql.cluster", "mssql.log.shipping"], "mssql.cluster": [{"mssql.cluster.node.name": "WIN-CL2LHC2DASA", "mssql.cluster.node.state": "up", "mssql.cluster.node.is.owner": true}, {"mssql.cluster.node.name": "WIN-3D1542AVLE8", "mssql.cluster.node.state": "up", "mssql.cluster.node.is.owner": false}]}, "errors": []}, "************": {"result": {"correlation.metrics": ["mssql.cluster", "mssql.log.shipping"], "mssql.alwayson": [{"mssql.alwayson": "93C09584-07E3-4DFC-8A3F-A544F5EDF26C", "mssql.alwayson.name": "SQLAG2017", "mssql.alwayson.replica.name": "SQLNODE1\\INSTANCE1", "mssql.alwayson.endpoint.url": "TCP://sqlnode1.sqlcluster.local:5022", "mssql.alwayson.connected.state": "CONNECTED", "mssql.alwayson.role": "PRIMARY", "mssql.alwayson.operational.state": "ONLINE", "mssql.alwayson.availability.mode": "SYNCHRONOUS_COMMIT", "mssql.alwayson.failover.mode": "AUTOMATIC"}, {"mssql.alwayson": "93C09584-07E3-4DFC-8A3F-A544F5EDF26C", "mssql.alwayson.name": "SQLAG2017", "mssql.alwayson.replica.name": "SQLNODE2\\INSTANCE1", "mssql.alwayson.endpoint.url": "TCP://sqlnode2.sqlcluster.local:5022", "mssql.alwayson.connected.state": "CONNECTED", "mssql.alwayson.role": "SECONDARY", "mssql.alwayson.availability.mode": "SYNCHRONOUS_COMMIT", "mssql.alwayson.failover.mode": "AUTOMATIC"}]}, "errors": []}, "***********": {"result": {"correlation.metrics": ["mssql.cluster", "mssql.log.shipping"], "mssql.log.shipping": [{"mssql.log.shipping.database.type": "primary", "mssql.log.shipping.database.name": "motadata2020", "mssql.log.shipping.last.backup.duration.seconds": 540, "mssql.log.shipping.last.backedup.file": "//***********/Backup\\motadata2020_20220608063001.trn"}, {"mssql.log.shipping.database.type": "secondary", "mssql.log.shipping.database.name": "motadata2020"}]}, "errors": []}}