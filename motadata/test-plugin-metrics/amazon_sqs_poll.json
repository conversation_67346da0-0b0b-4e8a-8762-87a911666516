{"127.0.0.1": {"_type": "1", "cloud.access.id": "********************", "cloud.secret.key": "pu6mxO0hJxJHQ3/ee5d4rNmErG/sKvlfVoYh+uvv", "cloud.type": "AWS Cloud", "credential.profile.name": "AWS-Cloud-Test1655203749779", "credential.profile.protocol": "Cloud", "discovery.category": "Cloud", "discovery.context": {}, "discovery.credential.profiles": [***************], "discovery.discovered.objects": 41, "discovery.event.processors": [176993700471165], "discovery.failed.objects": 0, "discovery.groups": [**************], "discovery.name": "AWS-Cloud-Test1655203749780", "discovery.object.type": "AWS Cloud", "discovery.progress": 100, "discovery.status": "Last ran at 04:19:50.876 pm 14/06/2022", "discovery.total.objects": 41, "errors": [], "event.id": 176993700500104, "event.timestamp": **********, "event.topic": "remote.event.processor ", "event.type": "metric.poll", "id": ***************, "metric.category": "Cloud", "metric.credential.profile": ***************, "metric.credential.profile.protocol": "Cloud", "metric.discovery.method": "REMOTE", "metric.name": "Amazon Simple Queue Service", "metric.object": ***************, "metric.plugin": "amazonsqs", "metric.polling.min.time": 600, "metric.polling.time": 600, "metric.state": "ENABLE", "metric.type": "Amazon SQS", "object.account.id": "************", "object.business.hour.profile": **************, "object.category": "Cloud", "object.creation.time": "04:20:28.194 pm 14/06/2022", "object.discovery.method": "REMOTE", "object.event.processors": [], "object.groups": [**************, **************, ***************, ***************], "object.id": 20, "object.name": "qsq(us-east-1)", "object.region": "ap-south-1", "object.state": "ENABLE", "object.target": "sqs(ap-south-1)", "object.type": "Amazon SQS", "object.vendor": "AWS Cloud", "plugin.engine": "go", "plugin.id": 212, "remote.event.processor.uuid": "4066de21-18b0-49df-b20d-fe59231e2bd4", "result": {"212": {"metadata.fields": {"workload-type": "sqs"}}, "aws.sqs.delayed.messages": 0, "aws.sqs.deleted.messages": 0, "aws.sqs.empty.messages": 0, "aws.sqs.hidden.messages": 0, "aws.sqs.oldest.message.age.seconds": 0, "aws.sqs.received.messages": 0, "aws.sqs.sent.messages": 0, "aws.sqs.visible.messages": 0}, "status": "succeed", "timeout": 60}}