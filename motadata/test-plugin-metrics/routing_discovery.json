{"***********": {"metric.timeout": 60, "object.ip": "***********", "object.vendor": "Cisco Systems", "objects": [{"object.ip": "***********"}], "port": 161, "rediscover.job": "Network Metric", "snmp.check.retries": 1, "snmp.community": "public", "snmp.version": "v2c", "status": "succeed"}, "***********": {"metric.timeout": 60, "object.ip": "***********", "object.vendor": "Cisco Systems", "objects": [{"object.ip": "***********"}], "port": 161, "rediscover.job": "Network Metric", "snmp.check.retries": 1, "snmp.community": "public", "snmp.version": "v2c", "status": "succeed"}, "***********": {"metric.timeout": 60, "object.ip": "***********", "object.vendor": "Juniper Networks", "objects": [{"object.ip": "***********"}], "port": 161, "rediscover.job": "Network Metric", "snmp.check.retries": 1, "snmp.community": "public", "snmp.version": "v2c", "status": "succeed"}, "***********": {"metric.timeout": 60, "object.ip": "***********", "object.vendor": "Cisco Systems", "objects": [{"object.ip": "***********"}], "port": 161, "rediscover.job": "Network Metric", "snmp.check.retries": 1, "snmp.community": "public", "snmp.version": "v2c", "status": "succeed"}}