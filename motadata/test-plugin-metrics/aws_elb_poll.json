{"127.0.0.1": {"9": {}, "_type": "1", "cloud.access.id": "********************", "cloud.secret.key": "pu6mxO0hJxJHQ3/ee5d4rNmErG/sKvlfVoYh+uvv", "cloud.type": "AWS Cloud", "credential.profile.name": "AWS-Cloud-Test1655203749779", "credential.profile.protocol": "Cloud", "discovery.category": "Cloud", "discovery.context": {}, "discovery.credential.profiles": [***************], "discovery.discovered.objects": 41, "discovery.event.processors": [176993700471165], "discovery.failed.objects": 0, "discovery.groups": [**************], "discovery.name": "AWS-Cloud-Test1655203749780", "discovery.object.type": "AWS Cloud", "discovery.progress": 100, "discovery.status": "Last ran at 04:19:50.876 pm 14/06/2022", "discovery.total.objects": 41, "errors": [], "event.id": 176993700500075, "event.timestamp": **********, "event.topic": "remote.event.processor ", "event.type": "metric.poll", "id": ***************, "metric.category": "Cloud", "metric.credential.profile": ***************, "metric.credential.profile.protocol": "Cloud", "metric.discovery.method": "REMOTE", "metric.name": "Amazon ELB", "metric.object": ***************, "metric.plugin": "a<PERSON><PERSON><PERSON>", "metric.polling.min.time": 300, "metric.polling.time": 300, "metric.state": "ENABLE", "metric.type": "AWS ELB", "object.account.id": "************", "object.business.hour.profile": **************, "object.category": "Cloud", "object.creation.time": "04:20:28.385 pm 14/06/2022", "object.custom.fields": {"***************": "Sampleebs-env", "***************": "e-kypxnsq6kf", "***************": "Sampleebs-env"}, "object.discovery.method": "REMOTE", "object.event.processors": [], "object.groups": [**************, **************, ***************, ***************, ***************], "object.id": 28, "object.make.model": "NETWORK", "object.name": "networkelb(ap-south-1)", "object.region": "ap-south-1", "object.state": "ENABLE", "object.target": "networkelb(ap-south-1)", "object.type": "AWS ELB", "object.vendor": "AWS Cloud", "plugin.engine": "go", "plugin.id": 9, "remote.event.processor.uuid": "4066de21-18b0-49df-b20d-fe59231e2bd4", "result": {"aws.availability.zone": "ap-south-1a, ap-south-1b", "aws.elb.active.flows": 1, "aws.elb.arn": "arn:aws:elasticloadbalancing:ap-south-1:************:loadbalancer/net/networkelb/d7e634c9012d4254", "aws.elb.consumed.lcus": 0, "aws.elb.dns.name": "networkelb-d7e634c9012d4254.elb.ap-south-1.amazonaws.com", "aws.elb.healthy.hosts": 0, "aws.elb.hosted.zone": "ZVDDRBQ08TROA", "aws.elb.instance.created.time": " 362 days 2 hours 7 minutes 2 seconds", "aws.elb.instance.created.time.sec": 31284422, "aws.elb.ipaddress.type": "ipv4", "aws.elb.load.balancer.name": "networkelb", "aws.elb.new.flows": 0, "aws.elb.processed.bytes.rate": 0, "aws.elb.region": "ap-south-1", "aws.elb.scheme": "internet-facing", "aws.elb.tcp.active.flows": 1, "aws.elb.tcp.client.resets": 0, "aws.elb.tcp.elb.resets": 0, "aws.elb.tcp.processed.bytes": 40, "aws.elb.tcp.target.elb.resets": 0, "aws.elb.type": "network", "aws.elb.unhealthy.hosts": 1, "aws.elb.vpc.id": "vpc-b9e834d0", "aws.state": "active", "status": "Up"}, "status": "succeed", "timeout": 60}}