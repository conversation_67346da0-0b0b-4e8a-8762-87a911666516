{"************": {"status": "succeed", "objects": [{"object.ip": "", "object.name": "\"Linux\"Server\"", "object.type": "vm", "status": "Down"}, {"object.ip": "", "object.name": "\"Linux-Server\"", "object.type": "vm", "status": "Down"}, {"object.ip": "************", "object.name": "@Motadata$Master@7.5.500", "object.type": "vm", "status": "Up"}, {"object.ip": "", "object.name": "'<PERSON><PERSON>\"S'erver", "object.type": "vm", "status": "Down"}, {"object.ip": "", "object.name": "<PERSON><PERSON>", "object.type": "vm", "status": "Down"}, {"object.ip": "", "object.name": "Motadata-Datanode-7.6.900", "object.type": "vm", "status": "Down"}, {"object.ip": "", "object.name": "Motadata-Master-7.6.900", "object.type": "vm", "status": "Down"}, {"object.ip": "", "object.name": "Motadata-RPE-7.6.900", "object.type": "vm", "status": "Down"}, {"object.ip": "", "object.name": "radwin-bare-metal", "object.type": "vm", "status": "Down"}, {"object.ip": "", "object.name": "radwin-bare-metal", "object.type": "vm", "status": "Down"}, {"object.ip": "", "object.name": "ServiceOps-7.3", "object.type": "vm", "status": "Down"}, {"object.ip": "", "object.name": "TEs@vm/1&th*and(or)%te\\st", "object.type": "vm", "status": "Up"}, {"object.ip": "*************", "object.name": "Windows-SCOM", "object.type": "vm", "status": "Up"}, {"object.ip": "***********", "object.name": "windows-scsm", "object.type": "vm", "status": "Up"}]}, "************": {"status": "succeed", "errors": [{"message": "failed to execute command [powershell -command \"$Host.UI.RawUI.BufferSize = New-Object Management.Automation.Host.Size (512,50);Get-WmiObject Win32_Computersystem | select-object Domain; echo \"hostname\";hostname | Format-List\"] on target http://************:5985/wsman", "error.code": "MD059", "error": "File C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\profile.ps1 cannot be loaded be\r\ncause the execution of scripts is disabled on this system. Please see \"get-help\r\n about_signing\" for more details.\r\nAt line:1 char:2\r\n+ . <<<<  'C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\profile.ps1'\r\n    + CategoryInfo          : NotSpecified: (:) [], PSSecurityException\r\n    + FullyQualifiedErrorId : RuntimeException\r\n \r\n"}, {"message": "failed to execute command [powershell -command \"$Host.UI.RawUI.BufferSize = New-Object Management.Automation.Host.Size (512,50);(get-counter '\\Hyper-V Hypervisor\\Partitions').countersamples | Format-List -Property Path,Cookedvalue | Format-List\"] on target http://************:5985/wsman", "error.code": "MD059", "error": "File C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\profile.ps1 cannot be loaded be\r\ncause the execution of scripts is disabled on this system. Please see \"get-help\r\n about_signing\" for more details.\r\nAt line:1 char:2\r\n+ . <<<<  'C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\profile.ps1'\r\n    + CategoryInfo          : NotSpecified: (:) [], PSSecurityException\r\n    + FullyQualifiedErrorId : RuntimeException\r\n \r\n"}, {"message": "failed to execute command [powershell -command \"$Host.UI.RawUI.BufferSize = New-Object Management.Automation.Host.Size (512,50);Get-WmiObject -Namespace 'root/virtualization' Msvm_ComputerSystem | Where-Object {$_.Description -eq'Microsoft Virtual Machine'}|Select-Object -Property ElementName| format-list | Format-List\"] on target http://************:5985/wsman", "error.code": "MD059", "error": "File C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\profile.ps1 cannot be loaded be\r\ncause the execution of scripts is disabled on this system. Please see \"get-help\r\n about_signing\" for more details.\r\nAt line:1 char:2\r\n+ . <<<<  'C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\profile.ps1'\r\n    + CategoryInfo          : NotSpecified: (:) [], PSSecurityException\r\n    + FullyQualifiedErrorId : RuntimeException\r\n \r\n"}, {"message": "failed to execute command [powershell -command \"$Host.UI.RawUI.BufferSize = New-Object Management.Automation.Host.Size (512,50);Get-WmiObject -Namespace 'root/virtualization' Msvm_ComputerSystem |  Where-Object {$_.Description -eq 'Microsoft Virtual Machine'} | fl -Property EnabledState,ElementName | Format-List\"] on target http://************:5985/wsman", "error.code": "MD059", "error": "File C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\profile.ps1 cannot be loaded be\r\ncause the execution of scripts is disabled on this system. Please see \"get-help\r\n about_signing\" for more details.\r\nAt line:1 char:2\r\n+ . <<<<  'C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\profile.ps1'\r\n    + CategoryInfo          : NotSpecified: (:) [], PSSecurityException\r\n    + FullyQualifiedErrorId : RuntimeException\r\n \r\n"}], "objects": []}, "***********": {"status": "succeed", "objects": [{"object.name": "small@#lvm/linux", "object.type": "vm", "status": "Down"}, {"object.name": "\"Linux\"Server\"", "object.type": "vm", "status": "Down"}, {"object.name": "TEs@vm1&th*and(or)%test", "object.type": "vm", "status": "Down"}, {"object.name": "TEst!@#vm/and$\\a", "object.type": "vm", "status": "Down"}, {"object.name": "'<PERSON><PERSON>\"S'erver", "object.type": "vm", "status": "Down"}, {"object.name": "\"Linux-Server\"", "object.type": "vm", "status": "Down"}]}}