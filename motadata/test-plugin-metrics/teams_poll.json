{"127.0.0.1": {"result": {"teams.team": [{"teams.team": "<PERSON><PERSON><PERSON>", "teams.visibility": "Private"}, {"teams.team": "QA", "teams.visibility": "Private"}, {"teams.team": "RSCCL", "teams.visibility": "Public"}, {"teams.team": "<PERSON><PERSON><PERSON>", "teams.visibility": "Private"}, {"teams.team": "Training"}, {"teams.team": "7.6.3 Upgrade", "teams.visibility": "Private"}, {"teams.team": "MarketingTeam", "teams.visibility": "Private"}, {"teams.team": "IOCL Deployment", "teams.visibility": "Private"}, {"teams.team": "Marketing", "teams.visibility": "Private"}, {"teams.team": "Technical Writer Team", "teams.visibility": "Private"}, {"teams.team": "Sprint 7.3.000 test", "teams.visibility": "Private"}, {"teams.team": "sales-internal", "teams.visibility": "Private"}, {"teams.team": "Test", "teams.visibility": "Private"}, {"teams.team": "CHiPS.Support"}, {"teams.team": "MUKESH MALI", "teams.visibility": "Private"}, {"teams.team": "Delivery", "teams.visibility": "Private"}, {"teams.team": "Radwin License", "teams.visibility": "Private"}, {"teams.team": "Marketing-Team", "teams.visibility": "Private"}, {"teams.team": "Newsletter", "teams.visibility": "Private"}, {"teams.team": "Support Team", "teams.visibility": "Private"}, {"teams.team": "Lucknow Smart City - Project Plan", "teams.visibility": "Private"}, {"teams.team": "HR - Internal", "teams.visibility": "Private"}, {"teams.team": "Communication Training Batch 1"}, {"teams.team": "Guest Posting for Motadata", "teams.visibility": "Private"}, {"teams.team": "Enterprise Presales", "teams.visibility": "Private"}, {"teams.team": "CHIPS KT", "teams.visibility": "Private"}, {"teams.team": "Motadata_Support_Internal", "teams.visibility": "Private"}, {"teams.team": "HDFC Account", "teams.visibility": "Private"}, {"teams.team": "aaa", "teams.visibility": "Private"}, {"teams.team": "ITI-Datacenter", "teams.visibility": "Private"}, {"teams.team": "CHiPS Team", "teams.visibility": "Private"}, {"teams.team": "GITEX Content Team", "teams.visibility": "Private"}, {"teams.team": "ServiceOps Mobile App", "teams.visibility": "Private"}, {"teams.team": "Research + Consulting Calls", "teams.visibility": "Private"}, {"teams.team": "QA Team", "teams.visibility": "Private"}, {"teams.team": "Marketing Motadata", "teams.visibility": "Private"}, {"teams.team": "Daily Bid Tracker", "teams.visibility": "Private"}, {"teams.team": "FreePatch"}, {"teams.team": "PreSales", "teams.visibility": "Private"}, {"teams.team": "Weblead", "teams.visibility": "Private"}, {"teams.team": "TerraCIS Technologies Ltd.", "teams.visibility": "Public"}, {"teams.team": "Engineering Team"}, {"teams.team": "Delivery and Support", "teams.visibility": "Private"}, {"teams.team": "PMG-Data", "teams.visibility": "Private"}, {"teams.team": "DevOps", "teams.visibility": "Private"}, {"teams.team": "Sales", "teams.visibility": "Public"}, {"teams.team": "Leads", "teams.visibility": "Private"}, {"teams.team": "Info", "teams.visibility": "Private"}, {"teams.team": "Tenders"}, {"teams.team": "RADWIN Projects", "teams.visibility": "Private"}, {"teams.team": "All Company", "teams.visibility": "Public"}, {"teams.team": "PMG", "teams.visibility": "Private"}, {"teams.team": "NDMC and EESL Tasks", "teams.visibility": "Private"}, {"teams.team": "NIC Hyd", "teams.visibility": "Private"}, {"teams.team": "Accounts", "teams.visibility": "Private"}, {"teams.team": "Bharti Airtel", "teams.visibility": "Private"}, {"teams.team": "International Business Team", "teams.visibility": "Private"}, {"teams.team": "International Sales", "teams.visibility": "Private"}, {"teams.team": "Motadata Partners", "teams.visibility": "Private"}, {"teams.team": "CloudTech-Nepal-Training", "teams.visibility": "Private"}, {"teams.team": "Maruti Suzuki", "teams.visibility": "Public"}, {"teams.team": "Inside Sales", "teams.visibility": "Private"}, {"teams.team": "PreSales Tech", "teams.visibility": "Private"}, {"teams.team": "Engineering", "teams.visibility": "Private"}, {"teams.team": "Chai Pe Charcha", "teams.visibility": "Private"}, {"teams.team": "Test Plan", "teams.visibility": "Private"}, {"teams.team": "Mindarray Staff"}, {"teams.team": "ServiceOps", "teams.visibility": "Private"}, {"teams.team": "Channel Business Plan", "teams.visibility": "Private"}, {"teams.team": "Rajkot Bank", "teams.visibility": "Private"}, {"teams.team": "Motadata 8.0 Team"}, {"teams.team": "Enterprise team", "teams.visibility": "Private"}, {"teams.team": "DevOps", "teams.visibility": "Private"}, {"teams.team": "Motadata Resource Center", "teams.visibility": "Public"}, {"teams.team": "Motadata Accounts", "teams.visibility": "Private"}, {"teams.team": "Motadata KB Articles", "teams.visibility": "Private"}, {"teams.team": "Celebrations"}, {"teams.team": "CHIPS", "teams.visibility": "Private"}, {"teams.team": "<PERSON><PERSON>", "teams.visibility": "Private"}, {"teams.team": "Motadata Inside Sales/Partners", "teams.visibility": "Private"}, {"teams.team": "Projects", "teams.visibility": "Private"}, {"teams.team": "Delivery_Team", "teams.visibility": "Private"}, {"teams.team": "<PERSON><PERSON><PERSON>, Hardik", "teams.visibility": "Private"}, {"teams.team": "Enterprise Demo Videos", "teams.visibility": "Public"}, {"teams.team": "Technical Doc 8.0", "teams.visibility": "Private"}, {"teams.team": "Social Media Group", "teams.visibility": "Private"}, {"teams.team": "Product Marketing Team", "teams.visibility": "Private"}, {"teams.team": "elearning"}, {"teams.team": "Motadata Webinar Team", "teams.visibility": "Private"}, {"teams.team": "8.0 Messaging & positioning", "teams.visibility": "Private"}, {"teams.team": "IOCL-Baitalpur Handover", "teams.visibility": "Private"}, {"teams.team": "JIRA", "teams.visibility": "Private"}, {"teams.team": "Motadata Memelords & Ladies", "teams.visibility": "Private"}, {"teams.team": "Pre-Sales", "teams.visibility": "Private"}, {"teams.team": "<PERSON><PERSON><PERSON>", "teams.visibility": "Public"}, {"teams.team": "ITSM Team", "teams.visibility": "Private"}, {"teams.team": "Sales"}], "teams.channel": [{"teams.channel": "General", "teams.channel.description": "QA internal", "teams.team": "QA"}, {"teams.channel": "ITSM QA", "teams.team": "QA"}, {"teams.channel": "General", "teams.channel.description": "Ranchi Smart City- Motadata Training and UAT", "teams.team": "RSCCL"}, {"teams.channel": "General", "teams.channel.description": "Temp channel for current polling issues", "teams.team": "7.6.3 Upgrade"}, {"teams.channel": "General", "teams.channel.description": "IOCL Deployment", "teams.team": "IOCL Deployment"}, {"teams.channel": "General", "teams.channel.description": "Private Marketing Channel", "teams.team": "Marketing-Team"}, {"teams.channel": "Inside_Sales", "teams.team": "Marketing-Team"}, {"teams.channel": "General", "teams.channel.description": "To have HR related internal discussion", "teams.team": "HR - Internal"}, {"teams.channel": "General", "teams.channel.description": "Guest Posting for Motadata", "teams.team": "Guest Posting for Motadata"}, {"teams.channel": "General", "teams.channel.description": "Enterprise Presales", "teams.team": "Enterprise Presales"}, {"teams.channel": "Presales Daily Demo FAQs", "teams.channel.description": "This channel is created to post all the questions asked by customer/partner during demo.", "teams.team": "Enterprise Presales"}, {"teams.channel": "General", "teams.channel.description": "Support Team's Internal Chat Room", "teams.team": "Motadata_Support_Internal"}, {"teams.channel": "General", "teams.channel.description": "CHiPS Team", "teams.team": "CHiPS Team"}, {"teams.channel": "General", "teams.channel.description": "GITEX Content Team", "teams.team": "GITEX Content Team"}, {"teams.channel": "General", "teams.channel.description": "ServiceOps Mobile App", "teams.team": "ServiceOps Mobile App"}, {"teams.channel": "General", "teams.channel.description": "Research + Consulting Calls", "teams.team": "Research + Consulting Calls"}, {"teams.channel": "General", "teams.channel.description": "Private PreSales Channel", "teams.team": "PreSales"}, {"teams.channel": "General", "teams.channel.description": "TerraCIS Technologies Ltd.", "teams.team": "TerraCIS Technologies Ltd."}, {"teams.channel": "General", "teams.channel.description": "This group is created to exchange general and important information between Delivery and Support teams.", "teams.team": "Delivery and Support"}, {"teams.channel": "General", "teams.channel.description": " ", "teams.team": "DevOps"}, {"teams.channel": "jenkins", "teams.team": "DevOps"}, {"teams.channel": "General", "teams.channel.description": "NIC HYD GEM", "teams.team": "NIC Hyd"}, {"teams.channel": "General", "teams.channel.description": "Accounts", "teams.team": "Accounts"}, {"teams.channel": "General", "teams.channel.description": "Bharti Airtel", "teams.team": "Bharti Airtel"}, {"teams.channel": "General", "teams.channel.description": "International Business Team", "teams.team": "International Business Team"}, {"teams.channel": "General", "teams.channel.description": "International Sales", "teams.team": "International Sales"}, {"teams.channel": "General", "teams.channel.description": "Maruti Suzuki", "teams.team": "Maruti Suzuki"}, {"teams.channel": "General", "teams.channel.description": "Core Engg. Dept of Motadata", "teams.team": "Engineering"}, {"teams.channel": "Azure DevOps", "teams.channel.description": "Azure DevOps Event Channel of 8.0 Team", "teams.team": "Engineering"}, {"teams.channel": "RADWIN", "teams.channel.description": "RADWIN Team", "teams.team": "Engineering"}, {"teams.channel": "8.0", "teams.channel.description": "Completely new thoughts and functional approaches to unlock hidden growth and potential of an individual and build a valued and customer centric product to fix pain points using innovation.", "teams.team": "Engineering"}, {"teams.channel": "ITSM", "teams.channel.description": "ServiceDesk Team", "teams.team": "Engineering"}, {"teams.channel": "QA", "teams.channel.description": "QA Dept. of Motadata", "teams.team": "Engineering"}, {"teams.channel": "7.x", "teams.channel.description": "7.x Team", "teams.team": "Engineering"}, {"teams.channel": "PMG_AIOps", "teams.channel.description": "Product Management Group", "teams.team": "Engineering"}, {"teams.channel": "CHiPS", "teams.channel.description": "Bharatnet CHiPS team", "teams.team": "Engineering"}, {"teams.channel": "PMG_ServOps", "teams.team": "Engineering"}, {"teams.channel": "TFS-Notification-7x", "teams.channel.description": "Azure DevOps notification channel for Motadata7x  Team", "teams.team": "Engineering"}, {"teams.channel": "General", "teams.channel.description": "Chai Pe Charcha", "teams.team": "Chai Pe Charcha"}, {"teams.channel": "General", "teams.channel.description": "ServiceOps", "teams.team": "ServiceOps"}, {"teams.channel": "<PERSON>", "teams.team": "ServiceOps"}, {"teams.channel": "QA", "teams.team": "ServiceOps"}, {"teams.channel": "Daily Scrum", "teams.team": "ServiceOps"}, {"teams.channel": "DEV_QA", "teams.team": "ServiceOps"}, {"teams.channel": "<PERSON>", "teams.team": "ServiceOps"}, {"teams.channel": "General", "teams.channel.description": "Rajkot Bank", "teams.team": "Rajkot Bank"}, {"teams.channel": "General", "teams.channel.description": "Delivery_Team", "teams.team": "Delivery_Team"}, {"teams.channel": "General", "teams.channel.description": "<PERSON><PERSON><PERSON>, Hardik", "teams.team": "<PERSON><PERSON><PERSON>, Hardik"}, {"teams.channel": "General", "teams.channel.description": "Technical Doc 8.0", "teams.team": "Technical Doc 8.0"}, {"teams.channel": "General", "teams.channel.description": "For Ideas and suggestions", "teams.team": "Social Media Group"}, {"teams.channel": "General", "teams.channel.description": "Daily task discussion", "teams.team": "Product Marketing Team"}, {"teams.channel": "General", "teams.channel.description": "This group is for presales and marketing only.", "teams.team": "Motadata Webinar Team"}, {"teams.channel": "General", "teams.channel.description": "8.0 Team", "teams.team": "8.0 Messaging & positioning"}, {"teams.channel": "General", "teams.channel.description": "IOCL-Baitalpur Handover", "teams.team": "IOCL-Baitalpur Handover"}, {"teams.channel": "General", "teams.channel.description": "Motadata Memelords & Ladies", "teams.team": "Motadata Memelords & Ladies"}, {"teams.channel": "General", "teams.channel.description": "<PERSON><PERSON><PERSON>", "teams.team": "<PERSON><PERSON><PERSON>"}, {"teams.channel": "Content-Topics-Read-Write", "teams.channel.description": "Use this channel to post content topics for blogs, share good content which is useful to others", "teams.team": "<PERSON><PERSON><PERSON>"}, {"teams.channel": "Test 1", "teams.team": "<PERSON><PERSON><PERSON>"}, {"teams.channel": "HR-Team", "teams.team": "<PERSON><PERSON><PERSON>"}, {"teams.channel": "<PERSON><PERSON><PERSON>_Divas", "teams.channel.description": "Motadata-Girls", "teams.team": "<PERSON><PERSON><PERSON>"}], "teams.channels": 600, "teams.calls": 10, "teams.meetings": 370, "teams.chat.messages": 0, "teams.web.users": 0, "teams.windows.users": 123, "teams.android.users": 24, "teams.ios.users": 0, "teams.mac.users": 34}}}