{"************": {"status": "succeed", "objects": [{"object.type": "vm", "object.ip": "", "object.name": "<PERSON><PERSON>-master-node", "status": "Down"}, {"object.type": "vm", "object.ip": "", "object.name": "kube-worker-1", "status": "Down"}, {"object.type": "vm", "object.ip": "", "object.name": "kube-worker-2", "status": "Down"}, {"object.type": "vm", "object.ip": "", "object.name": "Ubuntu14-10.183", "status": "Up"}, {"object.type": "vm", "object.ip": "", "object.name": "Latest-Windows-10-11.216", "status": "Up"}, {"object.type": "vm", "object.ip": "*************", "object.name": "sterlite-751_11.238", "status": "Up"}, {"object.type": "vm", "object.ip": "*************", "object.name": "motadata_Internal_monitoring_11.166", "status": "Up"}, {"object.type": "vm", "object.ip": "************", "object.name": "<PERSON><PERSON><PERSON>_UBI_Performance_RPE3_9.116_9.122", "status": "Up"}, {"object.type": "vm", "object.ip": "*************", "object.name": "8.0_QA_Scalibility_master-1_11.224", "status": "Up"}, {"object.type": "vm", "object.ip": "", "object.name": "Krunal_windows 10-11.246", "status": "Up"}, {"object.type": "vm", "object.ip": "", "object.name": "<PERSON><PERSON>_<PERSON><PERSON><PERSON>_Performance_master_8.182", "status": "Down"}, {"object.type": "vm", "object.ip": "", "object.name": "radwin-upgrade-iso", "status": "Down"}, {"object.type": "vm", "object.ip": "************", "object.name": "<PERSON><PERSON>_<PERSON><PERSON><PERSON>-<PERSON><PERSON><PERSON><PERSON>-<PERSON>-9.42", "status": "Up"}, {"object.type": "vm", "object.ip": "************", "object.name": "<PERSON><PERSON>_<PERSON><PERSON><PERSON>_Ubuntu_Test_9.83", "status": "Up"}, {"object.type": "vm", "object.ip": "************", "object.name": "Support_umang_vulnerabilities assessment_16.04.4_9.1", "status": "Up"}, {"object.type": "vm", "object.ip": "", "object.name": "Motadata ITSM Internal_8.94", "status": "Down"}, {"object.type": "vm", "object.ip": "", "object.name": "motadata IPAM (internal monitor)_9.52", "status": "Up"}, {"object.type": "vm", "object.ip": "************", "object.name": "<PERSON><PERSON><PERSON>_<PERSON><PERSON>_<PERSON><PERSON><PERSON>_rpe_8.177", "status": "Up"}]}, "************": {"status": "succeed", "objects": [{"object.type": "vm", "object.ip": "", "object.name": "Radwin-NileshT-Ubuntu-16 10.95", "status": "Down"}, {"object.type": "vm", "object.ip": "", "object.name": "8.0-nilesh database windows 10_10.132", "status": "Up"}, {"object.type": "vm", "object.ip": "************", "object.name": "8.0_ni<PERSON> parmar_ubuntu_8.136", "status": "Up"}, {"object.type": "vm", "object.ip": "************", "object.name": "iso -7.6.400 test -RPE_9.164", "status": "Up"}, {"object.type": "vm", "object.ip": "************", "object.name": "Motadata-ubuntu-20", "status": "Up"}, {"object.type": "vm", "object.ip": "*************", "object.name": "Radwin-ubuntu-20-11.237", "status": "Up"}, {"object.type": "vm", "object.ip": "************", "object.name": "8.0_ubuntu_mail server_8.114", "status": "Up"}, {"object.type": "vm", "object.ip": "", "object.name": "CentOS-8.3", "status": "Down"}, {"object.type": "vm", "object.ip": "************", "object.name": "Delivery_<PERSON><PERSON>_Master_(2)_8.223", "status": "Up"}]}, "*************": {"status": "succeed", "objects": [{"object.type": "vm", "object.ip": "", "object.name": "ubuntu 16vm", "status": "Up"}]}}