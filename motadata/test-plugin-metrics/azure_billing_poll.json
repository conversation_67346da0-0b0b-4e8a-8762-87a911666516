{"127.0.0.1": {"_type": "1", "cloud.client.id": "aaa130f3-5ed7-404b-87f5-90a5a42750ea", "cloud.secret.key": "**********************************", "cloud.tenant.id": "5b4acec3-2592-4187-9489-98c654cc6c87", "cloud.type": "Azure Cloud", "credential.profile.name": "Azure-Cloud-Test1655203790900", "credential.profile.protocol": "Cloud", "discovery.category": "Cloud", "discovery.context": {}, "discovery.credential.profiles": [***************], "discovery.discovered.objects": 24, "discovery.event.processors": [176993700471165], "discovery.failed.objects": 0, "discovery.groups": [**************], "discovery.name": "Azure-Cloud-Test-1655203790903", "discovery.object.type": "Azure Cloud", "discovery.progress": 100, "discovery.status": "Last ran at 04:20:25.670 pm 14/06/2022", "discovery.total.objects": 24, "errors": [], "event.id": 176993700500091, "event.timestamp": **********, "event.topic": "remote.event.processor ", "event.type": "metric.poll", "id": ***************, "metric.category": "Cloud", "metric.credential.profile": ***************, "metric.credential.profile.protocol": "Cloud", "metric.discovery.method": "REMOTE", "metric.name": "Azure Cloud Billing", "metric.object": ***************, "metric.plugin": "azurebilling", "metric.polling.min.time": 43200, "metric.polling.time": 5000, "metric.state": "ENABLE", "metric.type": "Azure Cloud", "object.account.id": "5807cfb0-41a6-4da6-b920-71d934d4a2af", "object.business.hour.profile": **************, "object.category": "Cloud", "object.creation.time": "04:20:35.677 pm 14/06/2022", "object.discovery.method": "REMOTE", "object.event.processors": [], "object.groups": [**************, **************], "object.id": 44, "object.name": "Azure Cloud (5807cfb0-41a6-4da6-b920-71d934d4a2af)", "object.state": "ENABLE", "object.target": "Azure Cloud (5807cfb0-41a6-4da6-b920-71d934d4a2af)", "object.type": "Azure Cloud", "object.vendor": "Azure Cloud", "plugin.engine": "go", "plugin.id": 14, "remote.event.processor.uuid": "4066de21-18b0-49df-b20d-fe59231e2bd4", "result": {"azure.billing.amount": 122.16, "azure.billing.service": [{"azure.billing.service": "Application Gateway", "azure.billing.service.usage.amount": 16.21, "azure.billing.service.used.units": "USD"}, {"azure.billing.service": "Automation", "azure.billing.service.usage.amount": 0, "azure.billing.service.used.units": "USD"}, {"azure.billing.service": "Azure App Service", "azure.billing.service.usage.amount": 0, "azure.billing.service.used.units": "USD"}, {"azure.billing.service": "Azure Cosmos DB", "azure.billing.service.usage.amount": 0, "azure.billing.service.used.units": "USD"}, {"azure.billing.service": "Azure Database for PostgreSQL", "azure.billing.service.usage.amount": 14.28, "azure.billing.service.used.units": "USD"}, {"azure.billing.service": "Azure DNS", "azure.billing.service.usage.amount": 0.44, "azure.billing.service.used.units": "USD"}, {"azure.billing.service": "Bandwidth", "azure.billing.service.usage.amount": 0.76, "azure.billing.service.used.units": "USD"}, {"azure.billing.service": "Content Delivery Network", "azure.billing.service.usage.amount": 0, "azure.billing.service.used.units": "USD"}, {"azure.billing.service": "<PERSON><PERSON>r", "azure.billing.service.usage.amount": 16.27, "azure.billing.service.used.units": "USD"}, {"azure.billing.service": "Log Analytics", "azure.billing.service.usage.amount": 0, "azure.billing.service.used.units": "USD"}, {"azure.billing.service": "Network Watcher", "azure.billing.service.usage.amount": 0.44, "azure.billing.service.used.units": "USD"}, {"azure.billing.service": "Service Bus", "azure.billing.service.usage.amount": 0, "azure.billing.service.used.units": "USD"}, {"azure.billing.service": "SQL Database", "azure.billing.service.usage.amount": 4.36, "azure.billing.service.used.units": "USD"}, {"azure.billing.service": "Storage", "azure.billing.service.usage.amount": 35.66, "azure.billing.service.used.units": "USD"}, {"azure.billing.service": "Virtual Machines", "azure.billing.service.usage.amount": 20.32, "azure.billing.service.used.units": "USD"}, {"azure.billing.service": "Virtual Network", "azure.billing.service.usage.amount": 13.41, "azure.billing.service.used.units": "USD"}]}, "status": "succeed", "timeout": 120}}