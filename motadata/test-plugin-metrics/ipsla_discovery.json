{"***********_ipslaicmpecho": {"status": "succeed", "result": {"objects": [{"destination.ip.address": "**********", "destination.router.location": "Delhi", "internet.service.provider": "Airtel", "object.ipsla.index": 123, "object.name": "Airtel-***********->**********", "source.ip.address": "***********", "source.router.location": "Ahmedabad", "object.type": "ipslaicmpjitter"}]}}, "***********_ipslaicmpjitter": {"status": "succeed", "result": {"objects": [{"destination.ip.address": "**********", "destination.router.location": "Delhi", "internet.service.provider": "Airtel", "object.ipsla.index": 124, "object.name": "Airtel-***********->**********", "source.ip.address": "***********", "source.router.location": "Ahmedabad", "object.type": "ipslaicmpjitter"}]}}, "***********_ipslapathecho": {"status": "succeed", "result": {"objects": [{"destination.ip.address": "**********", "destination.router.location": "Delhi", "internet.service.provider": "Airtel", "object.ipsla.index": 125, "object.name": "Airtel-***********->**********", "source.ip.address": "***********", "source.router.location": "Ahmedabad", "object.type": "ipslapathecho"}]}}, "***********_ipsla_csv": {"status": "succeed", "result": {"objects": [{"destination.ip.address": "**********", "destination.router.location": "Delhi", "internet.service.provider": "Airtel", "object.ipsla.index": 125, "object.name": "Airtel-***********->**********", "source.ip.address": "***********", "source.router.location": "Ahmedabad", "object.type": "ipslapathecho"}]}}}