{"172.16.10.165": {"result": {"esxi.vm": [{"esxi.vm.disk.capacity.bytes": 6694044883, "esxi.vm.disk.free.bytes": 432013810, "esxi.vm.disk.used.bytes": 6262031073, "esxi.vm.disk.used.percent": 94.0, "esxi.vm.virtual.processors": 3, "esxi.vm.virtual.disk.volumes": 1, "esxi.vm.connection.state": "connected", "esxi.vm.virtual.interfaces": 1, "esxi.vm.memory.bytes": 134217728, "esxi.vm": "ubuntu 16vm", "esxi.vm.path": "[datastore1] ubuntu 16vm/ubuntu 16vm.vmx", "esxi.vm.guest.os": "Ubuntu Linux (64-bit)", "esxi.vm.power.state": "poweredOn", "status": "Up", "esxi.vm.tool": false, "esxi.vm.cpu.max.usage.hz": 7182000000, "esxi.vm.started.time.sec": 37909693, "esxi.vm.started.time": "438 days, 18 hours, 28 minutes, 13 seconds", "esxi.vm.cpu.percent": 1.14, "esxi.vm.cpu.ready.seconds": 90000, "esxi.vm.balloon.memory.bytes": 0, "esxi.vm.active.memory.bytes": 85897216, "esxi.vm.shared.memory.bytes": 0, "esxi.vm.active.write.memory.bytes": 68448256, "esxi.vm.swap.out.memory.bytes": 0, "esxi.vm.cpu.wait.seconds": 59438000, "esxi.vm.swap.in.memory.bytes.per.sec": 0, "esxi.vm.overhead.memory.bytes": 23457792, "esxi.vm.disk.io.bytes.per.sec": 2048, "esxi.vm.consumed.memory.bytes": 134217728, "esxi.vm.swap.in.memory.bytes": 0, "esxi.vm.swap.out.memory.bytes.per.sec": 0, "esxi.vm.granted.memory.bytes": 134217728, "esxi.vm.memory.used.percent": 63.99, "esxi.vm.cpu.usage.hz": 82000000, "esxi.vm.disk.io.write.bytes.per.sec": 1024, "esxi.vm.disk.io.read.bytes.per.sec": 1024, "esxi.vm.disk.io.aborted.commands": 0, "esxi.vm.disk.io.avg.read.latency.ms": 5.0, "esxi.vm.disk.io.avg.write.latency.ms": 0.0, "esxi.vm.disk.io.pending.reads": 0, "esxi.vm.disk.io.pending.writes": 0, "esxi.vm.disk.io.read.ops.per.sec": 0, "esxi.vm.disk.io.write.ops.per.sec": 0, "esxi.vm.network.in.bytes.per.sec": 2048, "esxi.vm.network.out.bytes.per.sec": 0, "esxi.vm.network.bytes.per.sec": 3072}, {"esxi.vm": "test vm", "status": "Down"}]}, "errors": []}, "172.16.10.26": {"result": {"esxi.vm": [{"esxi.vm.disk.capacity.bytes": 87089799032, "esxi.vm.disk.free.bytes": 34195112523, "esxi.vm.disk.used.bytes": 52894686509, "esxi.vm.disk.used.percent": 61.0, "esxi.vm.virtual.processors": 1, "esxi.vm.virtual.disk.volumes": 1, "esxi.vm.connection.state": "connected", "esxi.vm.virtual.interfaces": 1, "esxi.vm.memory.bytes": 1073741824, "esxi.vm": "8.0_ni<PERSON> parmar_ubuntu_8.136", "esxi.vm.path": "[datastore1 (2)] nilesh parmar ubuntu/nilesh parmar ubuntu.vmx", "esxi.vm.guest.os": "Ubuntu Linux (64-bit)", "esxi.vm.power.state": "poweredOn", "status": "Up", "esxi.vm.ip": "************", "esxi.vm.guest.host": "ubuntu", "esxi.vm.tool": true, "esxi.vm.cpu.max.usage.hz": 2899000000, "esxi.vm.started.time.sec": 44411994, "esxi.vm.started.time": "514 days, 0 hour, 39 minutes, 54 seconds", "esxi.vm.cpu.wait.seconds": 19840000, "esxi.vm.cpu.percent": 0.97, "esxi.vm.cpu.ready.seconds": 5000, "esxi.vm.active.write.memory.bytes": 246960128, "esxi.vm.swap.out.memory.bytes.per.sec": 0, "esxi.vm.cpu.usage.hz": 28000000, "esxi.vm.shared.memory.bytes": 0, "esxi.vm.balloon.memory.bytes": 0, "esxi.vm.disk.io.bytes.per.sec": 5120, "esxi.vm.memory.used.percent": 25.99, "esxi.vm.consumed.memory.bytes": 1073741824, "esxi.vm.granted.memory.bytes": 1073741824, "esxi.vm.active.memory.bytes": 279171072, "esxi.vm.overhead.memory.bytes": 26296320, "esxi.vm.swap.in.memory.bytes": 0, "esxi.vm.swap.out.memory.bytes": 0, "esxi.vm.swap.in.memory.bytes.per.sec": 0, "esxi.vm.disk.io.write.bytes.per.sec": 5120, "esxi.vm.disk.io.read.bytes.per.sec": 0, "esxi.vm.disk.io.aborted.commands": 0, "esxi.vm.disk.io.avg.read.latency.ms": 0.0, "esxi.vm.disk.io.avg.write.latency.ms": 0.0, "esxi.vm.disk.io.pending.reads": 0, "esxi.vm.disk.io.pending.writes": 0, "esxi.vm.disk.io.read.ops.per.sec": 0, "esxi.vm.disk.io.write.ops.per.sec": 0, "esxi.vm.network.in.bytes.per.sec": 2048, "esxi.vm.network.out.bytes.per.sec": 0, "esxi.vm.network.bytes.per.sec": 2048}, {"esxi.vm": "<PERSON><PERSON><PERSON>_Master_7.5.2_ip_8.126_ip_8.232", "status": "Down"}]}, "errors": []}, "************": {"result": {"esxi.vm": [{"esxi.vm": "akash motadata upgrade 7.5.700 rpe_8.189", "status": "Down"}, {"esxi.vm": "8.0 CRM 2013_clone", "status": "Down"}]}, "errors": []}}