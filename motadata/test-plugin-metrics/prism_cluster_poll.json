{"172.16.10.212": {"result": {"prism.cluster": [{"prism.cluster": "Motadata-Cluster", "prism.cluster.arch": "X86_64", "prism.cluster.controller.io.bandwidth.bytes.per.sec": 0, "prism.cluster.controller.io.latency.ms": 4, "prism.cluster.controller.iops": 0, "prism.cluster.controller.read.iops": 0, "prism.cluster.controller.write.iops": 0, "prism.cluster.current.redundancy.factor": 1, "prism.cluster.desired.redundancy.factor": 1, "prism.cluster.external.subnet": "**********/*************", "prism.cluster.full.version": "el7.3-release-ce-2020.09.16-stable-d4fc219b73b4181935a3a19465eb922313fc735f", "prism.cluster.hosts": 1, "prism.cluster.hypervisor.cpu.used.percent": 12, "prism.cluster.hypervisor.io.bandwidth.bytes.per.sec": 0, "prism.cluster.hypervisor.io.latency.ms": 0, "prism.cluster.hypervisor.iops": 0, "prism.cluster.hypervisor.memory.used.percent": 60, "prism.cluster.hypervisor.read.iops": 0, "prism.cluster.hypervisor.write.iops": 0, "prism.cluster.internal.subnet": "***********/***************", "prism.cluster.io.bandwidth.bytes.per.sec": 2000, "prism.cluster.io.latency.ms": 112, "prism.cluster.iops": 0, "prism.cluster.operation.mode": "Normal", "prism.cluster.read.io.bandwidth.bytes.per.sec": 1000, "prism.cluster.read.iops": 0, "prism.cluster.storage.capacity.bytes": ************, "prism.cluster.storage.free.bytes": 509244076508, "prism.cluster.storage.reserved.capacity.bytes": 0, "prism.cluster.storage.reserved.free.bytes": 0, "prism.cluster.storage.reserved.used.bytes": 0, "prism.cluster.storage.tier.das.sata.capacity.bytes": 0, "prism.cluster.storage.tier.das.sata.free.bytes": 0, "prism.cluster.storage.tier.das.sata.used.bytes": 0, "prism.cluster.storage.tier.ssd.capacity.bytes": ************, "prism.cluster.storage.tier.ssd.free.bytes": ************, "prism.cluster.storage.tier.ssd.used.bytes": 2570115215, "prism.cluster.storage.type": "all_flash", "prism.cluster.storage.used.bytes": 2559266816, "prism.cluster.target.version": "2020.09.16", "prism.cluster.uuid": "0005f64e-ac3c-aa74-66ad-005056bb7be9", "prism.cluster.write.io.bandwidth.bytes.per.sec": 0, "prism.cluster.write.iops": 0}], "prism.clusters": 1}, "errors": []}}