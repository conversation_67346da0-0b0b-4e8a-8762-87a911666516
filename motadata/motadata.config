classpath lib/*.jar

option -Xms1000m
option -Xmx8000m

option -Dvertx.metrics.options.enabled=false

option -XX:MaxDirectMemorySize=5g
option -Dsun.reflect.inflationThreshold=2147483647
option -Dcom.sun.CORBA.transport.ORBUseNIOSelectToWait=false
option -XX:+UseCompressedOops
option -XX:+UseParallelGC
option -d64
option -server
option -XX:+AggressiveOpts
option -Dio.netty.noPreferDirect=true
option -Dio.netty.allocator.type=unpooled
option -Dio.netty.transport.outboundBufferEntrySizeOverhead=0
option -Dio.netty.transport.pendingWriteSizeOverhead=0
option -Djava.security.egd=file:/dev/./urandom
option --add-exports=java.base/jdk.internal.ref=ALL-UNNAMED
option --add-exports=java.base/sun.nio.ch=ALL-UNNAMED
option --add-exports=jdk.unsupported/sun.misc=ALL-UNNAMED
option --add-exports=jdk.compiler/com.sun.tools.javac.file=ALL-UNNAMED
option --add-opens=jdk.compiler/com.sun.tools.javac=ALL-UNNAMED
option --add-opens=java.base/java.lang=ALL-UNNAMED
option --add-opens=java.base/java.lang.reflect=ALL-UNNAMED
option --add-opens=java.base/java.io=ALL-UNNAMED
option --add-opens=java.base/java.util=ALL-UNNAMED

jvmdll ./jdk/lib/server/libjvm.so

forcewd .
stderr /dev/null
stdout /dev/null

