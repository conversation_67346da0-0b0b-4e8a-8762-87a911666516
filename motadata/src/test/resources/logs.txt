{"host":"***********","timestamp":1521722147,"fe1f688d-b1c0-43f5-89a7-d6966b52e857":"0","33230e68-c705-4bcf-9b7f-9d5fd40f653f":"Note","f003204b-388d-435d-927c-d052a8f37530":"MY-012487","d3ab3cc5-48b2-43ac-bb59-4905fea07788":"InnoDB","message":"InnoDB: DDL log recovery : begin","log.pattern.id":13005743925239}
{"host":"***********","timestamp":1521722147,"fe1f688d-b1c0-43f5-89a7-d6966b52e857":"0","33230e68-c705-4bcf-9b7f-9d5fd40f653f":"Note","f003204b-388d-435d-927c-d052a8f37530":"MY-012487","d3ab3cc5-48b2-43ac-bb59-4905fea07788":"InnoDB","message":"InnoDB: DDL log recovery : begin","log.pattern.id":13005743925239}
{"host":"***********","timestamp":1137431334,"475e9d20-2d64-4f2e-b7ca-67dd1feddcd3":"ERROR","message":"Fatal error: Can't open privilege tables: Table 'mysql.host' doesn't exist","log.pattern.id":13005743925240}
{"host":"***********","timestamp":1137431334,"475e9d20-2d64-4f2e-b7ca-67dd1feddcd3":"ERROR","message":"Fatal error: Can't open privilege tables: Table 'mysql.host' doesn't exist","log.pattern.id":13005743925240}
{"host":"***********","426f3947-0f9c-4a35-97fe-a3c34eb20be7":"86","timestamp":950000481,"19e12bcb-2d1a-4879-8669-3d21e4036641":"localhost","75589102-5df6-43db-956c-e37466ba63d2":"sshd","71b1977c-27fb-47b2-b10e-f19929c835f5":"4623","message":" Failed password for invalid user mindarray from ************* port 59682 ssh2","log.pattern.id":13005743925231}
{"host":"***********","426f3947-0f9c-4a35-97fe-a3c34eb20be7":"38","timestamp":949994427,"19e12bcb-2d1a-4879-8669-3d21e4036641":"mindarray-pc7","75589102-5df6-43db-956c-e37466ba63d2":"sshd","71b1977c-27fb-47b2-b10e-f19929c835f5":"5948","message":" message repeated 2 times: [ Failed password for mindarray from ************* port 43714 ssh2]","log.pattern.id":13005743925231}
{"host":"***********","426f3947-0f9c-4a35-97fe-a3c34eb20be7":"38","timestamp":949994427,"19e12bcb-2d1a-4879-8669-3d21e4036641":"mindarray-pc7","75589102-5df6-43db-956c-e37466ba63d2":"sshd","71b1977c-27fb-47b2-b10e-f19929c835f5":"5948","message":" message repeated 2 times: [ Failed password for mindarray from ************* port 43714 ssh2]","log.pattern.id":13005743925231}
{"host":"***********","b0b4d10f-5740-4e0b-ad60-4c61646bd0e8":"debian-sys-maint","e3b7265f-f8a4-4cca-8efe-7c61aee12218":"localhost","b61d2f3c-8c48-40b3-a53f-998369a1a453":"0.044939","414eb03e-2053-43f0-b211-707a1867111a":"0.000084","56e1e77a-ce61-4ea8-ae75-2e359868efdd":"1","b9260c68-0af8-45c7-bce3-489d31ebdf62":"5","da583f99-9a46-416f-bba5-806e66bb4fba":"SELECT count(*) FROM mysql.user WHERE user='root' and password='';","timestamp":**********,"log.pattern.id":13005743925237}
{"host":"***********","b0b4d10f-5740-4e0b-ad60-4c61646bd0e8":"debian-sys-maint","e3b7265f-f8a4-4cca-8efe-7c61aee12218":"localhost","b61d2f3c-8c48-40b3-a53f-998369a1a453":"0.044939","414eb03e-2053-43f0-b211-707a1867111a":"0.000084","56e1e77a-ce61-4ea8-ae75-2e359868efdd":"1","b9260c68-0af8-45c7-bce3-489d31ebdf62":"5","da583f99-9a46-416f-bba5-806e66bb4fba":"SELECT count(*) FROM poiii.user WHERE user='postgress' and password='';","timestamp":**********,"log.pattern.id":13005743925237}
{"host":"***********","b0b4d10f-5740-4e0b-ad60-4c61646bd0e8":"debian-sys-maint","e3b7265f-f8a4-4cca-8efe-7c61aee12218":"localhost","b61d2f3c-8c48-40b3-a53f-998369a1a453":"0.044939","414eb03e-2053-43f0-b211-707a1867111a":"0.000084","56e1e77a-ce61-4ea8-ae75-2e359868efdd":"1","b9260c68-0af8-45c7-bce3-489d31ebdf62":"5","da583f99-9a46-416f-bba5-806e66bb4fba":"SELECT count(*) FROM poiii.user WHERE user='postgress' and password='';","timestamp":**********,"log.pattern.id":13005743925237}
{"host":"***********","5133058e-bb05-45eb-9b59-3b6bd96b487b":"LOG","a60e88cc-6be1-4552-9583-ebee6a8d24ec":"16.000","message":" SELECT poll_time,poll_date,col_36188 FROM tbl_report_0_136  where poll_date='2015-8-10'  order by poll_date , poll_time","timestamp":1439200100,"log.pattern.id":13005743925236}
{"host":"***********","5133058e-bb05-45eb-9b59-3b6bd96b487b":"LOG","message":"relation \"tbl_report_17_64\" does not exist at character 23","timestamp":1437637617,"log.pattern.id":13005743925236}
{"host":"***********","5133058e-bb05-45eb-9b59-3b6bd96b487b":"ERROR","message":"relation \"tbl_report_6_64\" does not exist at character 23","timestamp":1437637618,"log.pattern.id":13005743925236}
{"host":"***********","timestamp":**********,"92637a4b-0048-48a3-8525-79a83c639bf7":"notice","message":"[pid 2736:tid 152] AH00364: Child: All worker threads have exited.","log.pattern.id":13005743925232}
{"host":"***********","timestamp":**********,"92637a4b-0048-48a3-8525-79a83c639bf7":"notice","message":"[pid 2736:tid 152] AH00364: Child: All worker threads have exited.","log.pattern.id":13005743925232}
{"host":"***********","timestamp":1377429128,"92637a4b-0048-48a3-8525-79a83c639bf7":"notice","message":"Apache/2.2.22 (Win64) PHP/5.3.13 configured -- resuming normal operations","log.pattern.id":13005743925232}
{"host":"***********","timestamp":33528,"f290f359-7c90-47b0-b219-a8fe7838b656":"INFO","6698f08c-ad4d-4cf0-981e-5b894bc0714a":"org.jboss.msc.service.fail","cdcd497d-77fb-4888-a272-a50c861ca948":"MSC service thread 1-1","298e18d7-1843-4ea7-ba99-a0b4ad66d6bf":"JBAS1064818:","message":" Listening on localhost/127.0.0.1:4447","log.pattern.id":13005743925242}
{"host":"***********","timestamp":33528,"f290f359-7c90-47b0-b219-a8fe7838b656":"INFO","6698f08c-ad4d-4cf0-981e-5b894bc0714a":"org.jboss.msc.service.fail","cdcd497d-77fb-4888-a272-a50c861ca948":"MSC service thread 1-1","298e18d7-1843-4ea7-ba99-a0b4ad66d6bf":"JBAS1064818:","message":" Listening on localhost/127.0.0.1:4447","log.pattern.id":13005743925242}
{"host":"***********","timestamp":33528,"f290f359-7c90-47b0-b219-a8fe7838b656":"INFO","6698f08c-ad4d-4cf0-981e-5b894bc0714a":"org.jboss.as.server.deployment","cdcd497d-77fb-4888-a272-a50c861ca948":"MSC service thread 1-2","298e18d7-1843-4ea7-ba99-a0b4ad66d6bf":"JBAS550598:","message":" Failed to start service jboss.web.connector.http: org.jboss.msc.service.StartException in service jboss.web.connector.http: JBAS018007: Error starting web connector","log.pattern.id":13005743925242}
{"host":"***********","timestamp":1549465149,"ca6fb506-8fb3-4599-ad77-c18eed0cea64":"Media Recovery Log","ca33878d-98fe-4bfd-9806-a4bcf97fe582":"+RECO01/sbisgnrdr/archivelog/1_6469_980122429.dbf Media Recovery Waiting for thread 1 sequence 6470","log.pattern.id":13005743925247}
{"host":"***********","timestamp":1549465149,"ca6fb506-8fb3-4599-ad77-c18eed0cea64":"Media Recovery Log","ca33878d-98fe-4bfd-9806-a4bcf97fe582":"+RECO01/sbisgnrdr/archivelog/1_6469_980122429.dbf Media Recovery Waiting for thread 1 sequence 6470","log.pattern.id":13005743925247}
{"host":"***********","timestamp":1538799875,"7ed46dbf-1dc2-41fc-bb66-d985a9b517d9":"164","b74725a7-d749-48fc-bb22-58383598af44":"CONNECT","7c8da16a-0db0-4b96-8f11-e374fdd2674b":"SYS","d9b17d84-7edf-43cd-9c3e-a8cd8111999f":"SYSDBA","ee8c396f-58be-4e76-8df8-d1588b9e9f08":"oracle","f331a424-d73f-4073-b044-0fcaffa2a0cd":"unknown","be72f756-63a3-4382-9c0a-d71751617224":"0","de9dc7b2-cc77-435b-b78d-9ff4ea6e8cc0":"1858003379","log.pattern.id":13005743925246}
{"host":"***********","timestamp":1538799875,"7ed46dbf-1dc2-41fc-bb66-d985a9b517d9":"164","b74725a7-d749-48fc-bb22-58383598af44":"CONNECT","7c8da16a-0db0-4b96-8f11-e374fdd2674b":"SYS","d9b17d84-7edf-43cd-9c3e-a8cd8111999f":"SYSDBA","ee8c396f-58be-4e76-8df8-d1588b9e9f08":"oracle","f331a424-d73f-4073-b044-0fcaffa2a0cd":"unknown","be72f756-63a3-4382-9c0a-d71751617224":"0","de9dc7b2-cc77-435b-b78d-9ff4ea6e8cc0":"1858003379","log.pattern.id":13005743925246}
{"host":"***********","timestamp":1509452795,"834ed792-d23c-4e5a-ab73-f17fa76f7eb9":"*************","fba46153-97f9-4731-bd42-8d36ee489324":"test.example.com","bccc9cfb-4718-43e3-98bc-43c9371ed77d":"A","log.pattern.id":13005743925245}
{"host":"***********","timestamp":1532371705,"834ed792-d23c-4e5a-ab73-f17fa76f7eb9":"*******","fba46153-97f9-4731-bd42-8d36ee489324":"emeidi.com","bccc9cfb-4718-43e3-98bc-43c9371ed77d":"A","1b9204be-afd2-4f2c-8145-c210b61deae3":"*******","log.pattern.id":13005743925245}
{"host":"***********","timestamp":1532371705,"834ed792-d23c-4e5a-ab73-f17fa76f7eb9":"*******","fba46153-97f9-4731-bd42-8d36ee489324":"emeidi.com","bccc9cfb-4718-43e3-98bc-43c9371ed77d":"A","1b9204be-afd2-4f2c-8145-c210b61deae3":"*******","log.pattern.id":13005743925245}
{"host":"***********","a8ccf274-d300-4c80-9259-fb209a571998":"9517:M","timestamp":965459252,"message":"DB loaded from disk: 0.000 seconds","log.pattern.id":13005743925238}
{"host":"***********","a8ccf274-d300-4c80-9259-fb209a571998":"9517:M","timestamp":965459253,"message":"DB 0: 2 keys (0 volatile) in 4 slots HT.","log.pattern.id":13005743925238}
{"host":"***********","a8ccf274-d300-4c80-9259-fb209a571998":"9517:M","timestamp":965459253,"message":"DB 0: 2 keys (0 volatile) in 4 slots HT.","log.pattern.id":13005743925238}
{"host":"***********","timestamp":1549406788,"dd9ad3e1-8b1f-4e52-953d-faca05422207":"Info","2a72fc38-4c1d-488a-b6ac-17f1602a2ed8":"com.polaris.canvas.viewdefinition.SimpleViewDefinitionInstruction","1901ab65-eaa3-40aa-84fe-538d6ef1427b":"custodialuat","70fbdee7-9869-4e85-8341-d81d8aaa9b78":"icustadm","5afcc33a-6343-44fe-a8ec-6399ec6d642b":"leroya","c09dc515-48fc-4c65-8667-a0a2bad55ade":"anonymous","cb7efb8b-acbd-4d74-bf97-e5febb7aa456":"1549449988410","2fe78dde-60a1-4cad-a4fc-ab8a638c59d9":"BEA-000000","message":"[CTVDF00377]:Exited from getViewData method ","log.pattern.id":13005743925243}
{"host":"***********","timestamp":1549406788,"dd9ad3e1-8b1f-4e52-953d-faca05422207":"Info","2a72fc38-4c1d-488a-b6ac-17f1602a2ed8":"com.polaris.canvas.viewdefinition.SimpleViewDefinitionInstruction","1901ab65-eaa3-40aa-84fe-538d6ef1427b":"custodialuat","70fbdee7-9869-4e85-8341-d81d8aaa9b78":"icustadm","5afcc33a-6343-44fe-a8ec-6399ec6d642b":"leroya","c09dc515-48fc-4c65-8667-a0a2bad55ade":"anonymous","cb7efb8b-acbd-4d74-bf97-e5febb7aa456":"1549449988410","2fe78dde-60a1-4cad-a4fc-ab8a638c59d9":"BEA-000000","message":"[CTVDF00377]:Exited from getViewData method ","log.pattern.id":13005743925243}
{"host":"***********","timestamp":1548646584,"92e4cb13-c5c2-4a88-9aba-fcb021c03289":"Info","2c669433-31a9-4911-996f-8efce9ec55a0":"Security","f55ff1d3-060b-4fd3-8fdb-83112b595d0f":"BEA-090906","message":"Changing the default Random Number Generator in RSA CryptoJ from ECDRBG128 to FIPS186PRNG. To disable this change, specify -Dweblogic.security.allowCryptoJDefaultPRNG=true.","log.pattern.id":13005743925244}
{"host":"***********","timestamp":1548646584,"92e4cb13-c5c2-4a88-9aba-fcb021c03289":"Info","2c669433-31a9-4911-996f-8efce9ec55a0":"Security","f55ff1d3-060b-4fd3-8fdb-83112b595d0f":"BEA-090906","message":"Changing the default Random Number Generator in RSA CryptoJ from ECDRBG128 to FIPS186PRNG. To disable this change, specify -Dweblogic.security.allowCryptoJDefaultPRNG=true.","log.pattern.id":13005743925244}
{"host":"***********","timestamp":1435704683,"303f5f7e-0b59-4d1e-96cd-9859386f98ab":"************","2ce16c96-1ebf-4c50-8e21-19b004c5f419":"GET","cd559129-d84b-428e-b3b8-c5be34eb772a":"80","9f510753-2a77-4f16-a6f2-622aa3be44f9":"*************","55d92cf9-d288-4b59-84f7-b398472230c2":"200","8714f9dd-bbd0-4fe8-91a1-192cdb384f94":"0","d438a926-2072-44c9-b6c9-2cd75c21591a":"64","e0d6b9b2-9310-42a5-af04-f82aac36278d":"14439","log.pattern.id":13005743925235}
{"host":"***********","timestamp":1435704683,"303f5f7e-0b59-4d1e-96cd-9859386f98ab":"************","2ce16c96-1ebf-4c50-8e21-19b004c5f419":"GET","cd559129-d84b-428e-b3b8-c5be34eb772a":"80","9f510753-2a77-4f16-a6f2-622aa3be44f9":"*************","55d92cf9-d288-4b59-84f7-b398472230c2":"200","8714f9dd-bbd0-4fe8-91a1-192cdb384f94":"0","d438a926-2072-44c9-b6c9-2cd75c21591a":"64","e0d6b9b2-9310-42a5-af04-f82aac36278d":"14439","log.pattern.id":13005743925235}
{"host":"***********","timestamp":1435728381,"303f5f7e-0b59-4d1e-96cd-9859386f98ab":"************","2ce16c96-1ebf-4c50-8e21-19b004c5f419":"GET","cd559129-d84b-428e-b3b8-c5be34eb772a":"80","9f510753-2a77-4f16-a6f2-622aa3be44f9":"*************","55d92cf9-d288-4b59-84f7-b398472230c2":"200","8714f9dd-bbd0-4fe8-91a1-192cdb384f94":"0","d438a926-2072-44c9-b6c9-2cd75c21591a":"0","e0d6b9b2-9310-42a5-af04-f82aac36278d":"519","log.pattern.id":13005743925235}
{"host":"***********","f957e8d6-8069-4119-80f6-fbd0ed0125d7":"**************","timestamp":1500835606,"51aee717-ab9f-4a4e-9404-5aa4c538c6d1":"GET","a27ce73d-29ab-4904-bc99-572657278cfc":"/info.php","7f11314f-e045-44fd-b039-4b709a7382e3":"1.1","be3032bc-5962-4d81-ac36-29d680af25a2":"200","6b3faef7-8281-401e-b42d-d38420ac68bb":"24564","68f17c59-458b-4371-8836-ebce5ab5eafc":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_12_4) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/59.0.3071.115 Safari/537.36","log.pattern.id":**************}
{"host":"***********","f957e8d6-8069-4119-80f6-fbd0ed0125d7":"************","timestamp":**********,"51aee717-ab9f-4a4e-9404-5aa4c538c6d1":"GET","a27ce73d-29ab-4904-bc99-572657278cfc":"/twiki/bin/rdiff/TWiki/NewUserTemplate?rev1=1.3&rev2=1.2","7f11314f-e045-44fd-b039-4b709a7382e3":"1","be3032bc-5962-4d81-ac36-29d680af25a2":"200","6b3faef7-8281-401e-b42d-d38420ac68bb":"4523","log.pattern.id":**************}
{"host":"***********","f957e8d6-8069-4119-80f6-fbd0ed0125d7":"************","timestamp":**********,"51aee717-ab9f-4a4e-9404-5aa4c538c6d1":"GET","a27ce73d-29ab-4904-bc99-572657278cfc":"/twiki/bin/rdiff/TWiki/NewUserTemplate?rev1=1.3&rev2=1.2","7f11314f-e045-44fd-b039-4b709a7382e3":"1","be3032bc-5962-4d81-ac36-29d680af25a2":"200","6b3faef7-8281-401e-b42d-d38420ac68bb":"4523","log.pattern.id":**************}
{"host":"***********","$id":"1","timestamp":**********,"template":"The computer attempted to validate the credentials for an account.  Authentication Package:\t%1 Logon Account:\t%2 Source Workstation:\t%3 Error Code:\t%4","message":"The computer attempted to validate the credentials for an account.  Authentication Package:\tMICROSOFT_AUTHENTICATION_PACKAGE_V1_0 Logon Account:\tito1531 Source Workstation:\t\\\\KSLGDC-PROXY01 Error Code:\t0x0","level-description":"Informational","id":4776,"provider":"Microsoft-Windows-Security-Auditing","task":"Credential Validation","audit":"Audit Success","level":0,"source":"Security","os":"Microsoft Windows Server 2008 R2 Standard ","timezone":"+05:30","log.pattern.id":**************}
{"host":"***********","$id":"1","timestamp":**********,"template":"The computer attempted to validate the credentials for an account.  Authentication Package:\t%1 Logon Account:\t%2 Source Workstation:\t%3 Error Code:\t%4","message":"The computer attempted to validate the credentials for an account.  Authentication Package:\tMICROSOFT_AUTHENTICATION_PACKAGE_V1_0 Logon Account:\tks11657 Source Workstation:\t\\\\KSLGDC-PROXY01 Error Code:\t0x0","level-description":"Informational","id":4776,"provider":"Microsoft-Windows-Security-Auditing","task":"Credential Validation","audit":"Audit Success","level":0,"source":"Security","os":"Microsoft Windows Server 2008 R2 Standard ","timezone":"+05:30","log.pattern.id":**************}
{"host":"***********","$id":"1","timestamp":**********,"template":"The computer attempted to validate the credentials for an account.  Authentication Package:\t%1 Logon Account:\t%2 Source Workstation:\t%3 Error Code:\t%4","message":"The computer attempted to validate the credentials for an account.  Authentication Package:\tMICROSOFT_AUTHENTICATION_PACKAGE_V1_0 Logon Account:\tks11657 Source Workstation:\t\\\\KSLGDC-PROXY01 Error Code:\t0x0","level-description":"Informational","id":4776,"provider":"Microsoft-Windows-Security-Auditing","task":"Credential Validation","audit":"Audit Success","level":0,"source":"Security","os":"Microsoft Windows Server 2008 R2 Standard ","timezone":"+05:30","log.pattern.id":**************}
{"host":"***********","timestamp":**********,"27595cc1-f3c7-4c9f-b8f1-87730e7025eb":"emerg","c979fde6-aaea-4980-906c-963bd8fef571":"11212","5465c55c-bfaf-4ac8-a941-f5b071a6b346":"0","message":" \"location\" directive is not allowed here in /etc/nginx/nginx.conf:75","log.pattern.id":**************}
{"host":"***********","timestamp":**********,"27595cc1-f3c7-4c9f-b8f1-87730e7025eb":"emerg","c979fde6-aaea-4980-906c-963bd8fef571":"11400","5465c55c-bfaf-4ac8-a941-f5b071a6b346":"0","message":" \"location\" directive is not allowed here in /etc/nginx/nginx.conf:75","log.pattern.id":**************}
{"host":"***********","timestamp":**********,"27595cc1-f3c7-4c9f-b8f1-87730e7025eb":"emerg","c979fde6-aaea-4980-906c-963bd8fef571":"11400","5465c55c-bfaf-4ac8-a941-f5b071a6b346":"0","message":" \"location\" directive is not allowed here in /etc/nginx/nginx.conf:75","log.pattern.id":**************}