{"traceSegmentId": "4.57.15677638236880000", "serviceId": 3, "serviceInstanceId": 4, "refs": [{"SegmentRefType": "CROSS_PROCESS", "traceSegmentId": "3.36.15677638229570000", "spanId": 1, "peerId": 0, "peerHost": "*************:61616", "entryServiceInstanceId": 4, "parentServiceInstanceId": 3, "entryEndpointName": "{GET}/", "entryEndpointId": 0, "parentEndpointName": "/api/users", "parentEndpointId": 0 } ], "spans": [{"spanId": 1, "parentSpanId": 0, "tags": [{"key": {"id": 3, "canOverwrite": false }, "value": "sql"}, {"key": {"id": 4, "canOverwrite": false }, "value": "testdb"}, {"key": {"id": 5, "canOverwrite": false }, "value": "SELECT * FROM TBL_EMPLOYEES "} ], "operationName": "H2/JDBI/PreparedStatement/execute", "operationId": 0, "layer": "DB", "startTime": 1567763823990, "endTime": 1567763824014, "componentId": 32, "componentName": "jdbc-jdbc-driver", "refs": null, "logs": null }, {"spanId": 2, "parentSpanId": 0, "tags": [{"key": {"id": 3, "canOverwrite": false }, "value": "Redis"}, {"key": {"id": 5, "canOverwrite": false }, "value": "get msg"} ], "operationName": "Jedis/get", "operationId": 0, "layer": "CACHE", "startTime": 1567763824015, "endTime": 1567763824030, "componentId": 30, "componentName": "Jedis", "refs": null, "logs": null }, {"spanId": 3, "parentSpanId": 0, "tags": [{"key": {"id": 8, "canOverwrite": false }, "value": "*************:61616"}, {"key": {"id": 7, "canOverwrite": false }, "value": "ID:darshti-ThinkPad-T440s-39167-1567763823183-1:1:1"} ], "operationName": "ActiveMQ/Queue/ID:darshti-ThinkPad-T440s-39167-1567763823183-1:1:1/Producer", "operationId": 0, "layer": "MQ", "startTime": 1567763824030, "endTime": 1567763824031, "componentId": 45, "componentName": "activemq-producer", "refs": null, "logs": null }, {"spanId": 0, "parentSpanId": -1, "tags": [{"key": {"id": 8, "canOverwrite": false }, "value": "*************:61616"}, {"key": {"id": 7, "canOverwrite": false }, "value": "client.messages"} ], "operationName": "ActiveMQ/Queue/client.messages/Consumer", "operationId": 0, "layer": "MQ", "startTime": 1567763823688, "endTime": 1567763824032, "componentId": 46, "componentName": "activemq-consumer", "refs": [{"SegmentRefType": "CROSS_PROCESS", "traceSegmentId": "3.36.15677638229570000", "spanId": 1, "peerId": 0, "peerHost": "*************:61616", "entryServiceInstanceId": 4, "parentServiceInstanceId": 3, "entryEndpointName": "{GET}/", "entryEndpointId": 0, "parentEndpointName": "/api/users", "parentEndpointId": 0 } ], "logs": null } ], "relatedGlobalTraces": ["4.33.15677638223910001"] }
{"traceSegmentId":"2.160.15713819711680002","serviceId":2,"serviceInstanceId":2,"refs":null,"spans":[{"spanId":0,"parentSpanId":-1,"tags":[{"key":{"id":1,"canOverwrite":false},"value":"http://************:8080/loadServerDateTime"},{"key":{"id":10,"canOverwrite":false},"value":"GET"}],"operationName":"/loadServerDateTime","operationId":6,"layer":"HTTP","startTime":1571381971168,"endTime":1571381971170,"componentId":15,"componentName":"Struts2","refs":null,"logs":null}],"relatedGlobalTraces":["2.160.15713819711680003"]}
{"traceSegmentId":"2.63.15713810665780000","serviceId":2,"serviceInstanceId":2,"refs":null,"spans":[{"spanId":0,"parentSpanId":-1,"tags":[{"key":{"id":3,"canOverwrite":false},"value":"Redis"}],"operationName":"Jedis/subscribe","operationId":0,"layer":"CACHE","startTime":1571381066578,"endTime":1571382350072,"componentId":30,"componentName":"Jedis","refs":null,"logs":[{"timestamp":1571382350072,"KeyValuePair":[{"key":"event","value":"error"},{"key":"error.kind","value":"redis.clients.jedis.exceptions.JedisConnectionException"},{"key":"message","value":"Unexpected end of stream."},{"key":"stack","value":"redis.clients.jedis.exceptions.JedisConnectionException: Unexpected end of stream.\nat redis.clients.util.RedisInputStream.ensureFill(RedisInputStream.java:199)\nat redis.clients.util.RedisInputStream.readByte(RedisInputStream.java:40)\nat redis.clients.jedis.Protocol.process(Protocol.java:151)\nat redis.clients.jedis.Protocol.read(Protocol.java:215)\nat redis.clients.jedis.Connection.readProtocolWithCheckingBroken(Connection.java:340)\nat redis.clients.jedis.Connection.getRawObjectMultiBulkReply(Connection.java:285)\nat redis.clients.jedis.JedisPubSub.process(JedisPubSub.java:121)\nat redis.clients.jedis.JedisPubSub.proceed(JedisPubSub.java:115)\nat redis.clients.jedis.Jedis.subscribe$original$qfTdblQT(Jedis.java:2680)\nat redis.clients.jedis.Jedis.subscribe$original$qfTdblQT$accessor$QR9l2Hd8(Jedis.java)\nat redis.clients.jedis.Jedis$auxiliary$bgGpHYJ4.call(Unknown Source)\nat org.apache.skywalking.apm.agent.core.plugin.interceptor.enhance.InstMethodsInter.intercept(InstMethodsInter.java:93)\nat redis.clients.jedis.Jedis.subscribe(Jedis.java)\nat com.motadata.traceorg.websocket.ncmDevice.TraceOrgNCMResultCollectorSubscriber.lambda$initNCMSubscriber$0(TraceOrgNCMResultCollectorSubscriber.java:45)\nat java.lang.Thread.run(Thread.java:745)\n"}]}]}],"relatedGlobalTraces":["2.63.15713810665780001"]}