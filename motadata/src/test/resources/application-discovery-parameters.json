{"testMariaDBDiscovery": {"metric.type": "MariaDB", "discovery.method": "REMOTE", "timeout": 60, "discovery.context": {"port": 33061, "timeout": 60, "database": "mysql"}, "discovery.target": "172.16.8.165", "discovery.object.type": "Linux", "discovery.category": "Server"}, "testWildflyDiscovery": {"metric.type": "<PERSON><PERSON><PERSON>", "discovery.method": "REMOTE", "discovery.context": {"port": 9590}, "discovery.target": "172.16.8.60", "discovery.object.type": "Linux", "discovery.category": "Server"}, "testTomcatDiscovery": {"metric.type": "Apache Tomcat", "discovery.method": "REMOTE", "discovery.context": {"port": 8067}, "discovery.target": "172.16.8.60", "discovery.object.type": "Linux", "discovery.category": "Server"}, "testNginxDiscovery": {"metric.type": "<PERSON><PERSON><PERSON>", "discovery.method": "REMOTE", "discovery.context": {"port": 8086}, "discovery.target": "172.16.8.60", "discovery.object.type": "Linux", "discovery.category": "Server"}, "testApacheDiscovery": {"metric.type": "Apache HTTP", "discovery.method": "REMOTE", "discovery.context": {"port": 80}, "discovery.target": "172.16.8.60", "discovery.object.type": "Linux", "discovery.category": "Server"}, "testLightHTTPDiscovery": {"metric.type": "Light Httpd", "discovery.method": "REMOTE", "discovery.context": {"port": 8081}, "discovery.target": "172.16.8.194", "discovery.object.type": "Linux", "discovery.category": "Server"}, "testBind9Discovery": {"metric.type": "Bind9", "discovery.method": "REMOTE", "discovery.context": {"port": 8080}, "discovery.target": "172.16.8.194", "discovery.object.type": "Linux", "discovery.category": "Server"}, "testIBMWebsphereDiscovery": {"metric.type": "IBM WebSphere", "discovery.method": "REMOTE", "discovery.context": {"port": 9080}, "discovery.target": "172.16.9.145", "discovery.object.type": "Windows", "discovery.category": "Server"}, "testRabbitMQDiscovery": {"metric.type": "RabbitMQ", "discovery.method": "REMOTE", "discovery.context": {"port": 15672}, "discovery.target": "172.16.9.145", "discovery.object.type": "Windows", "discovery.category": "Server"}, "testPostgreSQLDiscovery": {"metric.type": "PostgreSQL", "discovery.method": "REMOTE", "discovery.context": {"port": 5454, "database": "demo"}, "discovery.target": "fd00:1:1:1::132", "discovery.object.type": "Windows", "discovery.category": "Server"}, "testIBMDB2Discovery": {"metric.type": "IBM Db2", "discovery.method": "REMOTE", "discovery.context": {"port": 50000, "database": "sample"}, "discovery.target": "fd00:1:1:1::132", "discovery.object.type": "Windows", "discovery.category": "Server"}, "testMSSQLDiscovery": {"metric.type": "SQL Server", "discovery.method": "REMOTE", "timeout": 60, "discovery.context": {"port": 1440, "timeout": 60, "database": "SSEVAL2008"}, "discovery.target": "172.16.8.128", "discovery.object.type": "Windows", "discovery.category": "Server"}, "testMySQLDiscovery": {"metric.type": "MySQL", "discovery.method": "REMOTE", "timeout": 60, "discovery.context": {"port": 3317, "timeout": 60, "database": "mysql", "object": {"object.name": "mysqld.exe|\"C:\\Program Files\\MySQL\\MySQL Server 5.7\\bin\\mysqld.exe\" --defaults-file=\"C:\\ProgramData\\MySQL\\MySQL Server 5.7\\my.ini\" MySQL57", "object.type": "system.process"}}, "discovery.target": "172.16.8.166", "discovery.object.type": "Windows", "discovery.category": "Server"}, "testOracleDiscovery": {"metric.type": "Oracle Database", "discovery.method": "REMOTE", "discovery.context": {"port": 1521, "database": "SID=asmdb"}, "discovery.target": "172.16.8.166", "discovery.object.type": "Windows", "discovery.category": "Server"}, "testSybaseDiscovery": {"metric.type": "Sybase", "discovery.method": "REMOTE", "discovery.context": {"port": 5000, "database": "master"}, "discovery.target": "172.16.8.165", "discovery.object.type": "Linux", "discovery.category": "Server"}, "testSAPMaxDBDiscovery": {"metric.type": "SAP MaxDB", "discovery.method": "REMOTE", "discovery.context": {"port": 7203, "database": "MaxDB1"}, "discovery.target": "172.16.10.84", "discovery.object.type": "Linux", "discovery.category": "Server"}, "testSAPHANADiscovery": {"metric.type": "SAP HANA", "discovery.method": "REMOTE", "discovery.context": {"port.check.status": "no", "port": 39013, "database": "SYSTEMDB"}, "discovery.target": "172.16.10.81", "discovery.object.type": "Linux", "discovery.category": "Server"}, "testLinuxDHCPDiscovery": {"metric.type": "Linux DHCP", "discovery.method": "REMOTE", "discovery.context": {"config.file.name": "/etc/dhcp/dhcpd.conf", "lease.file.name": "/var/lib/dhcp/dhcpd.leases"}, "discovery.target": "172.16.10.84", "discovery.object.type": "Linux", "discovery.category": "Server"}, "testHAProxyDiscovery": {"metric.type": "HAProxy", "discovery.method": "REMOTE", "discovery.context": {}, "discovery.target": "172.16.9.73", "discovery.object.type": "Linux", "discovery.category": "Server"}, "testActiveDirectoryDiscovery": {"metric.type": "Active Directory", "discovery.method": "REMOTE", "discovery.context": {}, "discovery.target": "172.16.8.113", "discovery.object.type": "Windows", "discovery.category": "Server"}, "testIISDiscovery": {"metric.type": "Microsoft IIS", "discovery.method": "REMOTE", "discovery.context": {}, "discovery.target": "172.16.8.113", "discovery.object.type": "Windows", "discovery.category": "Server"}, "testWindowsDHCPDiscovery": {"metric.type": "Windows DHCP", "discovery.method": "REMOTE", "discovery.context": {}, "discovery.target": "172.16.8.113", "discovery.object.type": "Windows", "discovery.category": "Server"}, "testWindowsDNSDiscovery": {"metric.type": "Windows DNS", "discovery.method": "REMOTE", "discovery.context": {}, "discovery.target": "172.16.8.113", "discovery.object.type": "Windows", "discovery.category": "Server"}, "testWindowsRDPDiscovery": {"metric.type": "Windows RDP", "discovery.method": "REMOTE", "discovery.context": {}, "discovery.target": "172.16.8.113", "discovery.object.type": "Windows", "discovery.category": "Server"}, "testMSMQDiscovery": {"metric.type": "MSMQ", "discovery.method": "REMOTE", "discovery.context": {}, "discovery.target": "172.16.8.113", "discovery.object.type": "Windows", "discovery.category": "Server"}, "testExchangeClientAccessRoleDiscovery": {"metric.type": "Exchange Client Access Role", "discovery.method": "REMOTE", "discovery.context": {}, "discovery.target": "172.16.8.86", "discovery.object.type": "Windows", "discovery.category": "Server"}, "testExchangeMailBoxRoleDiscovery": {"metric.type": "Exchange Mailbox Role", "discovery.method": "REMOTE", "discovery.context": {}, "discovery.target": "172.16.8.86", "discovery.object.type": "Windows", "discovery.category": "Server"}, "testExchangeEdgeTransportRoleDiscovery": {"metric.type": "Exchange Edge Transport Role", "discovery.method": "REMOTE", "discovery.context": {}, "discovery.target": "172.16.8.86", "discovery.object.type": "Windows", "discovery.category": "Server"}, "testZimbraDiscovery": {"metric.type": "Zimbra", "discovery.method": "REMOTE", "discovery.context": {}, "discovery.target": "172.16.8.60", "discovery.object.type": "Linux", "discovery.category": "Server"}}