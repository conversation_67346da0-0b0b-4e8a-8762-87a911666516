
 /*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

 package com.mindarray.eventengine;

 import com.mindarray.Bootstrap;
 import com.mindarray.GlobalConstants;
 import com.mindarray.eventbus.EventBusConstants;
 import com.mindarray.eventbus.EventEngine;
 import com.mindarray.util.*;
 import io.github.artsok.ParameterizedRepeatedIfExceptionsTest;
 import io.github.artsok.RepeatedIfExceptionsTest;
 import io.vertx.core.Promise;
 import io.vertx.core.Vertx;
 import io.vertx.core.json.JsonArray;
 import io.vertx.core.json.JsonObject;
 import io.vertx.junit5.Timeout;
 import io.vertx.junit5.VertxExtension;
 import io.vertx.junit5.VertxTestContext;
 import org.junit.jupiter.api.*;
 import org.junit.jupiter.api.condition.EnabledIfSystemProperty;
 import org.junit.jupiter.api.extension.ExtendWith;
 import org.junit.jupiter.params.provider.ValueSource;
 import org.zeromq.SocketType;
 import org.zeromq.ZMQ;

 import java.time.Duration;
 import java.util.concurrent.TimeUnit;
 import java.util.concurrent.atomic.AtomicInteger;
 import java.util.concurrent.locks.LockSupport;

 import static org.junit.jupiter.api.Assertions.assertEquals;
 import static org.junit.jupiter.api.Assertions.assertTrue;

 @ExtendWith(VertxExtension.class)
 @Timeout(150 * 1000)
 @TestMethodOrder(MethodOrderer.OrderAnnotation.class)
 @EnabledIfSystemProperty(named = "test.mode", matches = "^APP|^PRIMARY|SLAVE$")

 public class TestEventEngine
 {
     private static final long CURRENT_TIME_MILLIS = System.currentTimeMillis();

     private static final Vertx VERTX = Bootstrap.vertx();
     private static final Logger LOGGER = new Logger(TestEventEngine.class, GlobalConstants.MOTADATA_EVENT_BUS, "Event Engine Test");

     @BeforeAll
     static void setup(VertxTestContext testContext) throws Exception
     {
         testContext.awaitCompletion(5, TimeUnit.SECONDS);

         var eventPath = GlobalConstants.CURRENT_DIR + GlobalConstants.PATH_SEPARATOR + "event";

         VERTX.fileSystem().exists(eventPath, testContext.succeeding(evenDirExist ->
         {
             if (evenDirExist)
             {
                 VERTX.fileSystem().deleteRecursive(eventPath, true, testContext.succeeding(result -> testContext.completeNow()));
             }
             else
             {
                 testContext.completeNow();
             }

         }));
     }

     private static void subscribe(ZMQ.Socket forwarderSubscriber, Promise<JsonArray> promise)
     {
         forwarderSubscriber.setHWM(MotadataConfigUtil.getEventBacklogSize());

         forwarderSubscriber.setRcvHWM(MotadataConfigUtil.getEventBacklogSize());

         forwarderSubscriber.connect("tcp://*:" + 1234);

         var retries = new AtomicInteger();

         new Thread(() ->
         {
             try
             {
                 var receive = true;

                 while (receive)
                 {
                     if (forwarderSubscriber.recv().length > 0 && retries.incrementAndGet() == 3)
                     {
                         receive = false;

                         promise.complete();
                     }
                 }
             }
             catch (Exception exception)
             {
                 LOGGER.error(exception);
             }

         }, "Forwarder Subscriber").start();
     }

     private static void publish(ZMQ.Socket remotePublisher)
     {
         remotePublisher.setHWM(MotadataConfigUtil.getEventBacklogSize());

         remotePublisher.setRcvHWM(MotadataConfigUtil.getEventBacklogSize());

         remotePublisher.bind("tcp://*:" + 3456);

         var cipherUtil = new CipherUtil();

         new Thread(() ->
         {
             try
             {
                 for (var index = 0; index < 3; index++)
                 {
                     remotePublisher.send(cipherUtil.encrypt(CodecUtil.compress(new JsonObject().put("message", "device poll @" + CURRENT_TIME_MILLIS).encode())));
                 }
             }
             catch (Exception exception)
             {
                 LOGGER.error(exception);
             }
         }, "Remote Publisher").start();
     }

     @BeforeEach
     void beforeEach()
     {
         try
         {
             var field = Bootstrap.class.getDeclaredField("bootstrapType");

             field.setAccessible(true);

             field.set(new Bootstrap(), GlobalConstants.BootstrapType.APP);
         }
         catch (Exception exception)
         {
             LOGGER.error(exception);
         }
     }

     @RepeatedIfExceptionsTest(suspend = 3000L)
     @Order(1)
     void testFailedToStartEngineIfEventTypeIsNotSpecified(VertxTestContext testContext)
     {
         try
         {

             var promise = Promise.<Void>promise();

             new EventEngine().setEventType(null).start(VERTX, promise);

             promise.future().onComplete(result ->
             {
                 testContext.verify(() ->
                 {
                     assertTrue(result.failed());

                     assertEquals("Event Type is missing...", result.cause().getMessage());

                 });

                 testContext.completeNow();

             });
         }
         catch (Exception exception)
         {
             LOGGER.error(exception);
         }
     }

     @RepeatedIfExceptionsTest(suspend = 3000L)
     @Order(2)
     void testFailedToStartEngineIfEventHandlerIsMissing(VertxTestContext testContext)
     {
         try
         {

             var promise = Promise.<Void>promise();

             new EventEngine()
                     .setEventType(EventBusConstants.CHANGE_NOTIFICATION_TYPE)
                     .start(VERTX, promise);

             promise.future().onComplete(result ->
             {
                 testContext.verify(() ->
                 {
                     assertTrue(result.failed());

                     assertEquals("Event Handler is missing...", result.cause().getMessage());

                 });

                 testContext.completeNow();

             });
         }
         catch (Exception exception)
         {
             LOGGER.error(exception);
         }
     }

     @ParameterizedRepeatedIfExceptionsTest(suspend = 3000L)
     @ValueSource(ints = {0, -4})
     @Order(3)
     void testFailedToStartEngineIfEventForwarderTrueAndEventForwarderPortIsInvalid(int eventForwarderPort, VertxTestContext testContext)
     {
         try
         {

             var promise = Promise.<Void>promise();

             new EventEngine()
                     .setEventType(EventBusConstants.EVENT_CHANGE_NOTIFICATION)
                     .setEventForwarder(true)
                     .setEventForwarderPort(eventForwarderPort)
                     .start(VERTX, promise);

             promise.future().onComplete(result ->
             {
                 testContext.verify(() ->
                 {
                     assertTrue(result.failed());

                     assertEquals("Event forwarder port is invalid...", result.cause().getMessage());
                 });

                 testContext.completeNow();

             });
         }
         catch (Exception exception)
         {
             LOGGER.error(exception);
         }
     }

     @ParameterizedRepeatedIfExceptionsTest(suspend = 3000L)
     @ValueSource(ints = {0, -4})
     @Order(4)
     void testFailedToStartEngineIfRemoteEventProcessorTrueAndRemoteEventPublisherPortIsInvalid(int eventForwarderPort, VertxTestContext testContext)
     {

         MotadataConfigUtil.loadConfigs(new JsonObject().put("system.boot.sequence", "COLLECTOR"));

         try
         {

             var field = Bootstrap.class.getDeclaredField("bootstrapType");

             field.setAccessible(true);

             field.set(new Bootstrap(), GlobalConstants.BootstrapType.COLLECTOR);

             var promise = Promise.<Void>promise();

             new EventEngine()
                     .setEventType(EventBusConstants.EVENT_LOG)
                     .setRemoteEventProcessor(true)
                     .setRemoteEventPublisherPort(eventForwarderPort)
                     .setEventHandler(handler ->
                     {
                     })
                     .start(VERTX, promise);

             promise.future().onComplete(result ->
             {
                 testContext.verify(() ->
                 {
                     assertTrue(result.failed());

                     assertEquals("Remote Event publisher port is invalid...", result.cause().getMessage());
                 });

                 testContext.completeNow();
             });
         }
         catch (Exception exception)
         {
             LOGGER.error(exception);
         }
     }

     @RepeatedIfExceptionsTest(suspend = 3000L)
     @Order(5)
     void testFailedToStartEngineIfRemoteEventProcessorTrueAndRemoteEventPublisherTopicIsNull(VertxTestContext testContext)
     {
         MotadataConfigUtil.loadConfigs(new JsonObject().put("system.boot.sequence", "COLLECTOR"));

         try
         {

             var field = Bootstrap.class.getDeclaredField("bootstrapType");

             field.setAccessible(true);

             field.set(new Bootstrap(), GlobalConstants.BootstrapType.COLLECTOR);

             var promise = Promise.<Void>promise();

             new EventEngine()
                     .setEventType(EventBusConstants.EVENT_FLOW)
                     .setRemoteEventProcessor(true)
                     .setRemoteEventPublisherPort(1)
                     .setRemoteEventPublisher(null)
                     .setEventHandler(handler ->
                     {
                     })
                     .start(VERTX, promise);

             promise.future().onComplete(result ->
             {
                 testContext.verify(() ->
                 {
                     assertTrue(result.failed());

                     assertEquals("Remote Event publisher is missing...", result.cause().getMessage());
                 });

                 testContext.completeNow();
             });
         }
         catch (Exception exception)
         {
             LOGGER.error(exception);
         }
     }

     @RepeatedIfExceptionsTest(suspend = 3000L)
     @Order(6)
     void testProcessEventUsingSubscriberAndPersistOffsetOn(VertxTestContext testContext)
     {
         try
         {

             var timestamp = System.currentTimeMillis();

             var startEngine = Promise.<Void>promise();

             var stopEngine = Promise.<Void>promise();

             var retries = new AtomicInteger();

             try
             {
                 var eventEngine = new EventEngine()
                         .setEventType(EventBusConstants.EVENT_CHANGE_NOTIFICATION).setPersistEventOffset(true).setLogger(LOGGER)
                         .setEventHandler(event ->
                         {
                             try
                             {
                                 var valid = CommonUtil.getString(event.getString("message")).endsWith("@" + timestamp);

                                 if (valid)
                                 {
                                     retries.getAndIncrement();

                                     if (retries.get() == 5)
                                     {
                                         stopEngine.complete();
                                     }
                                 }
                                 else
                                 {
                                     stopEngine.fail("Invalid record found.");
                                 }
                             }
                             catch (Exception exception)
                             {
                                 LOGGER.error(exception);
                             }
                         }).start(VERTX, startEngine);


                 //Publish events one engine is up.
                 startEngine.future().onComplete(response ->
                 {

                     if (response.succeeded())
                     {
                         for (var index = 0; index <= 4; index++)
                         {
                             VERTX.eventBus().publish(EventBusConstants.EVENT_CHANGE_NOTIFICATION, new JsonObject().put("message", "change @" + timestamp));
                         }
                     }
                     else
                     {
                         testContext.failNow(response.cause());
                     }
                 });

                 stopEngine.future().onComplete(asyncResult ->
                 {
                     var promise = Promise.<Void>promise();

                     eventEngine.stop(VERTX, promise);

                     promise.future().onComplete(result ->
                     {
                         if (asyncResult.succeeded())
                         {
                             testContext.verify(() -> assertEquals(5, retries.get()));

                             testContext.completeNow();
                         }
                         else
                         {
                             testContext.failNow(asyncResult.cause());
                         }
                     });
                 });
             }
             catch (Exception exception)
             {
                 LOGGER.error(exception);
             }

         }
         catch (Exception exception)
         {
             LOGGER.error(exception);
         }
     }

     @RepeatedIfExceptionsTest(suspend = 3000L)
     @Order(7)
     void testProcessEventsBasedOnPreviousOffset(VertxTestContext testContext)
     {

         try
         {
             var timestamp = System.currentTimeMillis();

             var startEngine = Promise.<Void>promise();

             var stopEngine = Promise.<Void>promise();

             var retries = new AtomicInteger();

             var eventEngine = new EventEngine()
                     .setEventType(EventBusConstants.EVENT_DEPENDENCY).setPersistEventOffset(true).setLogger(LOGGER)
                     .setEventHandler(event ->
                     {
                         try
                         {

                             var valid = CommonUtil.getString(event.getString("message")).endsWith("@" + timestamp);

                             if (valid)
                             {
                                 retries.getAndIncrement();

                                 if (retries.get() == 5)
                                 {
                                     stopEngine.complete();
                                 }
                             }
                             else
                             {
                                 stopEngine.fail("Invalid record found.");
                             }
                         }
                         catch (Exception exception)
                         {
                             LOGGER.error(exception);
                         }
                     }).start(VERTX, startEngine);

             //Publish events one engine is up.
             startEngine.future().onComplete(response ->
             {

                 if (response.succeeded())
                 {
                     for (var index = 0; index <= 4; index++)
                     {
                         VERTX.eventBus().publish(EventBusConstants.EVENT_DEPENDENCY, new JsonObject().put("message", "change @" + timestamp));
                     }
                 }
                 else
                 {
                     testContext.failNow(response.cause());
                 }
             });

             stopEngine.future().onComplete(asyncResult ->
             {
                 var promise = Promise.<Void>promise();

                 eventEngine.stop(VERTX, promise);

                 promise.future().onComplete(result ->
                 {
                     if (asyncResult.succeeded())
                     {
                         testContext.verify(() -> assertEquals(5, retries.get()));

                         testContext.completeNow();
                     }
                     else
                     {
                         testContext.failNow(asyncResult.cause());
                     }
                 });
             });

         }
         catch (Exception exception)
         {
             LOGGER.error(exception);
         }
     }

     @RepeatedIfExceptionsTest(suspend = 3000L)
     @Order(8)
     void testEventEngineWithoutPersistOffset(VertxTestContext testContext)
     {
         try
         {
             var startEngine = Promise.<Void>promise();
             var stopEngine = Promise.<Void>promise();
             var retries = new AtomicInteger();

             var eventEngine = new EventEngine().setEventType(EventBusConstants.EVENT_DISCOVERY).setLogger(LOGGER)
                     .setEventHandler(event ->
                     {
                         try
                         {

                             var valid = CommonUtil.getString(event.getString("message")).endsWith("@" + CURRENT_TIME_MILLIS);

                             if (valid)
                             {
                                 retries.getAndIncrement();
                                 if (retries.get() >= 5)
                                 {
                                     stopEngine.complete();
                                 }
                             }
                             else
                             {
                                 stopEngine.fail("Invalid record found.");
                             }
                         }
                         catch (Exception exception)
                         {
                             LOGGER.error(exception);
                         }
                     }).start(VERTX, startEngine);

             //Publish events one engine is up.
             startEngine.future().onComplete(response ->
             {
                 if (response.succeeded())
                 {
                     for (var index = 0; index <= 4; index++)
                     {
                         VERTX.eventBus().publish(EventBusConstants.EVENT_DISCOVERY, new JsonObject().put("message", "policy message " + index + "@" + CURRENT_TIME_MILLIS));
                     }
                 }
                 else
                 {
                     testContext.failNow(response.cause());
                 }
             });
             stopEngine.future().onComplete(asyncResult ->
             {
                 var promise = Promise.<Void>promise();

                 eventEngine.stop(VERTX, promise);

                 promise.future().onComplete(result ->
                 {
                     if (asyncResult.succeeded())
                     {
                         testContext.verify(() -> assertEquals(5, retries.get()));

                         testContext.completeNow();
                     }
                     else
                     {
                         testContext.failNow(asyncResult.cause());
                     }
                 });
             });
         }
         catch (Exception exception)
         {
             LOGGER.error(exception);
         }
     }

     @RepeatedIfExceptionsTest(suspend = 3000L)
     @Order(9)
     void testBlockingEventEngine(VertxTestContext testContext)
     {
         try
         {
             var startEngine = Promise.<Void>promise();

             var retries = new AtomicInteger(0);

             var events = new AtomicInteger(0);

             new EventEngine().setBlockingEvent(true).setEventQueueSize(1)
                     .setEventType("blocking-event-1")
                     .setLogger(LOGGER)
                     .setEventHandler(event ->
                     {
                         Assertions.assertEquals(1, events.incrementAndGet());

                         if (retries.incrementAndGet() < 5)
                         {
                             VERTX.eventBus().send("blocking-event-1.reply", new JsonObject().put("message", "received"));

                             events.decrementAndGet();

                             VERTX.eventBus().send("blocking-event-1", new JsonObject().put("message", "sample blocking event"));
                         }

                         if (retries.get() == 5)
                         {
                             testContext.completeNow();
                         }
                     }).start(VERTX, startEngine);

             startEngine.future().onComplete(response ->
             {
                 if (response.succeeded())
                 {
                     VERTX.eventBus().send("blocking-event-1", new JsonObject().put("message", "sample blocking event"));
                 }
                 else
                 {
                     testContext.failNow(response.cause());
                 }
             });
         }
         catch (Exception exception)
         {
             LOGGER.error(exception);

             testContext.failNow(exception.getMessage());
         }
     }

     @RepeatedIfExceptionsTest(suspend = 3000L)
     @Order(10)
     void testEventEngineEventForwarder(VertxTestContext testContext)
     {
         try
         {
             var startEngine = Promise.<Void>promise();
             var stopEngine = Promise.<JsonArray>promise();

             var forwarderSubscriber = Bootstrap.zcontext().socket(SocketType.PULL);

             var eventEngine = new EventEngine().setPersistEventOffset(true).setEventForwarder(true).setLogger(LOGGER)
                     .setEventType("remote-event")
                     .setEventForwarderPort(1234).start(VERTX, startEngine);

             startEngine.future().onComplete(response ->
             {
                 if (response.succeeded())
                 {
                     subscribe(forwarderSubscriber, stopEngine);

                     LockSupport.parkNanos(Duration.ofMillis(100).toNanos());

                     for (var i = 0; i < 3; i++)
                     {
                         VERTX.eventBus().publish("remote-event", new JsonObject().put("message", "demo event"));
                     }
                 }
                 else
                 {
                     testContext.failNow(response.cause());
                 }
             });

             stopEngine.future().onComplete(asyncResult ->
             {
                 var promise = Promise.<Void>promise();

                 eventEngine.stop(VERTX, promise);

                 promise.future().onComplete(result ->
                 {
                     if (asyncResult.succeeded())
                     {
                         testContext.completeNow();
                     }
                     else
                     {
                         testContext.failNow(asyncResult.cause());
                     }
                 });
             });
         }
         catch (Exception exception)
         {
             LOGGER.error(exception);
         }
     }

     @RepeatedIfExceptionsTest(suspend = 3000L)
     @Order(11)
     void testEventEngineRemoteEventProcessor(VertxTestContext testContext)
     {
         try
         {
             var startEngine = Promise.<Void>promise();
             var stopEngine = Promise.<JsonArray>promise();
             var retries = new AtomicInteger();
             var field = Bootstrap.class.getDeclaredField("bootstrapType");

             field.setAccessible(true);

             field.set(new Bootstrap(), GlobalConstants.BootstrapType.COLLECTOR);

             var remotePublisher = Bootstrap.zcontext().socket(SocketType.PUSH);

             var eventEngine = new EventEngine().setLogger(LOGGER).setEventType("test-event").setRemoteEventPublisher("*").setRemoteEventProcessor(true).setRemoteEventPublisherPort(3456)
                     .setEventHandler(event ->
                     {
                         retries.getAndIncrement();

                         if (retries.get() == 3)
                         {
                             stopEngine.complete();
                         }
                     }).start(VERTX, startEngine);

             startEngine.future().onComplete(response ->
             {
                 if (response.succeeded())
                 {
                     publish(remotePublisher);
                 }
                 else
                 {
                     testContext.failNow(response.cause());
                 }
             });

             stopEngine.future().onComplete(asyncResult ->
             {
                 var promise = Promise.<Void>promise();

                 eventEngine.stop(VERTX, promise);

                 promise.future().onComplete(result ->
                 {
                     if (asyncResult.succeeded())
                     {
                         testContext.completeNow();
                     }
                     else
                     {
                         testContext.failNow(asyncResult.cause());
                     }
                 });
             });
         }
         catch (Exception exception)
         {
             LOGGER.error(exception);
         }
     }
 }
