/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

package com.mindarray.poller;

import com.mindarray.GlobalConstants;
import com.mindarray.TestNMSUtil;
import com.mindarray.TestUtil;
import com.mindarray.api.Metric;
import com.mindarray.eventbus.EventBusConstants;
import com.mindarray.nms.NMSConstants;
import com.mindarray.store.ObjectConfigStore;
import io.github.artsok.RepeatedIfExceptionsTest;
import io.vertx.core.json.JsonObject;
import io.vertx.junit5.Timeout;
import io.vertx.junit5.VertxExtension;
import io.vertx.junit5.VertxTestContext;
import org.junit.jupiter.api.*;
import org.junit.jupiter.api.condition.EnabledIfSystemProperty;
import org.junit.jupiter.api.extension.ExtendWith;

import java.io.File;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;

@ExtendWith(VertxExtension.class)
@Timeout(220 * 1000)
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
@EnabledIfSystemProperty(named = "test.mode", matches = "^APP|^PRIMARY$")
public class TestOtherObjectPoller
{
    private static final JsonObject POLLER_CONTEXT = new JsonObject();

    @BeforeAll
    static void beforeAll(VertxTestContext testContext)
    {

        try
        {
            var file = new File(GlobalConstants.CURRENT_DIR + GlobalConstants.PATH_SEPARATOR + "src" + GlobalConstants.PATH_SEPARATOR
                    + "test" + GlobalConstants.PATH_SEPARATOR + "resources" + GlobalConstants.PATH_SEPARATOR + "poll-parameters.json");

            if (file.exists())
            {
                TestUtil.startEventStreaming(new JsonObject().put(EventBusConstants.EVENT_STATE, EventBusConstants.EVENT_STATE_COMPLETED));

                POLLER_CONTEXT.mergeIn(new JsonObject(Files.readString(file.toPath(), StandardCharsets.UTF_8)));

                testContext.completeNow();
            }
        }
        catch (Exception exception)
        {
            testContext.failNow(exception.getMessage());

            exception.printStackTrace();
        }
    }

    // Disabling cisco ucs testcases as presently we are not supporting it in product
    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Disabled
    @Order(1)
    void testPollCiscoUCSChassisMetric(VertxTestContext testContext, TestInfo testInfo)
    {
        Assertions.assertNotNull(ObjectConfigStore.getStore().getItemByIP(POLLER_CONTEXT.getString(testInfo.getTestMethod().get().getName())));

        TestNMSUtil.assertMetricPollResponseTestResult(TestNMSUtil.prepareMetricPollContext(new JsonObject().put(GlobalConstants.ID, ObjectConfigStore.getStore().getItemByIP(POLLER_CONTEXT.getString(testInfo.getTestMethod().get().getName()))).put(Metric.METRIC_PLUGIN, NMSConstants.MetricPlugin.CISCO_UCS_CHASSIS.getName()), testContext), testContext);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Disabled
    @Order(2)
    void testPollCiscoUCSRackMountMetric(VertxTestContext testContext, TestInfo testInfo)
    {
        Assertions.assertNotNull(ObjectConfigStore.getStore().getItemByIP(POLLER_CONTEXT.getString(testInfo.getTestMethod().get().getName())));

        TestNMSUtil.assertMetricPollResponseTestResult(TestNMSUtil.prepareMetricPollContext(new JsonObject().put(GlobalConstants.ID, ObjectConfigStore.getStore().getItemByIP(POLLER_CONTEXT.getString(testInfo.getTestMethod().get().getName()))).put(Metric.METRIC_PLUGIN, NMSConstants.MetricPlugin.CISCO_UCS_RACK_MOUNT.getName()), testContext), testContext);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Disabled
    @Order(3)
    void testPollCiscoUCSFabricInterconnectMetric(VertxTestContext testContext, TestInfo testInfo)
    {
        Assertions.assertNotNull(ObjectConfigStore.getStore().getItemByIP(POLLER_CONTEXT.getString(testInfo.getTestMethod().get().getName())));

        TestNMSUtil.assertMetricPollResponseTestResult(TestNMSUtil.prepareMetricPollContext(new JsonObject().put(GlobalConstants.ID, ObjectConfigStore.getStore().getItemByIP(POLLER_CONTEXT.getString(testInfo.getTestMethod().get().getName()))).put(Metric.METRIC_PLUGIN, NMSConstants.MetricPlugin.CISCO_UCS_FABRIC_INTERCONNECT.getName()), testContext), testContext);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Disabled
    @Order(4)
    void testPollCiscoUCSAvailabilityMetric(VertxTestContext testContext, TestInfo testInfo)
    {
        Assertions.assertNotNull(ObjectConfigStore.getStore().getItemByIP(POLLER_CONTEXT.getString(testInfo.getTestMethod().get().getName())));

        TestNMSUtil.assertMetricPollResponseTestResult(TestNMSUtil.prepareMetricPollContext(new JsonObject().put(GlobalConstants.ID, ObjectConfigStore.getStore().getItemByIP(POLLER_CONTEXT.getString(testInfo.getTestMethod().get().getName()))).put(Metric.METRIC_PLUGIN, NMSConstants.MetricPlugin.AVAILABILITY.getName()), testContext), testContext);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(5)
    void testPollIBMTapeLibraryMetric(VertxTestContext testContext, TestInfo testInfo)
    {
        Assertions.assertNotNull(ObjectConfigStore.getStore().getItemByIP(POLLER_CONTEXT.getString(testInfo.getTestMethod().get().getName())));

        TestNMSUtil.assertMetricPollResponseTestResult(TestNMSUtil.prepareMetricPollContext(new JsonObject().put(GlobalConstants.ID, ObjectConfigStore.getStore().getItemByIP(POLLER_CONTEXT.getString(testInfo.getTestMethod().get().getName()))).put(Metric.METRIC_PLUGIN, NMSConstants.MetricPlugin.IBM_TAPE_LIBRARY.getName()), testContext), testContext);
    }

}

