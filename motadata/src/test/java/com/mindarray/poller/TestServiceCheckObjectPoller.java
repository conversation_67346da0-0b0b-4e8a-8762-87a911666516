/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

package com.mindarray.poller;

import com.mindarray.*;
import com.mindarray.api.AIOpsObject;
import com.mindarray.api.APIConstants;
import com.mindarray.api.Metric;
import com.mindarray.eventbus.EventBusConstants;
import com.mindarray.nms.NMSConstants;
import com.mindarray.store.MetricConfigStore;
import com.mindarray.store.ObjectConfigStore;
import com.mindarray.util.Logger;
import io.github.artsok.RepeatedIfExceptionsTest;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import io.vertx.junit5.Timeout;
import io.vertx.junit5.VertxExtension;
import io.vertx.junit5.VertxTestContext;
import org.junit.jupiter.api.*;
import org.junit.jupiter.api.condition.EnabledIfSystemProperty;
import org.junit.jupiter.api.extension.ExtendWith;

import java.io.File;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.util.concurrent.TimeUnit;

import static com.mindarray.GlobalConstants.*;
import static com.mindarray.api.AIOpsObject.OBJECT_IP;
import static com.mindarray.api.AIOpsObject.OBJECT_NAME;
import static org.junit.jupiter.api.Assertions.assertEquals;

@ExtendWith(VertxExtension.class)
@Timeout(220 * 1000)
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
@EnabledIfSystemProperty(named = "test.mode", matches = "^APP|^PRIMARY$")
public class TestServiceCheckObjectPoller
{
    private static final JsonObject POLLER_CONTEXT = new JsonObject();

    private static final Logger LOGGER = new Logger(TestServiceCheckObjectPoller.class, MOTADATA_NMS, "Service Check Poller Test");

    @BeforeAll
    static void beforeAll(VertxTestContext testContext)
    {

        try
        {
            var file = new File(GlobalConstants.CURRENT_DIR + GlobalConstants.PATH_SEPARATOR + "src" + GlobalConstants.PATH_SEPARATOR
                    + "test" + GlobalConstants.PATH_SEPARATOR + "resources" + GlobalConstants.PATH_SEPARATOR + "poll-parameters.json");

            if (file.exists())
            {
                TestUtil.startEventStreaming(new JsonObject().put(EventBusConstants.EVENT_STATE, EventBusConstants.EVENT_STATE_COMPLETED));

                POLLER_CONTEXT.mergeIn(new JsonObject(Files.readString(file.toPath(), StandardCharsets.UTF_8)));

                testContext.completeNow();
            }
        }
        catch (Exception exception)
        {
            testContext.failNow(exception.getMessage());

            exception.printStackTrace();
        }
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(1)
    void testPollPingServiceCheckMetric(VertxTestContext testContext, TestInfo testInfo) throws InterruptedException
    {

        testContext.awaitCompletion(2, TimeUnit.SECONDS);

        Assertions.assertNotNull(ObjectConfigStore.getStore().getItemByValue(AIOpsObject.OBJECT_IP, POLLER_CONTEXT.getString(testInfo.getTestMethod().get().getName())));

        TestNMSUtil.assertMetricPollResponseTestResult(TestNMSUtil.prepareMetricPollContext(new JsonObject().put(GlobalConstants.ID, ObjectConfigStore.getStore().getItemByValue(AIOpsObject.OBJECT_IP, POLLER_CONTEXT.getString(testInfo.getTestMethod().get().getName())).getLong(GlobalConstants.ID)).put(Metric.METRIC_PLUGIN, NMSConstants.MetricPlugin.PING.getName()), testContext), testContext);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(2)
    void testPollPortServiceCheckMetric(VertxTestContext testContext, TestInfo testInfo)
    {

        Assertions.assertNotNull(ObjectConfigStore.getStore().getItemByValue(AIOpsObject.OBJECT_IP, POLLER_CONTEXT.getString(testInfo.getTestMethod().get().getName())));

        TestNMSUtil.assertMetricPollResponseTestResult(TestNMSUtil.prepareMetricPollContext(new JsonObject().put(GlobalConstants.ID, ObjectConfigStore.getStore().getItemByValue(AIOpsObject.OBJECT_IP, POLLER_CONTEXT.getString(testInfo.getTestMethod().get().getName())).getLong(GlobalConstants.ID)).put(Metric.METRIC_PLUGIN, NMSConstants.MetricPlugin.PORT.getName()), testContext), testContext);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(3)
    void testPollRADIUSServiceCheckMetric(VertxTestContext testContext, TestInfo testInfo)
    {
        Assertions.assertNotNull(ObjectConfigStore.getStore().getItemByValue(AIOpsObject.OBJECT_IP, POLLER_CONTEXT.getString(testInfo.getTestMethod().get().getName())));

        TestNMSUtil.assertMetricPollResponseTestResult(TestNMSUtil.prepareMetricPollContext(new JsonObject().put(GlobalConstants.ID, ObjectConfigStore.getStore().getItemByValue(AIOpsObject.OBJECT_IP, POLLER_CONTEXT.getString(testInfo.getTestMethod().get().getName())).getLong(GlobalConstants.ID)).put(Metric.METRIC_PLUGIN, NMSConstants.MetricPlugin.RADIUS.getName()), testContext), testContext);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(4)
    void testPollNTPServiceCheckMetric(VertxTestContext testContext, TestInfo testInfo)
    {
        Assertions.assertNotNull(ObjectConfigStore.getStore().getItemByValue(AIOpsObject.OBJECT_IP, POLLER_CONTEXT.getString(testInfo.getTestMethod().get().getName())));

        TestNMSUtil.assertMetricPollResponseTestResult(TestNMSUtil.prepareMetricPollContext(new JsonObject().put(GlobalConstants.ID, ObjectConfigStore.getStore().getItemByValue(AIOpsObject.OBJECT_IP, POLLER_CONTEXT.getString(testInfo.getTestMethod().get().getName())).getLong(GlobalConstants.ID)).put(Metric.METRIC_PLUGIN, NMSConstants.MetricPlugin.NTP.getName()), testContext), testContext);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(5)
    void testPollFTPServiceCheckMetric(VertxTestContext testContext, TestInfo testInfo)
    {
        Assertions.assertNotNull(ObjectConfigStore.getStore().getItemByValue(AIOpsObject.OBJECT_IP, POLLER_CONTEXT.getString(testInfo.getTestMethod().get().getName())));

        TestNMSUtil.assertMetricPollResponseTestResult(TestNMSUtil.prepareMetricPollContext(new JsonObject().put(GlobalConstants.ID, ObjectConfigStore.getStore().getItemByValue(AIOpsObject.OBJECT_IP, POLLER_CONTEXT.getString(testInfo.getTestMethod().get().getName())).getLong(GlobalConstants.ID)).put(Metric.METRIC_PLUGIN, NMSConstants.MetricPlugin.FTP.getName()), testContext), testContext);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(6)
    void testPollDomainServiceCheckMetric(VertxTestContext testContext, TestInfo testInfo)
    {
        Assertions.assertNotNull(ObjectConfigStore.getStore().getItemByValue(AIOpsObject.OBJECT_IP, POLLER_CONTEXT.getString(testInfo.getTestMethod().get().getName())));

        TestNMSUtil.assertMetricPollResponseTestResult(TestNMSUtil.prepareMetricPollContext(new JsonObject().put(GlobalConstants.ID, ObjectConfigStore.getStore().getItemByValue(AIOpsObject.OBJECT_IP, POLLER_CONTEXT.getString(testInfo.getTestMethod().get().getName())).getLong(GlobalConstants.ID)).put(Metric.METRIC_PLUGIN, NMSConstants.MetricPlugin.DOMAIN.getName()), testContext), testContext);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(7)
    void testPollDNSServiceCheckMetric(VertxTestContext testContext, TestInfo testInfo)
    {
        Assertions.assertNotNull(ObjectConfigStore.getStore().getItemByValue(AIOpsObject.OBJECT_IP, POLLER_CONTEXT.getString(testInfo.getTestMethod().get().getName())));

        TestNMSUtil.assertMetricPollResponseTestResult(TestNMSUtil.prepareMetricPollContext(new JsonObject().put(GlobalConstants.ID, ObjectConfigStore.getStore().getItemByValue(AIOpsObject.OBJECT_IP, POLLER_CONTEXT.getString(testInfo.getTestMethod().get().getName())).getLong(GlobalConstants.ID)).put(Metric.METRIC_PLUGIN, NMSConstants.MetricPlugin.DNS.getName()), testContext), testContext);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(8)
    void testPollPingMetricHavingObjectStatusDisabled(VertxTestContext testContext, TestInfo testInfo)
    {
        var context = ObjectConfigStore.getStore().getItemByIP("************", NMSConstants.Type.PING);

        var metricContext = MetricConfigStore.getStore().getItem(MetricConfigStore.getStore().getItemByMetricPlugin(context.getLong("id"), NMSConstants.Type.PING.getName()));

        TestUtil.vertx().eventBus().<JsonObject>request(EventBusConstants.EVENT_OBJECT_DISABLE, context, reply ->
        {
            if (reply.succeeded())
            {
                LOGGER.info(String.format("Object %s disabled successfully ", context.getString(OBJECT_IP, AIOpsObject.OBJECT_TARGET)));

                assertEquals("object " + context.getString(OBJECT_NAME) + " disabled successfully...", reply.result().body().getString(MESSAGE));

                metricContext.put(Metric.METRIC_POLLING_TIME, 1);

                TestAPIUtil.put(TestAPIConstants.METRIC_API_ENDPOINT + "/" + context.getLong(ID),
                        new JsonObject().put(APIConstants.REQUEST_PARAMS, new JsonArray().add(metricContext)),
                        testContext.succeeding(response -> testContext.verify(() ->
                                {
                                    LOGGER.debug("Response from Metric API Endpoint : " + response.bodyAsJsonObject());

                                    TestAPIUtil.assertValidResponseTestResult(response, LOGGER, testInfo.getTestMethod().get().getName());

                                    TestUtil.vertx().setTimer(5000, timer ->
                                    {
                                        TestUtil.vertx().cancelTimer(timer);

                                        try
                                        {
                                            TestNMSUtil.assertEnableObjectTestResult(context, metricContext, testContext, testInfo.getTestMethod().get().getName());
                                        }
                                        catch (Exception exception)
                                        {
                                            LOGGER.error(exception);

                                            testContext.failNow(exception.getMessage());
                                        }
                                    });
                                })
                        ));
            }
            else
            {
                testContext.failNow(reply.cause());
            }
        });
    }

/*
     @Test
    @Order(44)
    void testPollPingServiceCheckMetricInvalidIP(VertxTestContext testContext) throws Exception {
        TimeUnit.SECONDS.sleep(2);

        var metric = NMSTestUtil.prepareMetricPollContext(new JsonObject().put(GlobalConstants.ID, discoveryIds.getLong(NMSConstants.Type.PING.getName()))
                .put(Metric.METRIC_PLUGIN, NMSConstants.MetricPlugin.PING.getName()), testContext);

        metric.put(Object.OBJECT_IP, TestConstants.getStringValue(TestConstants.INVALID_IP)).put(Object.OBJECT_TARGET, TestConstants.getStringValue(TestConstants.INVALID_IP));

        NMSTestUtil.assertMetricPollTestResult(metric, testContext, new JsonObject().put(GlobalConstants.ERROR_CODE, ERROR_CODE_PROCESS_TIMEOUT)
                .put(GlobalConstants.STATUS, GlobalConstants.STATUS_TIME_OUT));
    }
    @Test
    @Order(54)
    void testURLServiceCheckDiscovery1(VertxTestContext context, TestInfo testInfo) {
        NMSTestUtil.testDiscovery(context, null, TestConstants.getJsonObject(testInfo.getTestMethod().get().getName()), ERROR_CODE_SUCCESS, 0L).onComplete(handler -> discoveryIds.put("url.without.endpoint", handler.result()));
    }

    @Test
    @Order(55)
    void testProvisionURLServiceCheck1(VertxTestContext context) throws InterruptedException {
        TimeUnit.SECONDS.sleep(5);

        assertTrue(discoveryIds.containsKey("url.without.endpoint"));

        NMSTestUtil.assertProvisionTestResult(discoveryIds.getLong("url.without.endpoint"), context);
    }

    @Test
    @Order(56)
    void testURLServiceCheckDiscovery2(VertxTestContext context, TestInfo testInfo) {
        NMSTestUtil.testDiscovery(context, null, TestConstants.getJsonObject(testInfo.getTestMethod().get().getName()), ERROR_CODE_SUCCESS, 0L).onComplete(handler -> discoveryIds.put("url.with.endpoint", handler.result()));
    }

    @Test
    @Order(57)
    void testProvisionURLServiceCheck2(VertxTestContext context) throws InterruptedException {
        TimeUnit.SECONDS.sleep(5);

        assertTrue(discoveryIds.containsKey("url.with.endpoint"));

        NMSTestUtil.assertProvisionTestResult(discoveryIds.getLong("url.with.endpoint"), context);
    }

    @Test
    @Order(58)
    void testURLServiceCheckDiscovery3(VertxTestContext context, TestInfo testInfo) throws InterruptedException {
        TimeUnit.SECONDS.sleep(5);

        NMSTestUtil.testDiscovery(context, null, TestConstants.getJsonObject(testInfo.getTestMethod().get().getName()), ERROR_CODE_SUCCESS, 0L).onComplete(handler -> discoveryIds.put("url.get.with.endpoint", handler.result()));
    }

    @Test
    @Order(59)
    void testProvisionURLServiceCheck3(VertxTestContext context) throws InterruptedException {

        TimeUnit.SECONDS.sleep(5);

        assertTrue(discoveryIds.containsKey("url.get.with.endpoint"));

        NMSTestUtil.assertProvisionTestResult(discoveryIds.getLong("url.get.with.endpoint"), context);
    }

    @Test
    @Order(60)
    void testPortServiceCheckDiscovery0(VertxTestContext context, TestInfo testInfo) {
        NMSTestUtil.testDiscovery(context, null, TestConstants.getJsonObject(testInfo.getTestMethod().get().getName()), ERROR_CODE_SUCCESS, 0L).onComplete(handler -> discoveryIds.put("port1", handler.result()));
    }

    @Test
    @Order(61)
    void testProvisionPortServiceCheck0(VertxTestContext context) throws InterruptedException {

        TimeUnit.SECONDS.sleep(5);

        assertTrue(discoveryIds.containsKey("port1"));

        NMSTestUtil.assertProvisionTestResult(discoveryIds.getLong("port1"), context);
    }

    @Test
    @Order(62)
    void testPortServiceCheckDiscovery1(VertxTestContext context, TestInfo testInfo) {
        NMSTestUtil.testDiscovery(context, null, TestConstants.getJsonObject(testInfo.getTestMethod().get().getName()), ERROR_CODE_SUCCESS, 0L).onComplete(handler -> discoveryIds.put("port2", handler.result()));
    }

    @Test
    @Order(63)
    void testProvisionPortServiceCheck1(VertxTestContext context) throws InterruptedException {

        TimeUnit.SECONDS.sleep(5);

        assertTrue(discoveryIds.containsKey("port2"));

        NMSTestUtil.assertProvisionTestResult(discoveryIds.getLong("port2"), context);
    }


    @Test
    @Order(64)
    void testDNSServiceCheckDiscovery1(VertxTestContext context, TestInfo testInfo) {
        NMSTestUtil.testDiscovery(context, null, TestConstants.getJsonObject(testInfo.getTestMethod().get().getName()), ERROR_CODE_SUCCESS, 0L);
    }

    @Test
    @Order(65)
    void testGetPortRemoteDiscovery(VertxTestContext testContext) {
        APITestUtil.get(DISCOVERY_API_ENDPOINT + "/" + discoveryIds.getLong(NMSConstants.Type.PORT.getName()) + "/result"
                , testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            assertEquals(SC_OK, response.statusCode());

                            assertEquals(SC_OK, response.bodyAsJsonObject().getInteger(RESPONSE_CODE));

                            testContext.completeNow();
                        })));
    }

    @Test
    @Order(66)
    void testGetRadiusRemoteDiscovery(VertxTestContext testContext) {
        APITestUtil.get(DISCOVERY_API_ENDPOINT + "/" + discoveryIds.getLong(NMSConstants.Type.RADIUS.getName()) + "/result"
                , testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            assertEquals(SC_OK, response.statusCode());

                            assertEquals(SC_OK, response.bodyAsJsonObject().getInteger(RESPONSE_CODE));

                            testContext.completeNow();
                        })));
    }

    //#24527 pruthviraj has changed name in test-parameters json will be updating in his PR for same
    @Test
    @Order(67)
    @Disabled
    void testCheckURLServiceDiscoveryURLParameters(VertxTestContext context, TestInfo testInfo) {
        NMSTestUtil.testDiscovery(context, null, TestConstants.getJsonObject(testInfo.getTestMethod().get().getName()), ERROR_CODE_SUCCESS, 0L).onComplete(handler -> {

            if (handler.succeeded()) {

                discoveryIds.put(NMSConstants.Type.URL.getName(), handler.result());

                context.completeNow();
            }
        });
    }

    @Test
    @Order(68)
    void testUpdateURLServiceCheck(VertxTestContext context) {

        var item = DiscoveryConfigStore.getStore().getItem(discoveryIds.getLong(NMSConstants.Type.URL.getName()));

        item.getJsonObject(Discovery.DISCOVERY_CONTEXT).put("url.parameters", new JsonObject());

        APITestUtil.put(DISCOVERY_API_ENDPOINT + "/" + discoveryIds.getLong(NMSConstants.Type.URL.getName()), item, context.succeeding(
                response -> context.verify(() -> {

                    Assertions.assertTrue(item.getJsonObject(Discovery.DISCOVERY_CONTEXT).getJsonObject("url.parameters").isEmpty());

                    context.completeNow();

                })
        ));

    }*/
}
