/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

package com.mindarray.discovery;

import com.mindarray.*;
import com.mindarray.api.AIOpsObject;
import com.mindarray.api.Discovery;
import com.mindarray.api.RemoteEventProcessor;
import com.mindarray.db.DBConstants;
import com.mindarray.eventbus.EventBusConstants;
import com.mindarray.nms.NMSConstants;
import com.mindarray.store.DiscoveryConfigStore;
import com.mindarray.store.RemoteEventProcessorCacheStore;
import com.mindarray.store.RemoteEventProcessorConfigStore;
import com.mindarray.util.CodecUtil;
import com.mindarray.util.CommonUtil;
import com.mindarray.util.Logger;
import io.vertx.core.eventbus.MessageConsumer;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import io.vertx.junit5.Timeout;
import io.vertx.junit5.VertxExtension;
import io.vertx.junit5.VertxTestContext;
import org.junit.jupiter.api.*;
import org.junit.jupiter.api.condition.EnabledIfSystemProperty;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.api.parallel.Execution;
import org.junit.jupiter.api.parallel.ExecutionMode;

import java.io.File;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;
import java.util.stream.Collectors;

import static com.mindarray.GlobalConstants.*;
import static com.mindarray.TestAPIConstants.DISCOVERY_API_ENDPOINT;
import static com.mindarray.api.APIConstants.RESPONSE_CODE;
import static com.mindarray.eventbus.EventBusConstants.*;
import static org.apache.http.HttpStatus.SC_OK;
import static org.junit.jupiter.api.Assertions.*;

@ExtendWith(VertxExtension.class)
@Execution(ExecutionMode.CONCURRENT)
@Timeout(100 * 1000)
@EnabledIfSystemProperty(named = "test.mode", matches = "^APP|^PRIMARY$")
public class TestCloudObjectDiscovery
{
    public static final JsonObject DISCOVERY_PROFILES = new JsonObject();
    public static final Map<Long, VertxTestContext> DISCOVERY_ITEMS = new HashMap<>();
    private static final Logger LOGGER = new Logger(TestCloudObjectDiscovery.class, MOTADATA_NMS, "Test Cloud Object Discovery");
    private static long eventProcessor = -1L;
    private static MessageConsumer<JsonObject> messageConsumer;

    @BeforeAll
    static void beforeAll(VertxTestContext testContext)
    {
        try
        {
            assertDiscoveryConsumerSetup();

            var file = new File(GlobalConstants.CURRENT_DIR + GlobalConstants.PATH_SEPARATOR + "src" + GlobalConstants.PATH_SEPARATOR
                    + "test" + GlobalConstants.PATH_SEPARATOR + "resources" + GlobalConstants.PATH_SEPARATOR + "discovery-parameters.json");

            if (file.exists())
            {
                TestUtil.startEventStreaming(new JsonObject().put(EventBusConstants.EVENT_STATE, EventBusConstants.EVENT_STATE_COMPLETED));

                DISCOVERY_PROFILES.mergeIn(new JsonObject(Files.readString(file.toPath(), StandardCharsets.UTF_8)));

                testContext.completeNow();
            }
        }
        catch (Exception exception)
        {
            testContext.failNow(exception.getMessage());

            exception.printStackTrace();
        }
    }

    private static void assertDiscoveryConsumerSetup()
    {
        messageConsumer = TestUtil.vertx().eventBus().localConsumer(EVENT_UI, message ->
        {
            var event = message.body();

            if (event.containsKey(EVENT_TYPE) && event.getString(EVENT_TYPE).equalsIgnoreCase(UI_NOTIFICATION_DISCOVERY_STATE_CHANGE) && event.getBinary(EVENT_CONTEXT) != null && message.body().getBinary(EVENT_CONTEXT) != null && message.body().containsKey(EVENT_COMPRESSION_TYPE) && message.body().getInteger(EVENT_COMPRESSION_TYPE) == COMPRESSION_TYPE_ONE)
            {
                var context = CodecUtil.toJSONObject(message.body().getBinary(EventBusConstants.EVENT_CONTEXT));

                if (context.containsKey(NMSConstants.STATE) && context.getString(NMSConstants.STATE).equalsIgnoreCase(NMSConstants.STATE_NOT_RUNNING))
                {
                    DISCOVERY_ITEMS.get(context.getLong(ID)).verify(() ->
                    {
                        try
                        {
                            var discovery = DiscoveryConfigStore.getStore().getItemByValue(ID, context.getLong(ID));

                            assertNotNull(discovery);

                            var testContext = DISCOVERY_ITEMS.get(context.getLong(ID));

                            TestAPIUtil.get(DISCOVERY_API_ENDPOINT + "/" + discovery.getLong(ID) + "/result", testContext.succeeding(response -> testContext.verify(() ->
                            {
                                try
                                {
                                    assertEquals(SC_OK, response.statusCode());

                                    assertEquals(SC_OK, response.bodyAsJsonObject().getInteger(RESPONSE_CODE));

                                    assertTrue(response.bodyAsJsonObject().containsKey(RESULT));

                                    assertFalse(response.bodyAsJsonObject().getJsonArray(RESULT).isEmpty());

                                    var result = response.bodyAsJsonObject().getJsonArray(RESULT).getJsonObject(0);

                                    assertNotNull(result.getString(AIOpsObject.OBJECT_TARGET));

                                    assertNotNull(result.getString(AIOpsObject.OBJECT_TYPE));

                                    assertNotNull(result.getString(AIOpsObject.OBJECT_CATEGORY));

                                    assertTrue(result.containsKey(AIOpsObject.OBJECT_STATE));

                                    assertTrue(result.getString(AIOpsObject.OBJECT_STATE).equalsIgnoreCase(NMSConstants.State.NEW.name()) || result.getString(AIOpsObject.OBJECT_STATE).equalsIgnoreCase(NMSConstants.State.UNPROVISION.name()));

                                    testContext.completeNow();

                                }
                                catch (Exception exception)
                                {
                                    LOGGER.error(exception);

                                    testContext.failNow(exception);
                                }
                            })));
                        }
                        catch (Exception exception)
                        {
                            LOGGER.error(exception);
                        }
                    });
                }
            }
        });
    }

    public static void runCloudDiscoveryTest(VertxTestContext testContext, JsonObject item)
    {
        DISCOVERY_ITEMS.put(item.getLong(ID), testContext);

        TestAPIUtil.post(String.format(TestAPIConstants.DISCOVERY_RUN_API_ENDPOINT, item.getLong(ID)), new JsonObject(), result -> testContext.verify(() -> assertEquals(SC_OK, result.result().statusCode())));
    }

    @AfterAll
    static void afterAll(VertxTestContext testContext)
    {
        messageConsumer.unregister(result -> testContext.completeNow());
    }

    @Test
    @Disabled
    void testAWSCloudDiscoveryDownCollector(VertxTestContext testContext, TestInfo testInfo)
    {
        var downEventProcessorUUID = UUID.randomUUID().toString().toLowerCase().trim();

        var context = new JsonObject().put(RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_HOST, "test.register1")
                .put(RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_UUID, downEventProcessorUUID);

        TestUtil.vertx().eventBus().send(EventBusConstants.EVENT_REGISTRATION, context.put(EVENT_TYPE, EventBusConstants.EVENT_REGISTRATION));

        TestUtil.vertx().setPeriodic(1000, timer ->
        {
            eventProcessor = RemoteEventProcessorConfigStore.getStore().getItemIdByUUID(downEventProcessorUUID, null, null);

            if (eventProcessor > 0L)
            {
                TestUtil.vertx().cancelTimer(timer);

                runCloudDiscoveryTest(testContext, DiscoveryConfigStore.getStore().getItemByValue(Discovery.DISCOVERY_NAME, DISCOVERY_PROFILES.getJsonObject(testInfo.getTestMethod().get().getName()).getString(Discovery.DISCOVERY_NAME)));
            }
        });
    }

    @Test
    @Disabled
    void testDiscoveryDeletedDownCollector(VertxTestContext testContext)
    {
        Assertions.assertNotEquals(-1, eventProcessor);

        Assertions.assertTrue(eventProcessor > 0L);

        var ids = new JsonArray(RemoteEventProcessorConfigStore.getStore().getItems().stream()
                .filter(item -> (JsonObject.mapFrom(item).getString(DBConstants.FIELD_TYPE).equalsIgnoreCase(DBConstants.ENTITY_TYPE_USER)
                        && RemoteEventProcessorCacheStore.getStore().getDuration(JsonObject.mapFrom(item).getLong(ID)) == 0) || JsonObject.mapFrom(item).getLong(ID).equals(eventProcessor))
                .map(item -> JsonObject.mapFrom(item).getLong(ID))
                .collect(Collectors.toList()));

        if (!ids.isEmpty())
        {
            Bootstrap.configDBService().deleteAll(DBConstants.TBL_REMOTE_EVENT_PROCESSOR,
                    new JsonObject().put(DBConstants.FIELD_NAME, ID).put(VALUE, ids),
                    DEFAULT_USER, SYSTEM_REMOTE_ADDRESS, handler ->
                    {
                        RemoteEventProcessorConfigStore.getStore().deleteItems(ids);

                        ids.forEach(id -> RemoteEventProcessorCacheStore.getStore().deleteItem(CommonUtil.getLong(id)));

                        testContext.completeNow();
                    });
        }
        else
        {
            testContext.completeNow();
        }
    }

    @Test
    void testAWSCloudDiscovery(VertxTestContext testContext, TestInfo testInfo)
    {
        runCloudDiscoveryTest(testContext, DiscoveryConfigStore.getStore().getItemByValue(Discovery.DISCOVERY_NAME, DISCOVERY_PROFILES.getJsonObject(testInfo.getTestMethod().get().getName()).getString(Discovery.DISCOVERY_NAME)));
    }

    @Test
    void testAzureCloudDiscovery(VertxTestContext testContext, TestInfo testInfo)
    {
        runCloudDiscoveryTest(testContext, DiscoveryConfigStore.getStore().getItemByValue(Discovery.DISCOVERY_NAME, DISCOVERY_PROFILES.getJsonObject(testInfo.getTestMethod().get().getName()).getString(Discovery.DISCOVERY_NAME)));
    }

    @Test
    void testOffice365CloudDiscovery(VertxTestContext testContext, TestInfo testInfo)
    {
        runCloudDiscoveryTest(testContext, DiscoveryConfigStore.getStore().getItemByValue(Discovery.DISCOVERY_NAME, DISCOVERY_PROFILES.getJsonObject(testInfo.getTestMethod().get().getName()).getString(Discovery.DISCOVERY_NAME)));
    }
}
