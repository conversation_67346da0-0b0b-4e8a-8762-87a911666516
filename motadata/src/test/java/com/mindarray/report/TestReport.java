/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

package com.mindarray.report;

import com.mindarray.TestAPIUtil;
import com.mindarray.TestUtil;
import com.mindarray.api.APIConstants;
import com.mindarray.api.Report;
import com.mindarray.api.Scheduler;
import com.mindarray.eventbus.EventBusConstants;
import com.mindarray.job.JobScheduler;
import com.mindarray.nms.NMSConstants;
import com.mindarray.store.ReportConfigStore;
import com.mindarray.util.CodecUtil;
import com.mindarray.util.Logger;
import com.mindarray.visualization.VisualizationConstants;
import io.github.artsok.RepeatedIfExceptionsTest;
import io.vertx.core.eventbus.MessageConsumer;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import io.vertx.junit5.Timeout;
import io.vertx.junit5.VertxExtension;
import io.vertx.junit5.VertxTestContext;
import org.junit.jupiter.api.*;
import org.junit.jupiter.api.condition.EnabledIfSystemProperty;
import org.junit.jupiter.api.extension.ExtendWith;

import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

import static com.mindarray.GlobalConstants.*;
import static com.mindarray.TestAPIConstants.REPORT_API_ENDPOINT;
import static com.mindarray.TestAPIConstants.SCHEDULER_API_ENDPOINT;
import static com.mindarray.api.User.USER_NAME;
import static com.mindarray.eventbus.EventBusConstants.*;

@ExtendWith(VertxExtension.class)
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
@Timeout(30 * 1000)
@EnabledIfSystemProperty(named = "test.mode", matches = "^APP|^PRIMARY$")
public class TestReport
{
    private static final Logger LOGGER = new Logger(TestReport.class, "report-test", "Report Test");
    private static final Map<String, String> MAPPINGS = new HashMap<>();
    private static final JsonArray OBJECTS = new JsonArray();

    private static MessageConsumer<JsonObject> messageConsumer;

    @BeforeAll
    static void beforeAll(VertxTestContext testContext)
    {
        // to avoid unnecessary visualizationManager responses
        // Visualization Manager is already undeployed in TestAutoScaler.java

        MAPPINGS.put("citrix.xen.cpu.percent", "Tyf-7xosAABQJ_7vGiwAAGQDAAAAAAAAAAUAAAAAAAAAAQIEAQAAAB4AAAAAsAcAbW9uaXRvcgcAbW9uaXRvcgIAAAAxMUIAAAABoBoAY2l0cml4Lnhlbi5jcHUucGVyY2VudF5hdmcWAGNpdHJpeC54ZW4uY3B1LnBlcmNlbnQK16NwPQrzP0IAAAACoBoAY2l0cml4Lnhlbi5jcHUucGVyY2VudF5taW4WAGNpdHJpeC54ZW4uY3B1LnBlcmNlbnTsUbgeheuxP0IAAAADoBoAY2l0cml4Lnhlbi5jcHUucGVyY2VudF5tYXgWAGNpdHJpeC54ZW4uY3B1LnBlcmNlbnRxPQrXo3ABQA");
        MAPPINGS.put("esxi.cpu.percent", "USf-7xosAABSJ_7vGiwAAGQEAAAAAAAAAAMAAAAAAAAAAQIEAQAAAB4AAAAAsAcAbW9uaXRvcgcAbW9uaXRvcgIAAAAxMDYAAAABoBQAZXN4aS5jcHUucGVyY2VudF5hdmcQAGVzeGkuY3B1LnBlcmNlbnSPwvUoXI8mQDYAAAACoBQAZXN4aS5jcHUucGVyY2VudF5taW4QAGVzeGkuY3B1LnBlcmNlbnSamZmZmRklQDYAAAADoBQAZXN4aS5jcHUucGVyY2VudF5tYXgQAGVzeGkuY3B1LnBlcmNlbnR7FK5H4XopQA");
        MAPPINGS.put("cisco.wireless.controller.cpu.percent", "o27rQHknAACkbutAeScAAGQCAAAAAAAAAAcAAAAAAAAAAQIEAQAAAB0AAAAAsAcAbW9uaXRvcgcAbW9uaXRvcgEAAAAyYAAAAAGgKQBjaXNjby53aXJlbGVzcy5jb250cm9sbGVyLmNwdS5wZXJjZW50XmF2ZyUAY2lzY28ud2lyZWxlc3MuY29udHJvbGxlci5jcHUucGVyY2VudAAAAAAAAAAAYAAAAAKgKQBjaXNjby53aXJlbGVzcy5jb250cm9sbGVyLmNwdS5wZXJjZW50Xm1pbiUAY2lzY28ud2lyZWxlc3MuY29udHJvbGxlci5jcHUucGVyY2VudAAAAAAAAAAAYAAAAAOgKQBjaXNjby53aXJlbGVzcy5jb250cm9sbGVyLmNwdS5wZXJjZW50Xm1heCUAY2lzY28ud2lyZWxlc3MuY29udHJvbGxlci5jcHUucGVyY2VudAAAAAAAAAAA");
        MAPPINGS.put("aruba.wireless.controller.cpu.percent", "pW7rQHknAACmbutAeScAAGQBAAAAAAAAAAcAAAAAAAAAAQIEAQAAAB0AAAAAsAcAbW9uaXRvcgcAbW9uaXRvcgEAAAAzYAAAAAGgKQBhcnViYS53aXJlbGVzcy5jb250cm9sbGVyLmNwdS5wZXJjZW50XmF2ZyUAYXJ1YmEud2lyZWxlc3MuY29udHJvbGxlci5jcHUucGVyY2VudAAAAAAAACZAYAAAAAOgKQBhcnViYS53aXJlbGVzcy5jb250cm9sbGVyLmNwdS5wZXJjZW50Xm1heCUAYXJ1YmEud2lyZWxlc3MuY29udHJvbGxlci5jcHUucGVyY2VudAAAAAAAACZAYAAAAAKgKQBhcnViYS53aXJlbGVzcy5jb250cm9sbGVyLmNwdS5wZXJjZW50Xm1pbiUAYXJ1YmEud2lyZWxlc3MuY29udHJvbGxlci5jcHUucGVyY2VudAAAAAAAACZA");
        MAPPINGS.put("hyperv.cpu.percent", "WSf-7xosAABaJ_7vGiwAAGQBAAAAAAAAAAcAAAAAAAAAAQIEAQAAAB4AAAAAsAcAbW9uaXRvcgcAbW9uaXRvcgIAAAAxMjoAAAABoBYAaHlwZXJ2LmNwdS5wZXJjZW50XmF2ZxIAaHlwZXJ2LmNwdS5wZXJjZW50j8L1KFw_U0A6AAAAAqAWAGh5cGVydi5jcHUucGVyY2VudF5taW4SAGh5cGVydi5jcHUucGVyY2VudI_C9ShcP1NAOgAAAAOgFgBoeXBlcnYuY3B1LnBlcmNlbnRebWF4EgBoeXBlcnYuY3B1LnBlcmNlbnSPwvUoXD9TQA");
        MAPPINGS.put("azure.vm.cpu.percent", "Wyf-7xosAABcJ_7vGiwAAGQ4AAAAAAAAAAcAAAAAAAAAAQIEBAAAADAAAAAAsAcAbW9uaXRvcgcAbW9uaXRvcgIAAAA0MQIAAAA0MgIAAAAyNAIAAAAzNVYAAAABoBgAYXp1cmUudm0uY3B1LnBlcmNlbnReYXZnFABhenVyZS52bS5jcHUucGVyY2VudFyPwvUoXBNAmpmZmZmZBUCuR-F6FK73P-F6FK5H4do_VgAAAAKgGABhenVyZS52bS5jcHUucGVyY2VudF5taW4UAGF6dXJlLnZtLmNwdS5wZXJjZW50UrgehetRE0CkcD0K16P0P83MzMzMzPA_mpmZmZmZ2T9WAAAAA6AYAGF6dXJlLnZtLmNwdS5wZXJjZW50Xm1heBQAYXp1cmUudm0uY3B1LnBlcmNlbnRcj8L1KFwTQClcj8L1qCBACtejcD0KCUDNzMzMzMzcPw");
        MAPPINGS.put("aws.ec2.cpu.percent", "q27rQHknAACsbutAeScAAGQAAAAAAAAAAAAAAAAAAAAAAFcAAABGYWlsZWQgdG8gZXhlY3V0ZSBxdWVyeSBmb3Igd2lkZ2V0IFByZXZpZXcgV2lkZ2V0LCBQb3NzaWJsZSByZWFzb246IE5vIGVudGl0eSBxdWFsaWZpZWQ");
        MAPPINGS.put("system.cpu.percent", "Uyf-7xosAABUJ_7vGiwAAGQWAAAAAAAAAAsAAAAAAAAAAQIEAgAAACIAAAAAsAcAbW9uaXRvcgcAbW9uaXRvcgEAAAAxAQAAADRCAAAAAaAWAHN5c3RlbS5jcHUucGVyY2VudF5hdmcSAHN5c3RlbS5jcHUucGVyY2VudArXo3A9iixAcT0K16Nw8T9CAAAAAqAWAHN5c3RlbS5jcHUucGVyY2VudF5taW4SAHN5c3RlbS5jcHUucGVyY2VudAAAAAAAACpAAAAAAAAAAABCAAAAA6AWAHN5c3RlbS5jcHUucGVyY2VudF5tYXgSAHN5c3RlbS5jcHUucGVyY2VudAAAAAAAADBAAAAAAAAAAEA");

        OBJECTS.add("12").add("1").add("10").add("3").add("41").add("42").add("11").add("4").add("24").add("2").add("35");

        testContext.completeNow();
    }

    @Test
    @Order(1)
    public void testReport(VertxTestContext testContext)
    {
        var ids = ReportConfigStore.getStore().getIds();

        if (!ids.isEmpty())
        {
            messageConsumer = TestUtil.vertx().eventBus().localConsumer(EVENT_UI, message ->
                    testContext.completeNow());

            var sessionId = UUID.randomUUID().toString().toLowerCase().trim();

            TestUtil.vertx().eventBus().send(UI_ACTION_REPORT_EXPORT, new JsonObject()
                    .put(ID, 10000000010130L)
                    .put(APIConstants.SESSION_ID, sessionId)
                    .put(UI_EVENT_UUID, UUID.randomUUID().toString())
                    .put(EVENT_TYPE, EVENT_REPORT)
                    .put(EVENT_CONTEXT, new JsonObject().put("subject", "Test subject").put("to", "<EMAIL>").put("from", "<EMAIL>").put(MESSAGE, "Hello World !"))
                    .put(USER_NAME, "admin"));

        }
    }

    @Test
    @Order(2)
    public void testExportReport(VertxTestContext testContext)
    {
        var ids = ReportConfigStore.getStore().getIds();

        if (!ids.isEmpty())
        {
            messageConsumer = TestUtil.vertx().eventBus().localConsumer(EVENT_UI, message ->
            {

                if (UI_NOTIFICATION_REPORT_PROGRESS.equalsIgnoreCase(message.body().getString("event.type")))
                {
                    var eventContext = CodecUtil.toJSONObject(message.body().getBinary(EventBusConstants.EVENT_CONTEXT));

                    if (eventContext.getInteger(PROGRESS) == 30)
                    {
                        messageConsumer.unregister();

                        Assertions.assertEquals("Exporting report", eventContext.getString(MESSAGE));

                        testContext.completeNow();
                    }
                }
            });
            var sessionId = UUID.randomUUID().toString().toLowerCase().trim();

            TestUtil.vertx().eventBus().send(UI_ACTION_REPORT_EXPORT, new JsonObject()
                    .put(ID, 10000000010130L)
                    .put(APIConstants.SESSION_ID, sessionId)
                    .put(UI_EVENT_UUID, UUID.randomUUID().toString())
                    .put(EVENT_TYPE, EVENT_REPORT_EXPORT)
                    .put(USER_NAME, "admin"));

        }
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(7)
    public void TestScheduleReport(VertxTestContext testContext)
    {
        var reportId = ReportConfigStore.getStore().getItemByValue(Report.REPORT_NAME, "Server Inventory").getLong(ID);

        var context = new JsonObject().put(Scheduler.SCHEDULER_EMAIL_RECIPIENTS, new JsonArray().add("<EMAIL>"))
                .put(Scheduler.SCHEDULER_JOB_TYPE, JobScheduler.JobType.REPORT.getName())
                .put(Scheduler.SCHEDULER_START_DATE, "29-07-2024")
                .put(Scheduler.SCHEDULER_TIMES, new JsonArray().add("00:05"))
                .put(Scheduler.SCHEDULER_TIMELINE, "Once")
                .put(Scheduler.SCHEDULER_CONTEXT, new JsonObject().put(NMSConstants.OBJECTS, new JsonArray().add(reportId)));

        TestAPIUtil.post(SCHEDULER_API_ENDPOINT, context, asyncResult ->
        {
            if (asyncResult.succeeded())
            {
                LOGGER.info("Scheduler assigned successfully");

                var schedulerId = asyncResult.result().bodyAsJsonObject().getJsonArray(ID).getLong(0);

                TestAPIUtil.put(SCHEDULER_API_ENDPOINT + PATH_SEPARATOR + schedulerId + PATH_SEPARATOR + "state", new JsonObject().put(Scheduler.SCHEDULER_JOB_TYPE, JobScheduler.JobType.REPORT.getName()).put(Scheduler.SCHEDULER_STATE, YES), schedulerResult ->
                {
                    if (asyncResult.succeeded())
                    {
                        LOGGER.info("state changed");

                        TestAPIUtil.put(REPORT_API_ENDPOINT + PATH_SEPARATOR + reportId + PATH_SEPARATOR + "schedule", new JsonObject(), reportResult ->
                        {
                            if (reportResult.succeeded())
                            {
                                LOGGER.info("Scheduler assigned successfully...");

                                var item = ReportConfigStore.getStore().getItem(reportId);

                                Assertions.assertNotNull(item);

                                var widgets = item.getJsonArray(Report.REPORT_WIDGETS).getJsonObject(0);

                                Assertions.assertNotNull(widgets);

                                Assertions.assertEquals("Server Inventory Summary", widgets.getString(VisualizationConstants.VISUALIZATION_NAME));

                                Assertions.assertNotNull(widgets.getJsonArray(VisualizationConstants.VISUALIZATION_DATA_SOURCES));

                                Assertions.assertNotNull(widgets.getJsonObject(VisualizationConstants.VISUALIZATION_PROPERTIES));

                                testContext.completeNow();

                            }
                            else
                            {
                                LOGGER.warn(String.format("failed to assign scheduler with reason: %s", reportResult.cause().getMessage()));
                            }
                        });
                    }
                    else
                    {
                        LOGGER.warn(String.format("failed to changed state with reason : %s", schedulerResult.cause().getMessage()));
                    }
                });
            }
            else
            {

                LOGGER.warn(String.format("failed to assigned scheduler with reason :  %s", asyncResult.cause().getMessage()));
            }
        });
    }
}
