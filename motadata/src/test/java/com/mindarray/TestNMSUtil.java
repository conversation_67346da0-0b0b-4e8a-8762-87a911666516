/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

package com.mindarray;

import com.mindarray.api.*;
import com.mindarray.db.DBConstants;
import com.mindarray.eventbus.EventBusConstants;
import com.mindarray.nms.NMSConstants;
import com.mindarray.store.*;
import com.mindarray.util.CodecUtil;
import com.mindarray.util.CommonUtil;
import com.mindarray.util.DateTimeUtil;
import com.mindarray.util.Logger;
import io.vertx.core.eventbus.MessageConsumer;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import io.vertx.junit5.VertxTestContext;
import org.apache.http.HttpStatus;
import org.junit.jupiter.api.Assertions;

import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;

import static com.mindarray.GlobalConstants.*;
import static com.mindarray.api.AIOpsObject.OBJECT_IP;
import static com.mindarray.api.APIConstants.*;
import static com.mindarray.api.CredentialProfile.CREDENTIAL_PROFILE_CONTEXT;
import static com.mindarray.api.User.USER_NAME;
import static com.mindarray.eventbus.EventBusConstants.*;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;

public class TestNMSUtil
{
    public static final Map<Long, VertxTestContext> METRIC_POLLER_ITEMS = new ConcurrentHashMap<>();
    public static final Map<Long, VertxTestContext> REDISCOVERY_ITEMS = new ConcurrentHashMap<>();
    private static final Logger LOGGER = new Logger(TestNMSUtil.class, MOTADATA_UTIL, "NMS Test Util");
    public static MessageConsumer<JsonObject> messageHandler;
    private static long schedulerId;

    public static long getSchedulerId()
    {
        return schedulerId;
    }

    public static void setSchedulerId(long schedulerId)
    {
        TestNMSUtil.schedulerId = schedulerId;
    }

    /*-------------------------------- Metric Poller Response -------------------------------------*/

    public static void assertMetricPollResponseTestResult()
    {
        TestUtil.vertx().eventBus().<JsonObject>localConsumer(EVENT_METRIC_POLL_RESPONSE, message ->
        {
            try
            {
                if (!METRIC_POLLER_ITEMS.isEmpty())
                {
                    var event = message.body();

                    if (METRIC_POLLER_ITEMS.containsKey(event.getLong(EVENT_ID)))
                    {
                        METRIC_POLLER_ITEMS.get(event.getLong(EVENT_ID)).verify(() ->
                        {
                            Assertions.assertTrue(event.containsKey(RESULT) && !event.getJsonObject(RESULT).isEmpty());

                            if (event.getString(Metric.METRIC_PLUGIN).equalsIgnoreCase(NMSConstants.MetricPlugin.AVAILABILITY.getName()))
                            {
                                Assertions.assertTrue(event.getJsonObject(RESULT).containsKey("ping.received.packets"));

                                Assertions.assertTrue(event.getJsonObject(RESULT).getInteger("ping.received.packets") >= 1);
                            }
                            else if (event.getString(Metric.METRIC_PLUGIN).equalsIgnoreCase(NMSConstants.MetricPlugin.NETWORK_SERVICE.getName()))
                            {
                                Assertions.assertTrue(event.getJsonObject(RESULT).containsKey("network.service"));

                                Assertions.assertEquals(event.getJsonObject(RESULT).getJsonArray("network.service").size(), event.getJsonArray(NMSConstants.OBJECTS).size());
                            }
                            else if (event.getString(Metric.METRIC_PLUGIN).endsWith(STATUS) && event.getString(Metric.METRIC_CATEGORY).equalsIgnoreCase(NMSConstants.Category.CLOUD.getName()))
                            {
                                Assertions.assertTrue(event.getJsonObject(RESULT).containsKey("cloud.latency.ms"));

                                Assertions.assertTrue(event.getJsonObject(RESULT).containsKey(STATUS));

                                Assertions.assertEquals(STATUS_UP, event.getJsonObject(RESULT).getString(STATUS));
                            }

                            METRIC_POLLER_ITEMS.get(event.getLong(EVENT_ID)).completeNow();

                            METRIC_POLLER_ITEMS.remove(event.getLong(EVENT_ID));
                        });
                    }
                }
            }
            catch (Exception exception)
            {
                LOGGER.error(exception);
            }
        });
    }

    public static JsonObject prepareMetricPollContext(JsonObject context)
    {
        if (context == null)
        {
            return null;
        }

        var metric = new JsonObject().mergeIn(context);

        var object = ObjectConfigStore.getStore().getItem(metric.getLong(Metric.METRIC_OBJECT));

        var credential = CredentialProfileConfigStore.getStore().getItem(metric.getLong(Metric.METRIC_CREDENTIAL_PROFILE));

        metric.put(EVENT_TIMESTAMP, DateTimeUtil.currentSeconds());

        Assertions.assertTrue(object != null && !object.isEmpty());

        Assertions.assertTrue(metric.containsKey(Metric.METRIC_CREDENTIAL_PROFILE));

        Assertions.assertNotNull(credential);

        object.remove(GlobalConstants.ID);

        credential.remove(GlobalConstants.ID);

        if (object.containsKey(AIOpsObject.OBJECT_CONTEXT))
        {
            object.mergeIn(object.getJsonObject(AIOpsObject.OBJECT_CONTEXT));

            object.remove(AIOpsObject.OBJECT_CONTEXT);
        }

        if (credential.containsKey(CREDENTIAL_PROFILE_CONTEXT))
        {
            credential.mergeIn(credential.getJsonObject(CREDENTIAL_PROFILE_CONTEXT));

            credential.remove(CREDENTIAL_PROFILE_CONTEXT);
        }

        metric.mergeIn(object);

        if (metric.containsKey(Metric.METRIC_CONTEXT))
        {
            metric.mergeIn(metric.getJsonObject(Metric.METRIC_CONTEXT));

            metric.remove(Metric.METRIC_CONTEXT);

            if (metric.containsKey(NMSConstants.SNMP_OID_GROUP_ID))
            {
                metric.mergeIn(SNMPTemplateOIDGroupCacheStore.getStore().getItem(CommonUtil.getString(metric.remove(NMSConstants.SNMP_OID_GROUP_ID))));
            }
        }

        metric.remove(EVENT_ID);

        if (metric.getJsonArray(AIOpsObject.OBJECT_EVENT_PROCESSORS) == null || metric.getJsonArray(AIOpsObject.OBJECT_EVENT_PROCESSORS).isEmpty())
        {
            metric.put(AIOpsObject.OBJECT_EVENT_PROCESSORS, new JsonArray()
                    .add(RemoteEventProcessorConfigStore.getStore().getItemByValue(RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_UUID, Bootstrap.getRegistrationId())
                            .getLong(ID)));
        }

        metric.put(EventBusConstants.EVENT_TYPE, EventBusConstants.EVENT_METRIC_POLL).mergeIn(credential, true);

        var eventId = CommonUtil.newEventId();

        TestUtil.vertx().eventBus().send(EventBusConstants.EVENT_ADD, new JsonObject().put(EventBusConstants.EVENT_ID, eventId)
                .put(EventBusConstants.EVENT_TYPE, EventBusConstants.EVENT_METRIC_POLL)
                .put(EventBusConstants.EVENT_STATE, EventBusConstants.EVENT_STATE_QUEUED)
                .put(USER_NAME, DEFAULT_USER)
                .put(EventBusConstants.EVENT_CONTEXT, metric));

        metric.put(EventBusConstants.EVENT_ID, eventId);

        EventBusConstants.updateEvent(eventId, String.format(InfoMessageConstants.EVENT_TRACKER_EVENT_DISPATCHED, Bootstrap.getRegistrationId(), DateTimeUtil.timestamp()));

        return metric;
    }

    public static JsonObject prepareMetricPollContext(JsonObject context, VertxTestContext testContext)
    {
        JsonObject metric = null;

        try
        {
            var metrics = MetricConfigStore.getStore().getItemsByValue(Metric.METRIC_OBJECT, context.getLong(ID));

            assertFalse(metrics.isEmpty());

            for (var index = 0; index < metrics.size(); index++)
            {
                if ((context.getString(Metric.METRIC_PLUGIN) != null && metrics.getJsonObject(index).getString(Metric.METRIC_PLUGIN).equalsIgnoreCase(context.getString(Metric.METRIC_PLUGIN)))
                        || (context.getString(Metric.METRIC_NAME) != null && metrics.getJsonObject(index).getString(Metric.METRIC_NAME).equalsIgnoreCase(context.getString(Metric.METRIC_NAME))))
                {
                    testContext.awaitCompletion(1, TimeUnit.SECONDS);

                    metric = prepareMetricPollContext(metrics.getJsonObject(index));

                    break;
                }
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);

            testContext.failNow(exception);
        }

        if (metric == null || metric.isEmpty())
        {
            metric = MetricConfigStore.getStore().getItemByValue(Metric.METRIC_PLUGIN, context.getString(Metric.METRIC_PLUGIN));

            if (metric == null)
            {
                testContext.failNow(new Exception("Failed to build metric context"));
            }
        }

        return metric;
    }

    public static JsonObject prepareApplicationMetricPollContext(JsonObject context, VertxTestContext testContext)
    {
        JsonObject metric = null;

        try
        {
            var metrics = MetricConfigStore.getStore().getItem(MetricConfigStore.getStore().getItemByMetricPlugin(context.getLong(ID), context.getString(Metric.METRIC_PLUGIN)));

            Assertions.assertTrue(metrics != null && !metrics.isEmpty());

            if ((context.getString(Metric.METRIC_PLUGIN) != null && metrics.getString(Metric.METRIC_PLUGIN).equalsIgnoreCase(context.getString(Metric.METRIC_PLUGIN)))
                    || (context.getString(Metric.METRIC_NAME) != null && metrics.getString(Metric.METRIC_NAME).equalsIgnoreCase(context.getString(Metric.METRIC_NAME))))
            {
                metric = prepareMetricPollContext(metrics);
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);

            testContext.failNow(exception);
        }

        if (metric == null || metric.isEmpty())
        {
            testContext.failNow(new Exception("Failed to build metric context"));
        }

        return metric;
    }

    public static void assertMetricPollResponseTestResult(JsonObject metric, VertxTestContext testContext)
    {
        try
        {
            METRIC_POLLER_ITEMS.put(metric.getLong(EVENT_ID), testContext);

            if (metric.getJsonArray(AIOpsObject.OBJECT_EVENT_PROCESSORS) == null || metric.getJsonArray(AIOpsObject.OBJECT_EVENT_PROCESSORS).isEmpty())
            {
                var item = RemoteEventProcessorConfigStore.getStore().getItemByValue(DBConstants.FIELD_TYPE, DBConstants.ENTITY_TYPE_SYSTEM);

                Assertions.assertNotNull(item);

                Assertions.assertTrue(item.getString(RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_UUID).equalsIgnoreCase(Bootstrap.getRegistrationId()));

                metric.put(AIOpsObject.OBJECT_EVENT_PROCESSORS, new JsonArray().add(item.getLong(ID)));
            }

            testContext.awaitCompletion(1, TimeUnit.SECONDS);

            TestUtil.vertx().eventBus().send(EVENT_ROUTER, metric);
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);

            testContext.failNow(exception);
        }
    }

    public static void testRediscover(VertxTestContext testContext, JsonObject rediscoveryParameters, JsonObject object, boolean abort)
    {
        var objects = rediscoveryParameters.getJsonArray(NMSConstants.OBJECTS);

        rediscoveryParameters.remove(NMSConstants.OBJECTS);

        var autoProvision = object.containsKey(NMSConstants.AUTO_PROVISION_STATUS) && object.getString(NMSConstants.AUTO_PROVISION_STATUS).equalsIgnoreCase(YES);

        TestAPIUtil.post(TestAPIConstants.SCHEDULER_API_ENDPOINT, rediscoveryParameters, testContext.succeeding(response ->
                testContext.verify(() ->
                {
                    try
                    {
                        LOGGER.info(String.format("scheduler API response : %s ", response.bodyAsJsonObject().encode()));

                        Assertions.assertEquals(HttpStatus.SC_OK, response.statusCode());

                        var retries = new AtomicInteger();

                        var schedulerId = response.bodyAsJsonObject().getJsonArray(ID).getLong(0);

                        objects.forEach(rediscoverObject -> ObjectStatusCacheStore.getStore().updateItem(CommonUtil.getLong(rediscoverObject), STATUS_UP, DateTimeUtil.currentSeconds()));

                        TestUtil.vertx().setPeriodic(TimeUnit.SECONDS.toMillis(3), timer ->
                        {
                            if (SchedulerConfigStore.getStore().getItem(schedulerId) != null)
                            {
                                TestUtil.vertx().cancelTimer(timer);

                                TestNMSUtil.setSchedulerId(response.bodyAsJsonObject().getJsonArray(ID).getLong(0));

                                TestUtil.vertx().eventBus().send(EventBusConstants.UI_ACTION_SCHEDULER_RUN, new JsonObject().put(SESSION_ID, TestUtil.getSessionId()).put(USER_NAME, DEFAULT_USER).put(ID, schedulerId));

                                if (abort)
                                {
                                    TestUtil.vertx().eventBus().send(EVENT_REDISCOVER_STOP, new JsonObject().put(ID, schedulerId)
                                            .put(SESSION_ID, TestUtil.getSessionId()).put(USER_NAME, DEFAULT_USER));

                                    for (var index = 0; index < 5; index++)
                                    {
                                        TestUtil.vertx().eventBus().send(EventBusConstants.UI_ACTION_SCHEDULER_RUN, new JsonObject().put(SESSION_ID, TestUtil.getSessionId()).put(USER_NAME, DEFAULT_USER).put(ID, schedulerId));

                                        TestUtil.vertx().setTimer(1000, id ->
                                                TestUtil.vertx().eventBus().send(EVENT_REDISCOVER_STOP, new JsonObject().put(ID, response.bodyAsJsonObject().getJsonArray(ID).getLong(0))
                                                        .put(SESSION_ID, TestUtil.getSessionId()).put(USER_NAME, DEFAULT_USER)));
                                    }

                                    testContext.completeNow();
                                }
                                else
                                {
                                    assertRediscoveryTestResult(object, autoProvision, rediscoveryParameters.getJsonObject(Scheduler.SCHEDULER_CONTEXT).getString(NMSConstants.REDISCOVER_JOB));

                                    REDISCOVERY_ITEMS.put(schedulerId, testContext);
                                }
                            }
                            else if (retries.get() > 5)
                            {
                                TestUtil.vertx().cancelTimer(timer);

                                testContext.failNow("scheduler id not found in scheduler config store");
                            }
                            else
                            {
                                retries.incrementAndGet();
                            }
                        });
                    }
                    catch (Exception exception)
                    {
                        testContext.failNow(exception);
                    }
                })));
    }

    private static void assertRediscoveryTestResult(JsonObject object, boolean autoProvision, String metricType)
    {
        messageHandler = TestUtil.vertx().eventBus().localConsumer(EVENT_USER + TestUtil.getSessionId(), message ->
        {
            try
            {
                var event = message.body();

                var eventContext = CodecUtil.toJSONObject(event.getBinary(EVENT_CONTEXT));

                if (event.getString(EVENT_TYPE) != null)
                {
                    if (event.getString(EVENT_TYPE).equalsIgnoreCase(UI_NOTIFICATION_STREAMING_BROADCAST))
                    {
                        if (eventContext.getString(EVENT_STATE) != null && eventContext.getString(EVENT_STATE).equals(EVENT_STATE_COMPLETED) && eventContext.getJsonObject(EVENT_CONTEXT) != null
                                && eventContext.getJsonObject(EVENT_CONTEXT).containsKey(EVENT_SCHEDULER) && Objects.equals(eventContext.getJsonObject(EVENT_CONTEXT).getLong(EVENT_SCHEDULER), schedulerId) && REDISCOVERY_ITEMS.containsKey(schedulerId))
                        {
                            var testContext = REDISCOVERY_ITEMS.get(schedulerId);

                            LOGGER.info(String.format("Result Of Event :: %s", eventContext.encode()));

                            if (eventContext.getString(STATUS).equalsIgnoreCase(STATUS_SUCCEED))
                            {
                                Assertions.assertTrue(Boolean.TRUE);

                                if ((object != null && object.isEmpty()) || autoProvision)
                                {
                                    messageHandler.unregister(result -> testContext.completeNow());
                                }
                            }
                            else if (eventContext.getJsonObject(EVENT_CONTEXT) == null || eventContext.getJsonObject(EVENT_CONTEXT).getString(NMSConstants.REDISCOVER_JOB) == null)
                            {
                                messageHandler.unregister();

                                testContext.failNow(new Exception("Rediscover failed"));
                            }
                        }
                    }
                    else
                    {
                        if (metricType != null && eventContext.containsKey(NMSConstants.REDISCOVER_JOB) && eventContext.getString(NMSConstants.REDISCOVER_JOB).equalsIgnoreCase(metricType) && new JsonArray().add(NMSConstants.RediscoverJob.CLOUD.getName()).add(NMSConstants.RediscoverJob.FILE_DIRECTORY.getName()).contains(eventContext.getString(NMSConstants.REDISCOVER_JOB)))
                        {
                            if (event.getString(EVENT_TYPE).equalsIgnoreCase(UI_NOTIFICATION_REDISCOVER_PROGRESS) && REDISCOVERY_ITEMS.containsKey(schedulerId))
                            {
                                var testContext = REDISCOVERY_ITEMS.get(schedulerId);

                                var items = eventContext.getJsonArray(NMSConstants.OBJECTS);

                                for (var index = 0; index < items.size(); index++)
                                {
                                    var item = items.getJsonObject(index);

                                    if (item.equals(object))
                                    {
                                        var params = new JsonArray();

                                        params.add(new JsonObject().put(ID, eventContext.getLong(ID))
                                                .put(NMSConstants.REDISCOVER_JOB, eventContext.getString(NMSConstants.REDISCOVER_JOB))
                                                .put(SESSION_ID, TestUtil.getSessionId())
                                                .put(NMSConstants.OBJECT, object).put(NMSConstants.OBJECTS, new JsonArray().add(object))
                                                .put(EVENT_SCHEDULER, schedulerId));

                                        TestAPIUtil.post(TestAPIConstants.OBJECT_PROVISION_API_ENDPOINT, new JsonObject().put(REQUEST_PARAMS, params), testContext.succeeding(response ->
                                        {
                                            Assertions.assertEquals(HttpStatus.SC_OK, response.statusCode());

                                            var result = response.bodyAsJsonObject();

                                            Assertions.assertEquals(HttpStatus.SC_OK, result.getInteger(RESPONSE_CODE));

                                            Assertions.assertEquals(STATUS_SUCCEED, result.getString(STATUS));

                                            Assertions.assertEquals(InfoMessageConstants.OBJECT_PROVISION_START_SUCCEEDED, result.getString(MESSAGE));

                                            testContext.completeNow();
                                        }));
                                    }
                                }
                            }
                            else if (event.getString(EVENT_TYPE).equalsIgnoreCase(UI_NOTIFICATION_REDISCOVER_PROVISION_PROGRESS) && eventContext.containsKey(MESSAGE))
                            {
                                var testContext = REDISCOVERY_ITEMS.get(schedulerId);

                                var items = eventContext.getJsonArray(NMSConstants.OBJECTS);

                                for (var index = 0; index < items.size(); index++)
                                {
                                    var item = items.getJsonObject(index);

                                    if (item.equals(object))
                                    {
                                        assertEquals(STATUS_SUCCEED, eventContext.getString(STATUS));

                                        assertEquals(String.format(InfoMessageConstants.REDISCOVER_JOB_OBJECT_PROVISIONED, eventContext.getString(NMSConstants.REDISCOVER_JOB),
                                                item.getString(AIOpsObject.OBJECT_IP) != null ? item.getString(AIOpsObject.OBJECT_NAME) + " (" + item.getString(AIOpsObject.OBJECT_IP) + ")" : item.getString(AIOpsObject.OBJECT_NAME)), eventContext.getString(MESSAGE));

                                        testContext.completeNow();

                                        break;
                                    }
                                }
                            }
                        }
                    }
                }
            }
            catch (Exception exception)
            {
                LOGGER.error(exception);

                messageHandler.unregister();
            }
        });
    }

    public static void assertEnableObjectTestResult(JsonObject context, JsonObject metricContext, VertxTestContext testContext, String methodName)
    {
        TestUtil.vertx().eventBus().<JsonObject>request(EventBusConstants.EVENT_OBJECT_ENABLE, context, reply ->
        {
            if (reply.succeeded())
            {
                LOGGER.info(String.format("Object %s enabled successfully ", context.getString(OBJECT_IP, AIOpsObject.OBJECT_TARGET)));

                LOGGER.info(String.format("Object is : %s", ObjectConfigStore.getStore().getItem(context.getLong(ID))));

                messageHandler = TestUtil.vertx().eventBus().localConsumer(EVENT_METRIC_POLL_RESPONSE, message ->
                {
                    try
                    {
                        var event = message.body();

                        LOGGER.info(String.format("Response received for %s : %s", event.getString(OBJECT_IP), event.getString(STATUS)));

                        if (CommonUtil.isNotNullOrEmpty(event.getString(OBJECT_IP)) && (event.getString(OBJECT_IP).equalsIgnoreCase("************") || event.getString(OBJECT_IP).equalsIgnoreCase("************")))
                        {
                            assertEquals(STATUS_SUCCEED, event.getString(STATUS));

                            assertFalse(event.getJsonObject(RESULT).isEmpty());

                            metricContext.put(Metric.METRIC_POLLING_TIME, 5000);

                            TestAPIUtil.put(TestAPIConstants.METRIC_API_ENDPOINT + "/" + context.getLong(ID),
                                    new JsonObject().put(APIConstants.REQUEST_PARAMS, new JsonArray().add(metricContext)),
                                    testContext.succeeding(result -> testContext.verify(() ->
                                            TestAPIUtil.assertValidResponseTestResult(result, LOGGER, methodName))));


                            messageHandler.unregister(result -> testContext.completeNow());

                        }
                    }
                    catch (Exception exception)
                    {
                        messageHandler.unregister();

                        testContext.failNow(exception);
                    }
                });
            }
            else
            {
                testContext.failNow(reply.cause());
            }
        });
    }
}
