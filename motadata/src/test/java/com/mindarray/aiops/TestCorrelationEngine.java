/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

package com.mindarray.aiops;

import com.mindarray.*;
import com.mindarray.agent.AgentConstants;
import com.mindarray.api.*;
import com.mindarray.db.DBConstants;
import com.mindarray.eventbus.EventBusConstants;
import com.mindarray.nms.NMSConstants;
import com.mindarray.store.*;
import com.mindarray.util.CommonUtil;
import com.mindarray.util.LicenseUtil;
import com.mindarray.util.Logger;
import com.mindarray.util.MotadataConfigUtil;
import io.github.artsok.RepeatedIfExceptionsTest;
import io.vertx.core.DeploymentOptions;
import io.vertx.core.Future;
import io.vertx.core.Handler;
import io.vertx.core.Promise;
import io.vertx.core.eventbus.DeliveryOptions;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import io.vertx.junit5.Timeout;
import io.vertx.junit5.VertxExtension;
import io.vertx.junit5.VertxTestContext;
import org.junit.jupiter.api.*;
import org.junit.jupiter.api.condition.EnabledIfSystemProperty;
import org.junit.jupiter.api.extension.ExtendWith;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;

import static com.mindarray.GlobalConstants.*;
import static com.mindarray.aiops.AIOpsConstants.*;
import static com.mindarray.api.Agent.*;
import static com.mindarray.api.User.USER_NAME;
import static com.mindarray.eventbus.EventBusConstants.EVENT_REPLY;
import static com.mindarray.eventbus.EventBusConstants.EVENT_TYPE;
import static com.mindarray.nms.NMSConstants.*;
import static org.apache.http.HttpStatus.SC_OK;

/**
 * Test class for the AvailabilityCorrelationEngine.
 * <p>
 * This class contains tests for various correlation scenarios:
 * <ul>
 *   <li>Network interface up/down events</li>
 *   <li>System process up/down events</li>
 *   <li>Unreachable object events for different categories (network, server, virtualization, etc.)</li>
 *   <li>Duplicate event handling</li>
 *   <li>Correlation serialization/deserialization</li>
 * </ul>
 * <p>
 * These tests verify that the correlation engine correctly identifies relationships
 * between events, suppresses secondary alerts, and properly manages the state of
 * objects and instances during availability events.
 */
@ExtendWith(VertxExtension.class)
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
@Timeout(240 * 1000)
@EnabledIfSystemProperty(named = "test.mode", matches = "^APP|^PRIMARY$")
public class TestCorrelationEngine
{
    public static final AvailabilityCorrelationEngine AVAILABILITY_CORRELATION_ENGINE = new AvailabilityCorrelationEngine();
    private static final DeliveryOptions DELIVERY_OPTIONS = new DeliveryOptions().setSendTimeout(TimeUnit.MINUTES.toMillis(10));
    private static final Logger LOGGER = new Logger(TestCorrelationEngine.class, MOTADATA_AIOPS, "Correlation Test");
    private static final JsonObject CONTEXT = new JsonObject();

    @BeforeAll
    static void beforeAll(VertxTestContext testContext)
    {
        LOGGER.info("License Usage: " + LicenseUtil.getLicenseDetails());

        var objects = ObjectConfigStore.getStore().flatItemsByValues(AIOpsObject.OBJECT_IP, new JsonArray().add("**********").add("************").add("************").add("************").add("*************"), GlobalConstants.ID);

        Assertions.assertFalse(objects.isEmpty());

        var futures = new ArrayList<Future<Void>>();

        for (var index = 0; index < objects.size(); index++)
        {
            var id = objects.getLong(index);

            for (var metric : MetricConfigStore.getStore().getItemsByObject(id))
            {
                var promise = Promise.<Void>promise();

                futures.add(promise.future());

                TestUtil.vertx().eventBus().<JsonObject>request(EventBusConstants.EVENT_METRIC_ENABLE,
                        new JsonObject().put(GlobalConstants.ID, metric.getLong(GlobalConstants.ID)), reply -> promise.complete());
            }
        }

        var promise = Promise.<Void>promise();

        futures.add(promise.future());

        Bootstrap.undeployVerticle(EventBusConstants.EVENT_AVAILABILITY_CORRELATION).onComplete(undeploy ->
        {
            if (undeploy.succeeded())
            {
                promise.complete();
            }
            else
            {
                promise.fail(undeploy.cause());
            }
        });

        Future.join(futures).onComplete(result ->
        {
            if (result.succeeded())
            {
                Bootstrap.vertx().deployVerticle(AVAILABILITY_CORRELATION_ENGINE, new DeploymentOptions().setConfig(new JsonObject().put(EventBusConstants.EVENT_TYPE, "availability.correlation.test").put(EventBusConstants.EVENT_ROUTER_CONFIG, "availability-correlation-test")), deploy ->
                {
                    if (deploy.succeeded())
                    {
                        Bootstrap.vertx().setPeriodic(MotadataConfigUtil.getCorrelatedResourceFlushTimerSeconds() * 1000L, timer -> writeCorrelatedResources());

                        testContext.completeNow();
                    }
                    else
                    {
                        testContext.failNow(new Exception(deploy.cause()));
                    }
                });
            }
            else
            {
                testContext.failNow(new Exception(result.cause()));
            }
        });
    }

    private static void writeCorrelatedResources()
    {
        var promise = Promise.<Void>promise();

        Bootstrap.vertx().eventBus().<JsonArray>request("availability.correlation.test" + ".write", null, new DeliveryOptions().setSendTimeout(600000L), reply -> promise.complete());

        promise.future();
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(1)
    void testCorrelateNetworkInterfaceDownEvent(VertxTestContext testContext, TestInfo testInfo)
    {
        LOGGER.info(String.format("running test case %s", testInfo.getTestMethod().get().getName()));

        runCorrelation("**********", NMSConstants.MetricPlugin.SNMP_INTERFACE.getName(), null, AIOpsConstants.AvailabilityProbeType.INSTANCE_DOWN.getName(), testContext, AIOpsObject.OBJECT_IP, null, true, handler -> testContext.completeNow());
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(2)
    void testCorrelateNetworkInterfaceUpEvent(VertxTestContext testContext, TestInfo testInfo)
    {
        LOGGER.info(String.format("running test case %s", testInfo.getTestMethod().get().getName()));

        runCorrelation("**********", NMSConstants.MetricPlugin.SNMP_INTERFACE.getName(), null, AIOpsConstants.AvailabilityProbeType.INSTANCE_UP.getName(), testContext, AIOpsObject.OBJECT_IP, null, true, handler -> testContext.completeNow());
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(3)
    void testCorrelateSystemProcessDownEvent(VertxTestContext testContext, TestInfo testInfo)
    {
        LOGGER.info(String.format("running test case %s", testInfo.getTestMethod().get().getName()));

        runCorrelation("************", NMSConstants.MetricPlugin.LINUX_PROCESS.getName(), "entrypoi|/bin/bash /entrypoint.sh mysqld", AIOpsConstants.AvailabilityProbeType.INSTANCE_DOWN.getName(), testContext, AIOpsObject.OBJECT_IP, null, true, event -> assertApplicationCorrelationTestResult(event, NMSConstants.Type.MARIADB.getName(), NMSConstants.State.SUSPEND.name(), testContext));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(4)
    void testCorrelateSystemProcessUpEvent(VertxTestContext testContext, TestInfo testInfo)
    {
        LOGGER.info(String.format("running test case %s", testInfo.getTestMethod().get().getName()));

        runCorrelation("************", NMSConstants.MetricPlugin.LINUX_PROCESS.getName(), "entrypoi|/bin/bash /entrypoint.sh mysqld", AIOpsConstants.AvailabilityProbeType.INSTANCE_UP.getName(), testContext, AIOpsObject.OBJECT_IP, null, true, event -> assertApplicationCorrelationTestResult(event, NMSConstants.Type.MARIADB.getName(), NMSConstants.State.ENABLE.name(), testContext));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(5)
    void testCorrelateUnreachableNetworkObjectEvent(VertxTestContext testContext, TestInfo testInfo)
    {
        LOGGER.info(String.format("running test case %s", testInfo.getTestMethod().get().getName()));

        runCorrelation("**********", NMSConstants.MetricPlugin.SNMP_INTERFACE.getName(), null, AIOpsConstants.AvailabilityProbeType.OBJECT_DOWN.getName(), testContext, AIOpsObject.OBJECT_IP, null, true, event ->
        {
            Assertions.assertTrue(event.containsKey(AIOpsConstants.INSTANCES));

            assertUnreachableInstancesTestResult(event.getJsonArray(AIOpsConstants.INSTANCES).getJsonObject(0), INTERFACE, testContext, event);
        });
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(6)
    void testCorrelateDuplicateEvent(VertxTestContext testContext, TestInfo testInfo)
    {
        LOGGER.info(String.format("running test case %s", testInfo.getTestMethod().get().getName()));

        var futures = new ArrayList<Future<String>>();

        for (var index = 0; index < 5; index++)
        {
            var promise = Promise.<String>promise();

            futures.add(promise.future());

            runCorrelation("**********", NMSConstants.MetricPlugin.SNMP_INTERFACE.getName(), null, AIOpsConstants.AvailabilityProbeType.OBJECT_DOWN.getName(), testContext, AIOpsObject.OBJECT_IP, null, true, event -> promise.complete(event.containsKey(GlobalConstants.MESSAGE) ? event.getString(GlobalConstants.MESSAGE) : ""));
        }

        Future.join(futures).onComplete(result ->
        {
            var messages = new JsonArray(result.result().list());

            var valid = false;

            for (var index = 0; index < messages.size(); index++)
            {
                if (messages.getString(index).contains("same object found on running correlations of any engine"))
                {
                    valid = true;

                    break;
                }
            }

            if (valid)
            {
                testContext.completeNow();
            }
            else
            {
                testContext.failNow(new Exception("failed to correlate already running events..."));
            }
        });
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(7)
    void testCorrelateUnreachableServerObjectEvent(VertxTestContext testContext, TestInfo testInfo)
    {
        LOGGER.info(String.format("running test case %s", testInfo.getTestMethod().get().getName()));

        runCorrelation("************", NMSConstants.MetricPlugin.LINUX_PROCESS.getName(), null, AIOpsConstants.AvailabilityProbeType.OBJECT_DOWN.getName(), testContext, AIOpsObject.OBJECT_IP, null, false, event ->
        {
            Assertions.assertTrue(event.containsKey(AIOpsConstants.INSTANCES));

            var instances = new JsonArray().add(NMSConstants.SYSTEM_PROCESS).add(NMSConstants.SYSTEM_SERVICE).add(NMSConstants.NETWORK_SERVICE);

            var objects = event.getJsonArray(AIOpsConstants.INSTANCES);

            Assertions.assertEquals(3, instances.size());

            for (var index = 0; index < objects.size(); index++)
            {
                for (var position = 0; position < instances.size(); position++)
                {
                    if (objects.getJsonObject(index).containsKey(instances.getString(position)))
                    {
                        assertUnreachableInstancesTestResult(objects.getJsonObject(index), instances.getString(position), testContext, null);

                        instances.remove(position);

                        break;
                    }
                }
            }

            assertMetricStateTestResult(event, testContext, NMSConstants.State.SUSPEND.name());

        });
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(8)
    void testCorrelateUnreachableVirtualizationObjectEvent(VertxTestContext testContext, TestInfo testInfo)
    {
        LOGGER.info(String.format("running test case %s", testInfo.getTestMethod().get().getName()));

        runCorrelation("************", NMSConstants.MetricPlugin.VMWARE_ESXI_VM.getName(), null, AIOpsConstants.AvailabilityProbeType.OBJECT_DOWN.getName(), testContext, AIOpsObject.OBJECT_IP, null, true, event ->
        {
            Assertions.assertTrue(event.containsKey(AIOpsConstants.INSTANCES));

            assertUnreachableInstancesTestResult(event.getJsonArray(AIOpsConstants.INSTANCES).getJsonObject(0), "esxi.vm", testContext, event);
        });
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(9)
    void testCorrelateUnreachableHCIObjectEvent(VertxTestContext testContext, TestInfo testInfo)
    {
        LOGGER.info(String.format("running test case %s", testInfo.getTestMethod().get().getName()));

        runCorrelation("*************", NMSConstants.MetricPlugin.NUTANIX_VM.getName(), null, AIOpsConstants.AvailabilityProbeType.OBJECT_DOWN.getName(), testContext, AIOpsObject.OBJECT_IP, null, true, event ->
        {
            Assertions.assertTrue(event.containsKey(AIOpsConstants.INSTANCES));

            assertUnreachableInstancesTestResult(event.getJsonArray(AIOpsConstants.INSTANCES).getJsonObject(0), "nutanix.vm", testContext, event);
        });
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(10)
    void testCorrelateUnreachableCloudObjectEvent(VertxTestContext testContext, TestInfo testInfo)
    {
        LOGGER.info(String.format("running test case %s", testInfo.getTestMethod().get().getName()));

        var object = ObjectConfigStore.getStore().getItemByValue(AIOpsObject.OBJECT_TYPE, NMSConstants.Type.AMAZON_EC2.getName());

        Assertions.assertNotNull(object);

        var futures = new ArrayList<Future<Void>>();

        for (var metric : MetricConfigStore.getStore().getItemsByObject(object.getLong(GlobalConstants.ID)))
        {
            var promise = Promise.<Void>promise();

            futures.add(promise.future());

            TestUtil.vertx().eventBus().<JsonObject>request(EventBusConstants.EVENT_METRIC_ENABLE,
                    new JsonObject().put(GlobalConstants.ID, metric.getLong(GlobalConstants.ID)), reply -> promise.complete());
        }

        Future.join(futures).onComplete(result ->
                runCorrelation(object.getString(AIOpsObject.OBJECT_TARGET), NMSConstants.MetricPlugin.AMAZON_EC2.getName(), "", AIOpsConstants.AvailabilityProbeType.OBJECT_DOWN.getName(), testContext, AIOpsObject.OBJECT_TARGET, object, true, event ->
                        assertMetricStateTestResult(event, testContext, NMSConstants.State.SUSPEND.name())));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(11)
    void testCorrelateNetworkObjectEvent(VertxTestContext testContext, TestInfo testInfo)
    {
        LOGGER.info(String.format("running test case %s", testInfo.getTestMethod().get().getName()));

        runCorrelation("**********", NMSConstants.MetricPlugin.SNMP_INTERFACE.getName(), null, AIOpsConstants.AvailabilityProbeType.OBJECT_UP.getName(), testContext, AIOpsObject.OBJECT_IP, null, true, event -> assertMetricStateTestResult(event, testContext, NMSConstants.State.ENABLE.name()));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(12)
    void testCorrelateServerObjectEvent(VertxTestContext testContext, TestInfo testInfo)
    {
        LOGGER.info(String.format("running test case %s", testInfo.getTestMethod().get().getName()));

        runCorrelation("************", NMSConstants.MetricPlugin.LINUX_PROCESS.getName(), null, AIOpsConstants.AvailabilityProbeType.OBJECT_UP.getName(), testContext, AIOpsObject.OBJECT_IP, null, false, event -> assertMetricStateTestResult(event, testContext, NMSConstants.State.ENABLE.name()));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(13)
    void testCorrelateVirtualizationObjectEvent(VertxTestContext testContext, TestInfo testInfo)
    {
        LOGGER.info(String.format("running test case %s", testInfo.getTestMethod().get().getName()));

        runCorrelation("************", NMSConstants.MetricPlugin.VMWARE_ESXI_VM.getName(), null, AIOpsConstants.AvailabilityProbeType.OBJECT_UP.getName(), testContext, AIOpsObject.OBJECT_IP, null, true, event -> assertMetricStateTestResult(event, testContext, NMSConstants.State.ENABLE.name()));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(14)
    void testCorrelateHCIObjectEvent(VertxTestContext testContext, TestInfo testInfo)
    {
        LOGGER.info(String.format("running test case %s", testInfo.getTestMethod().get().getName()));

        runCorrelation("*************", NMSConstants.MetricPlugin.NUTANIX_VM.getName(), null, AIOpsConstants.AvailabilityProbeType.OBJECT_UP.getName(), testContext, AIOpsObject.OBJECT_IP, null, true, event -> assertMetricStateTestResult(event, testContext, NMSConstants.State.ENABLE.name()));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(15)
    void testCorrelateCloudObjectEvent(VertxTestContext testContext, TestInfo testInfo)
    {
        LOGGER.info(String.format("running test case %s", testInfo.getTestMethod().get().getName()));

        var object = ObjectConfigStore.getStore().getItemByValue(AIOpsObject.OBJECT_TYPE, NMSConstants.Type.AZURE_VM.getName());

        Assertions.assertNotNull(object);

        var futures = new ArrayList<Future<Void>>();

        for (var metric : MetricConfigStore.getStore().getItemsByObject(object.getLong(GlobalConstants.ID)))
        {
            var promise = Promise.<Void>promise();

            futures.add(promise.future());

            TestUtil.vertx().eventBus().<JsonObject>request(EventBusConstants.EVENT_METRIC_ENABLE,
                    new JsonObject().put(GlobalConstants.ID, metric.getLong(GlobalConstants.ID)), reply -> promise.complete());
        }

        Future.join(futures).onComplete(result ->
                runCorrelation(object.getString(AIOpsObject.OBJECT_TARGET), NMSConstants.MetricPlugin.AZURE_VM.getName(), "", AIOpsConstants.AvailabilityProbeType.OBJECT_DOWN.getName(), testContext, AIOpsObject.OBJECT_TARGET, object, true, event ->
                        assertMetricStateTestResult(event, testContext, NMSConstants.State.SUSPEND.name())));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Timeout(3 * 1000 * 60)
    @Order(16)
    void testCorrelateNetworkDummyObjectEvent(VertxTestContext testContext, TestInfo testInfo)
    {
        LOGGER.info(String.format("running test case %s", testInfo.getTestMethod().get().getName()));

        var item = new JsonObject("{\"object.host\":\"cisco_Switch.test.com\",\"object.name\":\"cisco_Switch.test.com\",\"object.system.oid\":\".*******.*******.1\",\"object.ip\":\"************\",\"objects\":[{\"interface.alias\":\"\",\"interface.description\":\"Ethernet1/1\",\"object.name\":\"6\",\"interface.index\":\"6\",\"object.type\":\"interface\",\"interface.name\":\"Et1/1\",\"interface.type\":6,\"interface\":\"Et1/1-6\",\"interface.address\":\"AA:BB:CC:00:05:11\",\"interface.bit.type\":\"1\",\"interface.speed.bytes.per.sec\":1250000,\"status\":\"Up\"},{\"interface.alias\":\"\",\"interface.description\":\"Ethernet1/0\",\"object.name\":\"5\",\"interface.index\":\"5\",\"object.type\":\"interface\",\"interface.name\":\"Et1/0\",\"interface.type\":6,\"interface\":\"Et1/0-5\",\"interface.address\":\"AA:BB:CC:00:05:01\",\"interface.bit.type\":\"1\",\"interface.speed.bytes.per.sec\":1250000,\"status\":\"Up\"},{\"interface.alias\":\"\",\"interface.description\":\"Ethernet0/2\",\"object.type\":\"interface\",\"interface.name\":\"Et0/2\",\"interface.type\":6,\"interface\":\"Et0/2-3\",\"object.name\":\"3\",\"interface.index\":\"3\",\"interface.ip.address\":\"*************\",\"interface.address\":\"AA:BB:CC:00:05:20\",\"interface.bit.type\":\"1\",\"interface.speed.bytes.per.sec\":1250000,\"status\":\"Up\"},{\"interface.alias\":\"\",\"interface.description\":\"Ethernet0/1\",\"object.type\":\"interface\",\"interface.name\":\"Et0/1\",\"interface.type\":6,\"interface\":\"Et0/1-2\",\"object.name\":\"2\",\"interface.index\":\"2\",\"interface.ip.address\":\"************\",\"interface.address\":\"AA:BB:CC:00:05:10\",\"interface.bit.type\":\"1\",\"interface.speed.bytes.per.sec\":1250000,\"status\":\"Up\"},{\"interface.alias\":\"\",\"interface.description\":\"Ethernet0/0\",\"object.name\":\"1\",\"interface.index\":\"1\",\"object.type\":\"interface\",\"interface.name\":\"Et0/0\",\"interface.type\":6,\"interface\":\"Et0/0-1\",\"interface.address\":\"AA:BB:CC:00:05:00\",\"interface.bit.type\":\"1\",\"interface.speed.bytes.per.sec\":1250000,\"status\":\"Up\"},{\"interface.alias\":\"\",\"interface.description\":\"Null0\",\"object.name\":\"9\",\"interface.index\":\"9\",\"object.type\":\"interface\",\"interface.name\":\"Nu0\",\"interface.type\":1,\"interface\":\"Nu0-9\",\"interface.address\":\"\",\"interface.bit.type\":\"0\",\"interface.speed.bytes.per.sec\":536870911,\"status\":\"Up\"}],\"object.groups\":[10000000000002],\"object.user.tags\":[],\"object.type\":\"Router\",\"object.context\":{\"ping.check.status\":\"yes\",\"port\":161,\"snmp.check.retries\":0,\"interface.discovery\":\"yes\",\"topology.plugin.discovery\":\"no\",\"discovered.objects\":[{\"interface.alias\":\"\",\"interface.description\":\"Ethernet1/1\",\"object.name\":\"6\",\"interface.index\":\"6\",\"object.type\":\"interface\",\"interface.name\":\"Et1/1\",\"interface.type\":6,\"interface\":\"Et1/1-6\",\"interface.address\":\"AA:BB:CC:00:05:11\",\"interface.bit.type\":\"1\",\"interface.speed.bytes.per.sec\":1250000,\"status\":\"Up\"},{\"interface.alias\":\"\",\"interface.description\":\"Ethernet1/0\",\"object.name\":\"5\",\"interface.index\":\"5\",\"object.type\":\"interface\",\"interface.name\":\"Et1/0\",\"interface.type\":6,\"interface\":\"Et1/0-5\",\"interface.address\":\"AA:BB:CC:00:05:01\",\"interface.bit.type\":\"1\",\"interface.speed.bytes.per.sec\":1250000,\"status\":\"Up\"},{\"interface.alias\":\"\",\"interface.description\":\"Ethernet0/2\",\"object.type\":\"interface\",\"interface.name\":\"Et0/2\",\"interface.type\":6,\"interface\":\"Et0/2-3\",\"object.name\":\"3\",\"interface.index\":\"3\",\"interface.ip.address\":\"*************\",\"interface.address\":\"AA:BB:CC:00:05:20\",\"interface.bit.type\":\"1\",\"interface.speed.bytes.per.sec\":1250000,\"status\":\"Up\"},{\"interface.alias\":\"\",\"interface.description\":\"Ethernet0/1\",\"object.type\":\"interface\",\"interface.name\":\"Et0/1\",\"interface.type\":6,\"interface\":\"Et0/1-2\",\"object.name\":\"2\",\"interface.index\":\"2\",\"interface.ip.address\":\"************\",\"interface.address\":\"AA:BB:CC:00:05:10\",\"interface.bit.type\":\"1\",\"interface.speed.bytes.per.sec\":1250000,\"status\":\"Up\"},{\"interface.alias\":\"\",\"interface.description\":\"Ethernet0/0\",\"object.name\":\"1\",\"interface.index\":\"1\",\"object.type\":\"interface\",\"interface.name\":\"Et0/0\",\"interface.type\":6,\"interface\":\"Et0/0-1\",\"interface.address\":\"AA:BB:CC:00:05:00\",\"interface.bit.type\":\"1\",\"interface.speed.bytes.per.sec\":1250000,\"status\":\"Up\"},{\"interface.alias\":\"\",\"interface.description\":\"Null0\",\"object.name\":\"9\",\"interface.index\":\"9\",\"object.type\":\"interface\",\"interface.name\":\"Nu0\",\"interface.type\":1,\"interface\":\"Nu0-9\",\"interface.address\":\"\",\"interface.bit.type\":\"0\",\"interface.speed.bytes.per.sec\":536870911,\"status\":\"Up\"},{\"interface.alias\":\"\",\"interface.description\":\"Ethernet1/3\",\"object.name\":\"8\",\"interface.index\":\"8\",\"object.type\":\"interface\",\"interface.name\":\"Et1/3\",\"interface.type\":6,\"interface\":\"Et1/3-8\",\"interface.address\":\"AA:BB:CC:00:05:31\",\"interface.bit.type\":\"1\",\"interface.speed.bytes.per.sec\":1250000,\"status\":\"Down\"},{\"interface.alias\":\"\",\"interface.description\":\"Ethernet1/2\",\"object.name\":\"7\",\"interface.index\":\"7\",\"object.type\":\"interface\",\"interface.name\":\"Et1/2\",\"interface.type\":6,\"interface\":\"Et1/2-7\",\"interface.address\":\"AA:BB:CC:00:05:21\",\"interface.bit.type\":\"1\",\"interface.speed.bytes.per.sec\":1250000,\"status\":\"Down\"},{\"interface.alias\":\"\",\"interface.description\":\"Ethernet0/3\",\"object.name\":\"4\",\"interface.index\":\"4\",\"object.type\":\"interface\",\"interface.name\":\"Et0/3\",\"interface.type\":6,\"interface\":\"Et0/3-4\",\"interface.address\":\"AA:BB:CC:00:05:30\",\"interface.bit.type\":\"1\",\"interface.speed.bytes.per.sec\":1250000,\"status\":\"Down\"}]},\"object.discovery.method\":\"REMOTE\",\"object.credential.profile\":10000000000001,\"object.target\":\"************\",\"object.state\":\"UNPROVISION\",\"object.category\":\"Network\",\"_type\":\"1\",\"id\":8831711809131}");

        var futures = new ArrayList<Future<Void>>();

        futures.add(provisionDummyObject(item, 10000000112L, 12457L, "********", testContext));

        futures.add(provisionDummyObject(item, 10000000113L, 12458L, "********", testContext));

        futures.add(provisionDummyObject(item, 10000000114L, 12459L, "********", testContext));

        Future.join(futures).onComplete(response ->
        {

            if (response.succeeded())
            {
                // creating ring topology manually of dummy ip

                LOGGER.info("All dummy object provisioned successfully ...");

                var dependency = DependencyMapperConfigStore.getStore().getItemsByObject("********");

                if (dependency.get(DependencyMapper.PARENTS).isEmpty())
                {
                    futures.add(createManualLink("fd00:1:1:1::47", "********", "19", "6", "Et0/1", "Et1/1", testContext));

                    futures.add(createManualLink("fd00:1:1:1::47", "********", "13", "3", "Et0/2", "Et0/2", testContext));

                    futures.add(createManualLink("********", "********", "3", "1", "Et0/2", "Et0/0", testContext));

                    futures.add(createManualLink("********", "********", "3", "5", "Et0/2", "Et1/0", testContext));
                }

                Future.join(futures).onComplete(asyncResult ->
                {

                    if (asyncResult.succeeded())
                    {
                        var context = new JsonObject().put(AIOpsConstants.DEPENDENCY_TYPE, AIOpsConstants.DependencyType.LOCAL_DOMAIN.getName())
                                .put(AIOpsConstants.DEPENDENCY_FORMAT, AIOpsConstants.DependencyFormat.FLAT.getName())
                                .put(AIOpsConstants.DEPENDENCY_SOURCE, "fd00:1:1:1::47").put(AIOpsConstants.DEPENDENCY_CONNECTED_LINK, new JsonArray().add("19").add("13"));

                        query(context).onComplete(result ->
                        {
                            if (result.succeeded())
                            {
                                LOGGER.info("Links are created , Topology correlation running....");

                                var object = ObjectConfigStore.getStore().getItemByValue(AIOpsObject.OBJECT_IP, "fd00:1:1:1::47");

                                Assertions.assertNotNull(object);

                                runCorrelation(object.getString(AIOpsObject.OBJECT_TARGET), NMSConstants.MetricPlugin.SNMP_INTERFACE.getName(), null, AIOpsConstants.AvailabilityProbeType.OBJECT_DOWN.getName(), testContext, AIOpsObject.OBJECT_IP, object, false, event ->
                                {
                                    Assertions.assertEquals(1, event.getJsonArray(AIOpsConstants.CORRELATED_DOWN_OBJECTS).size());

                                    Assertions.assertEquals(3, event.getJsonArray(AIOpsConstants.CORRELATED_UNREACHABLE_OBJECTS).size());

                                    LOGGER.debug(String.format("id for down object %s ", ObjectConfigStore.getStore().getItemByIP("fd00:1:1:1::47")));

                                    Assertions.assertTrue(event.getJsonArray(AIOpsConstants.CORRELATED_DOWN_OBJECTS).contains(ObjectConfigStore.getStore().getItemByIP("fd00:1:1:1::47")));

                                    Assertions.assertTrue(event.getJsonArray(AIOpsConstants.CORRELATED_UNREACHABLE_OBJECTS).contains(10000000112L));

                                    Assertions.assertTrue(event.getJsonArray(AIOpsConstants.CORRELATED_UNREACHABLE_OBJECTS).contains(10000000113L));

                                    Assertions.assertTrue(event.getJsonArray(AIOpsConstants.CORRELATED_UNREACHABLE_OBJECTS).contains(10000000114L));

                                    assertUnreachableInstancesTestResult(event.getJsonArray(AIOpsConstants.INSTANCES).getJsonObject(0), INTERFACE, testContext, event);

                                    testContext.completeNow();
                                });
                            }
                            else
                            {
                                testContext.failNow(result.cause());
                            }
                        });
                    }
                    else
                    {
                        testContext.failNow("Failed to create manual link");
                    }
                });
            }
            else
            {
                testContext.failNow("Failed to provision dummy object");
            }
        });

    }

    // check that dependency/link is updated in dependency mapper or not
    // once updated then run correlation
    private Future<Void> query(JsonObject context)
    {
        var links = context.getJsonArray(AIOpsConstants.DEPENDENCY_CONNECTED_LINK);

        var promise = Promise.<Void>promise();

        var retries = new AtomicInteger(0);

        TestUtil.vertx().setPeriodic(2000, timer ->
                TestUtil.vertx().eventBus().<JsonObject>request(EventBusConstants.EVENT_DEPENDENCY + "." + context.getString(AIOpsConstants.DEPENDENCY_TYPE) + ".query",
                        context, DELIVERY_OPTIONS, reply ->
                        {
                            if (reply.succeeded())
                            {
                                var result = reply.result().body().getJsonObject(AIOpsConstants.DependencyType.LOCAL_DOMAIN.getName());

                                var valid = true;

                                var source = context.getString(AIOpsConstants.DEPENDENCY_SOURCE);

                                for (var index = 0; index < links.size(); index++)
                                {
                                    if (!result.containsKey(source + VALUE_SEPARATOR + links.getString(index)))
                                    {
                                        valid = false;

                                        break;
                                    }
                                }

                                retries.getAndIncrement();

                                if (valid)
                                {
                                    TestUtil.vertx().cancelTimer(timer);

                                    promise.complete();
                                }
                                else if (retries.get() == 5)
                                {
                                    TestUtil.vertx().cancelTimer(timer);

                                    promise.fail("dependency/link is not updated");
                                }
                            }
                        }));

        return promise.future();
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(17)
    void testDeserializeAvailabilityCorrelation(VertxTestContext testContext, TestInfo testInfo) throws Exception
    {
        LOGGER.info(String.format("running test case %s", testInfo.getTestMethod().get().getName()));

        testContext.awaitCompletion(15, TimeUnit.SECONDS);                                   // wait till data gets dump in file (timer is of 30 sec)

        Bootstrap.vertx().eventBus().<JsonArray>request("availability.correlation.test" + ".read", new JsonObject().put(EVENT_TYPE, "availability.correlation.test"), reply ->
        {
            if (reply.succeeded())
            {
                Assertions.assertNotNull(reply.result().body());

                Assertions.assertFalse(reply.result().body().isEmpty());

                testContext.completeNow();
            }
            else
            {
                testContext.failNow(reply.cause());
            }
        });
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Timeout(3 * 1000 * 60)
    @Order(18)
    void testCorrelateNetworkDummyObjectEvent2(VertxTestContext testContext, TestInfo testInfo) throws Exception
    {
        LOGGER.info(String.format("running test case %s", testInfo.getTestMethod().get().getName()));

        remove(ObjectConfigStore.getStore().getItemByIP("fd00:1:1:1::47"), true);

        remove(ObjectConfigStore.getStore().getItemByIP("********"), false);

        remove(ObjectConfigStore.getStore().getItemByIP("********"), false);

        var object = ObjectConfigStore.getStore().getItemByValue(AIOpsObject.OBJECT_IP, "********");

        Assertions.assertNotNull(object);

        runCorrelation(object.getString(AIOpsObject.OBJECT_TARGET), NMSConstants.MetricPlugin.SNMP_INTERFACE.getName(), EMPTY_VALUE, AIOpsConstants.AvailabilityProbeType.OBJECT_DOWN.getName(), testContext, AIOpsObject.OBJECT_IP, object, true, event ->
        {
            Assertions.assertTrue(event.containsKey(STATUS));

            Assertions.assertEquals(STATUS_ABORT, event.getString(STATUS));

            Assertions.assertEquals(AIOpsConstants.PENDING_REQUEST_FOUND, event.getString(MESSAGE));

            testContext.completeNow();
        });
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(19)
    void testCorrelateAgentUnreachableServerObjectEvent(VertxTestContext testContext, TestInfo testInfo)
    {
        LOGGER.info(String.format("running test case %s", testInfo.getTestMethod().get().getName()));

        var context = new JsonObject().put(AGENT_UUID, "testAgentUUID")
                .put(AGENT_VERSION, MotadataConfigUtil.getVersion())
                .put(AGENT_BUSINESS_HOUR_PROFILE, DEFAULT_ID)
                .put(AGENT_STATE, NMSConstants.State.ENABLE.name())
                .put(ID, CommonUtil.newId())
                .put(AGENT_STATUS_TYPE, AgentConstants.AgentStatusType.PING.getName())
                .put(AGENT_STATUS, STATUS_DOWN)
                .put(OBJECTS, new JsonArray().add("**********"));

        AgentConfigStore.getStore().addItem(context.getLong(ID), context);

        var serverObject = ObjectConfigStore.getStore().getItem(ObjectConfigStore.getStore().getItemByIP("************"), true);

        var object = new JsonObject().mergeIn(serverObject).put(AIOpsObject.OBJECT_AGENT, context.getLong(ID)).put(AIOpsObject.OBJECT_IP, "**********").put(AIOpsObject.OBJECT_NAME, "testAgent").put(AIOpsObject.OBJECT_HOST, "testAgent").put(ID, serverObject.getLong(ID));

        object.remove(AIOpsObject.OBJECT_CONTEXT);

        ObjectConfigStore.getStore().addItem(object.getLong(ID), object);

        LOGGER.info(String.format("object added : %s ", object.encode()));

        TestUtil.vertx().eventBus().<JsonObject>request("availability.correlation.test", object.put(EVENT_REPLY, "yes").put(Metric.METRIC_OBJECT, serverObject.getLong(ID)).put(AVAILABILITY_PROBE_TYPE, AIOpsConstants.AvailabilityProbeType.OBJECT_DOWN.getName()),
                DELIVERY_OPTIONS,
                reply ->
                {
                    try
                    {
                        if (reply.failed())
                        {
                            LOGGER.info("result not received");

                            testContext.failNow(reply.cause());
                        }
                        else
                        {
                            var result = reply.result().body();

                            LOGGER.info(String.format("result of agent event ; %s ", result.encode()));

                            Assertions.assertTrue(result.containsKey(INSTANCES));

                            Assertions.assertFalse(result.getJsonArray(INSTANCES).isEmpty());

                            Assertions.assertTrue(result.containsKey(CORRELATED_DOWN_OBJECTS));

                            Assertions.assertFalse(result.getJsonArray(CORRELATED_DOWN_OBJECTS).isEmpty());

                            ObjectConfigStore.getStore().addItem(serverObject.getLong(ID), serverObject);

                            testContext.completeNow();
                        }
                    }
                    catch (Exception exception)
                    {
                        LOGGER.error(exception);
                    }
                });
    }

    // remove entries from correlations and runningProbes,
    // so we can reuse dummy objects to run correlation
    private void remove(Long id, boolean correlation) throws Exception
    {
        if (id != null)
        {
            if (correlation)
            {
                var field = AvailabilityCorrelationEngine.class.getDeclaredField("correlations");

                field.setAccessible(true);

                Map<String, Map<String, Map<String, List<Object>>>> correlations = (Map<String, Map<String, Map<String, List<Object>>>>) field.get(AVAILABILITY_CORRELATION_ENGINE);

                correlations.remove(id + VALUE_SEPARATOR + EMPTY_VALUE);

                correlations.remove(CommonUtil.getString(id));
            }

            var field = AvailabilityCorrelationEngine.class.getDeclaredField("runningProbes");

            field.setAccessible(true);

            Map<String, Set<Object>> runningProbes = (Map<String, Set<Object>>) field.get(AVAILABILITY_CORRELATION_ENGINE);

            runningProbes.get(AIOpsConstants.AvailabilityProbeType.OBJECT_DOWN.getName()).remove(id);

        }
    }

    private void runCorrelation(String target, String metricPlugin, String instanceName, String correlationProbe, VertxTestContext testContext, String filter, JsonObject object, boolean instance, Handler<JsonObject> messageHandler)
    {
        if (object == null)
        {
            object = ObjectConfigStore.getStore().getItemByValue(filter, target);
        }

        Assertions.assertNotNull(object);

        Assertions.assertFalse(object.isEmpty());

        var id = object.getLong(ID);

        Bootstrap.configDBService().update(DBConstants.TBL_OBJECT,
                new JsonObject().put(DBConstants.FIELD_NAME, ID).put(VALUE, object.getLong(ID)),
                new JsonObject().put(AIOpsObject.OBJECT_CONTEXT, object.getJsonObject(AIOpsObject.OBJECT_CONTEXT).put(PING_CHECK_STATUS, YES)),
                DEFAULT_USER, SYSTEM_REMOTE_ADDRESS,
                handler -> ObjectConfigStore.getStore().updateItem(id));

        try
        {
            testContext.awaitCompletion(10, TimeUnit.SECONDS);
        }
        catch (Exception ignored)
        {
        }

        if (object.containsKey(AIOpsObject.OBJECT_CONTEXT))
        {
            object.mergeIn(object.getJsonObject(AIOpsObject.OBJECT_CONTEXT));

            object.remove(AIOpsObject.OBJECT_CONTEXT);
        }

        var metric = MetricConfigStore.getStore().getItemsByObject(object.getLong(GlobalConstants.ID))
                .stream().filter(item -> JsonObject.mapFrom(item).getString(Metric.METRIC_PLUGIN).equalsIgnoreCase(metricPlugin))
                .map(JsonObject::mapFrom).findFirst().orElse(null);

        Assertions.assertNotNull(metric);

        if (metric.containsKey(Metric.METRIC_CONTEXT))
        {
            metric.mergeIn(metric.getJsonObject(Metric.METRIC_CONTEXT));

            metric.remove(Metric.METRIC_CONTEXT);
        }

        if (instanceName == null && instance)
        {
            Assertions.assertTrue(metric.containsKey(NMSConstants.OBJECTS));

            var objects = metric.getJsonArray(NMSConstants.OBJECTS);

            Assertions.assertFalse(objects.isEmpty());

            instanceName = objects.getJsonObject(0).getString(AIOpsObject.OBJECT_NAME);
        }

        var key = target + GlobalConstants.VALUE_SEPARATOR + metricPlugin;

        if (!CONTEXT.containsKey(key))
        {
            CONTEXT.put(key, instanceName);
        }
        else
        {
            instanceName = CONTEXT.getString(key);
        }

        JsonObject credential = null;

        if (metric.containsKey(Metric.METRIC_CREDENTIAL_PROFILE) && metric.getLong(Metric.METRIC_CREDENTIAL_PROFILE) > 0)
        {
            credential = CredentialProfileConfigStore.getStore().getItem(metric.getLong(Metric.METRIC_CREDENTIAL_PROFILE));

            if (credential != null)
            {
                if (credential.containsKey(CredentialProfile.CREDENTIAL_PROFILE_CONTEXT))
                {
                    credential.mergeIn(credential.getJsonObject(CredentialProfile.CREDENTIAL_PROFILE_CONTEXT));

                    credential.remove(CredentialProfile.CREDENTIAL_PROFILE_CONTEXT);
                }

                credential.remove(GlobalConstants.ID);
            }
        }

        if (credential == null)
        {
            credential = new JsonObject();
        }

        var context = new JsonObject().mergeIn(metric).mergeIn(credential).mergeIn(object)
                .put(EventBusConstants.EVENT_TYPE, EventBusConstants.EVENT_AVAILABILITY_CORRELATION)
                .put(EventBusConstants.EVENT_REPLY, GlobalConstants.YES)
                .put(AIOpsConstants.AVAILABILITY_PROBE_TYPE, correlationProbe)
                .put(AIOpsConstants.CORRELATION_INSTANCE, instance ? instanceName : null);

        var finalObject = object;

        LOGGER.debug(String.format("request send for %s probe type %s", context.getString(AIOpsObject.OBJECT_NAME), correlationProbe));

        TestUtil.vertx().eventBus().<JsonObject>request("availability.correlation.test", context,
                DELIVERY_OPTIONS,
                reply ->
                {
                    try
                    {
                        if (reply.failed())
                        {
                            testContext.failNow(reply.cause());
                        }
                        else
                        {
                            var result = reply.result().body();

                            LOGGER.debug("result " + result);

                            Assertions.assertNotNull(result);

                            result.put(GlobalConstants.ID, finalObject.getLong(GlobalConstants.ID));

                            messageHandler.handle(result);
                        }
                    }
                    catch (Exception exception)
                    {
                        LOGGER.error(exception);
                    }
                });
    }

    private Future<Void> provisionDummyObject(JsonObject context, Long id, Long eventId, String ip, VertxTestContext testContext)
    {
        LOGGER.info(String.format("Sending request to provision ip %s with id %s", ip, id));

        var promise = Promise.<Void>promise();

        context.put(GlobalConstants.ID, id).put(EventBusConstants.EVENT_ID, eventId).put(AIOpsObject.OBJECT_IP, ip).put(AIOpsObject.OBJECT_TARGET, ip).put(AIOpsObject.OBJECT_HOST, ip).put(AIOpsObject.OBJECT_NAME, ip);

        if (ObjectConfigStore.getStore().getItemByIP(ip) == null)
        {
            Bootstrap.vertx().eventBus().<JsonObject>request(EventBusConstants.EVENT_OBJECT_PROVISION, context
                            .put(EventBusConstants.EVENT_REPLY, GlobalConstants.YES)
                            .put(APIConstants.SESSION_ID, TestUtil.getSessionId())
                            .put(USER_NAME, "admin"),
                    new DeliveryOptions().setSendTimeout(300000L), reply ->
                    {
                        if (reply.succeeded())
                        {
                            try
                            {
                                var result = reply.result().body();

                                LOGGER.info(result.encode());

                                Assertions.assertNotNull(result);

                                Assertions.assertTrue(result.containsKey(STATUS));

                                Assertions.assertEquals(STATUS_SUCCEED, result.getString(STATUS));

                                LOGGER.info(String.format("%s object provisioned successfully with id %s", result.getString(AIOpsObject.OBJECT_NAME), ObjectConfigStore.getStore().getItemByIP(ip)));

                                testContext.awaitCompletion(2, TimeUnit.SECONDS);

                            }
                            catch (Exception exception)
                            {
                                LOGGER.error(exception);
                            }
                            finally
                            {
                                LOGGER.info(String.format("provision completed for ip %s with id %s", ip, id));

                                promise.complete();
                            }
                        }

                    });
        }
        else
        {
            promise.complete();
        }

        return promise.future();
    }

    private Future<Void> createManualLink(String parent, String child, String parentInterface, String childInterface, String parentInterfaceName, String childInterfaceName, VertxTestContext testContext)
    {

        var promise = Promise.<Void>promise();

        var dependencyContext = new JsonObject().put(DependencyMapper.DEPENDENCY_MAPPER_PARENT, parent).put(DependencyMapper.DEPENDENCY_MAPPER_CHILD, child).put(DependencyMapper.DEPENDENCY_MAPPER_TYPE, DependencyMapper.DependencyMapperType.NETWORK.getName()).put(DependencyMapper.DEPENDENCY_MAPPER_ARCHIVED, NO)
                .put(DependencyMapper.DEPENDENCY_MAPPER_CONTEXT, new JsonObject().put(DependencyMapper.DEPENDENCY_MAPPER_PARENT_INTERFACE, parentInterface).put(DependencyMapper.DEPENDENCY_MAPPER_CHILD_INTERFACE, childInterface).put(DependencyMapper.DEPENDENCY_MAPPER_PARENT_INTERFACE_NAME, parentInterfaceName)
                        .put(DependencyMapper.DEPENDENCY_MAPPER_CHILD_INTERFACE_NAME, childInterfaceName).put(DependencyMapper.DEPENDENCY_MAPPER_LINK_LAYER, NMSConstants.TopologyLinkLayer.L2.getName()));

        TestAPIUtil.post(TestAPIConstants.DEPENDENCY_MAPPER_API_ENDPOINT, dependencyContext, response -> testContext.verify(() ->
        {
            LOGGER.debug(String.format("API response for parent %s child %s : %s ", parent, child, response.result().bodyAsJsonObject().encode()));

            Assertions.assertEquals(SC_OK, response.result().statusCode());

            Assertions.assertNotNull(response.result().bodyAsJsonObject());

            promise.complete();

        }));

        return promise.future();
    }

    private void assertApplicationCorrelationTestResult(JsonObject event, String metricPlugin, String metricState, VertxTestContext testContext)
    {
        Assertions.assertTrue(event.containsKey(AIOpsConstants.CORRELATED_APPLICATIONS));

        Assertions.assertFalse(event.getJsonArray(AIOpsConstants.CORRELATED_APPLICATIONS).isEmpty());

        Assertions.assertTrue(event.getJsonArray(AIOpsConstants.CORRELATED_APPLICATIONS).contains(metricPlugin));

        var retries = new AtomicInteger(0);

        TestUtil.vertx().setPeriodic(1000, timer ->
        {
            var metrics = MetricConfigStore.getStore().getItemsByObject(event.getLong(GlobalConstants.ID))
                    .stream().filter(item -> item.getString(Metric.METRIC_TYPE).equalsIgnoreCase(metricPlugin))
                    .map(JsonObject::mapFrom).toList();

            retries.getAndIncrement();

            if (metrics.isEmpty())
            {
                TestUtil.vertx().cancelTimer(timer);

                testContext.failNow(new Exception("failed to find application metrics..."));
            }
            else
            {
                var valid = true;

                for (var item : metrics)
                {
                    if (!item.getString(Metric.METRIC_STATE).equalsIgnoreCase(metricState))
                    {
                        valid = false;

                        break;
                    }
                }

                if (valid)
                {
                    TestUtil.vertx().cancelTimer(timer);

                    testContext.completeNow();
                }
                else if (retries.get() > 10)
                {
                    TestUtil.vertx().cancelTimer(timer);

                    testContext.failNow(new Exception("failed to find correlate application state"));
                }
            }
        });
    }

    private void assertUnreachableInstancesTestResult(JsonObject object, String instance, VertxTestContext testContext, JsonObject event)
    {
        Assertions.assertTrue(object.containsKey(instance));

        var items = object.getJsonArray(instance);

        Assertions.assertFalse(items.isEmpty());

        var valid = true;

        for (var index = 0; index < items.size(); index++)
        {
            var item = items.getJsonObject(index);

            if (!item.containsKey(STATUS) || !item.getString(STATUS).equalsIgnoreCase(GlobalConstants.STATUS_UNREACHABLE))
            {
                valid = false;

                break;
            }
        }

        if (!valid)
        {
            testContext.failNow(new Exception(String.format("failed to unreachable %s instances....", instance)));
        }

        if (event != null)
        {
            assertMetricStateTestResult(event, testContext, NMSConstants.State.SUSPEND.name());
        }
    }

    private void assertMetricStateTestResult(JsonObject event, VertxTestContext testContext, String state)
    {
        var retries = new AtomicInteger(0);

        var objects = new HashSet<String>();

        TestUtil.vertx().setPeriodic(4000, timer ->
        {
            objects.clear();

            retries.getAndIncrement();

            var metrics = MetricConfigStore.getStore().getItemsByObject(event.getLong(GlobalConstants.ID));

            Assertions.assertFalse(metrics.isEmpty());

            var valid = true;

            for (var metric : metrics)
            {
                if (!metric.getString(Metric.METRIC_PLUGIN).equalsIgnoreCase(NMSConstants.MetricPlugin.AVAILABILITY.getName()) && !metric.getString(Metric.METRIC_STATE).equalsIgnoreCase(state))
                {
                    objects.add(metric.getString(Metric.METRIC_NAME));

                    valid = false;
                }
            }

            if (valid)
            {
                TestUtil.vertx().cancelTimer(timer);

                testContext.completeNow();
            }
            else if (retries.get() > 10)
            {
                TestUtil.vertx().cancelTimer(timer);

                LOGGER.info(String.format("metric state has not changed %s", objects));

                testContext.failNow(new Exception("metric state not changed..."));
            }
        });
    }

}
