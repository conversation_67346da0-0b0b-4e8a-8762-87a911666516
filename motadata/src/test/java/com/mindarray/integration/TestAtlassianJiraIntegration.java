/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

package com.mindarray.integration;

import com.mindarray.GlobalConstants;
import com.mindarray.TestUtil;
import com.mindarray.aiops.AIOpsConstants;
import com.mindarray.api.*;
import com.mindarray.eventbus.EventBusConstants;
import com.mindarray.nms.NMSConstants;
import com.mindarray.policy.PolicyEngineConstants;
import com.mindarray.store.IntegrationCacheStore;
import com.mindarray.store.IntegrationProfileConfigStore;
import com.mindarray.store.ObjectConfigStore;
import com.mindarray.util.CommonUtil;
import com.mindarray.util.DateTimeUtil;
import com.mindarray.util.Logger;
import io.github.artsok.RepeatedIfExceptionsTest;
import io.vertx.core.eventbus.MessageConsumer;
import io.vertx.core.json.JsonObject;
import io.vertx.junit5.Timeout;
import io.vertx.junit5.VertxExtension;
import io.vertx.junit5.VertxTestContext;
import org.junit.jupiter.api.*;
import org.junit.jupiter.api.condition.EnabledIfSystemProperty;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.api.parallel.Execution;
import org.junit.jupiter.api.parallel.ExecutionMode;

import java.util.Map;

import static com.mindarray.GlobalConstants.*;
import static com.mindarray.eventbus.EventBusConstants.EVENT_INTEGRATION;

@ExtendWith(VertxExtension.class)
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
@Timeout(60 * 1000)
@EnabledIfSystemProperty(named = "test.mode", matches = "^APP|^PRIMARY$")
@Execution(ExecutionMode.SAME_THREAD)
public class TestAtlassianJiraIntegration
{
    private static final Logger LOGGER = new Logger(TestAtlassianJiraIntegration.class, INTEGRATION_DIR, "Test Atlassian Jira Integration");

    private static JsonObject payload;

    private static MessageConsumer<JsonObject> messageConsumer;

    private static Long id;

    private static Map<String, Long> profiles;

    @BeforeAll
    static void beforeAll(VertxTestContext testContext)
    {
        try
        {
            var object = ObjectConfigStore.getStore().getItemByIP("************", NMSConstants.Type.LINUX);

            payload = new JsonObject().put(SEVERITY, GlobalConstants.Severity.CRITICAL.name())
                    .put(MetricPolicy.POLICY_THRESHOLD, "80")
                    .put(Metric.METRIC_TYPE, "Linux")
                    .put(AIOpsObject.OBJECT_CATEGORY, object.getString(object.getString(AIOpsObject.OBJECT_CATEGORY)))
                    .put(PLUGIN_ID, 88).put(AIOpsConstants.ENTITY_ID, object.getLong(ID)).put(METRIC, "system.cpu.percent")
                    .put(PolicyEngineConstants.POLICY_TYPE, PolicyEngineConstants.PolicyType.STATIC.getName()).put(VALUE, "80.0")
                    .put(PolicyEngineConstants.POLICY_KEY, object.getLong(ID) + SEPARATOR + PolicyEngineConstants.PolicyType.STATIC.getName() + SEPARATOR + "system.cpu.process.percent")
                    .put(AIOpsObject.OBJECT_ID, object.getInteger(AIOpsObject.OBJECT_ID)).put(AIOpsObject.OBJECT_NAME, object.getString(AIOpsObject.OBJECT_NAME))
                    .put(AIOpsObject.OBJECT_TYPE, object.getString(AIOpsObject.OBJECT_TYPE)).put(AIOpsObject.OBJECT_IP, object.getString(AIOpsObject.OBJECT_IP))
                    .put(PolicyEngineConstants.POLICY_ID, DEFAULT_ID).put(User.USER_NAME, "admin").put(MESSAGE, "system.cpu.percent over xyz was >= 80 during last 5 mins with 1 abnormality occurrences.");

            id = IntegrationProfileConfigStore.getStore().getItemByValue(IntegrationProfile.INTEGRATION_PROFILE_NAME, "DemoAtlassianJiraIntegrationProfile").getLong(ID);

            var field = IntegrationCacheStore.class.getDeclaredField("profiles");

            field.setAccessible(true);

            profiles = (Map<String, Long>) field.get(IntegrationCacheStore.getStore());

            testContext.completeNow();
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);

            testContext.failNow(exception.getMessage());
        }
    }

    @BeforeEach
    void beforeEach(VertxTestContext testContext, TestInfo testInfo)
    {
        LOGGER.info(String.format("running test case %s", testInfo.getTestMethod().get().getName()));

        testContext.completeNow();

        payload.put(EventBusConstants.EVENT_TIMESTAMP, DateTimeUtil.currentSeconds()).put(EventBusConstants.EVENT_ID, CommonUtil.newEventId());
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(1)
    void testAtlassianJiraCreateTicket(VertxTestContext testContext)
    {
        messageConsumer = TestUtil.vertx().eventBus().localConsumer("test.integration", message -> testContext.verify(() ->
        {
            try
            {
                if (message.body().getString(Integration.INTEGRATION_TYPE, EMPTY_VALUE).equalsIgnoreCase(IntegrationConstants.IntegrationType.ATLASSIAN_JIRA.getName()))
                {
                    assertTicketCreateTestResult(message.body());

                    Assertions.assertTrue(profiles.containsKey(payload.getString(PolicyEngineConstants.POLICY_ID) + SEPARATOR + payload.getString(PolicyEngineConstants.POLICY_KEY)));

                    Assertions.assertEquals(id, profiles.get(payload.getString(PolicyEngineConstants.POLICY_ID) + SEPARATOR + payload.getString(PolicyEngineConstants.POLICY_KEY)));

                    messageConsumer.unregister();

                    testContext.completeNow();
                }
            }
            catch (Exception exception)
            {
                LOGGER.error(exception);
            }
        }));

        TestUtil.vertx().eventBus().send(EVENT_INTEGRATION, payload.copy().put(ID, id));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(2)
    void testAtlassianJiraUpdateTicket(VertxTestContext testContext)
    {
        messageConsumer = TestUtil.vertx().eventBus().localConsumer("test.integration", message -> testContext.verify(() ->
        {
            try
            {
                if (message.body().getString(Integration.INTEGRATION_TYPE, EMPTY_VALUE).equalsIgnoreCase(IntegrationConstants.IntegrationType.ATLASSIAN_JIRA.getName()))
                {
                    var event = message.body();

                    Assertions.assertTrue(event.containsKey(RESULT));

                    Assertions.assertFalse(event.getJsonObject(RESULT).isEmpty());

                    Assertions.assertTrue(event.getJsonObject(RESULT).containsKey("id"));

                    Assertions.assertTrue(event.getJsonObject(RESULT).containsKey("body"));

                    Assertions.assertEquals(STATUS_SUCCEED, event.getString(STATUS));

                    Assertions.assertTrue(event.containsKey(Integration.INTEGRATION_TYPE));

                    Assertions.assertTrue(event.getJsonObject(RESULT).containsKey("created"));

                    Assertions.assertTrue(profiles.containsKey(payload.getString(PolicyEngineConstants.POLICY_ID) + SEPARATOR + payload.getString(PolicyEngineConstants.POLICY_KEY)));

                    Assertions.assertEquals(id, profiles.get(payload.getString(PolicyEngineConstants.POLICY_ID) + SEPARATOR + payload.getString(PolicyEngineConstants.POLICY_KEY)));

                    messageConsumer.unregister();

                    testContext.completeNow();
                }
            }
            catch (Exception exception)
            {
                LOGGER.error(exception);
            }
        }));

        TestUtil.vertx().eventBus().send(EVENT_INTEGRATION, payload.copy().put(SEVERITY, Severity.MAJOR.name()));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(3)
    void testAtlassianJiraClearTicket(VertxTestContext testContext)
    {
        messageConsumer = TestUtil.vertx().eventBus().localConsumer("test.integration", message -> testContext.verify(() ->
        {
            try
            {
                if (message.body().getString(Integration.INTEGRATION_TYPE, EMPTY_VALUE).equalsIgnoreCase(IntegrationConstants.IntegrationType.ATLASSIAN_JIRA.getName()) && message.body().getString(SEVERITY, EMPTY_VALUE).equalsIgnoreCase(Severity.CLEAR.name()))
                {
                    var event = message.body();

                    Assertions.assertTrue(event.containsKey(RESULT));

                    Assertions.assertTrue(event.getJsonObject(RESULT).isEmpty());

                    Assertions.assertEquals(STATUS_SUCCEED, event.getString(STATUS));

                    Assertions.assertTrue(event.containsKey(Integration.INTEGRATION_TYPE));

                    Assertions.assertEquals("Resolved", event.getString("statusName"));

                    Assertions.assertFalse(profiles.containsKey(payload.getString(PolicyEngineConstants.POLICY_ID) + SEPARATOR + payload.getString(PolicyEngineConstants.POLICY_KEY)));

                    messageConsumer.unregister();

                    testContext.completeNow();
                }
            }
            catch (Exception exception)
            {
                LOGGER.error(exception);
            }
        }));

        TestUtil.vertx().eventBus().send(EVENT_INTEGRATION, payload.copy().put(SEVERITY, Severity.CLEAR.name()));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(4)
    void testAtlassianJiraReopenTicket(VertxTestContext testContext)
    {
        messageConsumer = TestUtil.vertx().eventBus().localConsumer("test.integration", message -> testContext.verify(() ->
        {
            try
            {
                if (message.body().getString(Integration.INTEGRATION_TYPE, EMPTY_VALUE).equalsIgnoreCase(IntegrationConstants.IntegrationType.ATLASSIAN_JIRA.getName()))
                {
                    var event = message.body();

                    Assertions.assertTrue(event.containsKey(RESULT));

                    Assertions.assertTrue(event.getJsonObject(RESULT).isEmpty());

                    Assertions.assertEquals(STATUS_SUCCEED, event.getString(STATUS));

                    Assertions.assertTrue(event.containsKey(Integration.INTEGRATION_TYPE));

                    Assertions.assertEquals("Reopened", event.getString("statusName"));

                    Assertions.assertTrue(profiles.containsKey(payload.getString(PolicyEngineConstants.POLICY_ID) + SEPARATOR + payload.getString(PolicyEngineConstants.POLICY_KEY)));

                    Assertions.assertEquals(id, profiles.get(payload.getString(PolicyEngineConstants.POLICY_ID) + SEPARATOR + payload.getString(PolicyEngineConstants.POLICY_KEY)));

                    messageConsumer.unregister();

                    testContext.completeNow();
                }
            }
            catch (Exception exception)
            {
                LOGGER.error(exception);
            }
        }));

        TestUtil.vertx().eventBus().send(EVENT_INTEGRATION, payload.copy().put(ID, id));
    }


    private void assertTicketCreateTestResult(JsonObject event)
    {
        Assertions.assertTrue(event.containsKey(RESULT));

        Assertions.assertFalse(event.getJsonObject(RESULT).isEmpty());

        var result = event.getJsonObject(RESULT);

        Assertions.assertTrue(result.containsKey("id"));

        Assertions.assertTrue(result.containsKey("key"));

        Assertions.assertTrue(result.containsKey("self"));

        Assertions.assertTrue(event.containsKey(STATUS));

        Assertions.assertEquals(STATUS_SUCCEED, event.getString(STATUS));

        Assertions.assertTrue(event.containsKey(Integration.INTEGRATION_TYPE));
    }
}
