/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

package com.mindarray.topology;

import com.mindarray.*;
import com.mindarray.api.*;
import com.mindarray.db.DBConstants;
import com.mindarray.eventbus.EventBusConstants;
import com.mindarray.job.JobScheduler;
import com.mindarray.nms.NMSConstants;
import com.mindarray.nms.TopologyEngine;
import com.mindarray.plugin.PluginEngineConstants;
import com.mindarray.store.*;
import com.mindarray.util.CodecUtil;
import com.mindarray.util.CommonUtil;
import com.mindarray.util.DateTimeUtil;
import com.mindarray.util.Logger;
import io.github.artsok.RepeatedIfExceptionsTest;
import io.vertx.core.Future;
import io.vertx.core.Promise;
import io.vertx.core.eventbus.MessageConsumer;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import io.vertx.junit5.Timeout;
import io.vertx.junit5.VertxExtension;
import io.vertx.junit5.VertxTestContext;
import org.apache.commons.io.FileUtils;
import org.junit.jupiter.api.*;
import org.junit.jupiter.api.condition.EnabledIfSystemProperty;
import org.junit.jupiter.api.extension.ExtendWith;

import java.io.File;
import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

import static com.mindarray.GlobalConstants.*;
import static com.mindarray.TestAPIConstants.*;
import static com.mindarray.api.APIConstants.RESPONSE_CODE;
import static com.mindarray.api.APIConstants.SESSION_ID;
import static com.mindarray.api.Scheduler.*;
import static com.mindarray.api.TopologyPlugin.TOPOLOGY_PLUGIN_FILTER_TARGET_TYPE;
import static com.mindarray.api.TopologyPlugin.TOPOLOGY_PLUGIN_TYPE;
import static com.mindarray.api.User.USER_NAME;
import static com.mindarray.eventbus.EventBusConstants.*;
import static com.mindarray.nms.NMSConstants.OBJECT;
import static com.mindarray.nms.NMSConstants.OBJECTS;
import static com.mindarray.util.CronExpressionUtil.CRON_ONCE;
import static org.apache.http.HttpStatus.SC_BAD_REQUEST;
import static org.apache.http.HttpStatus.SC_OK;
import static org.junit.jupiter.api.Assertions.assertEquals;

@ExtendWith(VertxExtension.class)
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
@Timeout(80 * 1000)
@EnabledIfSystemProperty(named = "test.mode", matches = "^APP|^PRIMARY$")

public class TestTopology
{
    private static final Map<String, Long> ENTITY_IDS = new HashMap<>();
    private static final Logger LOGGER = new Logger(TestTopology.class, TOPOLOGY_DIR, "Topology Test");
    private static final Map<Long, Integer> USED_COUNTS = new HashMap<>();
    private static final String PATTERN = "dd-MM-yyyy";
    private static JsonObject context = null;
    private static JsonArray targets = null;
    private static JsonArray objects = null;
    private static MessageConsumer<JsonObject> messageConsumer;

    @AfterAll
    static void afterAll(VertxTestContext testContext)
    {
        if (ENTITY_IDS.get("down.object") != null)
        {
            ObjectStatusCacheStore.getStore().updateItem(ENTITY_IDS.get("down.object"), STATUS_UP, DateTimeUtil.currentSeconds());
        }

        if (messageConsumer != null)
        {
            messageConsumer.unregister(result -> testContext.completeNow());
        }
        else
        {
            testContext.completeNow();
        }
    }

    @BeforeEach
    void beforeEach(VertxTestContext testContext, TestInfo testInfo)
    {
        LOGGER.info(String.format("running test case %s", testInfo.getTestMethod().get().getName()));

        if (messageConsumer != null)
        {
            messageConsumer.unregister(result -> testContext.completeNow());
        }
        else
        {
            testContext.completeNow();
        }
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Timeout(value = 150, timeUnit = TimeUnit.SECONDS)
    @Order(0)
    void testTopologyRunWithProtocols(VertxTestContext testContext, TestInfo testInfo)
    {
        var objects = ObjectConfigStore.getStore().flatItemsByValues(AIOpsObject.OBJECT_IP, new JsonArray().add("**********"), ID);

        Assertions.assertFalse(objects.isEmpty());

        var scheduler = new JsonObject().put(Scheduler.SCHEDULER_START_DATE, new SimpleDateFormat(PATTERN).format(new Date()))
                .put(Scheduler.SCHEDULER_TIMELINE, CRON_ONCE)
                .put(Scheduler.SCHEDULER_TIMES, new JsonArray().add("00:00"))
                .put(Scheduler.SCHEDULER_JOB_TYPE, JobScheduler.JobType.TOPOLOGY.getName())
                .put(SCHEDULER_CONTEXT, new JsonObject().put(SCHEDULER_EMAIL_RECIPIENTS, new JsonArray().add("<EMAIL>"))
                        .put(TopologyPlugin.TOPOLOGY_PLUGIN_ENTRY_POINTS, objects).put(OBJECTS, objects)
                        .put(NMSConstants.TOPOLOGY_LINK_LAYER, new JsonArray().add(NMSConstants.TopologyLinkLayer.L2.getName()))
                        .put(NMSConstants.TOPOLOGY_PROTOCOLS, new JsonArray().add("CDP").add("LLDP").add("OSPF"))
                        .put(SCHEDULER_SMS_RECIPIENTS, new JsonArray().add("7990311324")));

        TestAPIUtil.post(SCHEDULER_API_ENDPOINT, scheduler,
                testContext.succeeding(response ->
                        testContext.verify(() ->

                                TestUtil.vertx().setTimer(TimeUnit.SECONDS.toMillis(3), id ->
                                {
                                    TestAPIUtil.assertMultiCreateEntityTestResult(SchedulerConfigStore.getStore(), response.bodyAsJsonObject(), "Scheduler");

                                    ENTITY_IDS.put("topology.selected.protocols", response.bodyAsJsonObject().getJsonArray(ID).getLong(0));

                                    TestUtil.vertx().setTimer(5000, timer ->
                                    {

                                        var item = SchedulerConfigStore.getStore().getItem(ENTITY_IDS.get("topology.selected.protocols"));

                                        Assertions.assertNotNull(item);

                                        if (item.containsKey(SCHEDULER_CONTEXT))
                                        {
                                            item.mergeIn(item.getJsonObject(SCHEDULER_CONTEXT));

                                            item.remove(SCHEDULER_CONTEXT);
                                        }

                                        item.getJsonArray(TopologyPlugin.TOPOLOGY_PLUGIN_ENTRY_POINTS).forEach(identifier -> ObjectStatusCacheStore.getStore().updateItem(CommonUtil.getLong(identifier), STATUS_UP, DateTimeUtil.currentSeconds()));

                                        Bootstrap.vertx().eventBus().send(UI_ACTION_SCHEDULER_RUN, item.put(USER_NAME, DEFAULT_USER).put(SESSION_ID, TestUtil.getSessionId()));

                                        var retries = new AtomicInteger();

                                        TestUtil.vertx().setPeriodic(5000, handler ->
                                        {
                                            try
                                            {
                                                var buffer = FileUtils.readFileToString(new File(CURRENT_DIR + PATH_SEPARATOR + LOGS_DIR + PATH_SEPARATOR + "nms" + PATH_SEPARATOR + "$$$-Topology Engine.log".replace("$$$", new SimpleDateFormat("dd-MMMM-yyyy HH").format(new Date()))), StandardCharsets.UTF_8);

                                                LOGGER.info(testInfo.getTestMethod().get().getName() + " and retries = " + retries + " : " + buffer);

                                                if (buffer.contains("Protocols qualified : [\"CDP\",\"LLDP\"] for monitor **********") || buffer.contains("Protocols qualified : [\"LLDP\",\"CDP\"] for monitor **********"))
                                                {
                                                    testContext.completeNow();
                                                }
                                                else if (retries.get() > 15)
                                                {
                                                    TestUtil.vertx().cancelTimer(timer);

                                                    testContext.failNow("expected logs not found for topology");
                                                }
                                                else
                                                {
                                                    retries.incrementAndGet();
                                                }
                                            }
                                            catch (Exception exception)
                                            {
                                                LOGGER.error(exception);

                                                testContext.failNow(exception);
                                            }
                                        });
                                    });
                                }))));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(1)
    void testCreateCustomSNMPOSPFTopologyPlugin(VertxTestContext testContext, TestInfo testInfo)
    {
        context = TestConstants.prepareParams(testInfo.getTestMethod().get().getName()).put(TopologyPlugin.TOPOLOGY_PLUGIN_NAME, "SNMP OSPF-" + System.currentTimeMillis());

        targets = ObjectConfigStore.getStore().flatItems(AIOpsObject.OBJECT_IP, "***********", ID);

        prepareTestContext(context);

        Assertions.assertNotNull(targets);

        Assertions.assertFalse(targets.isEmpty());

        context.put(TopologyPlugin.TOPOLOGY_PLUGIN_ENTITIES, targets);

        assertCreateTopologyPluginTestResult(testContext, context, targets, testInfo);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(2)
    void testCreateDuplicateCustomSNMPOSPFTopologyPlugin(VertxTestContext testContext)
    {
        assertCreateTopologyPluginDuplicateTestResult(testContext, context);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(3)
    void testGetCreatedCustomSNMPOSPFTopologyPlugin(VertxTestContext testContext, TestInfo testInfo)
    {
        assertGetCustomTopologyPluginTestResult(testContext, testInfo);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(4)
    void testAssignObjectsToCustomSNMPOSPFTopologyPlugin(VertxTestContext testContext)
    {

        objects = new JsonArray().add(ObjectConfigStore.getStore().getItemByValue(AIOpsObject.OBJECT_IP, "***********").getLong(ID));

        Assertions.assertNotNull(objects);

        Assertions.assertFalse(objects.isEmpty());

        assertAssignObjectTestResult(testContext, objects);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(5)
    void testAssignDuplicateObjectsToCustomSNMPOSPFTopologyPlugin(VertxTestContext testContext)
    {
        assertAssignDuplicateObjectTestResult(testContext, objects);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(6)
    void testUnassignedObjectsToCustomSNMPOSPFTopologyPlugin(VertxTestContext testContext)
    {
        assertUnassignedObjectTestResult(testContext, objects);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(7)
    void testUnassignedDuplicateObjectsToCustomSNMPOSPFTopologyPlugin(VertxTestContext testContext)
    {
        assertUnassignedDuplicateObjectTestResult(testContext, objects);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(8)
    void testDeleteSNMPOSPFTopologyPluginAssignedObjects(VertxTestContext testContext)
    {
        assertDeleteTopologyPluginHavingAssignedObjectTestResult(testContext);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(9)
    void testUnassignedAllObjectsToCustomSNMPOSPFTopologyPlugin(VertxTestContext testContext)
    {
        assertUnassignedObjectTestResult(testContext, targets);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(10)
    void testDeleteSNMPOSPFTopologyPluginNotHavingAssignedObjects(VertxTestContext testContext)
    {
        assertDeleteTopologyPluginNotHavingAssignedObjectTestResult(testContext);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(11)
    void testGetCustomSNMPOSPFTopologyPluginAfterDelete(VertxTestContext testContext)
    {
        assertCustomTopologyPluginAfterDeleteTestResult(testContext);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(12)
    void testCreateCustomSNMPLLDPTopologyPlugin(VertxTestContext testContext, TestInfo testInfo)
    {
        context = TestConstants.prepareParams(testInfo.getTestMethod().get().getName()).put(TopologyPlugin.TOPOLOGY_PLUGIN_NAME, "SNMP LLDP-" + System.currentTimeMillis());

        targets = ObjectConfigStore.getStore().flatItems(AIOpsObject.OBJECT_IP, "************", ID);

        prepareTestContext(context);

        Assertions.assertNotNull(targets);

        Assertions.assertFalse(targets.isEmpty());

        context.put(TopologyPlugin.TOPOLOGY_PLUGIN_ENTITIES, targets);

        assertCreateTopologyPluginTestResult(testContext, context, targets, testInfo);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(13)
    void testCreateDuplicateCustomSNMPLLDPTopologyPlugin(VertxTestContext testContext)
    {
        assertCreateTopologyPluginDuplicateTestResult(testContext, context);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(14)
    void testGetCreatedCustomSNMPLLDPTopologyPlugin(VertxTestContext testContext, TestInfo testInfo)
    {
        assertGetCustomTopologyPluginTestResult(testContext, testInfo);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(15)
    void testAssignObjectsToCustomSNMPLLDPTopologyPlugin(VertxTestContext testContext)
    {

        objects = new JsonArray().add(ObjectConfigStore.getStore().getItemByValue(AIOpsObject.OBJECT_IP, "***********").getLong(ID));

        Assertions.assertNotNull(objects);

        Assertions.assertFalse(objects.isEmpty());

        assertAssignObjectTestResult(testContext, objects);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(16)
    void testAssignDuplicateObjectsToCustomSNMPLLDPTopologyPlugin(VertxTestContext testContext)
    {
        assertAssignDuplicateObjectTestResult(testContext, objects);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(17)
    void testUnassignedObjectsToCustomSNMPLLDPTopologyPlugin(VertxTestContext testContext)
    {
        assertUnassignedObjectTestResult(testContext, objects);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(18)
    void testUnassignedDuplicateObjectsToCustomSNMPLLDPTopologyPlugin(VertxTestContext testContext)
    {
        assertUnassignedDuplicateObjectTestResult(testContext, objects);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(19)
    void testDeleteSNMPLLDPTopologyPluginWithAssignedObjects(VertxTestContext testContext)
    {
        assertDeleteTopologyPluginHavingAssignedObjectTestResult(testContext);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(20)
    void testUnassignedAllObjectsToCustomSNMPLLDPTopologyPlugin(VertxTestContext testContext)
    {
        assertUnassignedObjectTestResult(testContext, targets);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(21)
    void testDeleteSNMPLLDPTopologyPluginNotHavingAssignedObjects(VertxTestContext testContext)
    {
        assertDeleteTopologyPluginNotHavingAssignedObjectTestResult(testContext);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(22)
    void testGetCustomSNMPLLDPTopologyPluginAfterDelete(VertxTestContext testContext)
    {
        assertCustomTopologyPluginAfterDeleteTestResult(testContext);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(23)
    void testCreateCustomSNMPCDPTopologyPlugin(VertxTestContext testContext, TestInfo testInfo)
    {
        context = TestConstants.prepareParams(testInfo.getTestMethod().get().getName()).put(TopologyPlugin.TOPOLOGY_PLUGIN_NAME, "SNMP CDP-" + System.currentTimeMillis());

        targets = ObjectConfigStore.getStore().flatItems(AIOpsObject.OBJECT_IP, "************", ID);

        prepareTestContext(context);

        Assertions.assertNotNull(targets);

        Assertions.assertFalse(targets.isEmpty());

        context.put(TopologyPlugin.TOPOLOGY_PLUGIN_ENTITIES, targets);

        assertCreateTopologyPluginTestResult(testContext, context, targets, testInfo);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(24)
    void testCreateDuplicateCustomSNMPCDPTopologyPlugin(VertxTestContext testContext)
    {
        assertCreateTopologyPluginDuplicateTestResult(testContext, context);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(25)
    void testGetCreatedCustomSNMPCDPTopologyPlugin(VertxTestContext testContext, TestInfo testInfo)
    {
        assertGetCustomTopologyPluginTestResult(testContext, testInfo);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(26)
    void testAssignObjectsToCustomSNMPCDPTopologyPlugin(VertxTestContext testContext)
    {

        objects = new JsonArray().add(ObjectConfigStore.getStore().getItemByValue(AIOpsObject.OBJECT_IP, "***********").getLong(ID));

        Assertions.assertNotNull(objects);

        Assertions.assertFalse(objects.isEmpty());

        assertAssignObjectTestResult(testContext, objects);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(27)
    void testAssignDuplicateObjectsToCustomSNMPCDPTopologyPlugin(VertxTestContext testContext)
    {
        assertAssignDuplicateObjectTestResult(testContext, objects);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(28)
    void testUnassignedObjectsToCustomSNMPCDPTopologyPlugin(VertxTestContext testContext)
    {
        assertUnassignedObjectTestResult(testContext, objects);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(29)
    void testUnassignedDuplicateObjectsToCustomSNMPCDPTopologyPlugin(VertxTestContext testContext)
    {
        assertUnassignedDuplicateObjectTestResult(testContext, objects);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(30)
    void testDeleteSNMPCDPTopologyPluginWithAssignedObjects(VertxTestContext testContext)
    {
        assertDeleteTopologyPluginHavingAssignedObjectTestResult(testContext);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(31)
    void testUnassignedAllObjectsToCustomSNMPCDPTopologyPlugin(VertxTestContext testContext)
    {
        assertUnassignedObjectTestResult(testContext, targets);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(32)
    void testDeleteSNMPCDPTopologyPluginNotHavingAssignedObjects(VertxTestContext testContext)
    {
        assertDeleteTopologyPluginNotHavingAssignedObjectTestResult(testContext);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(33)
    void testGetCustomSNMPCDPTopologyPluginAfterDelete(VertxTestContext testContext)
    {
        assertCustomTopologyPluginAfterDeleteTestResult(testContext);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(34)
    void testCreateCustomSNMPISISTopologyPlugin(VertxTestContext testContext, TestInfo testInfo)
    {
        context = TestConstants.prepareParams(testInfo.getTestMethod().get().getName()).put(TopologyPlugin.TOPOLOGY_PLUGIN_NAME, "SNMP ISIS-" + System.currentTimeMillis());

        targets = ObjectConfigStore.getStore().flatItems(AIOpsObject.OBJECT_IP, "***********", ID);

        prepareTestContext(context);

        Assertions.assertNotNull(targets);

        Assertions.assertFalse(targets.isEmpty());

        context.put(TopologyPlugin.TOPOLOGY_PLUGIN_ENTITIES, targets);

        assertCreateTopologyPluginTestResult(testContext, context, targets, testInfo);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(35)
    void testCreateDuplicateCustomSNMPISISTopologyPlugin(VertxTestContext testContext)
    {
        assertCreateTopologyPluginDuplicateTestResult(testContext, context);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(36)
    void testGetCreatedCustomSNMPISISTopologyPlugin(VertxTestContext testContext, TestInfo testInfo)
    {
        assertGetCustomTopologyPluginTestResult(testContext, testInfo);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(37)
    void testAssignObjectsToCustomSNMPISISTopologyPlugin(VertxTestContext testContext)
    {

        objects = new JsonArray().add(ObjectConfigStore.getStore().getItemByValue(AIOpsObject.OBJECT_IP, "***********").getLong(ID));

        Assertions.assertNotNull(objects);

        Assertions.assertFalse(objects.isEmpty());

        assertAssignObjectTestResult(testContext, objects);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(38)
    void testAssignDuplicateObjectsToCustomSNMPISISTopologyPlugin(VertxTestContext testContext)
    {
        assertAssignDuplicateObjectTestResult(testContext, objects);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(39)
    void testUnassignedObjectsToCustomSNMPISISTopologyPlugin(VertxTestContext testContext)
    {
        assertUnassignedObjectTestResult(testContext, objects);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(40)
    void testUnassignedDuplicateObjectsToCustomSNMPISISTopologyPlugin(VertxTestContext testContext)
    {
        assertUnassignedDuplicateObjectTestResult(testContext, objects);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(41)
    void testDeleteSNMPISISTopologyPluginAssignedObjects(VertxTestContext testContext)
    {
        assertDeleteTopologyPluginHavingAssignedObjectTestResult(testContext);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(42)
    void testUnassignedAllObjectsToCustomSNMPISISTopologyPlugin(VertxTestContext testContext)
    {
        assertUnassignedObjectTestResult(testContext, targets);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(43)
    void testDeleteSNMPISISTopologyPluginNotHavingAssignedObjects(VertxTestContext testContext)
    {
        assertDeleteTopologyPluginNotHavingAssignedObjectTestResult(testContext);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(44)
    void testGetCustomSNMPISISTopologyPluginAfterDelete(VertxTestContext testContext)
    {
        assertCustomTopologyPluginAfterDeleteTestResult(testContext);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(45)
    void testCreateCustomSNMPBGPTopologyPlugin(VertxTestContext testContext, TestInfo testInfo)
    {
        context = TestConstants.prepareParams(testInfo.getTestMethod().get().getName()).put(TopologyPlugin.TOPOLOGY_PLUGIN_NAME, "SNMP BGP-" + System.currentTimeMillis());

        targets = ObjectConfigStore.getStore().flatItems(AIOpsObject.OBJECT_IP, "***********", ID);

        prepareTestContext(context);

        Assertions.assertNotNull(targets);

        Assertions.assertFalse(targets.isEmpty());

        context.put(TopologyPlugin.TOPOLOGY_PLUGIN_ENTITIES, targets);

        assertCreateTopologyPluginTestResult(testContext, context, targets, testInfo);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(46)
    void testCreateDuplicateCustomSNMPBGPTopologyPlugin(VertxTestContext testContext)
    {
        assertCreateTopologyPluginDuplicateTestResult(testContext, context);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(47)
    void testGetCreatedCustomSNMPBGPTopologyPlugin(VertxTestContext testContext, TestInfo testInfo)
    {
        assertGetCustomTopologyPluginTestResult(testContext, testInfo);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(48)
    void testAssignObjectsToCustomSNMPBGPTopologyPlugin(VertxTestContext testContext)
    {

        objects = new JsonArray().add(ObjectConfigStore.getStore().getItemByValue(AIOpsObject.OBJECT_IP, "***********").getLong(ID));

        Assertions.assertNotNull(objects);

        Assertions.assertFalse(objects.isEmpty());

        assertAssignObjectTestResult(testContext, objects);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(49)
    void testAssignDuplicateObjectsToCustomSNMPBGPTopologyPlugin(VertxTestContext testContext)
    {
        assertAssignDuplicateObjectTestResult(testContext, objects);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(50)
    void testUnassignedObjectsToCustomSNMPBGPTopologyPlugin(VertxTestContext testContext)
    {
        assertUnassignedObjectTestResult(testContext, objects);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(51)
    void testUnassignedDuplicateObjectsToCustomSNMPBGPTopologyPlugin(VertxTestContext testContext)
    {
        assertUnassignedDuplicateObjectTestResult(testContext, objects);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(52)
    void testDeleteSNMPBGPTopologyPluginHavingAssignedObjects(VertxTestContext testContext)
    {
        assertDeleteTopologyPluginHavingAssignedObjectTestResult(testContext);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(53)
    void testUnassignedAllObjectsToCustomSNMPBGPTopologyPlugin(VertxTestContext testContext)
    {
        assertUnassignedObjectTestResult(testContext, targets);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(54)
    void testDeleteSNMPBGPTopologyPluginNotHavingAssignedObjects(VertxTestContext testContext)
    {
        assertDeleteTopologyPluginNotHavingAssignedObjectTestResult(testContext);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(55)
    void testGetCustomSNMPBGPTopologyPluginAfterDelete(VertxTestContext testContext)
    {
        assertCustomTopologyPluginAfterDeleteTestResult(testContext);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(56)
    void testCreateCustomSNMPSPMTopologyPlugin(VertxTestContext testContext, TestInfo testInfo)
    {
        context = TestConstants.prepareParams(testInfo.getTestMethod().get().getName()).put(TopologyPlugin.TOPOLOGY_PLUGIN_NAME, "SNMP SPM-" + System.currentTimeMillis());

        targets = ObjectConfigStore.getStore().flatItems(AIOpsObject.OBJECT_IP, "************", ID);

        prepareTestContext(context);

        Assertions.assertNotNull(targets);

        Assertions.assertFalse(targets.isEmpty());

        context.put(TopologyPlugin.TOPOLOGY_PLUGIN_ENTITIES, targets);

        assertCreateTopologyPluginTestResult(testContext, context, targets, testInfo);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(57)
    void testCreateDuplicateCustomSNMPSPMTopologyPlugin(VertxTestContext testContext)
    {
        assertCreateTopologyPluginDuplicateTestResult(testContext, context);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(58)
    void testGetCreatedCustomSNMPSPMTopologyPlugin(VertxTestContext testContext, TestInfo testInfo)
    {
        assertGetCustomTopologyPluginTestResult(testContext, testInfo);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(59)
    void testAssignObjectsToCustomSNMPSPMTopologyPlugin(VertxTestContext testContext)
    {

        objects = new JsonArray().add(ObjectConfigStore.getStore().getItemByValue(AIOpsObject.OBJECT_IP, "***********").getLong(ID));

        Assertions.assertNotNull(objects);

        Assertions.assertFalse(objects.isEmpty());

        assertAssignObjectTestResult(testContext, objects);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(60)
    void testAssignDuplicateObjectsToCustomSNMPSPMTopologyPlugin(VertxTestContext testContext)
    {
        assertAssignDuplicateObjectTestResult(testContext, objects);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(61)
    void testUnassignedObjectsToCustomSNMPSPMTopologyPlugin(VertxTestContext testContext)
    {
        assertUnassignedObjectTestResult(testContext, objects);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(62)
    void testUnassignedDuplicateObjectsToCustomSNMPSPMTopologyPlugin(VertxTestContext testContext)
    {
        assertUnassignedDuplicateObjectTestResult(testContext, objects);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(63)
    void testDeleteSNMPSPMTopologyPluginHavingAssignedObjects(VertxTestContext testContext)
    {
        assertDeleteTopologyPluginHavingAssignedObjectTestResult(testContext);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(64)
    void testUnassignedAllObjectsToCustomSNMPSPMTopologyPlugin(VertxTestContext testContext)
    {
        assertUnassignedObjectTestResult(testContext, targets);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(65)
    void testDeleteSNMPSPMTopologyPluginNotHavingAssignedObjects(VertxTestContext testContext)
    {
        assertDeleteTopologyPluginNotHavingAssignedObjectTestResult(testContext);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(66)
    void testGetCustomSNMPSPMTopologyPluginAfterDelete(VertxTestContext testContext)
    {
        assertCustomTopologyPluginAfterDeleteTestResult(testContext);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(67)
    void testCreateCustomSSHBGPTopologyPlugin(VertxTestContext testContext, TestInfo testInfo)
    {
        context.clear();

        context = TestConstants.prepareParams(testInfo.getTestMethod().get().getName()).put(TopologyPlugin.TOPOLOGY_PLUGIN_NAME, "SSH BGP - " + System.currentTimeMillis());

        prepareTestContext(context);

        targets = ObjectConfigStore.getStore().flatItems(AIOpsObject.OBJECT_IP, "***********", ID);

        Assertions.assertNotNull(targets);

        Assertions.assertFalse(targets.isEmpty());

        context.put(TopologyPlugin.TOPOLOGY_PLUGIN_ENTITIES, targets);

        var credential = TestConstants.prepareParams("testCreateCustomSSHBGPTopologyPluginCredentialProfile").put(CredentialProfile.CREDENTIAL_PROFILE_NAME, "Custom SSH BGP Topology Plugin-" + System.currentTimeMillis());

        TestAPIUtil.createCredentialProfile(credential, testContext).onComplete(result ->
        {
            if (result.succeeded())
            {
                var credentialId = result.result();

                if (credentialId != null && credentialId != 0)
                {
                    var topologyContext = context.getJsonObject(TopologyPlugin.TOPOLOGY_PLUGIN_CONTEXT);

                    topologyContext.put(TopologyPlugin.TOPOLOGY_PLUGIN_CREDENTIAL_PROFILE, credentialId);

                    topologyContext.put(PORT, 22);

                    context.put(TopologyPlugin.TOPOLOGY_PLUGIN_CONTEXT, topologyContext);

                    assertCreateTopologyPluginTestResult(testContext, context, targets, testInfo);
                }
            }
        });
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(68)
    void testCreateDuplicateCustomSSHBGPTopologyPlugin(VertxTestContext testContext)
    {
        assertCreateTopologyPluginDuplicateTestResult(testContext, context);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(69)
    void testGetCreatedCustomSSHBGPTopologyPlugin(VertxTestContext testContext, TestInfo testInfo)
    {
        assertGetCustomTopologyPluginTestResult(testContext, testInfo);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(70)
    void testAssignObjectsToCustomSSHBGPTopologyPlugin(VertxTestContext testContext)
    {
        objects = new JsonArray().add(ObjectConfigStore.getStore().getItemByValue(AIOpsObject.OBJECT_IP, "***********").getLong(ID));

        Assertions.assertNotNull(objects);

        Assertions.assertFalse(objects.isEmpty());

        assertAssignObjectTestResult(testContext, objects);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(71)
    void testAssignDuplicateObjectsToCustomSSHBGPTopologyPlugin(VertxTestContext testContext)
    {
        assertAssignDuplicateObjectTestResult(testContext, objects);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(72)
    void testUnassignedObjectsToCustomSSHBGPTopologyPlugin(VertxTestContext testContext)
    {
        assertUnassignedObjectTestResult(testContext, objects);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(73)
    void testUnassignedDuplicateObjectsToCustomSSHBGPTopologyPlugin(VertxTestContext testContext)
    {
        assertUnassignedDuplicateObjectTestResult(testContext, objects);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(74)
    void testDeleteSSHBGPTopologyPluginHavingAssignedObjects(VertxTestContext testContext)
    {
        assertDeleteTopologyPluginHavingAssignedObjectTestResult(testContext);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(75)
    void testUnassignedAllObjectsToCustomSSHBGPTopologyPlugin(VertxTestContext testContext)
    {
        assertUnassignedObjectTestResult(testContext, targets);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(76)
    void testDeleteSSHBGPTopologyPluginNotHavingAssignedObjects(VertxTestContext testContext)
    {
        assertDeleteTopologyPluginNotHavingAssignedObjectTestResult(testContext);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(77)
    void testGetCustomSSHBGPTopologyPluginAfterDelete(VertxTestContext testContext)
    {
        assertCustomTopologyPluginAfterDeleteTestResult(testContext);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(78)
    void testCreateCustomSNMPOSPFTopologyPluginHavingGroup(VertxTestContext testContext, TestInfo testInfo)
    {
        context = TestConstants.prepareParams("testCreateCustomSNMPOSPFTopologyPlugin").put(TopologyPlugin.TOPOLOGY_PLUGIN_NAME, "SNMP OSPF Topology Plugin Having Group - " + System.currentTimeMillis());

        prepareTestContext(context);

        targets = new JsonArray(GroupConfigStore.getStore().getItems().stream().map(item -> JsonObject.mapFrom(item).getLong(ID)).collect(Collectors.toList()));

        Assertions.assertNotNull(targets);

        Assertions.assertFalse(targets.isEmpty());

        context.put(TopologyPlugin.TOPOLOGY_PLUGIN_ENTITIES, targets);

        context.put(TopologyPlugin.TOPOLOGY_PLUGIN_ENTITY_TYPE, APIConstants.Entity.GROUP.getName());

        assertCreateTopologyPluginTestResult(testContext, context, targets, testInfo);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(79)
    void testCreateDuplicateCustomSNMPOSPFTopologyPluginHavingGroup(VertxTestContext testContext)
    {
        assertCreateTopologyPluginDuplicateTestResult(testContext, context);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(80)
    void testGetCreatedCustomSNMPOSPFTopologyPluginHavingGroup(VertxTestContext testContext, TestInfo testInfo)
    {
        assertGetCustomTopologyPluginTestResult(testContext, testInfo);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(81)
    void testUnassignedObjectsToCustomSNMPOSPFTopologyPluginHavingGroup(VertxTestContext testContext)
    {
        objects = ObjectConfigStore.getStore().flatItems(AIOpsObject.OBJECT_IP, "***********", ID);

        Assertions.assertNotNull(objects);

        Assertions.assertFalse(objects.isEmpty());

        assertUnassignedObjectTestResult(testContext, targets);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(82)
    void testUnassignedDuplicateObjectsToCustomSNMPOSPFTopologyPluginHavingGroup(VertxTestContext testContext)
    {
        assertUnassignedDuplicateObjectTestResult(testContext, targets);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(83)
    void testAssignObjectsToCustomSNMPOSPFTopologyPluginHavingGroup(VertxTestContext testContext)
    {
        assertAssignObjectTestResult(testContext, targets);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(84)
    void testAssignDuplicateObjectsToCustomSNMPOSPFTopologyPluginHavingGroup(VertxTestContext testContext)
    {
        assertAssignDuplicateObjectTestResult(testContext, targets);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(85)
    void testDeleteSNMPOSPFTopologyPluginHavingAssignedObjects(VertxTestContext testContext)
    {
        assertDeleteTopologyPluginHavingAssignedObjectTestResult(testContext);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(86)
    void testUnassignedAllObjectsToCustomSNMPOSPFTopologyPluginHavingGroup(VertxTestContext testContext)
    {
        assertUnassignedObjectTestResult(testContext, targets);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(87)
    void testDeleteSNMPOSPFTopologyPluginNotHavingGroupAssignedObjects(VertxTestContext testContext)
    {
        assertDeleteTopologyPluginNotHavingAssignedObjectTestResult(testContext);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(88)
    void testGetCustomSNMPOSPFTopologyPluginHavingGroupAfterDelete(VertxTestContext testContext)
    {
        assertCustomTopologyPluginAfterDeleteTestResult(testContext);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(89)
    void testCreateSSHCDPTopologyPlugin(VertxTestContext testContext, TestInfo testInfo)
    {
        context = TestConstants.prepareParams("testCreateCustomSSHCDPTopologyPlugin").put(TopologyPlugin.TOPOLOGY_PLUGIN_NAME, "SSH CDP-" + System.currentTimeMillis());

        prepareTestContext(context);

        targets = ObjectConfigStore.getStore().flatItems(AIOpsObject.OBJECT_IP, "**********", ID);

        Assertions.assertNotNull(targets);

        Assertions.assertFalse(targets.isEmpty());

        context.put(TopologyPlugin.TOPOLOGY_PLUGIN_ENTITIES, targets);

        context.put(ID, targets.getLong(0));

        context.put(USER_NAME, DEFAULT_USER);

        context.put(SESSION_ID, TestUtil.getSessionId());

        var credential = TestConstants.prepareParams("testCreateCustomSSHCDPTopologyPluginCredentialProfile").put(CredentialProfile.CREDENTIAL_PROFILE_NAME, "Custom SSH CDP Topology Plugin-" + System.currentTimeMillis());

        TestAPIUtil.createCredentialProfile(credential, testContext).onComplete(result ->
        {
            if (result.succeeded())
            {
                var credentialId = result.result();

                if (credentialId != null && credentialId != 0)
                {
                    var topologyContext = context.getJsonObject(TopologyPlugin.TOPOLOGY_PLUGIN_CONTEXT);

                    topologyContext.put(TopologyPlugin.TOPOLOGY_PLUGIN_CREDENTIAL_PROFILE, credentialId);

                    topologyContext.put(PORT, 22);

                    context.put(TopologyPlugin.TOPOLOGY_PLUGIN_CONTEXT, topologyContext);

                    assertCreateTopologyPluginTestResult(testContext, context, targets, testInfo);

                }
                else
                {
                    testContext.failNow(new Exception("credential not found..."));
                }
            }
            else
            {
                testContext.failNow(result.cause());
            }
        });
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(90)
    void testCreateSNMPLLDPTopologyPlugin(VertxTestContext testContext, TestInfo testInfo)
    {
        context = TestConstants.prepareParams("testCreateCustomSNMPLLDPTopologyPlugin").put(TopologyPlugin.TOPOLOGY_PLUGIN_NAME, "SNMP LLDP-" + System.currentTimeMillis());

        prepareTestContext(context);

        targets = new JsonArray(GroupConfigStore.getStore().getItems().stream().map(item -> JsonObject.mapFrom(item).getLong(ID)).collect(Collectors.toList()));

        Assertions.assertFalse(targets.isEmpty());

        context.put(TopologyPlugin.TOPOLOGY_PLUGIN_ENTITIES, targets).put(TopologyPlugin.TOPOLOGY_PLUGIN_ENTITY_TYPE, APIConstants.Entity.GROUP.getName());

        assertCreateTopologyPluginTestResult(testContext, context, targets, testInfo);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(92)
    void testCreateSSHPluginTopologyScanner(VertxTestContext testContext)
    {
        var objects = ObjectConfigStore.getStore().flatItems(AIOpsObject.OBJECT_IP, "**********", ID);

        Assertions.assertFalse(objects.isEmpty());

        var scheduler = new JsonObject().put(Scheduler.SCHEDULER_START_DATE, new SimpleDateFormat(PATTERN).format(new Date()))
                .put(Scheduler.SCHEDULER_TIMELINE, CRON_ONCE)
                .put(Scheduler.SCHEDULER_TIMES, new JsonArray().add("00:00"))
                .put(Scheduler.SCHEDULER_JOB_TYPE, JobScheduler.JobType.TOPOLOGY.getName())
                .put(SCHEDULER_CONTEXT, new JsonObject().put(SCHEDULER_EMAIL_RECIPIENTS, new JsonArray().add("<EMAIL>"))
                        .put(TopologyPlugin.TOPOLOGY_PLUGIN_ENTRY_POINTS, objects).put(OBJECTS, objects)
                        .put(NMSConstants.TOPOLOGY_LINK_LAYER, new JsonArray().add(NMSConstants.TopologyLinkLayer.L2.getName()))
                        .put(NMSConstants.TOPOLOGY_PROTOCOLS, new JsonArray().add("CDP").add("LLDP"))
                        .put(SCHEDULER_SMS_RECIPIENTS, new JsonArray().add("7990311324")));

        TestAPIUtil.post(SCHEDULER_API_ENDPOINT, scheduler,
                testContext.succeeding(response ->
                        testContext.verify(() ->

                                TestUtil.vertx().setTimer(TimeUnit.SECONDS.toMillis(10), id ->
                                {
                                    TestAPIUtil.assertMultiCreateEntityTestResult(SchedulerConfigStore.getStore(), response.bodyAsJsonObject(), "Scheduler");

                                    ENTITY_IDS.put("topology.ssh.scanner", response.bodyAsJsonObject().getJsonArray(ID).getLong(0));

                                    // object references test case for topology schedulers
                                    TestAPIUtil.delete(OBJECT_API_ENDPOINT + "/" + objects.getLong(0),
                                            testContext.succeeding(httpResponse -> testContext.verify(() ->
                                                    {
                                                        assertEquals(SC_BAD_REQUEST, httpResponse.statusCode());

                                                        var body = httpResponse.bodyAsJsonObject();

                                                        Assertions.assertNotNull(body);

                                                        assertEquals(SC_BAD_REQUEST, body.getInteger(RESPONSE_CODE));

                                                        Assertions.assertNotNull(body.getJsonObject(RESULT));

                                                        body = body.getJsonObject(RESULT);

                                                        Assertions.assertTrue(body.containsKey(APIConstants.Entity.SCHEDULER.getName()));

                                                        Assertions.assertFalse(body.getJsonArray(APIConstants.Entity.SCHEDULER.getName()).isEmpty());

                                                        testContext.completeNow();
                                                    }
                                            )));
                                }))));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(93)
    void testTriggerSSHTopologyScheduler(VertxTestContext testContext) throws Exception
    {
        testContext.awaitCompletion(1, TimeUnit.SECONDS);

        var object = ObjectConfigStore.getStore().getItemByValue(AIOpsObject.OBJECT_IP, "**********");

        Assertions.assertNotNull(object);

        var metric = MetricConfigStore.getStore().getItemsByObject(object.getLong(ID)).stream()
                .filter(item -> item.getString(Metric.METRIC_PLUGIN).equalsIgnoreCase(NMSConstants.MetricPlugin.SNMP_INTERFACE.getName())).map(JsonObject::copy)
                .findFirst().orElse(null);

        Assertions.assertNotNull(metric);

        Assertions.assertTrue(metric.containsKey(Metric.METRIC_CONTEXT));

        var discoveredObjects = metric.getJsonObject(Metric.METRIC_CONTEXT).getJsonArray(NMSConstants.DISCOVERED_OBJECTS);

        var promise = Promise.<JsonArray>promise();

        var probes = new JsonArray();

        if (discoveredObjects == null || discoveredObjects.isEmpty())
        {
            var discoveries = DiscoveryConfigStore.getStore().getItemsByValue(Discovery.DISCOVERY_TARGET, "**********");

            Assertions.assertFalse(discoveries.isEmpty());

            var futures = new ArrayList<Future<Void>>();

            for (var index = 0; index < discoveries.size(); index++)
            {
                var future = Promise.<Void>promise();

                futures.add(future.future());

                Bootstrap.configDBService().getAll(DBConstants.TBL_DISCOVERY_RESULT + discoveries.getJsonObject(index).getLong(ID),
                        result ->
                        {
                            if (result.succeeded() && !result.result().isEmpty())
                            {
                                probes.addAll(result.result());
                            }

                            future.complete();
                        });
            }

            Future.join(futures).onComplete(result ->
            {
                if (!probes.isEmpty())
                {
                    var probe = probes.getJsonObject(0);

                    var objects = probe.getJsonArray(OBJECTS);

                    Assertions.assertNotNull(objects);

                    Assertions.assertFalse(objects.isEmpty());

                    promise.complete(objects);
                }
                else
                {
                    promise.fail(new Exception("failed to find discovery result"));
                }
            });
        }
        else
        {
            promise.complete(discoveredObjects);
        }

        promise.future().onComplete(result ->
        {
            if (result.succeeded())
            {
                var objects = result.result();

                Assertions.assertNotNull(objects);

                Assertions.assertFalse(objects.isEmpty());

                for (var index = 0; index < objects.size(); index++)
                {
                    var discoveredObject = objects.getJsonObject(index);

                    discoveredObject.put(STATUS, STATUS_UP);
                }

                metric.getJsonObject(Metric.METRIC_CONTEXT).put(OBJECTS, objects);

                Bootstrap.configDBService().update(DBConstants.TBL_METRIC,
                        new JsonObject().put(DBConstants.FIELD_NAME, ID).put(VALUE, metric.getLong(ID)),
                        metric,
                        DEFAULT_USER, SYSTEM_REMOTE_ADDRESS,
                        response ->
                        {
                            if (response.failed())
                            {
                                testContext.failNow(response.cause());
                            }
                            else
                            {
                                MetricConfigStore.getStore().updateItem(metric.getLong(ID)).onComplete(asyncResult ->
                                {
                                    if (asyncResult.failed())
                                    {
                                        testContext.failNow(asyncResult.cause());
                                    }
                                    else
                                    {
                                        var item = SchedulerConfigStore.getStore().getItem(ENTITY_IDS.get("topology.ssh.scanner"));

                                        Assertions.assertNotNull(item);

                                        if (item.containsKey(SCHEDULER_CONTEXT))
                                        {
                                            item.mergeIn(item.getJsonObject(SCHEDULER_CONTEXT));

                                            item.remove(SCHEDULER_CONTEXT);
                                        }

                                        item.getJsonArray(TopologyPlugin.TOPOLOGY_PLUGIN_ENTRY_POINTS).forEach(id -> ObjectStatusCacheStore.getStore().updateItem(CommonUtil.getLong(id), STATUS_UP, DateTimeUtil.currentSeconds()));

                                        assertTestResult(testContext);

                                        Bootstrap.vertx().eventBus().send(UI_ACTION_SCHEDULER_RUN, item.put(USER_NAME, DEFAULT_USER).put(SESSION_ID, TestUtil.getSessionId()));
                                    }
                                });
                            }
                        });
            }
            else
            {
                testContext.failNow(result.cause());
            }
        });
    }

    /*-------------------------------------------------------------------------- SNMP Topology Plugin Test ----------------------------------------------------------------------*/


    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(95)
    void testCustomSNMPCDPTopologyPlugin(VertxTestContext testContext, TestInfo testInfo)
    {
        var context = TestConstants.prepareParams("testCreateCustomSNMPCDPTopologyPlugin").put(TopologyPlugin.TOPOLOGY_PLUGIN_NAME, "SNMP CDP-" + System.currentTimeMillis());

        prepareTestContext(context);

        targets = ObjectConfigStore.getStore().flatItems(AIOpsObject.OBJECT_IP, "************", ID);

        Assertions.assertNotNull(targets);

        Assertions.assertFalse(targets.isEmpty());

        context.put(TopologyPlugin.TOPOLOGY_PLUGIN_ENTITIES, targets);

        context.put(TopologyPlugin.TOPOLOGY_PLUGIN_ENTITY_TYPE, APIConstants.Entity.OBJECT.getName());

        context.put(ID, targets.getLong(0));

        context.put(USER_NAME, DEFAULT_USER);

        context.put(SESSION_ID, TestUtil.getSessionId());

        assertTopologyPluginTestResult(testContext, STATUS_SUCCEED, InfoMessageConstants.TOPOLOGY_PLUGIN_TEST_SUCCEEDED, ErrorCodes.ERROR_CODE_SUCCESS, context, STATUS_UP, testInfo.getTestMethod().get().getName());
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(96)
    void testCustomSNMPLLDPTopologyPlugin(VertxTestContext testContext, TestInfo testInfo)
    {
        var context = TestConstants.prepareParams("testCreateCustomSNMPLLDPTopologyPlugin").put(TopologyPlugin.TOPOLOGY_PLUGIN_NAME, "SNMP LLDP-" + System.currentTimeMillis());

        prepareTestContext(context);

        targets = ObjectConfigStore.getStore().flatItems(AIOpsObject.OBJECT_IP, "**********", ID);

        Assertions.assertNotNull(targets);

        Assertions.assertFalse(targets.isEmpty());

        context.put(TopologyPlugin.TOPOLOGY_PLUGIN_ENTITIES, targets);

        context.put(TopologyPlugin.TOPOLOGY_PLUGIN_ENTITY_TYPE, APIConstants.Entity.OBJECT.getName());

        context.put(ID, targets.getLong(0));

        context.put(USER_NAME, DEFAULT_USER);

        context.put(SESSION_ID, TestUtil.getSessionId());

        assertTopologyPluginTestResult(testContext, STATUS_SUCCEED, InfoMessageConstants.TOPOLOGY_PLUGIN_TEST_SUCCEEDED, ErrorCodes.ERROR_CODE_SUCCESS, context, STATUS_UP, testInfo.getTestMethod().get().getName());
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(97)
    void testCustomSNMPISISTopologyPlugin(VertxTestContext testContext, TestInfo testInfo)
    {
        var context = TestConstants.prepareParams("testCreateCustomSNMPISISTopologyPlugin").put(TopologyPlugin.TOPOLOGY_PLUGIN_NAME, "SNMP ISIS-" + System.currentTimeMillis());

        prepareTestContext(context);

        targets = ObjectConfigStore.getStore().flatItems(AIOpsObject.OBJECT_IP, "***********", ID);

        Assertions.assertNotNull(targets);

        Assertions.assertFalse(targets.isEmpty());

        context.put(TopologyPlugin.TOPOLOGY_PLUGIN_ENTITIES, targets);

        context.put(TopologyPlugin.TOPOLOGY_PLUGIN_ENTITY_TYPE, APIConstants.Entity.OBJECT.getName());

        context.put(ID, targets.getLong(0));

        context.put(USER_NAME, DEFAULT_USER);

        context.put(SESSION_ID, TestUtil.getSessionId());

        assertTopologyPluginTestResult(testContext, STATUS_SUCCEED, InfoMessageConstants.TOPOLOGY_PLUGIN_TEST_SUCCEEDED, ErrorCodes.ERROR_CODE_SUCCESS, context, STATUS_UP, testInfo.getTestMethod().get().getName());
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(98)
    void testCustomSNMPBGPTopologyPlugin(VertxTestContext testContext, TestInfo testInfo)
    {
        var context = TestConstants.prepareParams("testCreateCustomSNMPBGPTopologyPlugin").put(TopologyPlugin.TOPOLOGY_PLUGIN_NAME, "SNMP BGP-" + System.currentTimeMillis());

        prepareTestContext(context);

        targets = ObjectConfigStore.getStore().flatItems(AIOpsObject.OBJECT_IP, "***********", ID);

        Assertions.assertNotNull(targets);

        Assertions.assertFalse(targets.isEmpty());

        context.put(TopologyPlugin.TOPOLOGY_PLUGIN_ENTITIES, targets);

        context.put(TopologyPlugin.TOPOLOGY_PLUGIN_ENTITY_TYPE, APIConstants.Entity.OBJECT.getName());

        context.put(ID, targets.getLong(0));

        context.put(USER_NAME, DEFAULT_USER);

        context.put(SESSION_ID, TestUtil.getSessionId());

        assertTopologyPluginTestResult(testContext, STATUS_SUCCEED, InfoMessageConstants.TOPOLOGY_PLUGIN_TEST_SUCCEEDED, ErrorCodes.ERROR_CODE_SUCCESS, context, STATUS_UP, testInfo.getTestMethod().get().getName());
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(99)
    void testCustomSNMPOSPFTopologyPlugin(VertxTestContext testContext, TestInfo testInfo)
    {
        var context = TestConstants.prepareParams("testCreateCustomSNMPOSPFTopologyPlugin").put(TopologyPlugin.TOPOLOGY_PLUGIN_NAME, "SNMP OSPF-" + System.currentTimeMillis());

        prepareTestContext(context);

        targets = ObjectConfigStore.getStore().flatItems(AIOpsObject.OBJECT_IP, "***********", ID);

        Assertions.assertNotNull(targets);

        Assertions.assertFalse(targets.isEmpty());

        context.put(TopologyPlugin.TOPOLOGY_PLUGIN_ENTITIES, targets);

        context.put(TopologyPlugin.TOPOLOGY_PLUGIN_ENTITY_TYPE, APIConstants.Entity.OBJECT.getName());

        context.put(ID, targets.getLong(0));

        context.put(USER_NAME, DEFAULT_USER);

        context.put(SESSION_ID, TestUtil.getSessionId());

        assertTopologyPluginTestResult(testContext, STATUS_SUCCEED, InfoMessageConstants.TOPOLOGY_PLUGIN_TEST_SUCCEEDED, ErrorCodes.ERROR_CODE_SUCCESS, context, STATUS_UP, testInfo.getTestMethod().get().getName());
    }


    /*-------------------------------------------------------------------------- Custom Topology Plugin Test ----------------------------------------------------------------------*/


    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(101)
    void testCustomSSHBGPTopologyPlugin(VertxTestContext testContext, TestInfo testInfo) throws Exception
    {
        testContext.awaitCompletion(10, TimeUnit.SECONDS);

        var context = TestConstants.prepareParams("testCreateCustomSSHBGPTopologyPlugin").put(TopologyPlugin.TOPOLOGY_PLUGIN_NAME, "Test SSH BGP-" + System.currentTimeMillis());

        prepareTestContext(context);

        targets = ObjectConfigStore.getStore().flatItems(AIOpsObject.OBJECT_IP, "***********", ID);

        Assertions.assertNotNull(targets);

        Assertions.assertFalse(targets.isEmpty());

        context.put(TopologyPlugin.TOPOLOGY_PLUGIN_ENTITIES, targets);

        context.put(TopologyPlugin.TOPOLOGY_PLUGIN_ENTITY_TYPE, APIConstants.Entity.OBJECT.getName());

        context.put(ID, targets.getLong(0));

        context.put(USER_NAME, DEFAULT_USER);

        context.put(SESSION_ID, TestUtil.getSessionId());

        var credential = TestConstants.prepareParams("testCreateCustomSSHBGPTopologyPluginCredentialProfile").put(CredentialProfile.CREDENTIAL_PROFILE_NAME, "Custom SSH BGP Topology Plugin-" + System.currentTimeMillis());

        TestAPIUtil.createCredentialProfile(credential, testContext).onComplete(result ->
        {
            if (result.succeeded())
            {
                var credentialId = result.result();

                if (credentialId != null && credentialId != 0)
                {
                    var topologyContext = context.getJsonObject(TopologyPlugin.TOPOLOGY_PLUGIN_CONTEXT);

                    topologyContext.put(TopologyPlugin.TOPOLOGY_PLUGIN_CREDENTIAL_PROFILE, credentialId);

                    topologyContext.put(PORT, 22);

                    context.put(TopologyPlugin.TOPOLOGY_PLUGIN_CONTEXT, topologyContext);

                    assertTopologyPluginTestResult(testContext, STATUS_SUCCEED, InfoMessageConstants.TOPOLOGY_PLUGIN_TEST_SUCCEEDED, ErrorCodes.ERROR_CODE_SUCCESS, context, STATUS_UP, testInfo.getTestMethod().get().getName());
                }
            }
        });
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(102)
    void testCustomSSHOSPFTopologyPlugin(VertxTestContext testContext, TestInfo testInfo) throws Exception
    {
        testContext.awaitCompletion(1, TimeUnit.SECONDS);

        var context = TestConstants.prepareParams("testCreateCustomSSHOSPFTopologyPlugin").put(TopologyPlugin.TOPOLOGY_PLUGIN_NAME, "Test SSH OSPF-" + System.currentTimeMillis());

        prepareTestContext(context);

        targets = ObjectConfigStore.getStore().flatItems(AIOpsObject.OBJECT_IP, "***********", ID);

        Assertions.assertNotNull(targets);

        Assertions.assertFalse(targets.isEmpty());

        context.put(TopologyPlugin.TOPOLOGY_PLUGIN_ENTITIES, targets);

        context.put(TopologyPlugin.TOPOLOGY_PLUGIN_ENTITY_TYPE, APIConstants.Entity.OBJECT.getName());

        context.put(ID, targets.getLong(0));

        context.put(USER_NAME, DEFAULT_USER);

        context.put(SESSION_ID, TestUtil.getSessionId());

        var credential = TestConstants.prepareParams("testCreateCustomSSHOSPFTopologyPluginCredentialProfile").put(CredentialProfile.CREDENTIAL_PROFILE_NAME, "Custom SSH OSPF Topology Plugin-" + System.currentTimeMillis());

        TestAPIUtil.createCredentialProfile(credential, testContext).onComplete(result ->
        {
            if (result.succeeded())
            {
                var credentialId = result.result();

                if (credentialId != null && credentialId != 0)
                {
                    var topologyContext = context.getJsonObject(TopologyPlugin.TOPOLOGY_PLUGIN_CONTEXT);

                    topologyContext.put(TopologyPlugin.TOPOLOGY_PLUGIN_CREDENTIAL_PROFILE, credentialId);

                    topologyContext.put(PORT, 22);

                    context.put(TopologyPlugin.TOPOLOGY_PLUGIN_CONTEXT, topologyContext);

                    assertTopologyPluginTestResult(testContext, STATUS_SUCCEED, InfoMessageConstants.TOPOLOGY_PLUGIN_TEST_SUCCEEDED, ErrorCodes.ERROR_CODE_SUCCESS, context, STATUS_UP, testInfo.getTestMethod().get().getName());
                }
            }
        });
    }


    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(104)
    void testCustomSSHISISTopologyPlugin(VertxTestContext testContext, TestInfo testInfo) throws Exception
    {
        testContext.awaitCompletion(1, TimeUnit.SECONDS);

        var context = TestConstants.prepareParams("testCreateCustomSSHISISTopologyPlugin").put(TopologyPlugin.TOPOLOGY_PLUGIN_NAME, "Test SSH ISIS-" + System.currentTimeMillis());

        prepareTestContext(context);

        targets = ObjectConfigStore.getStore().flatItems(AIOpsObject.OBJECT_IP, "***********", ID);

        Assertions.assertNotNull(targets);

        Assertions.assertFalse(targets.isEmpty());

        context.put(TopologyPlugin.TOPOLOGY_PLUGIN_ENTITIES, targets);

        context.put(TopologyPlugin.TOPOLOGY_PLUGIN_ENTITY_TYPE, APIConstants.Entity.OBJECT.getName());

        context.put(ID, targets.getLong(0));

        context.put(USER_NAME, DEFAULT_USER);

        context.put(SESSION_ID, TestUtil.getSessionId());

        var credential = TestConstants.prepareParams("testCreateCustomSSHISISTopologyPluginCredentialProfile").put(CredentialProfile.CREDENTIAL_PROFILE_NAME, "Custom SSH ISIS Topology Plugin-" + System.currentTimeMillis());

        TestAPIUtil.createCredentialProfile(credential, testContext).onComplete(result ->
        {
            if (result.succeeded())
            {
                var credentialId = result.result();

                if (credentialId != null && credentialId != 0)
                {
                    var topologyContext = context.getJsonObject(TopologyPlugin.TOPOLOGY_PLUGIN_CONTEXT);

                    topologyContext.put(TopologyPlugin.TOPOLOGY_PLUGIN_CREDENTIAL_PROFILE, credentialId);

                    topologyContext.put(PORT, 22);

                    context.put(TopologyPlugin.TOPOLOGY_PLUGIN_CONTEXT, topologyContext);

                    assertTopologyPluginTestResult(testContext, STATUS_SUCCEED, InfoMessageConstants.TOPOLOGY_PLUGIN_TEST_SUCCEEDED, ErrorCodes.ERROR_CODE_SUCCESS, context, STATUS_UP, testInfo.getTestMethod().get().getName());
                }
            }
        });
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(105)
    void testCustomSSHCDPTopologyPlugin(VertxTestContext testContext, TestInfo testInfo) throws Exception
    {
        testContext.awaitCompletion(10, TimeUnit.SECONDS);

        var context = TestConstants.prepareParams("testCreateCustomSSHCDPTopologyPlugin").put(TopologyPlugin.TOPOLOGY_PLUGIN_NAME, "Test SSH CDP-" + System.currentTimeMillis());

        prepareTestContext(context);

        targets = ObjectConfigStore.getStore().flatItems(AIOpsObject.OBJECT_IP, "**********", ID);

        Assertions.assertNotNull(targets);

        Assertions.assertFalse(targets.isEmpty());

        context.put(TopologyPlugin.TOPOLOGY_PLUGIN_ENTITIES, targets);

        context.put(TopologyPlugin.TOPOLOGY_PLUGIN_ENTITY_TYPE, APIConstants.Entity.OBJECT.getName());

        context.put(ID, targets.getLong(0));

        context.put(USER_NAME, DEFAULT_USER);

        context.put(SESSION_ID, TestUtil.getSessionId());

        var credential = TestConstants.prepareParams("testCreateCustomSSHCDPTopologyPluginCredentialProfile").put(CredentialProfile.CREDENTIAL_PROFILE_NAME, "Custom SSH CDP Topology Plugin-" + System.currentTimeMillis());

        TestAPIUtil.createCredentialProfile(credential, testContext).onComplete(result ->
        {
            if (result.succeeded())
            {
                var credentialId = result.result();

                if (credentialId != null && credentialId != 0)
                {
                    var topologyContext = context.getJsonObject(TopologyPlugin.TOPOLOGY_PLUGIN_CONTEXT);

                    topologyContext.put(TopologyPlugin.TOPOLOGY_PLUGIN_CREDENTIAL_PROFILE, credentialId);

                    topologyContext.put(PORT, 22);

                    context.put(TopologyPlugin.TOPOLOGY_PLUGIN_CONTEXT, topologyContext);

                    assertTopologyPluginTestResult(testContext, STATUS_SUCCEED, InfoMessageConstants.TOPOLOGY_PLUGIN_TEST_SUCCEEDED, ErrorCodes.ERROR_CODE_SUCCESS, context, STATUS_UP, testInfo.getTestMethod().get().getName());
                }
            }
        });
    }

    //Juniper test scripts

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(107)
    void testCustomSSHJuniperBGPTopologyPlugin(VertxTestContext testContext, TestInfo testInfo) throws Exception
    {
        testContext.awaitCompletion(1, TimeUnit.SECONDS);

        var context = TestConstants.prepareParams("testCreateCustomSSHJuniperBGPTopologyPlugin").put(TopologyPlugin.TOPOLOGY_PLUGIN_NAME, "Test SSH Juniper BGP-" + System.currentTimeMillis());

        prepareTestContext(context);

        targets = ObjectConfigStore.getStore().flatItems(AIOpsObject.OBJECT_IP, "***********", ID);

        Assertions.assertNotNull(targets);

        Assertions.assertFalse(targets.isEmpty());

        context.put(TopologyPlugin.TOPOLOGY_PLUGIN_ENTITIES, targets);

        context.put(TopologyPlugin.TOPOLOGY_PLUGIN_ENTITY_TYPE, APIConstants.Entity.OBJECT.getName());

        context.put(ID, targets.getLong(0));

        context.put(USER_NAME, DEFAULT_USER);

        context.put(SESSION_ID, TestUtil.getSessionId());

        var credential = TestConstants.prepareParams("testCreateCustomSSHJuniperBGPTopologyPluginCredentialProfile").put(CredentialProfile.CREDENTIAL_PROFILE_NAME, "Custom SSH Juniper BGP Topology Plugin-" + System.currentTimeMillis());

        TestAPIUtil.createCredentialProfile(credential, testContext).onComplete(result ->
        {
            if (result.succeeded())
            {
                var credentialId = result.result();

                if (credentialId != null && credentialId != 0)
                {
                    var topologyContext = context.getJsonObject(TopologyPlugin.TOPOLOGY_PLUGIN_CONTEXT);

                    topologyContext.put(TopologyPlugin.TOPOLOGY_PLUGIN_CREDENTIAL_PROFILE, credentialId);

                    topologyContext.put(PORT, 22);

                    context.put(TopologyPlugin.TOPOLOGY_PLUGIN_CONTEXT, topologyContext);

                    assertTopologyPluginTestResult(testContext, STATUS_SUCCEED, InfoMessageConstants.TOPOLOGY_PLUGIN_TEST_SUCCEEDED, ErrorCodes.ERROR_CODE_SUCCESS, context, STATUS_UP, testInfo.getTestMethod().get().getName());
                }
            }
        });
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(108)
    void testCustomSSHJuniperOSPFTopologyPlugin(VertxTestContext testContext, TestInfo testInfo) throws Exception
    {
        testContext.awaitCompletion(1, TimeUnit.SECONDS);

        var context = TestConstants.prepareParams("testCreateCustomSSHJuniperOSPFTopologyPlugin").put(TopologyPlugin.TOPOLOGY_PLUGIN_NAME, "Test SSH Juniper OSPF-" + System.currentTimeMillis());

        prepareTestContext(context);

        targets = ObjectConfigStore.getStore().flatItems(AIOpsObject.OBJECT_IP, "***********", ID);

        Assertions.assertNotNull(targets);

        Assertions.assertFalse(targets.isEmpty());

        context.put(TopologyPlugin.TOPOLOGY_PLUGIN_ENTITIES, targets);

        context.put(TopologyPlugin.TOPOLOGY_PLUGIN_ENTITY_TYPE, APIConstants.Entity.OBJECT.getName());

        context.put(ID, targets.getLong(0));

        context.put(USER_NAME, DEFAULT_USER);

        context.put(SESSION_ID, TestUtil.getSessionId());

        var credential = TestConstants.prepareParams("testCreateCustomSSHJuniperOSPFTopologyPluginCredentialProfile").put(CredentialProfile.CREDENTIAL_PROFILE_NAME, "Custom SSH Juniper OSPF Topology Plugin-" + System.currentTimeMillis());

        TestAPIUtil.createCredentialProfile(credential, testContext).onComplete(result ->
        {
            if (result.succeeded())
            {
                var credentialId = result.result();

                if (credentialId != null && credentialId != 0)
                {
                    var topologyContext = context.getJsonObject(TopologyPlugin.TOPOLOGY_PLUGIN_CONTEXT);

                    topologyContext.put(TopologyPlugin.TOPOLOGY_PLUGIN_CREDENTIAL_PROFILE, credentialId);

                    topologyContext.put(PORT, 22);

                    context.put(TopologyPlugin.TOPOLOGY_PLUGIN_CONTEXT, topologyContext);

                    assertTopologyPluginTestResult(testContext, STATUS_SUCCEED, InfoMessageConstants.TOPOLOGY_PLUGIN_TEST_SUCCEEDED, ErrorCodes.ERROR_CODE_SUCCESS, context, STATUS_UP, testInfo.getTestMethod().get().getName());
                }
            }
        });
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(109)
    void testCustomSSHJuniperISISTopologyPlugin(VertxTestContext testContext, TestInfo testInfo) throws Exception
    {
        testContext.awaitCompletion(1, TimeUnit.SECONDS);

        var context = TestConstants.prepareParams("testCreateCustomSSHJuniperISISTopologyPlugin").put(TopologyPlugin.TOPOLOGY_PLUGIN_NAME, "Test SSH Juniper ISIS-" + System.currentTimeMillis());

        prepareTestContext(context);

        targets = ObjectConfigStore.getStore().flatItems(AIOpsObject.OBJECT_IP, "***********", ID);

        Assertions.assertNotNull(targets);

        Assertions.assertFalse(targets.isEmpty());

        context.put(TopologyPlugin.TOPOLOGY_PLUGIN_ENTITIES, targets);

        context.put(TopologyPlugin.TOPOLOGY_PLUGIN_ENTITY_TYPE, APIConstants.Entity.OBJECT.getName());

        context.put(ID, targets.getLong(0));

        context.put(USER_NAME, DEFAULT_USER);

        context.put(SESSION_ID, TestUtil.getSessionId());

        var credential = TestConstants.prepareParams("testCreateCustomSSHJuniperISISTopologyPluginCredentialProfile").put(CredentialProfile.CREDENTIAL_PROFILE_NAME, "Custom SSH Juniper ISIS Topology Plugin-" + System.currentTimeMillis());

        TestAPIUtil.createCredentialProfile(credential, testContext).onComplete(result ->
        {
            if (result.succeeded())
            {
                var credentialId = result.result();

                if (credentialId != null && credentialId != 0)
                {
                    var topologyContext = context.getJsonObject(TopologyPlugin.TOPOLOGY_PLUGIN_CONTEXT);

                    topologyContext.put(TopologyPlugin.TOPOLOGY_PLUGIN_CREDENTIAL_PROFILE, credentialId);

                    topologyContext.put(PORT, 22);

                    context.put(TopologyPlugin.TOPOLOGY_PLUGIN_CONTEXT, topologyContext);

                    assertTopologyPluginTestResult(testContext, STATUS_SUCCEED, InfoMessageConstants.TOPOLOGY_PLUGIN_TEST_SUCCEEDED, ErrorCodes.ERROR_CODE_SUCCESS, context, STATUS_UP, testInfo.getTestMethod().get().getName());
                }
            }
        });
    }


    // topology scanner

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(110)
    void testCreateTopologyScanner(VertxTestContext testContext)
    {
        var objects = ObjectConfigStore.getStore().flatItemsByValues(AIOpsObject.OBJECT_TYPE, new JsonArray(new ArrayList<>(NMSConstants.NETWORK_DEVICES)), ID);

        Assertions.assertFalse(objects.isEmpty());

        var scheduler = new JsonObject().put(Scheduler.SCHEDULER_START_DATE, new SimpleDateFormat(PATTERN).format(new Date()))
                .put(Scheduler.SCHEDULER_TIMELINE, CRON_ONCE)
                .put(Scheduler.SCHEDULER_TIMES, new JsonArray().add("00:00"))
                .put(Scheduler.SCHEDULER_JOB_TYPE, JobScheduler.JobType.TOPOLOGY.getName())
                .put(SCHEDULER_CONTEXT, new JsonObject().put(SCHEDULER_EMAIL_RECIPIENTS, new JsonArray().add("<EMAIL>"))
                        .put(TopologyPlugin.TOPOLOGY_PLUGIN_ENTRY_POINTS, objects).put(OBJECTS, objects)
                        .put(NMSConstants.TOPOLOGY_LINK_LAYER, new JsonArray().add(NMSConstants.TopologyLinkLayer.L3.getName()).add(NMSConstants.TopologyLinkLayer.L2.getName()))
                        .put(NMSConstants.TOPOLOGY_PROTOCOLS, new JsonArray().add("CDP").add("LLDP").add("OSPF").add("IS-IS"))
                        .put(SCHEDULER_SMS_RECIPIENTS, new JsonArray().add("7990311324")));

        TestAPIUtil.post(SCHEDULER_API_ENDPOINT, scheduler,
                testContext.succeeding(response ->
                        testContext.verify(() ->
                                TestUtil.vertx().setTimer(TimeUnit.SECONDS.toMillis(3), id ->
                                {
                                    TestAPIUtil.assertMultiCreateEntityTestResult(SchedulerConfigStore.getStore(), response.bodyAsJsonObject(), "Scheduler");

                                    ENTITY_IDS.put("topology.scanner", response.bodyAsJsonObject().getJsonArray(ID).getLong(0));

                                    testContext.completeNow();
                                }))));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(111)
    void testGetTopologyScanner(VertxTestContext testContext, TestInfo testInfo)
    {
        TestAPIUtil.get(SCHEDULER_API_ENDPOINT + "?filter=" + new JsonObject()
                        .put(Scheduler.SCHEDULER_JOB_TYPE, JobScheduler.JobType.TOPOLOGY.getName())
                , testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertValidResponseTestResult(response, LOGGER, testInfo.getTestMethod().get().getName());

                            var items = response.bodyAsJsonObject().getJsonArray(GlobalConstants.RESULT);

                            Assertions.assertNotNull(items);

                            Assertions.assertFalse(items.isEmpty());

                            testContext.completeNow();
                        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(112)
    void testUpdateTopologyScanner(VertxTestContext testContext, TestInfo testInfo)
    {
        var objects = ObjectConfigStore.getStore().flatItemsByValues(AIOpsObject.OBJECT_TYPE, new JsonArray(new ArrayList<>(NMSConstants.NETWORK_DEVICES)), ID);

        Assertions.assertFalse(objects.isEmpty());

        var scheduler = new JsonObject().put(Scheduler.SCHEDULER_START_DATE, new SimpleDateFormat(PATTERN).format(new Date()))
                .put(Scheduler.SCHEDULER_TIMELINE, CRON_ONCE)
                .put(Scheduler.SCHEDULER_TIMES, new JsonArray().add("10:00"))
                .put(Scheduler.SCHEDULER_JOB_TYPE, JobScheduler.JobType.TOPOLOGY.getName())
                .put(SCHEDULER_CONTEXT, new JsonObject().put(SCHEDULER_EMAIL_RECIPIENTS, new JsonArray().add("<EMAIL>"))
                        .put(TopologyPlugin.TOPOLOGY_PLUGIN_ENTRY_POINTS, objects).put(OBJECTS, objects)
                        .put(NMSConstants.TOPOLOGY_LINK_LAYER, new JsonArray().add(NMSConstants.TopologyLinkLayer.L3.getName()).add(NMSConstants.TopologyLinkLayer.L2.getName()))
                        .put(NMSConstants.TOPOLOGY_PROTOCOLS, new JsonArray().add("CDP").add("LLDP").add("BGP").add("OSPF"))
                        .put(SCHEDULER_SMS_RECIPIENTS, new JsonArray().add("7990311324")));

        TestAPIUtil.put(SCHEDULER_API_ENDPOINT + "/" + ENTITY_IDS.get("topology.scanner"), scheduler,
                testContext.succeeding(response ->
                        testContext.verify(() ->
                                TestUtil.vertx().setTimer(TimeUnit.SECONDS.toMillis(5), id ->
                                {
                                    TestAPIUtil.assertUpdateEntityTestResult(SchedulerConfigStore.getStore(), scheduler, response.bodyAsJsonObject(),
                                            String.format(InfoMessageConstants.ENTITY_UPDATED, "Scheduler"), LOGGER, testInfo.getTestMethod().get().getName());

                                    testContext.completeNow();
                                }))));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(113)
    void testTriggerTopologyScheduler(VertxTestContext testContext)
    {
        var item = SchedulerConfigStore.getStore().getItem(ENTITY_IDS.get("topology.scanner"));

        Assertions.assertNotNull(item);

        if (item.containsKey(SCHEDULER_CONTEXT))
        {
            item.mergeIn(item.getJsonObject(SCHEDULER_CONTEXT));

            item.remove(SCHEDULER_CONTEXT);
        }

        item.getJsonArray(TopologyPlugin.TOPOLOGY_PLUGIN_ENTRY_POINTS).forEach(id -> ObjectStatusCacheStore.getStore().updateItem(CommonUtil.getLong(id), STATUS_UP, DateTimeUtil.currentSeconds()));

        Bootstrap.vertx().eventBus().send(UI_ACTION_SCHEDULER_RUN, item.put(USER_NAME, DEFAULT_USER).put(SESSION_ID, TestUtil.getSessionId()));

        TestUtil.vertx().setTimer(2000, result -> testContext.completeNow());
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(114)
    void testCreateTopologyScannerDisabledObject(VertxTestContext testContext)
    {
        var object = ObjectConfigStore.getStore().getItemByValue(AIOpsObject.OBJECT_TYPE, NMSConstants.Type.PING.getName());

        Assertions.assertNotNull(object);

        TestUtil.vertx().eventBus().<JsonObject>request(EVENT_OBJECT_DISABLE, new JsonObject().put(ID, object.getLong(ID)), reply ->
        {
            var scheduler = new JsonObject().put(Scheduler.SCHEDULER_START_DATE, new SimpleDateFormat(PATTERN).format(new Date()))
                    .put(Scheduler.SCHEDULER_TIMELINE, CRON_ONCE)
                    .put(Scheduler.SCHEDULER_TIMES, new JsonArray().add("00:00"))
                    .put(Scheduler.SCHEDULER_JOB_TYPE, JobScheduler.JobType.TOPOLOGY.getName())
                    .put(SCHEDULER_CONTEXT, new JsonObject().put(SCHEDULER_EMAIL_RECIPIENTS, new JsonArray().add("<EMAIL>"))
                            .put(TopologyPlugin.TOPOLOGY_PLUGIN_ENTRY_POINTS, new JsonArray().add(object.getLong(ID))).put(OBJECTS, new JsonArray().add(object.getLong(ID)))
                            .put(NMSConstants.TOPOLOGY_LINK_LAYER, new JsonArray().add(NMSConstants.TopologyLinkLayer.L3.getName()).add(NMSConstants.TopologyLinkLayer.L2.getName()))
                            .put(NMSConstants.TOPOLOGY_PROTOCOLS, new JsonArray().add("CDP").add("LLDP").add("SPM").add("OSPF"))
                            .put(SCHEDULER_SMS_RECIPIENTS, new JsonArray().add("7990311324")));

            TestAPIUtil.post(SCHEDULER_API_ENDPOINT, scheduler,
                    testContext.succeeding(response ->
                            testContext.verify(() ->
                                    TestUtil.vertx().setTimer(TimeUnit.SECONDS.toMillis(3), id ->
                                    {
                                        TestAPIUtil.assertMultiCreateEntityTestResult(SchedulerConfigStore.getStore(), response.bodyAsJsonObject(), "Scheduler");

                                        ENTITY_IDS.put("topology.disabled.scanner", response.bodyAsJsonObject().getJsonArray(ID).getLong(0));

                                        testContext.completeNow();
                                    }))));
        });
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(115)
    void testCreateTopologyScannerNoSeedIP(VertxTestContext testContext)
    {
        var scheduler = new JsonObject().put(Scheduler.SCHEDULER_START_DATE, new SimpleDateFormat(PATTERN).format(new Date()))
                .put(Scheduler.SCHEDULER_TIMELINE, CRON_ONCE)
                .put(Scheduler.SCHEDULER_TIMES, new JsonArray().add("00:00"))
                .put(Scheduler.SCHEDULER_JOB_TYPE, JobScheduler.JobType.TOPOLOGY.getName())
                .put(SCHEDULER_CONTEXT, new JsonObject().put(SCHEDULER_EMAIL_RECIPIENTS, new JsonArray().add("<EMAIL>"))
                        .put(NMSConstants.TOPOLOGY_LINK_LAYER, new JsonArray().add(NMSConstants.TopologyLinkLayer.L2.getName()))
                        .put(NMSConstants.TOPOLOGY_PROTOCOLS, new JsonArray().add("CDP").add("LLDP"))
                        .put(SCHEDULER_SMS_RECIPIENTS, new JsonArray().add("7990311324")));

        TestAPIUtil.post(SCHEDULER_API_ENDPOINT, scheduler,
                testContext.succeeding(response ->
                        testContext.verify(() ->
                                TestUtil.vertx().setTimer(TimeUnit.SECONDS.toMillis(3), id ->
                                {
                                    TestAPIUtil.assertMultiCreateEntityTestResult(SchedulerConfigStore.getStore(), response.bodyAsJsonObject(), "Scheduler");

                                    ENTITY_IDS.put("topology.scanner.entrypoint.missing", response.bodyAsJsonObject().getJsonArray(ID).getLong(0));

                                    testContext.completeNow();
                                }))));
    }


    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(117)
    void testRunTopologyAfterDiscovery(VertxTestContext testContext) throws Exception
    {
        testContext.awaitCompletion(1, TimeUnit.SECONDS);

        var object = ObjectConfigStore.getStore().getItemByIP("************");

        Assertions.assertNotNull(object);

        var context = DiscoveryConfigStore.getStore().getItemByValue(Discovery.DISCOVERY_TARGET, "**********");

        Assertions.assertNotNull(context);

        var dateTime = DateTimeUtil.getScheduledTimestamp(System.currentTimeMillis() + (2 * 1000L));

        var event = new JsonObject().put(Scheduler.SCHEDULER_TIMELINE, CRON_ONCE)
                .put(Scheduler.SCHEDULER_TIMES, new JsonArray().add(dateTime.split(" ")[1]))
                .put(Scheduler.SCHEDULER_START_DATE, dateTime.split(" ")[0])
                .put(Scheduler.SCHEDULER_JOB_TYPE, JobScheduler.JobType.TOPOLOGY.getName())
                .put(SCHEDULER_STATE, YES)
                .put(SCHEDULER_CONTEXT, new JsonObject().put(TopologyPlugin.TOPOLOGY_PLUGIN_ENTRY_POINTS, new JsonArray().add(object))
                        .put(NMSConstants.TOPOLOGY_PROTOCOLS, new JsonArray().add("LLDP").add("CDP").add("SPM"))
                        .put(NMSConstants.TOPOLOGY_LINK_LAYER, new JsonArray().add("L2"))
                        .put(TopologyPlugin.TOPOLOGY_PLUGIN_DISCOVERY, YES));

        Bootstrap.configDBService().save(DBConstants.TBL_SCHEDULER, event, DEFAULT_USER, SYSTEM_REMOTE_ADDRESS, result ->
        {
            if (result.succeeded())
            {
                SchedulerConfigStore.getStore().addItem(result.result()).onComplete(asyncResult -> JobScheduler.scheduleCustomJob(SchedulerConfigStore.getStore().getItem(result.result())));
            }
        });

        testContext.awaitCompletion(1, TimeUnit.SECONDS);

        testContext.completeNow();
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(118)
    void testAssertExcludeIPAddresses(VertxTestContext testContext) throws Exception
    {
        var method = TopologyEngine.class.getDeclaredMethod("setFilterTargets", JsonObject.class);

        method.setAccessible(true);

        var values = (Set<String>) method.invoke(new TopologyEngine(), new JsonObject().put(TopologyPlugin.TOPOLOGY_PLUGIN_FILTER_TARGETS, new JsonArray().add("***********-24")).put(TOPOLOGY_PLUGIN_FILTER_TARGET_TYPE, "include-ip.address.range"));

        Assertions.assertNotNull(values);

        Assertions.assertFalse(values.isEmpty());

        Assertions.assertEquals(24, values.size());

        values = (Set<String>) method.invoke(new TopologyEngine(), new JsonObject().put(TopologyPlugin.TOPOLOGY_PLUGIN_FILTER_TARGETS, new JsonArray().add("***********").add("***********").add("***********"))
                .put(TOPOLOGY_PLUGIN_FILTER_TARGET_TYPE, "include-ip.address"));

        Assertions.assertNotNull(values);

        Assertions.assertFalse(values.isEmpty());

        Assertions.assertEquals(2, values.size());

        testContext.completeNow();
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(119)
    void testAbortSchedulerEvent(VertxTestContext testContext)
    {
        var scheduler = SchedulerConfigStore.getStore().getItem(ENTITY_IDS.get("topology.scanner"));

        Assertions.assertNotNull(scheduler);

        var retries = new AtomicInteger(0);

        TestUtil.vertx().eventBus().send(UI_ACTION_SCHEDULER_RUN, scheduler.put(USER_NAME, DEFAULT_USER).put(SESSION_ID, TestUtil.getSessionId()));

        TestUtil.vertx().setPeriodic(500, timer ->
        {
            retries.getAndIncrement();

            var status = TopologyCacheStore.getStore().topologyRunning(ENTITY_IDS.get("topology.scanner"));

            if (!status)
            {
                TestUtil.vertx().cancelTimer(timer);

                testContext.completeNow();
            }
            else if (retries.get() >= 100)
            {
                TestUtil.vertx().cancelTimer(timer);

                testContext.failNow(new Exception("failed to abort topology scheduler...."));
            }
            else
            {
                TestUtil.vertx().eventBus().send(EVENT_TOPOLOGY_STOP, new JsonObject().put(EVENT_SCHEDULER, ENTITY_IDS.get("topology.scanner")).put(TopologyPlugin.TOPOLOGY_PLUGIN_TYPE, NMSConstants.TopologyPluginType.SNMP.getName()));

                TestUtil.vertx().eventBus().send(EVENT_TOPOLOGY_STOP, new JsonObject().put(EVENT_SCHEDULER, ENTITY_IDS.get("topology.scanner")).put(TopologyPlugin.TOPOLOGY_PLUGIN_TYPE, NMSConstants.TopologyPluginType.CUSTOM.getName()));
            }
        });
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(120)
    void testDeleteTopologyScanner(VertxTestContext testContext)
    {
        TestAPIUtil.delete(SCHEDULER_API_ENDPOINT + "/" + ENTITY_IDS.get("topology.scanner") + "?" + SCHEDULER_JOB_TYPE + "=" + JobScheduler.JobType.TOPOLOGY.getName(),
                testContext.succeeding(response ->
                        testContext.verify(() ->
                                TestUtil.vertx().setTimer(TimeUnit.SECONDS.toMillis(3), id ->
                                {
                                    TestAPIUtil.assertDeleteEntityTestResult(SchedulerConfigStore.getStore(), response.bodyAsJsonObject(),
                                            String.format(InfoMessageConstants.ENTITY_DELETED, "Scheduler"));

                                    testContext.completeNow();
                                }))));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(121)
    void testCustomSSHJuniperISISTopologyPluginDownObject(VertxTestContext testContext, TestInfo testInfo) throws Exception
    {
        testContext.awaitCompletion(1, TimeUnit.SECONDS);

        var context = TestConstants.prepareParams("testCreateCustomSSHJuniperISISTopologyPlugin").put(TopologyPlugin.TOPOLOGY_PLUGIN_NAME, "Test SSH Juniper ISIS-" + System.currentTimeMillis());

        prepareTestContext(context);

        targets = ObjectConfigStore.getStore().flatItems(AIOpsObject.OBJECT_IP, "***********", ID);

        Assertions.assertNotNull(targets);

        Assertions.assertFalse(targets.isEmpty());

        context.put(TopologyPlugin.TOPOLOGY_PLUGIN_ENTITIES, targets);

        context.put(TopologyPlugin.TOPOLOGY_PLUGIN_ENTITY_TYPE, APIConstants.Entity.OBJECT.getName());

        context.put(ID, targets.getLong(0));

        ENTITY_IDS.put("down.object", targets.getLong(0));

        context.put(USER_NAME, DEFAULT_USER);

        context.put(SESSION_ID, TestUtil.getSessionId());

        context.put(TopologyPlugin.TOPOLOGY_PLUGIN_CONTEXT, context.getJsonObject(TopologyPlugin.TOPOLOGY_PLUGIN_CONTEXT).put(PORT, 22));

        assertTopologyPluginTestResult(testContext, STATUS_FAIL, String.format(ErrorMessageConstants.TOPOLOGY_PLUGIN_TEST_FAILED, ErrorMessageConstants.OBJECT_ERROR), ErrorCodes.ERROR_CODE_NO_ITEM_FOUND, context, STATUS_DOWN, testInfo.getTestMethod().get().getName());
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(122)
    void testGetVendors(VertxTestContext testContext, TestInfo testInfo)
    {
        TestAPIUtil.get(MISC_MAKE_MODEL_API_ENDPOINT + "?filter=" + new JsonObject().put(AIOpsObject.OBJECT_VENDOR, "Cisco Systems")
                , testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertValidResponseTestResult(response, LOGGER, testInfo.getTestMethod().get().getName());

                            var items = response.bodyAsJsonObject().getJsonArray(RESULT);

                            Assertions.assertNotNull(items);

                            Assertions.assertFalse(items.isEmpty());

                            testContext.completeNow();
                        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(123)
    void testGetVendorsInvalidFilter(VertxTestContext testContext)
    {
        TestAPIUtil.get(MISC_MAKE_MODEL_API_ENDPOINT + "?filter=" + new JsonObject()
                , testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            assertEquals(SC_BAD_REQUEST, response.statusCode());

                            var body = response.bodyAsJsonObject();

                            Assertions.assertNotNull(body);

                            assertEquals(SC_BAD_REQUEST, body.getInteger(RESPONSE_CODE));

                            assertEquals(ErrorMessageConstants.API_INVALID_INPUT_PARAMETERS, body.getString(MESSAGE));

                            testContext.completeNow();
                        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(124)
    void testGetTopologyPlugins(VertxTestContext testContext)
    {
        TestAPIUtil.get(TOPOLOGY_PLUGIN_API_ENDPOINT, testContext.succeeding(response -> testContext.verify(() ->
        {
            assertEquals(SC_OK, response.statusCode());

            var body = response.bodyAsJsonObject();

            Assertions.assertNotNull(body);

            assertEquals(SC_OK, body.getInteger(RESPONSE_CODE));

            var items = body.getJsonArray(RESULT);

            Assertions.assertNotNull(items);

            Assertions.assertFalse(items.isEmpty());

            testContext.completeNow();

        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(125)
    void testCreateDependencyTopologyScanner(VertxTestContext testContext)
    {
        var object = ObjectConfigStore.getStore().getItemByValue(AIOpsObject.OBJECT_IP, "**********");

        Assertions.assertNotNull(object);

        Assertions.assertFalse(object.isEmpty());

        var scheduler = new JsonObject().put(Scheduler.SCHEDULER_START_DATE, new SimpleDateFormat(PATTERN).format(new Date()))
                .put(Scheduler.SCHEDULER_TIMELINE, CRON_ONCE)
                .put(Scheduler.SCHEDULER_TIMES, new JsonArray().add("00:00"))
                .put(Scheduler.SCHEDULER_JOB_TYPE, JobScheduler.JobType.TOPOLOGY.getName())
                .put(SCHEDULER_CONTEXT, new JsonObject().put(SCHEDULER_EMAIL_RECIPIENTS, new JsonArray().add("<EMAIL>"))
                        .put(TopologyPlugin.TOPOLOGY_PLUGIN_ENTRY_POINTS, new JsonArray().add(object.getLong(ID))).put(OBJECTS, new JsonArray().add(object.getLong(ID)))
                        .put(NMSConstants.TOPOLOGY_LINK_LAYER, new JsonArray().add(NMSConstants.TopologyLinkLayer.L3.getName()).add(NMSConstants.TopologyLinkLayer.L2.getName()))
                        .put(NMSConstants.TOPOLOGY_PROTOCOLS, new JsonArray().add("CDP").add("LLDP").add("SPM").add("OSPF").add("BGP").add("IS-IS"))
                        .put(SCHEDULER_SMS_RECIPIENTS, new JsonArray().add("9726133493")));

        TestAPIUtil.post(SCHEDULER_API_ENDPOINT, scheduler,
                testContext.succeeding(response ->
                        testContext.verify(() ->
                                TestUtil.vertx().setTimer(TimeUnit.SECONDS.toMillis(3), id ->
                                {
                                    TestAPIUtil.assertMultiCreateEntityTestResult(SchedulerConfigStore.getStore(), response.bodyAsJsonObject(), "Scheduler");

                                    ENTITY_IDS.put("dependency.topology.scanner", response.bodyAsJsonObject().getJsonArray(ID).getLong(0));

                                    testContext.completeNow();
                                }))));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(126)
    void testTriggerDependencyTopologyScheduler(VertxTestContext testContext)
    {
        var item = SchedulerConfigStore.getStore().getItem(ENTITY_IDS.get("dependency.topology.scanner"));

        Assertions.assertNotNull(item);

        if (item.containsKey(SCHEDULER_CONTEXT))
        {
            item.mergeIn(item.getJsonObject(SCHEDULER_CONTEXT));

            item.remove(SCHEDULER_CONTEXT);
        }

        item.getJsonArray(TopologyPlugin.TOPOLOGY_PLUGIN_ENTRY_POINTS).forEach(id -> ObjectStatusCacheStore.getStore().updateItem(CommonUtil.getLong(id), STATUS_UP, DateTimeUtil.currentSeconds()));

        Bootstrap.vertx().eventBus().send(UI_ACTION_SCHEDULER_RUN, item.put(USER_NAME, DEFAULT_USER).put(SESSION_ID, TestUtil.getSessionId()));

        TestUtil.vertx().setTimer(2000, handler -> testContext.completeNow());
    }


    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(127)
    void testCreateCustomSNMPExtremeTopologyPlugin(VertxTestContext testContext, TestInfo testInfo)
    {

        context = TestConstants.prepareParams(testInfo.getTestMethod().get().getName()).put(TopologyPlugin.TOPOLOGY_PLUGIN_NAME, "SNMP Extreme-" + System.currentTimeMillis());

        targets = ObjectConfigStore.getStore().flatItems(AIOpsObject.OBJECT_IP, "***********", ID);

        prepareTestContext(context);

        Assertions.assertNotNull(targets);

        Assertions.assertFalse(targets.isEmpty());

        context.put(TopologyPlugin.TOPOLOGY_PLUGIN_ENTITIES, targets);

        assertCreateTopologyPluginTestResult(testContext, context, targets, testInfo);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(128)
    void testCreateTopologyScanner1(VertxTestContext testContext)
    {
        var objects = ObjectConfigStore.getStore().flatItemsByValues(AIOpsObject.OBJECT_IP, new JsonArray().add("fd00:1:1:1::47").add("***********"), ID);

        Assertions.assertFalse(objects.isEmpty());

        var scheduler = new JsonObject().put(Scheduler.SCHEDULER_START_DATE, new SimpleDateFormat(PATTERN).format(new Date()))
                .put(Scheduler.SCHEDULER_TIMELINE, CRON_ONCE)
                .put(Scheduler.SCHEDULER_TIMES, new JsonArray().add("00:00"))
                .put(Scheduler.SCHEDULER_JOB_TYPE, JobScheduler.JobType.TOPOLOGY.getName())
                .put(SCHEDULER_CONTEXT, new JsonObject().put(SCHEDULER_EMAIL_RECIPIENTS, new JsonArray().add("<EMAIL>"))
                        .put(TopologyPlugin.TOPOLOGY_PLUGIN_ENTRY_POINTS, objects).put(OBJECTS, objects)
                        .put(NMSConstants.TOPOLOGY_LINK_LAYER, new JsonArray().add(NMSConstants.TopologyLinkLayer.L3.getName()).add(NMSConstants.TopologyLinkLayer.L2.getName()))
                        .put(NMSConstants.TOPOLOGY_PROTOCOLS, new JsonArray().add("CDP").add("LLDP").add("SPM").add("OSPF").add("BGP").add("IS-IS"))
                        .put(SCHEDULER_SMS_RECIPIENTS, new JsonArray().add("7990311324")));

        TestAPIUtil.post(SCHEDULER_API_ENDPOINT, scheduler,
                testContext.succeeding(response ->
                        testContext.verify(() ->
                                TestUtil.vertx().setTimer(TimeUnit.SECONDS.toMillis(3), id ->
                                {
                                    TestAPIUtil.assertMultiCreateEntityTestResult(SchedulerConfigStore.getStore(), response.bodyAsJsonObject(), "Scheduler");

                                    ENTITY_IDS.put("topology.scanner", response.bodyAsJsonObject().getJsonArray(ID).getLong(0));

                                    testContext.completeNow();
                                }))));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(129)
    void testTriggerTopologyScheduler1(VertxTestContext testContext) throws InterruptedException
    {
        var item = SchedulerConfigStore.getStore().getItem(ENTITY_IDS.get("topology.scanner"));

        Assertions.assertNotNull(item);

        if (item.containsKey(SCHEDULER_CONTEXT))
        {
            item.mergeIn(item.getJsonObject(SCHEDULER_CONTEXT));

            item.remove(SCHEDULER_CONTEXT);
        }

        item.getJsonArray(TopologyPlugin.TOPOLOGY_PLUGIN_ENTRY_POINTS).forEach(id -> ObjectStatusCacheStore.getStore().updateItem(CommonUtil.getLong(id), STATUS_UP, DateTimeUtil.currentSeconds()));

        Bootstrap.vertx().eventBus().send(UI_ACTION_SCHEDULER_RUN, item.put(USER_NAME, DEFAULT_USER).put(SESSION_ID, TestUtil.getSessionId()));

        TestUtil.vertx().setTimer(2000, handler -> testContext.completeNow());
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(130)
    void testAssertIncludeMonitorFilterGroup(VertxTestContext testContext) throws Exception
    {
        var method = TopologyEngine.class.getDeclaredMethod("setFilterTargets", JsonObject.class);

        method.setAccessible(true);

        var values = (Set<String>) method.invoke(new TopologyEngine(), new JsonObject().put(TopologyPlugin.TOPOLOGY_PLUGIN_FILTER_TARGETS, new JsonArray().add(10000000000002L)).put(TOPOLOGY_PLUGIN_FILTER_TARGET_TYPE, "include-group"));

        Assertions.assertNotNull(values);

        Assertions.assertFalse(values.isEmpty());

        Assertions.assertTrue(values.contains("**********") && values.contains("************"));

        testContext.completeNow();
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(131)
    void testAssertIncludeMonitorFilterTag(VertxTestContext testContext) throws Exception
    {
        var method = TopologyEngine.class.getDeclaredMethod("setFilterTargets", JsonObject.class);

        method.setAccessible(true);

        var values = (Set<String>) method.invoke(new TopologyEngine(), new JsonObject().put(TopologyPlugin.TOPOLOGY_PLUGIN_FILTER_TARGETS, new JsonArray().add("topology")).put(TOPOLOGY_PLUGIN_FILTER_TARGET_TYPE, "include-tag"));

        Assertions.assertNotNull(values);

        Assertions.assertFalse(values.isEmpty());

        Assertions.assertTrue(values.contains("************"));

        testContext.completeNow();
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(132)
    void testAssertExcludeMonitorFilterGroup(VertxTestContext testContext) throws Exception
    {
        var method = TopologyEngine.class.getDeclaredMethod("setFilterTargets", JsonObject.class);

        method.setAccessible(true);

        var values = (Set<String>) method.invoke(new TopologyEngine(), new JsonObject().put(TopologyPlugin.TOPOLOGY_PLUGIN_FILTER_TARGETS, new JsonArray().add(10000000000002L)).put(TOPOLOGY_PLUGIN_FILTER_TARGET_TYPE, "exclude-group"));

        Assertions.assertNotNull(values);

        Assertions.assertFalse(values.isEmpty());

        Assertions.assertTrue(values.contains("**********") && values.contains("************"));

        testContext.completeNow();
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(133)
    void testAssertExcludeMonitorFilterTag(VertxTestContext testContext) throws Exception
    {
        var method = TopologyEngine.class.getDeclaredMethod("setFilterTargets", JsonObject.class);

        method.setAccessible(true);

        var values = (Set<String>) method.invoke(new TopologyEngine(), new JsonObject().put(TopologyPlugin.TOPOLOGY_PLUGIN_FILTER_TARGETS, new JsonArray().add("topology")).put(TOPOLOGY_PLUGIN_FILTER_TARGET_TYPE, "exclude-tag"));

        Assertions.assertNotNull(values);

        Assertions.assertFalse(values.isEmpty());

        Assertions.assertTrue(values.contains("************"));

        testContext.completeNow();
    }

    //asserts
    private void assertTestResult(VertxTestContext testContext)
    {
        messageConsumer = TestUtil.vertx().eventBus().localConsumer(EVENT_USER + TestUtil.getSessionId(), message ->
        {
            try
            {
                if (message.body().getBinary(EVENT_CONTEXT) != null && message.body().containsKey(EVENT_COMPRESSION_TYPE) && message.body().getInteger(EVENT_COMPRESSION_TYPE) == COMPRESSION_TYPE_ONE)
                {
                    var eventContext = CodecUtil.toJSONObject(message.body().getBinary(EventBusConstants.EVENT_CONTEXT));

                    if (eventContext.getString(EVENT_TYPE) != null && eventContext.getString(EVENT_TYPE).contains(": Topology") && eventContext.containsKey(STATUS)
                            && eventContext.getString(AIOpsObject.OBJECT_IP).equalsIgnoreCase("**********")
                            && eventContext.getJsonObject(EVENT_CONTEXT) != null && eventContext.getJsonObject(EVENT_CONTEXT).containsKey(OBJECT)
                            && eventContext.getJsonObject(EVENT_CONTEXT).containsKey(TOPOLOGY_PLUGIN_TYPE) && !eventContext.getJsonObject(EVENT_CONTEXT).getString(TOPOLOGY_PLUGIN_TYPE).equalsIgnoreCase("Custom")
                            && (eventContext.getJsonObject(EVENT_CONTEXT).getString(OBJECT).equalsIgnoreCase(NMSConstants.TopologyProtocol.CDP.getName())
                            || eventContext.getJsonObject(EVENT_CONTEXT).getString(OBJECT).equalsIgnoreCase(NMSConstants.TopologyProtocol.LLDP.getName())))
                    {
                        testContext.verify(() ->
                        {
                            Assertions.assertTrue(eventContext.getJsonObject(EVENT_CONTEXT).containsKey(OBJECT));

                            Assertions.assertTrue(eventContext.containsKey(STATUS));

                            Assertions.assertEquals(STATUS_SUCCEED, eventContext.getString(STATUS));

                            messageConsumer.unregister(result -> testContext.completeNow());
                        });
                    }
                }
            }
            catch (Exception exception)
            {
                testContext.failNow(exception);
            }
        });
    }

    private void assertTopologyPluginTestResult(VertxTestContext testContext, String status, String value, String errorCode, JsonObject context, String objectStatus, String methodName)
    {
        try
        {
            ObjectStatusCacheStore.getStore().updateItem(context.getLong(ID), objectStatus, DateTimeUtil.currentSeconds());

            messageConsumer = TestUtil.vertx().eventBus().localConsumer(EVENT_USER + TestUtil.getSessionId(), message ->
            {
                try
                {
                    if (message.body().containsKey(EVENT_COMPRESSION_TYPE) && message.body().getInteger(EVENT_COMPRESSION_TYPE) == COMPRESSION_TYPE_ONE && message.body().containsKey(EVENT_CONTEXT) && message.body().getBinary(EVENT_CONTEXT) != null)
                    {
                        var eventContext = CodecUtil.toJSONObject(message.body().getBinary(EventBusConstants.EVENT_CONTEXT));

                        if (eventContext.getString(EVENT_TYPE) != null && eventContext.getString(EVENT_TYPE).contains("Topology Plugin Test") && eventContext.containsKey(STATUS))
                        {
                            LOGGER.debug(methodName + ": assertTopologyPluginTestResult: " + eventContext.encode());

                            if (eventContext.getString(MESSAGE) != null)
                            {
                                Assertions.assertEquals(eventContext.getString(MESSAGE), value != null ? value : InfoMessageConstants.TOPOLOGY_PLUGIN_TEST_SUCCEEDED);
                            }

                            Assertions.assertEquals(eventContext.getString(STATUS), status != null ? status : STATUS_SUCCEED);

                            Assertions.assertEquals(eventContext.getString(ERROR_CODE), errorCode);

                            messageConsumer.unregister(result -> testContext.completeNow());
                        }
                    }
                }
                catch (Exception exception)
                {
                    LOGGER.error(exception);

                    testContext.failNow(exception);
                }
            });

            TestUtil.vertx().eventBus().send(UI_ACTION_TOPOLOGY_PLUGIN_TEST, context);
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);

            testContext.failNow(exception);
        }
    }

    void assertCreateTopologyPluginTestResult(VertxTestContext testContext, JsonObject context, JsonArray objects, TestInfo testInfo)
    {
        TestAPIUtil.post(TOPOLOGY_PLUGIN_API_ENDPOINT, context, testContext.succeeding(response ->
                testContext.verify(() ->
                        TestUtil.vertx().setTimer(TimeUnit.SECONDS.toMillis(3), id ->
                        {
                            USED_COUNTS.put(response.bodyAsJsonObject().getLong(ID), objects.size());

                            TestAPIUtil.assertCreateEntityTestResult(TopologyPluginConfigStore.getStore(), context, response.bodyAsJsonObject(), String.format(InfoMessageConstants.ENTITY_CREATED, APIConstants.Entity.TOPOLOGY_PLUGIN.getName()), null, LOGGER, testInfo.getTestMethod().get().getName());

                            Assertions.assertNotNull(response.bodyAsJsonObject().getLong(ID));

                            ENTITY_IDS.put(ID, response.bodyAsJsonObject().getLong(ID));

                            testContext.completeNow();
                        }))));
    }

    void assertCreateTopologyPluginDuplicateTestResult(VertxTestContext testContext, JsonObject context)
    {
        TestAPIUtil.post(TOPOLOGY_PLUGIN_API_ENDPOINT, context, testContext.succeeding(response ->
                testContext.verify(() ->
                        TestUtil.vertx().setTimer(TimeUnit.SECONDS.toMillis(3), id ->
                        {
                            TestAPIUtil.assertAlreadyExistedEntityTestResult(response.bodyAsJsonObject(), String.format(ErrorMessageConstants.API_FIELD_UNIQUE_RULE, "Topology Name"),
                                    TopologyPluginConfigStore.getStore(), TopologyPlugin.TOPOLOGY_PLUGIN_NAME, context.getString(TopologyPlugin.TOPOLOGY_PLUGIN_NAME));

                            testContext.completeNow();
                        }))));
    }

    void assertGetCustomTopologyPluginTestResult(VertxTestContext testContext, TestInfo testInfo)
    {
        TestAPIUtil.get(TOPOLOGY_PLUGIN_API_ENDPOINT + "/" + ENTITY_IDS.get(ID), testContext.succeeding(response ->
                testContext.verify(() ->
                {
                    TestAPIUtil.assertGETRequestTestResult(response, ENTITY_IDS.get(ID), TopologyPluginConfigStore.getStore(), null, LOGGER, testInfo.getTestMethod().get().getName());

                    assertAssignObjectTestResult(testContext, ENTITY_IDS.get(ID));
                })));
    }

    void assertAssignObjectTestResult(VertxTestContext testContext, JsonArray objects)
    {

        TestAPIUtil.put(TOPOLOGY_PLUGIN_API_ENDPOINT + "/" + ENTITY_IDS.get(ID) + "/assign", new JsonObject().put(APIConstants.REQUEST_PARAM_IDS, objects), testContext.succeeding(response ->
                testContext.verify(() ->
                {
                    assertEquals(String.format(InfoMessageConstants.ASSIGN_SUCCEEDED, APIConstants.Entity.TOPOLOGY_PLUGIN.getName()), response.bodyAsJsonObject().getString(GlobalConstants.MESSAGE));

                    assertEquals(SC_OK, response.bodyAsJsonObject().getInteger(APIConstants.RESPONSE_CODE));

                    var total = USED_COUNTS.get(ENTITY_IDS.get(ID)) + objects.size();

                    USED_COUNTS.put(ENTITY_IDS.get(ID), total);

//                    testContext.awaitCompletion(1, TimeUnit.SECONDS);

                    assertAssignObjectTestResult(testContext, ENTITY_IDS.get(ID));

                })));
    }

    void assertAssignDuplicateObjectTestResult(VertxTestContext testContext, JsonArray objects)
    {

        TestAPIUtil.put(TOPOLOGY_PLUGIN_API_ENDPOINT + "/" + ENTITY_IDS.get(ID) + "/assign", new JsonObject().put(APIConstants.REQUEST_PARAM_IDS, objects), testContext.succeeding(response ->
                testContext.verify(() ->
                {
                    assertEquals(String.format(InfoMessageConstants.ASSIGN_SUCCEEDED, APIConstants.Entity.TOPOLOGY_PLUGIN.getName()), response.bodyAsJsonObject().getString(GlobalConstants.MESSAGE));

                    assertEquals(SC_OK, response.bodyAsJsonObject().getInteger(APIConstants.RESPONSE_CODE));

                    testContext.awaitCompletion(1, TimeUnit.SECONDS);

                    assertAssignObjectTestResult(testContext, ENTITY_IDS.get(ID));

                })));
    }

    void assertUnassignedObjectTestResult(VertxTestContext testContext, JsonArray objects)
    {

        TestAPIUtil.put(TOPOLOGY_PLUGIN_API_ENDPOINT + "/" + ENTITY_IDS.get(ID) + "/unassign", new JsonObject().put(APIConstants.REQUEST_PARAM_IDS, objects), testContext.succeeding(response ->
                testContext.verify(() ->
                {
                    assertEquals(TestConstants.TOPOLOGY_PLUGIN_UNASSIGNED_SUCCEEDED, response.bodyAsJsonObject().getString(GlobalConstants.MESSAGE));

                    assertEquals(SC_OK, response.bodyAsJsonObject().getInteger(APIConstants.RESPONSE_CODE));

                    var total = USED_COUNTS.get(ENTITY_IDS.get(ID)) - objects.size();

                    USED_COUNTS.put(ENTITY_IDS.get(ID), total);

//                    testContext.awaitCompletion(1, TimeUnit.SECONDS);

                    assertAssignObjectTestResult(testContext, ENTITY_IDS.get(ID));

                })));
    }

    void assertUnassignedDuplicateObjectTestResult(VertxTestContext testContext, JsonArray objects)
    {

        TestAPIUtil.put(TOPOLOGY_PLUGIN_API_ENDPOINT + "/" + ENTITY_IDS.get(ID) + "/unassign", new JsonObject().put(APIConstants.REQUEST_PARAM_IDS, objects), testContext.succeeding(response ->
                testContext.verify(() ->
                {
                    assertEquals(TestConstants.TOPOLOGY_PLUGIN_UNASSIGNED_SUCCEEDED, response.bodyAsJsonObject().getString(GlobalConstants.MESSAGE));

                    assertEquals(SC_OK, response.bodyAsJsonObject().getInteger(APIConstants.RESPONSE_CODE));

                    testContext.awaitCompletion(1, TimeUnit.SECONDS);

                    assertAssignObjectTestResult(testContext, ENTITY_IDS.get(ID));

                })));
    }

    void assertDeleteTopologyPluginHavingAssignedObjectTestResult(VertxTestContext testContext)
    {
        TestAPIUtil.delete(TOPOLOGY_PLUGIN_API_ENDPOINT + "/" + ENTITY_IDS.get(ID), testContext.succeeding(response ->
                testContext.verify(() ->
                {
                    TestAPIUtil.assertEntityInUsedDeleteTestResult(response, APIConstants.Entity.TOPOLOGY_PLUGIN.getName());

                    testContext.completeNow();
                })));
    }

    void assertDeleteTopologyPluginNotHavingAssignedObjectTestResult(VertxTestContext testContext)
    {
        TestAPIUtil.delete(TOPOLOGY_PLUGIN_API_ENDPOINT + "/" + ENTITY_IDS.get(ID), testContext.succeeding(response ->
                testContext.verify(() ->
                {
                    TestAPIUtil.assertDeleteEntityTestResult(TopologyPluginConfigStore.getStore(), response.bodyAsJsonObject(), String.format(InfoMessageConstants.ENTITY_DELETED, APIConstants.Entity.TOPOLOGY_PLUGIN.getName()));

                    testContext.completeNow();
                })));
    }

    void assertCustomTopologyPluginAfterDeleteTestResult(VertxTestContext testContext)
    {
        TestAPIUtil.get(TOPOLOGY_PLUGIN_API_ENDPOINT + "/" + ENTITY_IDS.get(ID), testContext.succeeding(response ->
                testContext.verify(() ->
                {
                    assertEquals(SC_OK, response.statusCode());

                    var body = response.bodyAsJsonObject();

                    Assertions.assertNotNull(body);

                    assertEquals(SC_OK, body.getInteger(RESPONSE_CODE));

                    body = body.getJsonObject(RESULT);

                    Assertions.assertNotNull(body);

                    Assertions.assertEquals(0, body.size());

                    testContext.completeNow();
                })));
    }

    private void assertAssignObjectTestResult(VertxTestContext testContext, long id)
    {
        var retries = new AtomicInteger(0);

        TestUtil.vertx().setPeriodic(200, timer ->
        {
            retries.getAndIncrement();

            var size = USED_COUNTS.get(id);

            var item = TopologyPluginConfigStore.getStore().getItem(id);

            if (item == null)
            {
                TestUtil.vertx().cancelTimer(timer);

                testContext.failNow(new Exception("item not found"));
            }
            else
            {
                if (item.containsKey(TopologyPlugin.TOPOLOGY_PLUGIN_CONTEXT))
                {
                    item.mergeIn(item.getJsonObject(TopologyPlugin.TOPOLOGY_PLUGIN_CONTEXT));

                    item.remove(TopologyPlugin.TOPOLOGY_PLUGIN_CONTEXT);
                }

                Assertions.assertNotNull(item.getJsonArray(TopologyPlugin.TOPOLOGY_PLUGIN_ENTITIES));

                if (item.getJsonArray(TopologyPlugin.TOPOLOGY_PLUGIN_ENTITIES).size() == size)
                {
                    TestUtil.vertx().cancelTimer(timer);

                    testContext.completeNow();
                }

                if (retries.get() > 10)
                {
                    TestUtil.vertx().cancelTimer(timer);

                    testContext.failNow(new Exception("Topology plugin entities not match..."));
                }
            }
        });
    }

    private void prepareTestContext(JsonObject context)
    {
        var key = context.getString(TopologyPlugin.TOPOLOGY_PLUGIN_TYPE).equalsIgnoreCase(NMSConstants.TopologyPluginType.CUSTOM.getName()) ? PluginEngineConstants.SCRIPT : PluginEngineConstants.PARSING_SCRIPT;

        var item = context.getJsonObject(TopologyPlugin.TOPOLOGY_PLUGIN_CONTEXT);

        if (item != null && item.getString(key) != null && !item.getString(key).isEmpty())
        {
            var file = GlobalConstants.CURRENT_DIR + GlobalConstants.PATH_SEPARATOR + GlobalConstants.PLUGIN_SCRIPT_DIR + GlobalConstants.PATH_SEPARATOR + TOPOLOGY_DIR + GlobalConstants.PATH_SEPARATOR +
                    item.getString(key);

            if (Bootstrap.vertx().fileSystem().existsBlocking(file))
            {
                item.put(key, CommonUtil.getString(Bootstrap.vertx().fileSystem().readFileBlocking(file)).trim());

                item.put(TIMEOUT, 120);
            }
        }

    }
}
