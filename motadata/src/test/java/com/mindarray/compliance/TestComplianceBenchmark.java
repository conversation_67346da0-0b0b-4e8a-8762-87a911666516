/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

package com.mindarray.compliance;

import com.mindarray.*;
import com.mindarray.api.APIConstants;
import com.mindarray.api.ComplianceBenchmark;
import com.mindarray.api.ComplianceRule;
import com.mindarray.store.ComplianceBenchmarkConfigStore;
import com.mindarray.store.ComplianceRuleConfigStore;
import com.mindarray.util.Logger;
import io.github.artsok.RepeatedIfExceptionsTest;
import io.vertx.core.json.JsonObject;
import io.vertx.junit5.Timeout;
import io.vertx.junit5.VertxExtension;
import io.vertx.junit5.VertxTestContext;
import org.junit.jupiter.api.*;
import org.junit.jupiter.api.condition.EnabledIfSystemProperty;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.api.parallel.Execution;
import org.junit.jupiter.api.parallel.ExecutionMode;

import java.util.Set;

import static com.mindarray.GlobalConstants.*;
import static com.mindarray.api.APIConstants.RESPONSE_CODE;
import static org.apache.http.HttpStatus.SC_OK;
import static org.junit.jupiter.api.Assertions.assertEquals;

@Timeout(60 * 1000)
@ExtendWith(VertxExtension.class)
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
@Execution(ExecutionMode.SAME_THREAD)
@EnabledIfSystemProperty(named = "test.mode", matches = "^APP|^PRIMARY$")
public class TestComplianceBenchmark
{
    private static final Logger LOGGER = new Logger(TestComplianceBenchmark.class, MOTADATA_API, "Test Benchmark");

    private static final JsonObject CONTEXT = new JsonObject();

    private static final Set<String> CLI_RULES = Set.of("Test Version 15.2(4)M7", "Test Version 15.2(4)M7 1", "Test Version 15.2(4)M7 2");

    @BeforeEach
    void beforeEach(VertxTestContext testContext, TestInfo testInfo)
    {
        LOGGER.info(String.format("running test case %s", testInfo.getTestMethod().get().getName()));

        testContext.completeNow();
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(1)
    void testCreateBenchmark(VertxTestContext testContext, TestInfo testInfo)
    {
        var context = new JsonObject("{\"compliance.benchmark.name\":\"This is benchmark 1\",\"compliance.benchmark.description\":\"\",\"compliance.benchmark.tags\":[],\"compliance.benchmark.category\":\"Network\",\"compliance.benchmark.rule.ids\":[],\"compliance.benchmark.rule.groups\":[{\"compliance.benchmark.rule.name\":\"rule1\",\"compliance.benchmark.rules\":[55926513367,55946480908,55946480909],\"compliance.benchmark.parent\":\"\"},{\"compliance.benchmark.rule.name\":\"inner-rule1\",\"compliance.benchmark.rules\":[55843281951],\"compliance.benchmark.parent\":\"rule1\"}]}");

        context.getJsonArray(ComplianceBenchmark.COMPLIANCE_BENCHMARK_RULE_IDS).add(ComplianceRuleConfigStore.getStore().getItem().getLong(GlobalConstants.ID));

        TestAPIUtil.post(TestAPIConstants.COMPLIANCE_BENCHMARK_API_ENDPOINT, context, testContext.succeeding(response -> testContext.verify(() ->
        {
            LOGGER.info(String.format("Response: %s", response.bodyAsJsonObject()));

            TestAPIUtil.assertCreateEntityTestResult(ComplianceBenchmarkConfigStore.getStore(), response.bodyAsJsonObject(), String.format(InfoMessageConstants.ENTITY_CREATED, APIConstants.Entity.COMPLIANCE_BENCHMARK.getName()), LOGGER, testInfo.getTestMethod().get().getName());

            CONTEXT.put(APIConstants.Entity.COMPLIANCE_BENCHMARK.getName(), response.bodyAsJsonObject().getLong(ID));

            testContext.completeNow();
        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(2)
    void testCreateBenchmarkSecond(VertxTestContext testContext, TestInfo testInfo)
    {
        var context = new JsonObject("{\"compliance.benchmark.name\":\"This is benchmark 2\",\"compliance.benchmark.description\":\"\",\"compliance.benchmark.tags\":[],\"compliance.benchmark.category\":\"Network\",\"compliance.benchmark.rule.ids\":[],\"compliance.benchmark.rule.groups\":[{\"compliance.benchmark.rule.name\":\"rule1\",\"compliance.benchmark.rules\":[55926513367,55946480908,55946480909],\"compliance.benchmark.parent\":\"\"},{\"compliance.benchmark.rule.name\":\"inner-rule1\",\"compliance.benchmark.rules\":[55843281951],\"compliance.benchmark.parent\":\"rule1\"}]}");

        context.getJsonArray(ComplianceBenchmark.COMPLIANCE_BENCHMARK_RULE_IDS).add(ComplianceRuleConfigStore.getStore().getItem().getLong(GlobalConstants.ID));

        context.getJsonArray(ComplianceBenchmark.COMPLIANCE_BENCHMARK_RULE_GROUPS).getJsonObject(0).getJsonArray(ComplianceBenchmark.COMPLIANCE_BENCHMARK_RULES).add(ComplianceRuleConfigStore.getStore().getItem().getLong(GlobalConstants.ID));

        TestAPIUtil.post(TestAPIConstants.COMPLIANCE_BENCHMARK_API_ENDPOINT, context, testContext.succeeding(response -> testContext.verify(() ->
        {
            LOGGER.info(String.format("Response: %s", response.bodyAsJsonObject()));

            TestAPIUtil.assertCreateEntityTestResult(ComplianceBenchmarkConfigStore.getStore(), response.bodyAsJsonObject(), String.format(InfoMessageConstants.ENTITY_CREATED, APIConstants.Entity.COMPLIANCE_BENCHMARK.getName()), LOGGER, testInfo.getTestMethod().get().getName());

            testContext.completeNow();
        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(3)
    void testCreateBenchmarkDuplicate(VertxTestContext testContext, TestInfo testInfo)
    {
        var context = new JsonObject("{\"compliance.benchmark.name\":\"This is benchmark 1\",\"compliance.benchmark.description\":\"\",\"compliance.benchmark.tags\":[],\"compliance.benchmark.category\":\"Network\",\"compliance.benchmark.rule.ids\":[],\"compliance.benchmark.rule.groups\":[{\"compliance.benchmark.rule.name\":\"rule1\",\"compliance.benchmark.rules\":[55926513367,55946480908,55946480909],\"compliance.benchmark.parent\":\"\"},{\"compliance.benchmark.rule.name\":\"inner-rule1\",\"compliance.benchmark.rules\":[55843281951],\"compliance.benchmark.parent\":\"rule1\"}]}");

        context.getJsonArray(ComplianceBenchmark.COMPLIANCE_BENCHMARK_RULE_IDS).add(ComplianceRuleConfigStore.getStore().getItem().getLong(GlobalConstants.ID));

        context.getJsonArray(ComplianceBenchmark.COMPLIANCE_BENCHMARK_RULE_GROUPS).getJsonObject(0).getJsonArray(ComplianceBenchmark.COMPLIANCE_BENCHMARK_RULES).add(ComplianceRuleConfigStore.getStore().getItem().getLong(GlobalConstants.ID));

        TestAPIUtil.post(TestAPIConstants.COMPLIANCE_BENCHMARK_API_ENDPOINT, context, testContext.succeeding(response -> testContext.verify(() ->
        {
            LOGGER.info(String.format("Response: %s", response.bodyAsJsonObject()));

            TestAPIUtil.assertAlreadyExistedEntityTestResult(response.bodyAsJsonObject(), String.format(ErrorMessageConstants.API_FIELD_UNIQUE_RULE, "Benchmark Name"),
                    ComplianceBenchmarkConfigStore.getStore(), ComplianceBenchmark.COMPLIANCE_BENCHMARK_NAME, context.getString(ComplianceBenchmark.COMPLIANCE_BENCHMARK_NAME));

            testContext.completeNow();
        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(4)
    void testUpdateBenchmark(VertxTestContext testContext, TestInfo testInfo)
    {
        var context = new JsonObject("{\"compliance.benchmark.tags\":[]}");

        TestAPIUtil.put(TestAPIConstants.COMPLIANCE_BENCHMARK_API_ENDPOINT + CONTEXT.getLong(APIConstants.Entity.COMPLIANCE_BENCHMARK.getName()), context, testContext.succeeding(response -> testContext.verify(() ->
        {
            LOGGER.info(String.format("Response: %s", response.bodyAsJsonObject()));

            TestAPIUtil.assertUpdateEntityTestResult(ComplianceBenchmarkConfigStore.getStore(), context, response.bodyAsJsonObject(), String.format(InfoMessageConstants.ENTITY_UPDATED, APIConstants.Entity.COMPLIANCE_BENCHMARK.getName()), LOGGER, testInfo.getTestMethod().get().getName());

            testContext.completeNow();

        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(5)
    void testGetAllBenchmark(VertxTestContext testContext, TestInfo testInfo)
    {
        TestAPIUtil.get(TestAPIConstants.COMPLIANCE_BENCHMARK_API_ENDPOINT, testContext.succeeding(response -> testContext.verify(() ->
        {
            LOGGER.info(String.format("Response: %s", response.bodyAsJsonObject()));

            assertEquals(SC_OK, response.statusCode());

            var body = response.bodyAsJsonObject();

            Assertions.assertNotNull(body);

            assertEquals(SC_OK, body.getInteger(RESPONSE_CODE));

            var entities = body.getJsonArray(RESULT);

            Assertions.assertNotNull(entities);

            testContext.completeNow();
        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(6)
    void testDeleteBenchmark(VertxTestContext testContext, TestInfo testInfo)
    {
        TestAPIUtil.delete(TestAPIConstants.COMPLIANCE_BENCHMARK_API_ENDPOINT + CONTEXT.getLong(APIConstants.Entity.COMPLIANCE_BENCHMARK.getName()), testContext.succeeding(response -> testContext.verify(() ->
        {
            LOGGER.info(String.format("Response: %s", response.bodyAsJsonObject()));

            TestAPIUtil.assertDeleteEntityTestResult(ComplianceBenchmarkConfigStore.getStore(), response.bodyAsJsonObject(), String.format(InfoMessageConstants.ENTITY_DELETED, APIConstants.Entity.COMPLIANCE_BENCHMARK.getName()));

            testContext.completeNow();
        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(7)
    void testCreateBenchmarkThird(VertxTestContext testContext, TestInfo testInfo)
    {
        var context = new JsonObject("{\"compliance.benchmark.name\":\"This is benchmark\",\"compliance.benchmark.description\":\"\",\"compliance.benchmark.tags\":[],\"compliance.benchmark.category\":\"Network\",\"compliance.benchmark.rule.ids\":[],\"compliance.benchmark.rule.groups\":[{\"compliance.benchmark.rule.name\":\"rule1\",\"compliance.benchmark.rules\":[55926513367,55946480908,55946480909],\"compliance.benchmark.parent\":\"\"},{\"compliance.benchmark.rule.name\":\"inner-rule1\",\"compliance.benchmark.rules\":[55843281951],\"compliance.benchmark.parent\":\"rule1\"}]}");

        context.getJsonArray(ComplianceBenchmark.COMPLIANCE_BENCHMARK_RULE_IDS).add(ComplianceRuleConfigStore.getStore().getItemByValue(ComplianceRule.COMPLIANCE_RULE_NAME, "Test Rule 2").getLong(ID)).add(ComplianceRuleConfigStore.getStore().getItemByValue(ComplianceRule.COMPLIANCE_RULE_NAME, "Rule Name 3").getLong(ID));

        context.getJsonArray(ComplianceBenchmark.COMPLIANCE_BENCHMARK_RULE_GROUPS).getJsonObject(0).getJsonArray(ComplianceBenchmark.COMPLIANCE_BENCHMARK_RULES).add(ComplianceRuleConfigStore.getStore().getItem().getLong(GlobalConstants.ID));

        TestAPIUtil.post(TestAPIConstants.COMPLIANCE_BENCHMARK_API_ENDPOINT, context, testContext.succeeding(response -> testContext.verify(() ->
        {
            LOGGER.info(String.format("Response: %s", response.bodyAsJsonObject()));

            TestAPIUtil.assertCreateEntityTestResult(ComplianceBenchmarkConfigStore.getStore(), response.bodyAsJsonObject(), String.format(InfoMessageConstants.ENTITY_CREATED, APIConstants.Entity.COMPLIANCE_BENCHMARK.getName()), LOGGER, testInfo.getTestMethod().get().getName());

            CONTEXT.put(APIConstants.Entity.COMPLIANCE_BENCHMARK.getName(), response.bodyAsJsonObject().getLong(ID));

            testContext.completeNow();
        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(7)
    void testCreateBenchmarkCLIFourth(VertxTestContext testContext, TestInfo testInfo)
    {
        try
        {
            var context = new JsonObject("{\"compliance.benchmark.name\":\"Test Version 15.2(4)M7\",\"compliance.benchmark.category\":\"Network\",\"compliance.benchmark.rule.ids\":[],\"compliance.benchmark.rule.groups\":[{\"compliance.benchmark.rule.name\":\"Test Version 15.2(4)M7\",\"compliance.benchmark.rules\":[],\"compliance.benchmark.parent\":\"\"}],\"compliance.benchmark.tags\":[]}");

            for (var rule : CLI_RULES)
            {
                var ruleId = ComplianceRuleConfigStore.getStore().getItemByValue(ComplianceRule.COMPLIANCE_RULE_NAME, rule).getLong(ID);

                context.getJsonArray(ComplianceBenchmark.COMPLIANCE_BENCHMARK_RULE_IDS).add(ruleId);

                context.getJsonArray(ComplianceBenchmark.COMPLIANCE_BENCHMARK_RULE_GROUPS).getJsonObject(0).getJsonArray(ComplianceBenchmark.COMPLIANCE_BENCHMARK_RULES).add(ruleId);
            }

            TestAPIUtil.post(TestAPIConstants.COMPLIANCE_BENCHMARK_API_ENDPOINT, context, testContext.succeeding(response -> testContext.verify(() ->
            {
                try
                {
                    LOGGER.info(String.format("Response: %s", response.bodyAsJsonObject()));

                    TestAPIUtil.assertCreateEntityTestResult(ComplianceBenchmarkConfigStore.getStore(), response.bodyAsJsonObject(), String.format(InfoMessageConstants.ENTITY_CREATED, APIConstants.Entity.COMPLIANCE_BENCHMARK.getName()), LOGGER, testInfo.getTestMethod().get().getName());

                    CONTEXT.put(APIConstants.Entity.COMPLIANCE_BENCHMARK.getName(), response.bodyAsJsonObject().getLong(ID));

                    testContext.completeNow();
                }
                catch (Exception exception)
                {
                    LOGGER.error(exception);

                    testContext.failNow(exception);
                }

            })));
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);

            testContext.failNow(exception);
        }
    }
}
