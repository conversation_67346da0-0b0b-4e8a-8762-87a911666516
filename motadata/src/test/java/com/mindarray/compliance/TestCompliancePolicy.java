/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

package com.mindarray.compliance;

import com.mindarray.GlobalConstants;
import com.mindarray.InfoMessageConstants;
import com.mindarray.TestAPIConstants;
import com.mindarray.TestAPIUtil;
import com.mindarray.api.AIOpsObject;
import com.mindarray.api.APIConstants;
import com.mindarray.api.ComplianceBenchmark;
import com.mindarray.api.CompliancePolicy;
import com.mindarray.store.ComplianceBenchmarkConfigStore;
import com.mindarray.store.CompliancePolicyConfigStore;
import com.mindarray.store.ObjectConfigStore;
import com.mindarray.util.Logger;
import io.github.artsok.RepeatedIfExceptionsTest;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import io.vertx.junit5.Timeout;
import io.vertx.junit5.VertxExtension;
import io.vertx.junit5.VertxTestContext;
import org.junit.jupiter.api.*;
import org.junit.jupiter.api.condition.EnabledIfSystemProperty;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.api.parallel.Execution;
import org.junit.jupiter.api.parallel.ExecutionMode;

import static com.mindarray.GlobalConstants.*;
import static com.mindarray.api.APIConstants.RESPONSE_CODE;
import static org.apache.http.HttpStatus.SC_OK;
import static org.junit.jupiter.api.Assertions.assertEquals;

@Timeout(60 * 1000)
@ExtendWith(VertxExtension.class)
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
@Execution(ExecutionMode.SAME_THREAD)
@EnabledIfSystemProperty(named = "test.mode", matches = "^APP|^PRIMARY$")
public class TestCompliancePolicy
{
    private static final Logger LOGGER = new Logger(TestCompliancePolicy.class, MOTADATA_API, "Test Compliance Policy");

    @BeforeEach
    void beforeEach(VertxTestContext testContext, TestInfo testInfo)
    {
        LOGGER.info(String.format("running test case %s", testInfo.getTestMethod().get().getName()));

        testContext.completeNow();
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(1)
    void testCreateCompliancePolicy(VertxTestContext testContext, TestInfo testInfo)
    {
        var context = new JsonObject("{\"compliance.policy.name\":\"112 Policy\",\"compliance.policy.description\":\"This is compliance.policy description\",\"compliance.policy.tags\":[],\"compliance.policy.context\":{\"entity.type\":\"Monitor\",\"entities\":[56972387298],\"compliance.policy.config.file.type\":\"startup.config\",\"compliance.policy.category\":\"Network\",\"compliance.policy.benchmark\":56972387294},\"compliance.policy.notification.context\":{\"compliance.policy.email.notification.recipients\":[\"<EMAIL>\"],\"compliance.policy.user.notification.recipients\":[\"admin\"]},\"compliance.policy.generate.report\":\"no\"}");

        context.getJsonObject(CompliancePolicy.COMPLIANCE_POLICY_CONTEXT).getJsonArray(GlobalConstants.ENTITIES).add(ObjectConfigStore.getStore().getItemByIP("***********"));

        context.getJsonObject(CompliancePolicy.COMPLIANCE_POLICY_CONTEXT).put(CompliancePolicy.COMPLIANCE_POLICY_BENCHMARK, ComplianceBenchmarkConfigStore.getStore().getItemByValue(ComplianceBenchmark.COMPLIANCE_BENCHMARK_NAME, "This is benchmark").getLong(GlobalConstants.ID));

        TestAPIUtil.post(TestAPIConstants.COMPLIANCE_POLICY_API_ENDPOINT, context, testContext.succeeding(response -> testContext.verify(() ->
        {
            LOGGER.info(String.format("Response: %s", response.bodyAsJsonObject()));

            TestAPIUtil.assertCreateEntityTestResult(CompliancePolicyConfigStore.getStore(), response.bodyAsJsonObject(), String.format(InfoMessageConstants.ENTITY_CREATED, APIConstants.Entity.COMPLIANCE_POLICY.getName()), LOGGER, testInfo.getTestMethod().get().getName());

            testContext.completeNow();
        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(2)
    void testAssignMonitorCompliancePolicy(VertxTestContext testContext, TestInfo testInfo)
    {
        var id = CompliancePolicyConfigStore.getStore().getItemByValue(CompliancePolicy.COMPLIANCE_POLICY_NAME, "112 Policy").getLong(GlobalConstants.ID);

        var monitorId = ObjectConfigStore.getStore().getItemByIP("fd00:1:1:1::47");

        TestAPIUtil.put(TestAPIConstants.COMPLIANCE_POLICY_API_ENDPOINT + id + PATH_SEPARATOR + "assign", new JsonObject().put(APIConstants.REQUEST_PARAM_IDS, new JsonArray().add(monitorId)), testContext.succeeding(response ->
                testContext.verify(() ->
                {
                    LOGGER.info(String.format("%s response %s", testInfo.getTestMethod().get().getName(), response.bodyAsJsonObject()));

                    var item = CompliancePolicyConfigStore.getStore().getItem(id);

                    LOGGER.info(String.format("item: %s", item));

                    Assertions.assertTrue(item.getJsonObject(CompliancePolicy.COMPLIANCE_POLICY_CONTEXT).getJsonArray(ENTITIES).contains(monitorId));

                    testContext.completeNow();
                })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(3)
    void testUnassignMonitorCompliancePolicy(VertxTestContext testContext, TestInfo testInfo)
    {
        var id = CompliancePolicyConfigStore.getStore().getItemByValue(CompliancePolicy.COMPLIANCE_POLICY_NAME, "112 Policy").getLong(GlobalConstants.ID);

        var monitorId = ObjectConfigStore.getStore().getItemByIP("fd00:1:1:1::47");

        TestAPIUtil.put(TestAPIConstants.COMPLIANCE_POLICY_API_ENDPOINT + id + PATH_SEPARATOR + "unassign", new JsonObject().put(APIConstants.REQUEST_PARAM_IDS, new JsonArray().add(monitorId)), testContext.succeeding(response ->
                testContext.verify(() ->
                {
                    LOGGER.info(String.format("%s response %s", testInfo.getTestMethod().get().getName(), response.bodyAsJsonObject()));

                    var item = CompliancePolicyConfigStore.getStore().getItem(id);

                    LOGGER.info(String.format("item: %s", item));

                    Assertions.assertFalse(item.getJsonObject(CompliancePolicy.COMPLIANCE_POLICY_CONTEXT).getJsonArray(ENTITIES).contains(monitorId));

                    testContext.completeNow();
                })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(4)
    void testAssignMonitorCompliancePolicy2(VertxTestContext testContext, TestInfo testInfo)
    {
        var id = CompliancePolicyConfigStore.getStore().getItemByValue(CompliancePolicy.COMPLIANCE_POLICY_NAME, "112 Policy").getLong(GlobalConstants.ID);

        var monitorId = ObjectConfigStore.getStore().getItemByIP("fd00:1:1:1::47");

        TestAPIUtil.put(TestAPIConstants.COMPLIANCE_POLICY_API_ENDPOINT + id + PATH_SEPARATOR + "assign", new JsonObject().put(APIConstants.REQUEST_PARAM_IDS, new JsonArray().add(monitorId)), testContext.succeeding(response ->
                testContext.verify(() ->
                {
                    LOGGER.info(String.format("%s response %s", testInfo.getTestMethod().get().getName(), response.bodyAsJsonObject()));

                    var item = CompliancePolicyConfigStore.getStore().getItem(id);

                    LOGGER.info(String.format("item: %s", item));

                    Assertions.assertTrue(item.getJsonObject(CompliancePolicy.COMPLIANCE_POLICY_CONTEXT).getJsonArray(ENTITIES).contains(monitorId));

                    testContext.completeNow();
                })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(5)
    void testAssignMonitorCompliancePolicySecond(VertxTestContext testContext, TestInfo testInfo)
    {
        var id = CompliancePolicyConfigStore.getStore().getItemByValue(CompliancePolicy.COMPLIANCE_POLICY_NAME, "112 Policy").getLong(GlobalConstants.ID);

        var monitorId = ObjectConfigStore.getStore().getItemByIP("***********");

        TestAPIUtil.put(TestAPIConstants.COMPLIANCE_POLICY_API_ENDPOINT + id + PATH_SEPARATOR + "assign", new JsonObject().put(APIConstants.REQUEST_PARAM_IDS, new JsonArray().add(monitorId)), testContext.succeeding(response ->
                testContext.verify(() ->
                {
                    LOGGER.info(String.format("%s response %s", testInfo.getTestMethod().get().getName(), response.bodyAsJsonObject()));

                    var item = CompliancePolicyConfigStore.getStore().getItem(id);

                    LOGGER.info(String.format("item: %s", item));

                    Assertions.assertTrue(item.getJsonObject(CompliancePolicy.COMPLIANCE_POLICY_CONTEXT).getJsonArray(ENTITIES).contains(monitorId));

                    testContext.completeNow();
                })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(4)
    void testGetReferencesCompliancePolicy(VertxTestContext testContext, TestInfo testInfo)
    {
        var id = CompliancePolicyConfigStore.getStore().getItemByValue(CompliancePolicy.COMPLIANCE_POLICY_NAME, "112 Policy").getLong(GlobalConstants.ID);

        TestAPIUtil.get(TestAPIConstants.COMPLIANCE_POLICY_API_ENDPOINT + id + PATH_SEPARATOR + "references", testContext.succeeding(response ->
                testContext.verify(() ->
                {
                    LOGGER.info(String.format("%s response %s", testInfo.getTestMethod().get().getName(), response));

                    var monitor = response.bodyAsJsonObject().getJsonObject(RESULT).getJsonArray(APIConstants.Entity.OBJECT.getName());

                    Assertions.assertTrue(monitor.getJsonObject(0).getString(AIOpsObject.OBJECT_IP).equalsIgnoreCase("***********"));

                    testContext.completeNow();
                })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(5)
    void testGetAllCompliancePolicy(VertxTestContext testContext, TestInfo testInfo)
    {
        TestAPIUtil.get(TestAPIConstants.COMPLIANCE_POLICY_API_ENDPOINT, testContext.succeeding(response ->
                testContext.verify(() ->
                {
                    LOGGER.info(String.format("%s response %s", testInfo.getTestMethod().get().getName(), response));

                    assertEquals(SC_OK, response.statusCode());

                    var body = response.bodyAsJsonObject();

                    Assertions.assertNotNull(body);

                    assertEquals(SC_OK, body.getInteger(RESPONSE_CODE));

                    var entities = body.getJsonArray(RESULT);

                    Assertions.assertNotNull(entities);

                    testContext.completeNow();
                })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(6)
    void testCreateCompliancePolicySecond(VertxTestContext testContext, TestInfo testInfo)
    {
        var context = new JsonObject("{\"compliance.policy.name\":\"Test Version 15.2(4)M7\",\"compliance.policy.description\":\"\",\"compliance.policy.context\":{\"compliance.policy.config.file.type\":\"startup.config\",\"compliance.policy.benchmark\":66646467024,\"entity.type\":\"Monitor\",\"entities\":[],\"compliance.policy.category\":\"Network\"},\"compliance.policy.generate.report\":\"no\",\"compliance.policy.notification.context\":{\"compliance.policy.email.notification.recipients\":[],\"compliance.policy.user.notification.recipients\":[]},\"compliance.policy.tags\":[],\"compliance.policy.creation.time\":1739177940179,\"compliance.policy.scheduler\":\"no\",\"state\":\"Not Running\"}");

        context.getJsonObject(CompliancePolicy.COMPLIANCE_POLICY_CONTEXT).getJsonArray(GlobalConstants.ENTITIES).add(ObjectConfigStore.getStore().getItemByIP("************"));

        context.getJsonObject(CompliancePolicy.COMPLIANCE_POLICY_CONTEXT).put(CompliancePolicy.COMPLIANCE_POLICY_BENCHMARK, ComplianceBenchmarkConfigStore.getStore().getItemByValue(ComplianceBenchmark.COMPLIANCE_BENCHMARK_NAME, "Test Version 15.2(4)M7").getLong(GlobalConstants.ID));

        TestAPIUtil.post(TestAPIConstants.COMPLIANCE_POLICY_API_ENDPOINT, context, testContext.succeeding(response -> testContext.verify(() ->
        {
            LOGGER.info(String.format("Response: %s", response.bodyAsJsonObject()));

            TestAPIUtil.assertCreateEntityTestResult(CompliancePolicyConfigStore.getStore(), response.bodyAsJsonObject(), String.format(InfoMessageConstants.ENTITY_CREATED, APIConstants.Entity.COMPLIANCE_POLICY.getName()), LOGGER, testInfo.getTestMethod().get().getName());

            testContext.completeNow();
        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(6)
    void testCreateCompliancePolicyThird(VertxTestContext testContext, TestInfo testInfo)
    {
        var context = new JsonObject("{\"compliance.policy.name\":\"Demo Fail Policy\",\"compliance.policy.description\":\"\",\"compliance.policy.context\":{\"compliance.policy.config.file.type\":\"startup.config\",\"compliance.policy.benchmark\":66646467024,\"entity.type\":\"Monitor\",\"entities\":[],\"compliance.policy.category\":\"Network\"},\"compliance.policy.generate.report\":\"no\",\"compliance.policy.notification.context\":{\"compliance.policy.email.notification.recipients\":[],\"compliance.policy.user.notification.recipients\":[]},\"compliance.policy.tags\":[],\"compliance.policy.creation.time\":1739177940179,\"compliance.policy.scheduler\":\"no\",\"state\":\"Not Running\"}");

        context.getJsonObject(CompliancePolicy.COMPLIANCE_POLICY_CONTEXT).getJsonArray(GlobalConstants.ENTITIES).add(1234567890); // adding random entities suppose attached monitor is deleted in any case.

        context.getJsonObject(CompliancePolicy.COMPLIANCE_POLICY_CONTEXT).put(CompliancePolicy.COMPLIANCE_POLICY_BENCHMARK, ComplianceBenchmarkConfigStore.getStore().getItemByValue(ComplianceBenchmark.COMPLIANCE_BENCHMARK_NAME, "Test Version 15.2(4)M7").getLong(GlobalConstants.ID));

        TestAPIUtil.post(TestAPIConstants.COMPLIANCE_POLICY_API_ENDPOINT, context, testContext.succeeding(response -> testContext.verify(() ->
        {
            LOGGER.info(String.format("Response: %s", response.bodyAsJsonObject()));

            TestAPIUtil.assertCreateEntityTestResult(CompliancePolicyConfigStore.getStore(), response.bodyAsJsonObject(), String.format(InfoMessageConstants.ENTITY_CREATED, APIConstants.Entity.COMPLIANCE_POLICY.getName()), LOGGER, testInfo.getTestMethod().get().getName());

            testContext.completeNow();
        })));
    }
}
