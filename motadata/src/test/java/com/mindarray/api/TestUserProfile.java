/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

package com.mindarray.api;

import com.mindarray.*;
import com.mindarray.store.*;
import com.mindarray.util.Logger;
import io.github.artsok.RepeatedIfExceptionsTest;
import io.vertx.core.Future;
import io.vertx.core.Promise;
import io.vertx.core.http.HttpHeaders;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import io.vertx.junit5.Timeout;
import io.vertx.junit5.VertxExtension;
import io.vertx.junit5.VertxTestContext;
import org.apache.http.HttpStatus;
import org.junit.jupiter.api.*;
import org.junit.jupiter.api.condition.EnabledIfSystemProperty;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.api.parallel.Execution;
import org.junit.jupiter.api.parallel.ExecutionMode;

import java.util.ArrayList;
import java.util.concurrent.atomic.AtomicInteger;

import static com.mindarray.ErrorMessageConstants.*;
import static com.mindarray.GlobalConstants.*;
import static com.mindarray.TestAPIConstants.*;
import static com.mindarray.api.APIConstants.*;
import static com.mindarray.api.Group.*;
import static com.mindarray.api.User.*;
import static com.mindarray.api.UserRole.USER_ROLE_CONTEXT;
import static com.mindarray.api.UserRole.USER_ROLE_NAME;
import static org.apache.http.HttpStatus.SC_BAD_REQUEST;
import static org.apache.http.HttpStatus.SC_OK;
import static org.junit.jupiter.api.Assertions.*;

@ExtendWith(VertxExtension.class)
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
@Timeout(60 * 1000)
@Execution(ExecutionMode.SAME_THREAD)
@EnabledIfSystemProperty(named = "test.mode", matches = "^APP|^PRIMARY$")
public class TestUserProfile
{

    private static final long CURRENT_TIME_MILLIS = System.currentTimeMillis();
    private static final String EXPIRY_DAYS = "password.policy.password.expiry.days";
    private static final JsonObject IDS = new JsonObject();
    private static final JsonObject USER1_CONTEXT = new JsonObject()
            .put(USER_NAME, "user1")
            .put(USER_FIRST_NAME, "motadata1")
            .put(USER_LAST_NAME, "admin1")
            .put(USER_EMAIL, "<EMAIL>")
            .put(USER_MOBILE, 123456789)
            .put(USER_PASSWORD, "MindUser@123")
            .put(USER_STATUS, "yes")
            .put(USER_ROLE, DEFAULT_ID)
            .put(USER_GROUPS, new JsonArray().add(10000000000013L))
            .put(USER_TYPE, USER_TYPE_SYSTEM);
    private static final JsonObject USER2_CONTEXT = new JsonObject()
            .put(USER_NAME, "user2")
            .put(USER_FIRST_NAME, "motadata2")
            .put(USER_LAST_NAME, "admin1")
            .put(USER_EMAIL, "<EMAIL>")
            .put(USER_MOBILE, 123456789)
            .put(USER_PASSWORD, "MindUser@123")
            .put(USER_STATUS, "yes")
            .put(USER_ROLE, DEFAULT_ID)
            .put(USER_GROUPS, new JsonArray().add(DEFAULT_ID))
            .put(USER_TYPE, USER_TYPE_SYSTEM);
    private static final JsonObject LDAP_USER_CONTEXT = new JsonObject()
            .put(USER_NAME, "ldapUser")
            .put(USER_FIRST_NAME, "ldap")
            .put(USER_LAST_NAME, "admin1")
            .put(USER_EMAIL, "<EMAIL>")
            .put(USER_MOBILE, 123456789)
            .put(USER_PASSWORD, "MindUser@123")
            .put(USER_STATUS, "yes")
            .put(USER_ROLE, DEFAULT_ID)
            .put(USER_GROUPS, new JsonArray().add(DEFAULT_ID))
            .put(USER_TYPE, USER_TYPE_LDAP);
    private static final JsonObject USER3_CONTEXT = new JsonObject()
            .put(USER_NAME, "user3")
            .put(USER_FIRST_NAME, "motadata")
            .put(USER_LAST_NAME, "motadata1")
            .put(USER_EMAIL, "<EMAIL>")
            .put(USER_MOBILE, 123456789)
            .put(USER_PASSWORD, "Mind@123")
            .put(USER_STATUS, "yes")
            .put(USER_ROLE, DEFAULT_ID)
            .put(USER_GROUPS, new JsonArray().add(DEFAULT_ID))
            .put(USER_TYPE, USER_TYPE_SYSTEM);
    private static final JsonObject USER4_CONTEXT = new JsonObject()
            .put(USER_NAME, "user4")
            .put(USER_FIRST_NAME, "motadata4")
            .put(USER_LAST_NAME, "admin4")
            .put(USER_EMAIL, "<EMAIL>")
            .put(USER_MOBILE, 123456744)
            .put(USER_PASSWORD, "MindUser@123")
            .put(USER_STATUS, "yes")
            .put(USER_ROLE, DEFAULT_ID)
            .put(USER_GROUPS, new JsonArray().add(10000000000013L))
            .put(USER_TYPE, USER_TYPE_SYSTEM);
    private static final JsonObject USER_DATA_SECURITY_CONTEXT = new JsonObject()
            .put(USER_NAME, "userdata")
            .put(USER_FIRST_NAME, "motadata5")
            .put(USER_LAST_NAME, "admin5")
            .put(USER_EMAIL, "<EMAIL>")
            .put(USER_MOBILE, 123456787)
            .put(USER_PASSWORD, "Mind@123")
            .put(USER_STATUS, "yes")
            .put(USER_ROLE, DEFAULT_ID)
            .put(USER_GROUPS, new JsonArray().add(10000000000017L).add(10000000000002L))
            .put(USER_TYPE, USER_TYPE_SYSTEM);

    private static final JsonObject MINIMAL_ACCESS_USER = new JsonObject()
            .put(USER_NAME, "MinimalAccess")
            .put(USER_FIRST_NAME, "motadata5")
            .put(USER_LAST_NAME, "admin5")
            .put(USER_EMAIL, "<EMAIL>")
            .put(USER_MOBILE, 123456787)
            .put(USER_PASSWORD, "Mind@123")
            .put(USER_STATUS, "yes")
            .put(USER_ROLE, DEFAULT_ID)
            .put(USER_GROUPS, new JsonArray().add(10000000000017L))
            .put(USER_TYPE, USER_TYPE_SYSTEM);

    private static final JsonObject VAPT_USER_CONTEXT = new JsonObject()
            .put(USER_NAME, "vaptuser")
            .put(USER_FIRST_NAME, "motadata6")
            .put(USER_LAST_NAME, "admin6")
            .put(USER_EMAIL, "<EMAIL>")
            .put(USER_MOBILE, 123456789)
            .put(USER_PASSWORD, "MindUser@123")
            .put(USER_STATUS, "yes")
            .put(USER_ROLE, DEFAULT_ID)
            .put(USER_GROUPS, new JsonArray().add(10000000000013L))
            .put(USER_TYPE, USER_TYPE_SYSTEM);
    private static final Logger LOGGER = new Logger(TestUserProfile.class, MOTADATA_API, "Test User Profile");
    private static String refreshToken = null;
    private static String accessToken = null;

    @BeforeAll
    static void beforeAll(VertxTestContext testContext)
    {
        TestAPIUtil.post(USER_API_ENDPOINT, LDAP_USER_CONTEXT,

                testContext.succeeding(response ->

                        testContext.verify(() ->
                        {
                            try
                            {
                                TestAPIUtil.assertCreateEntityTestResult(UserConfigStore.getStore(), LDAP_USER_CONTEXT, response.bodyAsJsonObject(),
                                        String.format(InfoMessageConstants.ENTITY_CREATED, Entity.USER.getName()), new JsonArray().add(USER_PREFERENCES).add(USER_PASSWORD_LAST_UPDATED_TIME), LOGGER, "beforeall");

                                IDS.put("ldap.user", response.bodyAsJsonObject().getLong(ID));

                                testContext.completeNow();
                            }
                            catch (Exception exception)
                            {
                                LOGGER.info(exception);

                                testContext.failNow(exception);
                            }

                        })));

    }

    @AfterAll
    static void afterAll(VertxTestContext testContext) throws Exception
    {
        TestAPIUtil.delete(USER_API_ENDPOINT + IDS.getLong("ldap.user"),
                testContext.succeeding(response ->

                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertDeleteEntityTestResult(UserConfigStore.getStore(), response.bodyAsJsonObject(),
                                    String.format(InfoMessageConstants.ENTITY_DELETED, Entity.USER.getName()));

                            testContext.completeNow();
                        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(1)
    void testGetAllUsers(VertxTestContext testContext)
    {
        TestAPIUtil.get(USER_API_ENDPOINT, testContext.succeeding(response -> testContext.verify(() ->
        {
            TestAPIUtil.assertGETAllRequestTestResult(response, UserConfigStore.getStore(), null);

            testContext.completeNow();

        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(2)
    void testCreateGroup(VertxTestContext testContext, TestInfo testInfo)
    {
        var context = new JsonObject()
                .put(FIELD_GROUP_NAME, "grouptest").put(FIELD_PARENT_GROUP, 0)
                .put(GROUP_CONTEXT, new JsonObject().put(GROUP_AUTO_ASSIGN, NO));

        TestAPIUtil.post(GROUP_API_ENDPOINT, context,
                testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertCreateEntityTestResult(GroupConfigStore.getStore(), context, response.bodyAsJsonObject(), String.format(InfoMessageConstants.ENTITY_CREATED, Entity.GROUP.getName()), null, LOGGER, testInfo.getTestMethod().get().getName());

                            IDS.put("group", response.bodyAsJsonObject().getLong(ID));

                            testContext.completeNow();
                        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(3)
    void testCreateChildGroup(VertxTestContext testContext, TestInfo testInfo)
    {
        var context = new JsonObject().put(FIELD_GROUP_NAME, "groupChild").put(FIELD_PARENT_GROUP, IDS.getLong("group"))
                .put(GROUP_CONTEXT, new JsonObject().put(GROUP_AUTO_ASSIGN, NO));

        TestAPIUtil.post(GROUP_API_ENDPOINT, context,
                testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertCreateEntityTestResult(GroupConfigStore.getStore(), context, response.bodyAsJsonObject(), String.format(InfoMessageConstants.ENTITY_CREATED, Entity.GROUP.getName()), null, LOGGER, testInfo.getTestMethod().get().getName());

                            IDS.put("child.group", response.bodyAsJsonObject().getLong(ID));

                            testContext.completeNow();
                        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(4)
    void testCreateRoleHavingCreateUserSettingsPermission(VertxTestContext testContext, TestInfo testInfo)
    {
        var context = new JsonObject()
                .put(USER_ROLE_NAME, "test" + CURRENT_TIME_MILLIS).put(USER_ROLE_CONTEXT, new JsonArray().add("user-settings:read").add("user-settings:read-write").add("my-account-settings:read").add("audit-settings:read").add("my-account-settings:read-write"));

        TestAPIUtil.post(USER_ROLE_API_ENDPOINT, context,
                testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertCreateEntityTestResult(UserRoleConfigStore.getStore(), context, response.bodyAsJsonObject(), String.format(InfoMessageConstants.ENTITY_CREATED, Entity.USER_ROLE.getName()), null, LOGGER, testInfo.getTestMethod().get().getName());

                            IDS.put("user.role", response.bodyAsJsonObject().getLong(ID));

                            testContext.completeNow();
                        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(5)
    void testCreateRoleHavingUpdateAndDeleteUserSettingsPermission(VertxTestContext testContext, TestInfo testInfo)
    {
        var context = new JsonObject()
                .put(USER_ROLE_NAME, "Mind" + CURRENT_TIME_MILLIS).put(USER_ROLE_CONTEXT,
                        new JsonArray().add("user-settings:read").add("user-settings:delete").add("my-account-settings:read").add("my-account-settings:read-write"));

        TestAPIUtil.post(USER_ROLE_API_ENDPOINT, context,
                testContext.succeeding(response ->

                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertCreateEntityTestResult(UserRoleConfigStore.getStore(), context, response.bodyAsJsonObject(), String.format(InfoMessageConstants.ENTITY_CREATED, Entity.USER_ROLE.getName()), null, LOGGER, testInfo.getTestMethod().get().getName());

                            IDS.put("role.two", response.bodyAsJsonObject().getLong(ID));

                            testContext.completeNow();
                        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(6)
    void testGetPasswordPolicy(VertxTestContext testContext)
    {
        TestAPIUtil.get(PASSWORD_POLICY_API_ENDPOINT,
                testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            assertEquals(HttpStatus.SC_OK, response.statusCode());

                            var body = response.bodyAsJsonObject();

                            Assertions.assertNotNull(body);

                            assertEquals(HttpStatus.SC_OK, body.getInteger(RESPONSE_CODE));

                            var items = body.getJsonArray(RESULT);

                            Assertions.assertNotNull(items);

                            Assertions.assertFalse(items.isEmpty());

                            Assertions.assertEquals(1, items.size());

                            var policy = items.getJsonObject(0);

                            Assertions.assertEquals(YES, policy.getString(PasswordPolicy.PASSWORD_POLICY_EXPIRY));

                            Assertions.assertEquals(YES, policy.getString(PasswordPolicy.PASSWORD_POLICY_UPPERCASE_CHECK));

                            Assertions.assertEquals(YES, policy.getString(PasswordPolicy.PASSWORD_POLICY_LOWERCASE_CHECK));

                            Assertions.assertEquals(YES, policy.getString(PasswordPolicy.PASSWORD_POLICY_NUMBER_CHECK));

                            Assertions.assertEquals(YES, policy.getString(PasswordPolicy.PASSWORD_POLICY_SPECIAL_CHARACTER_CHECK));

                            assertEquals(15, policy.getInteger(EXPIRY_DAYS));

                            assertEquals(8, policy.getInteger(PasswordPolicy.PASSWORD_POLICY_MINIMUM_LENGTH));

                            IDS.put("password.policy", policy.getLong(ID));

                            testContext.completeNow();
                        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(7)
    void testUpdateIfPasswordPolicyDaysMoreThanSixtyDays(VertxTestContext testContext)
    {
        TestAPIUtil.put(PASSWORD_POLICY_API_ENDPOINT + "/" + IDS.getLong("password.policy"),
                new JsonObject().put(EXPIRY_DAYS, 62),
                testContext.succeeding(response ->

                        testContext.verify(() ->
                        {
                            Assertions.assertEquals("Password Expiry Days should have a value in the range [1,60]", response.bodyAsJsonObject().getString(MESSAGE));

                            assertEquals(SC_BAD_REQUEST, response.statusCode());

                            testContext.completeNow();
                        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(8)
    void testUpdatePasswordPolicyDays(VertxTestContext testContext, TestInfo testInfo)
    {
        var context = new JsonObject().put(EXPIRY_DAYS, 30);

        TestAPIUtil.put(PASSWORD_POLICY_API_ENDPOINT + "/" + IDS.getLong("password.policy"), context,
                testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertUpdateEntityTestResult(PasswordPolicyConfigStore.getStore(), context, response.bodyAsJsonObject(), String.format(InfoMessageConstants.ENTITY_UPDATED, "Password Policy"), LOGGER, testInfo.getTestMethod().get().getName());

                            testContext.completeNow();
                        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(9)
    void testUpdateLDAPUserRoleAndGroups(VertxTestContext testContext, TestInfo testInfo)
    {
        var context = new JsonObject().put(USER_GROUPS, new JsonArray().add(0L))
                .put(USER_ROLE, IDS.getLong("user.role"));

        TestAPIUtil.put(USER_API_ENDPOINT + IDS.getLong("ldap.user"), context,
                testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertUpdateEntityTestResult(UserConfigStore.getStore(), context, response.bodyAsJsonObject(), String.format(InfoMessageConstants.ENTITY_UPDATED, Entity.USER.getName()), LOGGER, testInfo.getTestMethod().get().getName());

                            testContext.completeNow();
                        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(10)
    void testResetLDAPUserPassword(VertxTestContext testContext)
    {
        TestAPIUtil.put(USER_API_ENDPOINT + IDS.getLong("ldap.user") + "/password",
                new JsonObject().put(USER_PASSWORD, "Mind@123")
                        .put(AUTH_REFRESH_TOKEN, TestUtil.getRefreshToken()),
                testContext.succeeding(response -> testContext.verify(() ->
                {
                    assertEquals(SC_BAD_REQUEST, response.statusCode());

                    assertEquals(ErrorCodes.ERROR_CODE_RESET_PASSWORD_LDAP_USER, response.bodyAsJsonObject().getString(ERROR_CODE));

                    Assertions.assertEquals(String.format(ErrorMessageConstants.RESET_PASSWORD_FAILED, ErrorMessageConstants.RESET_PASSWORD_LDAP_USER_ERROR), response.bodyAsJsonObject().getString(MESSAGE));

                    testContext.completeNow();

                })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(11)
    void testCreateNestedChildGroup(VertxTestContext testContext, TestInfo testInfo)
    {

        var firstNestedChild = new JsonObject()
                .put(FIELD_GROUP_NAME, "firstInnerChild").put(FIELD_PARENT_GROUP, IDS.getLong("child.group"))
                .put(GROUP_CONTEXT, new JsonObject().put(GROUP_AUTO_ASSIGN, NO));

        TestAPIUtil.post(GROUP_API_ENDPOINT, firstNestedChild,
                testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertCreateEntityTestResult(GroupConfigStore.getStore(), firstNestedChild, response.bodyAsJsonObject(), String.format(InfoMessageConstants.ENTITY_CREATED, Entity.GROUP.getName()), null, LOGGER, testInfo.getTestMethod().get().getName());

                            IDS.put("child.inner.group", response.bodyAsJsonObject().getLong(ID));

                            testContext.completeNow();
                        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(12)
    void testCreateUser(VertxTestContext testContext, TestInfo testInfo)
    {

        var context = USER1_CONTEXT.copy().put(USER_GROUPS, new JsonArray().add(DEFAULT_ID))
                .put(USER_ROLE, IDS.getLong("user.role"));

        TestAPIUtil.post(USER_API_ENDPOINT, context,
                testContext.succeeding(response ->

                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertCreateEntityTestResult(UserConfigStore.getStore(), context, response.bodyAsJsonObject(),
                                    String.format(InfoMessageConstants.ENTITY_CREATED, Entity.USER.getName()), new JsonArray().add(USER_PREFERENCES).add(USER_PASSWORD_LAST_UPDATED_TIME), LOGGER, testInfo.getTestMethod().get().getName());

                            IDS.put("user.id", response.bodyAsJsonObject().getLong(ID));

                            testContext.completeNow();
                        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(13)
    void testResetPasswordUsingSuperUser(VertxTestContext testContext, TestInfo testInfo)
    {
        var context = new JsonObject().put(USER_PASSWORD, "MindUser@123")
                .put(AUTH_REFRESH_TOKEN, TestUtil.getRefreshToken());

        TestAPIUtil.put(USER_API_ENDPOINT + IDS.getLong("user.id") + "/password", context,
                testContext.succeeding(response -> testContext.verify(() ->
                {
                    // will not get this refresh.token while we get from config store
                    context.remove(AUTH_REFRESH_TOKEN);

                    TestAPIUtil.assertUpdateEntityTestResult(UserConfigStore.getStore(), context, response.bodyAsJsonObject(),
                            String.format(InfoMessageConstants.RESET_PASSWORD_SUCCEEDED, UserConfigStore.getStore().getItem(IDS.getLong("user.id")).getString(USER_NAME)), LOGGER, testInfo.getTestMethod().get().getName());

                    testContext.completeNow();

                })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(14)
    void testResetPasswordInvalidPasswordPolicy(VertxTestContext testContext)
    {
        TestAPIUtil.put(USER_API_ENDPOINT + IDS.getLong("user.id") + "/password",
                new JsonObject().put(USER_PASSWORD, "Mind")
                        .put(AUTH_REFRESH_TOKEN, TestUtil.getRefreshToken()),
                testContext.succeeding(response -> testContext.verify(() ->
                {
                    assertEquals(SC_BAD_REQUEST, response.statusCode());

                    Assertions.assertEquals(PASSWORD_POLICY_NOT_SATISFIED, response.bodyAsJsonObject().getString(MESSAGE));

                    assertEquals(ErrorCodes.ERROR_CODE_PASSWORD_POLICY_NOT_SATISFIED, response.bodyAsJsonObject().getString(ERROR_CODE));

                    testContext.completeNow();

                })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(15)
    void testResetPasswordInvalidUser(VertxTestContext testContext)
    {
        TestAPIUtil.put(USER_API_ENDPOINT + 12345 + "/password",
                new JsonObject().put(USER_PASSWORD, "Mind")
                        .put(AUTH_REFRESH_TOKEN, TestUtil.getRefreshToken()),
                testContext.succeeding(response -> testContext.verify(() ->
                {
                    assertEquals(SC_BAD_REQUEST, response.statusCode());

                    Assertions.assertEquals(USER_NOT_FOUND, response.bodyAsJsonObject().getString(MESSAGE));

                    testContext.completeNow();

                })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(16)
    void testGetUser(VertxTestContext testContext, TestInfo testInfo)
    {
        TestAPIUtil.get(USER_API_ENDPOINT + IDS.getLong("user.id"), testContext.succeeding(response ->
                testContext.verify(() ->
                {
                    TestAPIUtil.assertGETRequestTestResult(response, IDS.getLong("user.id"), UserConfigStore.getStore(), new JsonArray().add(USER_PASSWORD), LOGGER, testInfo.getTestMethod().get().getName());

                    testContext.completeNow();

                })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(17)
    void testCreateDuplicateUser(VertxTestContext testContext)
    {
        var userBody = USER1_CONTEXT.copy().put(USER_GROUPS, new JsonArray().add(IDS.getLong("group")))
                .put(USER_ROLE, IDS.getLong("user.role"));

        TestAPIUtil.post(USER_API_ENDPOINT, userBody,
                testContext.succeeding(response ->

                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertAlreadyExistedEntityTestResult(response.bodyAsJsonObject(), String.format(ErrorMessageConstants.API_FIELD_UNIQUE_RULE, "User Name"), UserConfigStore.getStore(), USER_NAME, USER1_CONTEXT.getString(USER_NAME));

                            testContext.completeNow();
                        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(18)
    void testLoginUser1(VertxTestContext testContext)
    {
        TestAPIUtil.post("/api/v1/token", new JsonObject().put(USER_NAME, USER1_CONTEXT.getString(USER_NAME))
                        .put(USER_PASSWORD, USER1_CONTEXT.getString(USER_PASSWORD)),
                testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            assertEquals(SC_OK, response.statusCode());

                            var body = response.bodyAsJsonObject();

                            Assertions.assertNotNull(body);

                            Assertions.assertTrue(body.containsKey(AUTH_ACCESS_TOKEN));

                            Assertions.assertTrue(body.containsKey(AUTH_REFRESH_TOKEN));

                            Assertions.assertTrue(body.containsKey(SESSION_ID));

                            accessToken = "Bearer " + body.getString(AUTH_ACCESS_TOKEN);

                            refreshToken = body.getString(AUTH_REFRESH_TOKEN);

                            testContext.completeNow();

                        })));
    }

    @Disabled
    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(19)
    void testGetUsersInAuditUser1(VertxTestContext testContext)
    {
        TestAPIUtil.get(AUDIT_USER_API_ENDPOINT,
                new JsonObject().put(HttpHeaders.AUTHORIZATION.toString(), accessToken),
                testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            var result = response.bodyAsJsonObject();

                            assertEquals(SC_OK, response.statusCode());

                            assertEquals(SC_OK, result.getInteger(RESPONSE_CODE));

                            assertEquals(STATUS_SUCCEED, result.getString(STATUS));

                            assertNotEquals(0, result.getJsonArray(RESULT).size());

                            testContext.completeNow();
                        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(20)
    void testCreateUserValidPermission(VertxTestContext testContext, TestInfo testInfo)
    {
        var context = USER2_CONTEXT.copy();

        context.put(USER_GROUPS, new JsonArray()
                        .add(IDS.getLong("child.group"))
                        .add(IDS.getLong("group"))
                        .add(IDS.getLong("child.inner.group")))
                .put(USER_ROLE, IDS.getLong("role.two"));

        TestAPIUtil.post(USER_API_ENDPOINT, new JsonObject().put(HttpHeaders.AUTHORIZATION.toString(), accessToken), context,
                testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertCreateEntityTestResult(UserConfigStore.getStore(), context, response.bodyAsJsonObject(),
                                    String.format(InfoMessageConstants.ENTITY_CREATED, Entity.USER.getName()), new JsonArray().add(USER_PREFERENCES).add(USER_PASSWORD_LAST_UPDATED_TIME), LOGGER, testInfo.getTestMethod().get().getName());

                            IDS.put("test.user", response.bodyAsJsonObject().getLong(ID));

                            testContext.completeNow();
                        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(21)
    void testUpdateUserValidPermission(VertxTestContext testContext, TestInfo testInfo)
    {
        var context = new JsonObject().put(USER_MOBILE, 1234567890);

        TestAPIUtil.put(USER_API_ENDPOINT + IDS.getLong("test.user"),
                new JsonObject().put(HttpHeaders.AUTHORIZATION.toString(), accessToken),
                context,
                testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertUpdateEntityTestResult(UserConfigStore.getStore(), context, response.bodyAsJsonObject(), String.format(InfoMessageConstants.ENTITY_UPDATED, Entity.USER.getName()), LOGGER, testInfo.getTestMethod().get().getName());

                            testContext.completeNow();
                        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(22)
    void testDeleteUserInvalidPermission(VertxTestContext testContext)
    {
        TestAPIUtil.delete(USER_API_ENDPOINT + IDS.getLong("test.user"), new JsonObject().put(HttpHeaders.AUTHORIZATION.toString(), accessToken),
                testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertForbiddenRequestTestResult(response, "User settings Delete");

                            testContext.completeNow();
                        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(23)
    void testLoginUser2(VertxTestContext testContext)
    {
        TestAPIUtil.post("/api/v1/token", new JsonObject().put(USER_NAME, USER2_CONTEXT.getString(USER_NAME))
                        .put(USER_PASSWORD, USER2_CONTEXT.getString(USER_PASSWORD)),
                testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            assertEquals(SC_OK, response.statusCode());

                            var body = response.bodyAsJsonObject();

                            Assertions.assertNotNull(body);

                            Assertions.assertTrue(body.containsKey(AUTH_ACCESS_TOKEN));

                            Assertions.assertTrue(body.containsKey(AUTH_REFRESH_TOKEN));

                            Assertions.assertTrue(body.containsKey(SESSION_ID));

                            accessToken = "Bearer " + body.getString(AUTH_ACCESS_TOKEN);

                            refreshToken = body.getString(AUTH_REFRESH_TOKEN);

                            testContext.completeNow();

                        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(24)
    void testOnlyAssignedRoleDisplayedIfNotAdminUser(VertxTestContext testContext)
    {
        TestAPIUtil.get(USER_ROLE_API_ENDPOINT,
                new JsonObject().put(HttpHeaders.AUTHORIZATION.toString(), accessToken), // access token of user2 used
                testContext.succeeding(response -> testContext.verify(() ->
                {
                    var items = response.bodyAsJsonObject().getJsonArray(RESULT);

                    Assertions.assertEquals(1, items.size());

                    Assertions.assertTrue(items
                            .stream()
                            .map(x -> ((JsonObject) x).getLong(ID))
                            .anyMatch(id -> !id.equals(DEFAULT_ID)));

                    testContext.completeNow();
                })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(24)
    void testUpdateUserInvalidPermission(VertxTestContext testContext)
    {
        TestAPIUtil.put(USER_API_ENDPOINT + IDS.getLong("test.user"),
                new JsonObject().put(HttpHeaders.AUTHORIZATION.toString(), accessToken),
                new JsonObject().put(USER_NAME, "adminUser"),
                testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertForbiddenRequestTestResult(response, "User settings Read-write");

                            testContext.completeNow();
                        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(25)
    void testCreateUserInvalidPermission(VertxTestContext testContext)
    {
        TestAPIUtil.post(USER_API_ENDPOINT, new JsonObject().put(HttpHeaders.AUTHORIZATION.toString(), accessToken),
                USER2_CONTEXT.put(USER_NAME, "test" + CURRENT_TIME_MILLIS),
                testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertForbiddenRequestTestResult(response, "User settings Read-write");

                            testContext.completeNow();
                        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(26)
    void testUpdateUser(VertxTestContext testContext, TestInfo testInfo)
    {
        var context = new JsonObject().put(USER_NAME, "user1");

        TestAPIUtil.put(USER_API_ENDPOINT + IDS.getLong("user.id"), context,

                testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertUpdateEntityTestResult(UserConfigStore.getStore(), context, response.bodyAsJsonObject(), String.format(InfoMessageConstants.ENTITY_UPDATED, Entity.USER.getName()), LOGGER, testInfo.getTestMethod().get().getName());

                            testContext.completeNow();
                        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(27)
    void testGetUserRoleReference(VertxTestContext testContext)
    {
        TestAPIUtil.get(USER_ROLE_API_ENDPOINT + IDS.getLong("user.role") + "/references", testContext.succeeding(response -> testContext.verify(() ->
        {
            TestAPIUtil.assertEntityReferenceTestResult(response, Entity.USER.getName(), 2);

            testContext.completeNow();

        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(29)
    void testGetUserRoleUsedCount(VertxTestContext testContext, TestInfo testInfo)
    {
        TestAPIUtil.get(USER_ROLE_API_ENDPOINT, testContext.succeeding(response ->
                testContext.verify(() ->
                {
                    TestAPIUtil.assertEntityUsedCountTestResult(response, IDS.getLong("user.role"), 2, LOGGER, testInfo.getTestMethod().get().getName());

                    testContext.completeNow();
                })));

    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(30)
    void testDeleteUserRoleEntityInUsed(VertxTestContext testContext)
    {
        TestAPIUtil.delete(USER_ROLE_API_ENDPOINT + IDS.getLong("user.role"), testContext.succeeding(response -> testContext.verify(() ->
        {
            TestAPIUtil.assertEntityInUsedDeleteTestResult(response, Entity.USER_ROLE.getName());

            testContext.completeNow();

        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(31)
    void testUpdateUserInvalidPassword(VertxTestContext testContext)
    {
        var context = USER2_CONTEXT.copy().put(User.USER_OLD_PASSWORD, USER2_CONTEXT.getString(USER_PASSWORD) + "test").put("user.password", "MindUser@#123");

        TestAPIUtil.put(USER_API_ENDPOINT + IDS.getLong("test.user"), context,
                testContext.succeeding(response ->

                        testContext.verify(() ->
                        {
                            assertEquals(SC_BAD_REQUEST, response.statusCode());

                            Assertions.assertEquals(INCORRECT_OLD_PASSWORD, response.bodyAsJsonObject().getString(MESSAGE));

                            testContext.completeNow();

                        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(32)
    void testUpdateUserValidPassword(VertxTestContext testContext, TestInfo testInfo)
    {
        var context = USER2_CONTEXT.copy().put(User.USER_OLD_PASSWORD, USER2_CONTEXT.getString(USER_PASSWORD)).put("user.password", "MindUser@#123");

        TestAPIUtil.put(USER_API_ENDPOINT + IDS.getLong("test.user"), context,
                testContext.succeeding(response ->

                        testContext.verify(() ->
                        {
                            // this is just for API Use config store not having this
                            context.remove(USER_OLD_PASSWORD);

                            TestAPIUtil.assertUpdateEntityTestResult(UserConfigStore.getStore(), context, response.bodyAsJsonObject(), String.format(InfoMessageConstants.ENTITY_UPDATED, Entity.USER.getName()), LOGGER, testInfo.getTestMethod().get().getName());

                            TestAPIUtil.post("/api/v1/token", new JsonObject().put(USER_NAME, USER2_CONTEXT.getString(USER_NAME))
                                            .put(USER_PASSWORD, "MindUser@#123"),
                                    testContext.succeeding(httpResponse ->
                                            testContext.verify(() ->
                                            {
                                                assertEquals(SC_OK, httpResponse.statusCode());

                                                var body = httpResponse.bodyAsJsonObject();

                                                Assertions.assertNotNull(body);

                                                Assertions.assertTrue(body.containsKey(AUTH_ACCESS_TOKEN));

                                                Assertions.assertTrue(body.containsKey(AUTH_REFRESH_TOKEN));

                                                Assertions.assertTrue(body.containsKey(SESSION_ID));

                                                testContext.completeNow();

                                            })));
                        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(33)
    void testDeleteUser(VertxTestContext testContext)
    {

        TestAPIUtil.delete(USER_API_ENDPOINT + IDS.getLong("test.user"),
                testContext.succeeding(response ->

                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertDeleteEntityTestResult(UserConfigStore.getStore(), response.bodyAsJsonObject(),
                                    String.format(InfoMessageConstants.ENTITY_DELETED, Entity.USER.getName()));

                            testContext.completeNow();
                        })));

    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(34)
    void testDeleteNotExistUser(VertxTestContext testContext)
    {
        TestAPIUtil.delete(USER_API_ENDPOINT + IDS.getLong("test.user"),
                testContext.succeeding(response ->

                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertNotExistEntityDeleteTestResult(response, Entity.USER.getName(), String.format(ErrorMessageConstants.ITEM_NOT_FOUND_IN_STORE, Entity.USER.getName()));

                            testContext.completeNow();
                        })));

    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(35)
    void testGetAllUserRole(VertxTestContext context)
    {
        TestAPIUtil.get(USER_ROLE_API_ENDPOINT, context.succeeding(response -> context.verify(() ->
        {
            TestAPIUtil.assertGETAllRequestTestResult(response, UserRoleConfigStore.getStore(), null);

            context.completeNow();

        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(36)
    void testCreateDuplicateUserRole(VertxTestContext testContext)
    {
        var context = new JsonObject()
                .put(USER_ROLE_NAME, "test" + CURRENT_TIME_MILLIS).put(USER_ROLE_CONTEXT, new JsonArray().add("user-settings:read").add("user-settings:read-write").add("my-account-settings:read-write"));

        TestAPIUtil.post(USER_ROLE_API_ENDPOINT, context,
                testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertAlreadyExistedEntityTestResult(response.bodyAsJsonObject(), String.format(ErrorMessageConstants.API_FIELD_UNIQUE_RULE, "Role Name"),
                                    UserRoleConfigStore.getStore(), USER_ROLE_NAME, context.getString(USER_ROLE_NAME));

                            testContext.completeNow();
                        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(37)
    void testGetUserRole(VertxTestContext testContext, TestInfo testInfo)
    {
        TestAPIUtil.get(USER_ROLE_API_ENDPOINT + IDS.getLong("user.role"), testContext.succeeding(response ->
                testContext.verify(() ->
                {
                    TestAPIUtil.assertGETRequestTestResult(response, IDS.getLong("user.role"), UserRoleConfigStore.getStore(), null, LOGGER, testInfo.getTestMethod().get().getName());

                    testContext.completeNow();

                })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(38)
    void testUpdateUserRole(VertxTestContext testContext, TestInfo testInfo)
    {
        var context = new JsonObject()
                .put(USER_ROLE_NAME, "test1update" + CURRENT_TIME_MILLIS).put(USER_ROLE_CONTEXT, new JsonArray().add("user-settings:read").add("user-settings:delete").add("group-settings:read").add("my-account-settings:read-write"));

        TestAPIUtil.put(USER_ROLE_API_ENDPOINT + IDS.getLong("user.role"), context,
                testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertUpdateEntityTestResult(UserRoleConfigStore.getStore(), context, response.bodyAsJsonObject(),
                                    String.format(InfoMessageConstants.ENTITY_UPDATED, Entity.USER_ROLE.getName()), LOGGER, testInfo.getTestMethod().get().getName());

                            testContext.completeNow();

                        })));

    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(39)
    void testCreateRole(VertxTestContext testContext, TestInfo testInfo)
    {
        var context = new JsonObject()
                .put(USER_ROLE_NAME, "test2").put(USER_ROLE_CONTEXT, new JsonArray().add("user-settings:read").add("user-settings:read-write").add("my-account-settings:read").add("my-account-settings:read-write"));

        TestAPIUtil.post(USER_ROLE_API_ENDPOINT, context,
                testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertCreateEntityTestResult(UserRoleConfigStore.getStore(), context, response.bodyAsJsonObject(), String.format(InfoMessageConstants.ENTITY_CREATED, Entity.USER_ROLE.getName()), null, LOGGER, testInfo.getTestMethod().get().getName());

                            IDS.put("user.role2", response.bodyAsJsonObject().getLong(ID));

                            testContext.completeNow();
                        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(40)
    void testCreateRoleWithoutUserSettingsViewPermission(VertxTestContext testContext, TestInfo testInfo)
    {
        var context = new JsonObject()
                .put(USER_ROLE_NAME, "CreateRoleWithoutUserSettingsViewPermission" + CURRENT_TIME_MILLIS).put(USER_ROLE_CONTEXT, new JsonArray().add("audit-settings:read").add("my-account-settings:read").add("my-account-settings:read-write"));

        TestAPIUtil.post(USER_ROLE_API_ENDPOINT, context,
                testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertCreateEntityTestResult(UserRoleConfigStore.getStore(), context, response.bodyAsJsonObject(), String.format(InfoMessageConstants.ENTITY_CREATED, Entity.USER_ROLE.getName()), null, LOGGER, testInfo.getTestMethod().get().getName());

                            IDS.put("user.role.4", response.bodyAsJsonObject().getLong(ID));

                            testContext.completeNow();
                        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(41)
    void testCreateUserWithRoleWithoutUserSettingsViewPermission(VertxTestContext testContext, TestInfo testInfo)
    {

        var context = USER4_CONTEXT.copy().put(USER_GROUPS, new JsonArray().add(0L))
                .put(USER_ROLE, IDS.getLong("user.role.4"));

        TestAPIUtil.post(USER_API_ENDPOINT, context,
                testContext.succeeding(response ->

                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertCreateEntityTestResult(UserConfigStore.getStore(), context, response.bodyAsJsonObject(),
                                    String.format(InfoMessageConstants.ENTITY_CREATED, Entity.USER.getName()), new JsonArray().add(USER_PREFERENCES).add(USER_PASSWORD_LAST_UPDATED_TIME), LOGGER, testInfo.getTestMethod().get().getName());

                            IDS.put("user.4", response.bodyAsJsonObject().getLong(ID));

                            testContext.completeNow();
                        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(42)
    void testLoginUser4(VertxTestContext testContext)
    {
        TestAPIUtil.post("/api/v1/token", new JsonObject().put(USER_NAME, USER4_CONTEXT.getString(USER_NAME))
                        .put(USER_PASSWORD, USER4_CONTEXT.getString(USER_PASSWORD)),
                testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            assertEquals(SC_OK, response.statusCode());

                            var body = response.bodyAsJsonObject();

                            Assertions.assertNotNull(body);

                            Assertions.assertTrue(body.containsKey(AUTH_ACCESS_TOKEN));

                            Assertions.assertTrue(body.containsKey(AUTH_REFRESH_TOKEN));

                            Assertions.assertTrue(body.containsKey(SESSION_ID));

                            accessToken = "Bearer " + body.getString(AUTH_ACCESS_TOKEN);

                            refreshToken = body.getString(AUTH_REFRESH_TOKEN);

                            testContext.completeNow();

                        })));
    }

    @Disabled
    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(43)
    void testGetUsersInAuditUser4(VertxTestContext testContext)
    {
        TestAPIUtil.get(AUDIT_USER_API_ENDPOINT,
                new JsonObject().put(HttpHeaders.AUTHORIZATION.toString(), accessToken),
                testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            var result = response.bodyAsJsonObject();

                            assertEquals(SC_OK, response.statusCode());

                            assertEquals(SC_OK, result.getInteger(RESPONSE_CODE));

                            assertEquals(STATUS_SUCCEED, result.getString(STATUS));

                            assertEquals(new JsonArray(), result.getJsonArray(RESULT));

                            testContext.completeNow();
                        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(44)
    void testDeletedUserLogin(VertxTestContext testContext, TestInfo testInfo)
    {
        TestAPIUtil.post(USER_API_ENDPOINT, USER3_CONTEXT,
                testContext.succeeding(response ->

                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertCreateEntityTestResult(UserConfigStore.getStore(), USER3_CONTEXT, response.bodyAsJsonObject(),
                                    String.format(InfoMessageConstants.ENTITY_CREATED, Entity.USER.getName()), new JsonArray().add(USER_PREFERENCES).add(USER_PASSWORD_LAST_UPDATED_TIME), LOGGER, testInfo.getTestMethod().get().getName());

                            TestAPIUtil.delete(USER_API_ENDPOINT + response.bodyAsJsonObject().getLong(ID),
                                    testContext.succeeding(result ->

                                            testContext.verify(() ->
                                            {
                                                TestAPIUtil.assertDeleteEntityTestResult(UserConfigStore.getStore(), result.bodyAsJsonObject(),
                                                        String.format(InfoMessageConstants.ENTITY_DELETED, Entity.USER.getName()));

                                                TestAPIUtil.create(ACCESS_TOKEN_API_ENDPOINT, new JsonObject().put(USER_NAME, "user3").put(USER_PASSWORD, "Mind@123"),
                                                        testContext.succeeding(httpResponse ->
                                                                testContext.verify(() ->
                                                                {
                                                                    assertEquals(LOGIN_FAILED_USER_NOT_FOUND, httpResponse.bodyAsJsonObject().getString(MESSAGE));

                                                                    testContext.completeNow();

                                                                })));
                                            })));

                        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(45)
    void testCreateUserForDataSecurity(VertxTestContext testContext, TestInfo testInfo)
    {
        var context = USER_DATA_SECURITY_CONTEXT.copy()
                .put(USER_ROLE, IDS.getLong("user.role2"));

        TestAPIUtil.post(USER_API_ENDPOINT, context,
                testContext.succeeding(response ->

                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertCreateEntityTestResult(UserConfigStore.getStore(), context, response.bodyAsJsonObject(),
                                    String.format(InfoMessageConstants.ENTITY_CREATED, Entity.USER.getName()), new JsonArray().add(USER_PREFERENCES).add(USER_PASSWORD_LAST_UPDATED_TIME), LOGGER, testInfo.getTestMethod().get().getName());

                            IDS.put("user.id", response.bodyAsJsonObject().getLong(ID));

                            testContext.completeNow();
                        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(46)
    void TestChangePassword(VertxTestContext testContext, TestInfo testInfo)
    {

        var context = VAPT_USER_CONTEXT.copy().put(USER_GROUPS, new JsonArray().add(DEFAULT_ID))
                .put(USER_ROLE, IDS.getLong("user.role"));

        TestAPIUtil.post(USER_API_ENDPOINT, context,
                testContext.succeeding(response ->

                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertCreateEntityTestResult(UserConfigStore.getStore(), context, response.bodyAsJsonObject(),
                                    String.format(InfoMessageConstants.ENTITY_CREATED, Entity.USER.getName()), new JsonArray().add(USER_PREFERENCES).add(USER_PASSWORD_LAST_UPDATED_TIME), LOGGER, testInfo.getTestMethod().get().getName());

                            IDS.put("user.id", response.bodyAsJsonObject().getLong(ID));

                            TestAPIUtil.post("/api/v1/token", new JsonObject().put(USER_NAME, VAPT_USER_CONTEXT.getString(USER_NAME))
                                            .put(USER_PASSWORD, VAPT_USER_CONTEXT.getString(USER_PASSWORD)),
                                    testContext.succeeding(httpResponse ->
                                            testContext.verify(() ->
                                            {
                                                assertEquals(SC_OK, httpResponse.statusCode());

                                                var result = httpResponse.bodyAsJsonObject();

                                                Assertions.assertNotNull(result);

                                                Assertions.assertTrue(result.containsKey(AUTH_ACCESS_TOKEN));

                                                Assertions.assertTrue(result.containsKey(AUTH_REFRESH_TOKEN));

                                                Assertions.assertTrue(result.containsKey(SESSION_ID));

                                                accessToken = "Bearer " + result.getString(AUTH_ACCESS_TOKEN);

                                                refreshToken = result.getString(AUTH_REFRESH_TOKEN);

                                                Assertions.assertNotNull(AuthTokenConfigStore.getStore()
                                                        .getItemByValue(USER_ID, UserConfigStore.getStore().getItemByValue(USER_NAME, VAPT_USER_CONTEXT.getString(USER_NAME)).getLong(ID)));

                                                var changePasswordContext = new JsonObject().put(USER_PASSWORD, "MindUser@1234")
                                                        .put(AUTH_REFRESH_TOKEN, TestUtil.getRefreshToken());

                                                TestAPIUtil.put(USER_API_ENDPOINT + UserConfigStore.getStore().getItemByValue(USER_NAME,
                                                                VAPT_USER_CONTEXT.getString(USER_NAME)).getLong(ID) + "/password", changePasswordContext,
                                                        testContext.succeeding(changePasswordResponse -> testContext.verify(() ->
                                                        {
                                                            changePasswordContext.remove(AUTH_REFRESH_TOKEN);

                                                            TestAPIUtil.assertUpdateEntityTestResult(UserConfigStore.getStore(), changePasswordContext, changePasswordResponse.bodyAsJsonObject(),
                                                                    String.format(InfoMessageConstants.RESET_PASSWORD_SUCCEEDED,
                                                                            UserConfigStore.getStore().getItem(IDS.getLong("user.id")).getString(USER_NAME)), LOGGER, testInfo.getTestMethod().get().getName());

                                                            var retries = new AtomicInteger(0);

                                                            TestUtil.vertx().setPeriodic(5000, timer ->
                                                            {

                                                                try
                                                                {
                                                                    if (retries.incrementAndGet() > 3)
                                                                    {
                                                                        TestUtil.vertx().cancelTimer(timer);

                                                                        testContext.failNow(testInfo.getDisplayName() + " timed out");
                                                                    }
                                                                    else
                                                                    {
                                                                        if (AuthTokenConfigStore.getStore().getItemByValue(USER_ID, UserConfigStore.getStore().getItemByValue(USER_NAME, VAPT_USER_CONTEXT.getString(USER_NAME)).getLong(ID)) == null && AuthTokenConfigStore.getStore().getItemByValue(USER_ID, DEFAULT_ID) != null)
                                                                        {
                                                                            TestUtil.vertx().cancelTimer(timer);

                                                                            testContext.completeNow();
                                                                        }
                                                                    }
                                                                }
                                                                catch (Exception exception)
                                                                {
                                                                    TestUtil.vertx().cancelTimer(timer);

                                                                    LOGGER.error(exception);
                                                                }
                                                            });

                                                        })));

                                            })));
                        })));


    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(47)
    void TestDisableUser(VertxTestContext testContext, TestInfo testInfo)
    {

        TestAPIUtil.post("/api/v1/token", new JsonObject().put(USER_NAME, VAPT_USER_CONTEXT.getString(USER_NAME))
                        .put(USER_PASSWORD, "MindUser@1234"),
                testContext.succeeding(loginResponse ->
                        testContext.verify(() ->
                        {
                            assertEquals(SC_OK, loginResponse.statusCode());

                            Assertions.assertNotNull(AuthTokenConfigStore.getStore()
                                    .getItemByValue(USER_ID, UserConfigStore.getStore().getItemByValue(USER_NAME, VAPT_USER_CONTEXT.getString(USER_NAME)).getLong(ID)));

                            TestAPIUtil.put(USER_API_ENDPOINT + UserConfigStore.getStore().getItemByValue(USER_NAME, VAPT_USER_CONTEXT.getString(USER_NAME)).getLong(ID),
                                    new JsonObject().put(USER_STATUS, NO),
                                    testContext.succeeding(response ->
                                            testContext.verify(() ->
                                            {
                                                var retries = new AtomicInteger(0);

                                                TestUtil.vertx().setPeriodic(5000, timer ->
                                                {

                                                    try
                                                    {
                                                        if (retries.incrementAndGet() > 3)
                                                        {
                                                            TestUtil.vertx().cancelTimer(timer);

                                                            testContext.failNow(testInfo.getDisplayName() + " timed out");
                                                        }
                                                        else
                                                        {
                                                            if (AuthTokenConfigStore.getStore().getItemByValue(USER_ID, UserConfigStore.getStore().getItemByValue(USER_NAME, VAPT_USER_CONTEXT.getString(USER_NAME)).getLong(ID)) == null && AuthTokenConfigStore.getStore().getItemByValue(USER_ID, DEFAULT_ID) != null)
                                                            {
                                                                TestUtil.vertx().cancelTimer(timer);

                                                                testContext.completeNow();
                                                            }
                                                        }
                                                    }
                                                    catch (Exception exception)
                                                    {
                                                        TestUtil.vertx().cancelTimer(timer);

                                                        LOGGER.error(exception);
                                                    }
                                                });
                                            })));
                        })
                )
        );
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(48)
    void testBulkUpdateRole(VertxTestContext testContext, TestInfo testInfo)
    {
        LOGGER.info("Running test case " + testInfo.getTestMethod().get().getName());

        var context = USER4_CONTEXT.copy().put(USER_NAME, "chandresh1");

        var futures = new ArrayList<Future<Void>>();

        var promise = Promise.<Void>promise();

        futures.add(promise.future());

        TestAPIUtil.post(USER_API_ENDPOINT, context,
                testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertCreateEntityTestResult(UserConfigStore.getStore(), context, response.bodyAsJsonObject(),
                                    String.format(InfoMessageConstants.ENTITY_CREATED, Entity.USER.getName()), new JsonArray().add(USER_PREFERENCES).add(USER_PASSWORD_LAST_UPDATED_TIME), LOGGER, testInfo.getTestMethod().get().getName());

                            IDS.put("user.5", response.bodyAsJsonObject().getLong(ID));

                            promise.complete();
                        })));

        var updatedContext = USER4_CONTEXT.copy().put(USER_NAME, "chandresh2");

        var updatePromise = Promise.<Void>promise();

        futures.add(updatePromise.future());

        TestAPIUtil.post(USER_API_ENDPOINT, updatedContext,
                testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertCreateEntityTestResult(UserConfigStore.getStore(), updatedContext, response.bodyAsJsonObject(),
                                    String.format(InfoMessageConstants.ENTITY_CREATED, Entity.USER.getName()), new JsonArray().add(USER_PREFERENCES).add(USER_PASSWORD_LAST_UPDATED_TIME), LOGGER, testInfo.getTestMethod().get().getName());

                            IDS.put("user.6", response.bodyAsJsonObject().getLong(ID));

                            updatePromise.complete();
                        })));

        Future.all(futures).onComplete(result ->
        {
            if (result.succeeded())
            {
                var updateContext = new JsonObject().put("ids", new JsonArray().add(IDS.getLong("user.5")).add(IDS.getLong("user.6"))).put("user.role", 10000000000001L);

                TestAPIUtil.post(USER_API_ENDPOINT + "update", updateContext, asyncResult ->
                {
                    if (asyncResult.succeeded())
                    {
                        var response = asyncResult.result().bodyAsJsonObject();

                        assertEquals(HttpStatus.SC_OK, response.getInteger(APIConstants.RESPONSE_CODE));

                        assertEquals(10000000000001L, UserConfigStore.getStore().getItem(IDS.getLong("user.5")).getLong(USER_ROLE));

                        assertEquals(10000000000001L, UserConfigStore.getStore().getItem(IDS.getLong("user.6")).getLong(USER_ROLE));

                        testContext.completeNow();
                    }
                    else
                    {
                        testContext.failNow(asyncResult.cause());
                    }
                });
            }
            else
            {
                testContext.failNow(result.cause());
            }
        });
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(49)
    void testBulkUpdateGroup(VertxTestContext testContext, TestInfo testInfo)
    {
        LOGGER.info("Running test case " + testInfo.getTestMethod().get().getName());

        if (IDS.containsKey("user.5") && IDS.containsKey("user.6"))
        {
            var context = new JsonObject().put("ids", new JsonArray().add(IDS.getLong("user.5")).add(IDS.getLong("user.6"))).put(USER_GROUPS, new JsonArray().add(10000000000001L).add(10000000000002L));

            TestAPIUtil.post(USER_API_ENDPOINT + "update", context, result ->
            {
                if (result.succeeded())
                {
                    var response = result.result().bodyAsJsonObject();

                    assertEquals(HttpStatus.SC_OK, response.getInteger(APIConstants.RESPONSE_CODE));

                    assertTrue(UserConfigStore.getStore().getItem(IDS.getLong("user.5")).getJsonArray(USER_GROUPS).contains(10000000000001L));

                    assertTrue(UserConfigStore.getStore().getItem(IDS.getLong("user.5")).getJsonArray(USER_GROUPS).contains(10000000000002L));

                    assertTrue(UserConfigStore.getStore().getItem(IDS.getLong("user.6")).getJsonArray(USER_GROUPS).contains(10000000000001L));

                    assertTrue(UserConfigStore.getStore().getItem(IDS.getLong("user.6")).getJsonArray(USER_GROUPS).contains(10000000000002L));

                    testContext.completeNow();
                }
                else
                {
                    testContext.failNow(result.cause());
                }
            });
        }
        else
        {
            testContext.failNow("users not created!");
        }
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(50)
    void testBulkDelete(VertxTestContext testContext, TestInfo testInfo)
    {
        LOGGER.info("Running test case " + testInfo.getTestMethod().get().getName());

        if (IDS.containsKey("user.5") && IDS.containsKey("user.6"))
        {
            var context = new JsonObject().put("ids", new JsonArray().add(IDS.getLong("user.5")).add(IDS.getLong("user.6")));

            TestAPIUtil.deleteAll(USER_API_ENDPOINT, context, result ->
            {
                if (result.succeeded())
                {
                    var response = result.result().bodyAsJsonObject();

                    assertEquals(HttpStatus.SC_OK, response.getInteger(APIConstants.RESPONSE_CODE));

                    assertNull(UserConfigStore.getStore().getItem(IDS.getLong("user.5")));

                    assertNull(UserConfigStore.getStore().getItem(IDS.getLong("user.6")));

                    testContext.completeNow();
                }
                else
                {
                    testContext.failNow(result.cause());
                }
            });
        }
        else
        {
            testContext.failNow("users not created!");
        }
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(51)
    void testLoginUserSettingsReadOnlyPermissionUser(VertxTestContext testContext)
    {
        TestAPIUtil.post("/api/v1/token", new JsonObject().put(USER_NAME, USER1_CONTEXT.getString(USER_NAME))
                        .put(USER_PASSWORD, USER1_CONTEXT.getString(USER_PASSWORD)),
                testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            assertEquals(SC_OK, response.statusCode());

                            var result = response.bodyAsJsonObject();

                            Assertions.assertNotNull(result);

                            Assertions.assertTrue(result.containsKey(AUTH_ACCESS_TOKEN));

                            Assertions.assertTrue(result.containsKey(AUTH_REFRESH_TOKEN));

                            Assertions.assertTrue(result.containsKey(SESSION_ID));

                            accessToken = "Bearer " + result.getString(AUTH_ACCESS_TOKEN);

                            refreshToken = result.getString(AUTH_REFRESH_TOKEN);

                            testContext.completeNow();

                        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(52)
    void testUserSettingsReadOnlyPermissionGetUsers(VertxTestContext testContext, TestInfo testInfo)
    {
        LOGGER.info("Running test case " + testInfo.getTestMethod().get().getName());

        TestAPIUtil.get(USER_API_ENDPOINT,
                new JsonObject().put(HttpHeaders.AUTHORIZATION.toString(), accessToken),
                testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            var result = response.bodyAsJsonObject();

                            LOGGER.trace(String.format("response : %s", result));

                            assertEquals(SC_OK, response.statusCode());

                            assertEquals(STATUS_SUCCEED, result.getString(STATUS));

                            assertEquals(1, result.getJsonArray(RESULT).size());

                            testContext.completeNow();
                        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(53)
    void testGetUserSettingsReadOnlyPermissionUserRoleReference(VertxTestContext testContext)
    {
        TestAPIUtil.get(USER_ROLE_API_ENDPOINT + IDS.getLong("user.role") + "/references",
                new JsonObject().put(HttpHeaders.AUTHORIZATION.toString(), accessToken),
                testContext.succeeding(response -> testContext.verify(() ->
                {
                    TestAPIUtil.assertEntityReferenceTestResult(response, Entity.USER.getName(), 1);

                    testContext.completeNow();

                })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(54)
    void testGetUserSettingsReadOnlyPermissionGroupReference(VertxTestContext testContext)
    {
        TestAPIUtil.get(GROUP_API_ENDPOINT + DEFAULT_ID + "/references",
                new JsonObject().put(HttpHeaders.AUTHORIZATION.toString(), accessToken),
                testContext.succeeding(response -> testContext.verify(() ->
                {
                    TestAPIUtil.assertEntityReferenceTestResult(response, Entity.USER.getName(), 1);

                    testContext.completeNow();

                })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(56)
    void testCreateUserForDataSecurityMinimalAccess(VertxTestContext testContext, TestInfo testInfo)
    {
        var context = MINIMAL_ACCESS_USER.copy();

        TestAPIUtil.post(USER_API_ENDPOINT, context,
                testContext.succeeding(response ->

                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertCreateEntityTestResult(UserConfigStore.getStore(), context, response.bodyAsJsonObject(),
                                    String.format(InfoMessageConstants.ENTITY_CREATED, Entity.USER.getName()), new JsonArray().add(USER_PREFERENCES).add(USER_PASSWORD_LAST_UPDATED_TIME), LOGGER, testInfo.getTestMethod().get().getName());

                            testContext.completeNow();
                        })));
    }

}
