/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

package com.mindarray.api;

import com.mindarray.Bootstrap;
import com.mindarray.GlobalConstants;
import com.mindarray.InfoMessageConstants;
import com.mindarray.TestAPIUtil;
import com.mindarray.aiops.AIOpsConstants;
import com.mindarray.datastore.DatastoreConstants;
import com.mindarray.eventbus.EventBusConstants;
import com.mindarray.log.LogEngineConstants;
import com.mindarray.nms.NMSConstants;
import com.mindarray.store.ObjectConfigStore;
import com.mindarray.store.TemplateConfigStore;
import com.mindarray.util.CommonUtil;
import com.mindarray.util.Logger;
import com.mindarray.visualization.VisualizationConstants;
import io.github.artsok.RepeatedIfExceptionsTest;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import io.vertx.junit5.Timeout;
import io.vertx.junit5.VertxExtension;
import io.vertx.junit5.VertxTestContext;
import org.junit.jupiter.api.*;
import org.junit.jupiter.api.condition.EnabledIfSystemProperty;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.api.parallel.Execution;
import org.junit.jupiter.api.parallel.ExecutionMode;

import java.util.concurrent.TimeUnit;

import static com.mindarray.GlobalConstants.*;
import static com.mindarray.TestAPIConstants.VISUALIZATION_TEMPLATE_API_ENDPOINT;
import static com.mindarray.api.Template.TEMPLATE_NAME;
import static com.mindarray.eventbus.EventBusConstants.*;
import static com.mindarray.eventbus.EventBusConstants.ChangeNotificationType.ADD_EVENT_SOURCE;
import static com.mindarray.eventbus.EventBusConstants.ChangeNotificationType.DELETE_EVENT_SOURCE;
import static org.apache.http.HttpStatus.SC_OK;
import static org.junit.jupiter.api.Assertions.*;

@Timeout(60 * 1000)
@ExtendWith(VertxExtension.class)
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
@Execution(ExecutionMode.SAME_THREAD)
@EnabledIfSystemProperty(named = "test.mode", matches = "^APP|^PRIMARY$")
public class TestTemplate
{

    private static final Logger LOGGER = new Logger(TestTemplate.class, MOTADATA_API, "Test Template");
    private static long templateId;
    private static long entityID;

    @BeforeAll
    static void beforeAll(VertxTestContext testContext) throws InterruptedException
    {
        var item = ObjectConfigStore.getStore().getItem(ObjectConfigStore.getStore().getItemsByCategory(NMSConstants.Category.NETWORK).getLong(0));

        entityID = item.getLong(ID);

        LOGGER.info("Going to add event sources for IP :: " + item.getString(AIOpsObject.OBJECT_IP) + " | item :: " + item.encode());

        Bootstrap.vertx().eventBus().send(EVENT_CHANGE_LOCAL_NOTIFICATION, new JsonObject().put(EventBusConstants.EVENT_SOURCE, item.getString(AIOpsObject.OBJECT_IP)).put(EventBusConstants.EVENT, EVENT_LOG).put(GlobalConstants.PLUGIN_ID, 500005).put(LogEngineConstants.EVENT_CATEGORY, "Other").put(CHANGE_NOTIFICATION_TYPE, ADD_EVENT_SOURCE));

        Bootstrap.vertx().eventBus().send(EVENT_CHANGE_LOCAL_NOTIFICATION, new JsonObject().put(EventBusConstants.EVENT_SOURCE, item.getString(AIOpsObject.OBJECT_IP)).put(EventBusConstants.EVENT, EVENT_FLOW).put(GlobalConstants.PLUGIN_ID, DatastoreConstants.PluginId.FLOW_EVENT.getName()).put(CHANGE_NOTIFICATION_TYPE, ADD_EVENT_SOURCE));

        Bootstrap.vertx().eventBus().send(EVENT_CHANGE_LOCAL_NOTIFICATION, new JsonObject().put(EventBusConstants.EVENT_SOURCE, item.getString(AIOpsObject.OBJECT_IP)).put(EventBusConstants.EVENT, EVENT_TRAP).put(GlobalConstants.PLUGIN_ID, DatastoreConstants.PluginId.TRAP_EVENT.getName()).put(CHANGE_NOTIFICATION_TYPE, ADD_EVENT_SOURCE));

        testContext.awaitCompletion(5, TimeUnit.SECONDS);

        testContext.completeNow();
    }

    @AfterAll
    static void afterAll(VertxTestContext testContext) throws InterruptedException
    {
        var item = ObjectConfigStore.getStore().getItem(ObjectConfigStore.getStore().getItemsByCategory(NMSConstants.Category.NETWORK).getLong(0));

        entityID = item.getLong(ID);

        LOGGER.info("Going to remove event sources for IP :: " + item.getString(AIOpsObject.OBJECT_IP) + " | item :: " + item.encode());

        Bootstrap.vertx().eventBus().send(EVENT_CHANGE_LOCAL_NOTIFICATION, new JsonObject().put(EventBusConstants.EVENT_SOURCE, item.getString(AIOpsObject.OBJECT_IP)).put(EventBusConstants.EVENT, EVENT_LOG).put(CHANGE_NOTIFICATION_TYPE, DELETE_EVENT_SOURCE));

        Bootstrap.vertx().eventBus().send(EVENT_CHANGE_LOCAL_NOTIFICATION, new JsonObject().put(EventBusConstants.EVENT_SOURCE, item.getString(AIOpsObject.OBJECT_IP)).put(EventBusConstants.EVENT, EVENT_FLOW).put(CHANGE_NOTIFICATION_TYPE, DELETE_EVENT_SOURCE));

        Bootstrap.vertx().eventBus().send(EVENT_CHANGE_LOCAL_NOTIFICATION, new JsonObject().put(EventBusConstants.EVENT_SOURCE, item.getString(AIOpsObject.OBJECT_IP)).put(EventBusConstants.EVENT, EVENT_TRAP).put(CHANGE_NOTIFICATION_TYPE, DELETE_EVENT_SOURCE));

        testContext.awaitCompletion(5, TimeUnit.SECONDS);

        testContext.completeNow();
    }


    @BeforeEach
    void beforeEach(VertxTestContext testContext, TestInfo testInfo)
    {
        LOGGER.info(String.format("running test case %s", testInfo.getTestMethod().get().getName()));

        testContext.completeNow();
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(1)
    void testTemplateServerCategory(VertxTestContext testContext, TestInfo testInfo)
    {
        var items = ObjectConfigStore.getStore().getItemsByCategory(NMSConstants.Category.SERVER);

        var context = new JsonObject("{\"entity.type\":\"Monitor\",\"category\":\"Server\"}");

        assertFalse(items.isEmpty());

        context.put("entity.id", items.getValue(0));

        TestAPIUtil.get(VISUALIZATION_TEMPLATE_API_ENDPOINT + "?filter=" + context,
                testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertValidResponseTestResult(response, LOGGER, testInfo.getTestMethod().get().getName());

                            testContext.completeNow();

                        })));

    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(2)
    void testTemplateNetworkCategory(VertxTestContext testContext, TestInfo testInfo)
    {
        var items = ObjectConfigStore.getStore().getItemsByCategory(NMSConstants.Category.NETWORK);

        assertFalse(items.isEmpty());

        var context = new JsonObject("{\"entity.type\":\"Monitor\",\"category\":\"Network\"}");

        context.put("entity.id", items.getValue(0));

        TestAPIUtil.get(VISUALIZATION_TEMPLATE_API_ENDPOINT + "?filter=" + context,
                testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertValidResponseTestResult(response, LOGGER, testInfo.getTestMethod().get().getName());

                            testContext.completeNow();

                        })));

    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(3)
    void testTemplateVirtualizationCategory(VertxTestContext testContext, TestInfo testInfo)
    {
        var items = ObjectConfigStore.getStore().getItemsByCategory(NMSConstants.Category.VIRTUALIZATION);

        var context = new JsonObject("{\"entity.type\":\"Monitor\",\"category\":\"Virtualization\"}");

        assertFalse(items.isEmpty());

        context.put("entity.id", items.getValue(0));

        TestAPIUtil.get(VISUALIZATION_TEMPLATE_API_ENDPOINT + "?filter=" + context,
                testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertValidResponseTestResult(response, LOGGER, testInfo.getTestMethod().get().getName());

                            testContext.completeNow();

                        })));

    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(4)
    void testTemplateOtherCategory(VertxTestContext testContext, TestInfo testInfo)
    {
        var items = ObjectConfigStore.getStore().getItemsByCategory(NMSConstants.Category.OTHER);

        var context = new JsonObject("{\"entity.type\":\"Monitor\",\"category\":\"Other\"}");

        assertFalse(items.isEmpty());

        context.put("entity.id", items.getValue(0));

        TestAPIUtil.get(VISUALIZATION_TEMPLATE_API_ENDPOINT + "?filter=" + context,
                testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertValidResponseTestResult(response, LOGGER, testInfo.getTestMethod().get().getName());

                            testContext.completeNow();

                        })));

    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(5)
    void testTemplateArubaWireless(VertxTestContext testContext, TestInfo testInfo)
    {
        var entityId = ObjectConfigStore.getStore().getItemByIP("*************");

        var context = new JsonObject("{\"entity.type\":\"Monitor\",\"category\":\"Network\"}").put("entity.id", entityId);

        TestAPIUtil.get(VISUALIZATION_TEMPLATE_API_ENDPOINT + "?filter=" + context,
                testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertValidResponseTestResult(response, LOGGER, testInfo.getTestMethod().get().getName());

                            testContext.completeNow();

                        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(6)
    void testTemplateRuckusWireless(VertxTestContext testContext, TestInfo testInfo)
    {
        var entityId = ObjectConfigStore.getStore().getItemByIP("**********");

        var context = new JsonObject("{\"entity.type\":\"Monitor\",\"category\":\"Network\"}").put("entity.id", entityId);

        TestAPIUtil.get(VISUALIZATION_TEMPLATE_API_ENDPOINT + "?filter=" + context,
                testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertValidResponseTestResult(response, LOGGER, testInfo.getTestMethod().get().getName());

                            testContext.completeNow();

                        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(7)
    void testTemplateApplication(VertxTestContext testContext, TestInfo testInfo)
    {
        var entityId = ObjectConfigStore.getStore().getItemByIP("************");

        var context = new JsonObject("{\"entity.type\":\"Monitor\",\"category\":\"Server\"}");

        context.put("entity.id", entityId).put("application", VisualizationConstants.Template.ACTIVE_DIRECTORY);

        TestAPIUtil.get(VISUALIZATION_TEMPLATE_API_ENDPOINT + "?filter=" + context,
                testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertValidResponseTestResult(response, LOGGER, testInfo.getTestMethod().get().getName());

                            testContext.completeNow();

                        })));

    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(8)
    void testTemplateTemplateUnKnownCategory(VertxTestContext testContext)
    {
        var context = new JsonObject("{\"entity.type\":\"Monitor\",\"category\":\"UnKnown\"}").put("entity.id", 0);

        TestAPIUtil.get(VISUALIZATION_TEMPLATE_API_ENDPOINT + "?filter=" + context,
                testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            assertEquals(200, SC_OK);

                            testContext.completeNow();

                        })));

    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(9)
    void testGetTemplate(VertxTestContext testContext)
    {
        TestAPIUtil.get(VISUALIZATION_TEMPLATE_API_ENDPOINT + "/" + 10000000000001L,
                testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            var result = response.bodyAsJsonObject().getJsonObject(RESULT);

                            assertNotNull(result);

                            Assertions.assertFalse(result.isEmpty());

                            testContext.completeNow();

                        })));

    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(10)
    void testAddCustomTemplateTab(VertxTestContext testContext, TestInfo testInfo)
    {
        var context = new JsonObject("{\"template.name\": \"test1\", \"template.tabs\": [{\"tab.id\": 10000000000099, \"text\": \"Overview\", \"user.created\": \"no\"}],\"style\": {\"font.size\": \"medium\", \"h.gap\": 8, \"v.gap\": 8, \"row.height\": 85},\"template.widgets\": []}");

        context.put(TEMPLATE_NAME, "Template Test " + System.currentTimeMillis());

        TestAPIUtil.post(VISUALIZATION_TEMPLATE_API_ENDPOINT, context,
                testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            LOGGER.info(testInfo.getTestMethod().get().getName() + ": POST response: " + response.bodyAsJsonObject().encode());

                            templateId = response.bodyAsJsonObject().getLong(GlobalConstants.ID);

                            Assertions.assertFalse(TemplateConfigStore.getStore().getItem(templateId).isEmpty());

                            var templateContext = TemplateConfigStore.getStore().getItem(10000000000099L);

                            templateContext.getJsonArray("template.tabs").add(new JsonObject().put("tab.id", templateId).put("text", CommonUtil.getString(context.getValue(TEMPLATE_NAME))));

                            TestAPIUtil.put(VISUALIZATION_TEMPLATE_API_ENDPOINT + "/" + 10000000000099L, templateContext,
                                    testContext.succeeding(updateResponse ->
                                            testContext.verify(() ->
                                            {
                                                TestAPIUtil.assertUpdateEntityTestResult(TemplateConfigStore.getStore(), templateContext, updateResponse.bodyAsJsonObject(),
                                                        String.format(InfoMessageConstants.ENTITY_UPDATED, "Template"), LOGGER, testInfo.getTestMethod().get().getName());

                                                testContext.completeNow();
                                            })));
                        })));

    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(11)
    void testDeleteCustomTemplateTab(VertxTestContext testContext, TestInfo testInfo)
    {
        TestAPIUtil.delete(VISUALIZATION_TEMPLATE_API_ENDPOINT + "/" + templateId,
                testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            var templateTabs = new JsonArray();

                            var templateContext = TemplateConfigStore.getStore().getItem(10000000000099L);

                            TestAPIUtil.assertDeleteEntityTestResult(TemplateConfigStore.getStore(), response.bodyAsJsonObject(),
                                    String.format(InfoMessageConstants.ENTITY_DELETED, "Template"));

                            testContext.awaitCompletion(2, TimeUnit.SECONDS);

                            templateContext.getJsonArray("template.tabs").stream().filter(templateTab -> JsonObject.mapFrom(templateTab).getLong("tab.id") != templateId).forEach(templateTabs::add);

                            templateContext.put("template.tabs", templateTabs);

                            TestAPIUtil.put(VISUALIZATION_TEMPLATE_API_ENDPOINT + "/" + 10000000000099L, templateContext,
                                    testContext.succeeding(updateResponse ->
                                            testContext.verify(() ->
                                            {
                                                LOGGER.info(testInfo.getTestMethod().get().getName() + ": PUT response: " + response.bodyAsJsonObject().encode());

                                                TestAPIUtil.assertUpdateEntityTestResult(TemplateConfigStore.getStore(), templateContext, updateResponse.bodyAsJsonObject(),
                                                        String.format(InfoMessageConstants.ENTITY_UPDATED, "Template"), LOGGER, testInfo.getTestMethod().get().getName());

                                                testContext.completeNow();
                                            })));
                        })));

    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(12)
    void testRenderNetworkTemplateCustomMetric(VertxTestContext testContext, TestInfo testInfo)
    {
        var context = new JsonObject("{\"entity.type\":\"Monitor\",\"category\":\"Network\"}");

        context.put("entity.id", ObjectConfigStore.getStore().getItemByIP("***********"));

        TestAPIUtil.get(VISUALIZATION_TEMPLATE_API_ENDPOINT + "?filter=" + context,
                testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertValidResponseTestResult(response, LOGGER, testInfo.getTestMethod().get().getName());

                            assertTrue(response.bodyAsJsonObject().getJsonObject(RESULT).containsKey(MetricPlugin.METRIC_PLUGIN_PROTOCOL));

                            testContext.completeNow();

                        })));

    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(13)
    void testTemplateHPEILO(VertxTestContext testContext, TestInfo testInfo)
    {
        var entityId = ObjectConfigStore.getStore().getItemByIP("*************");

        var context = new JsonObject("{\"entity.type\":\"Monitor\",\"category\":\"Network\"}").put("entity.id", entityId);

        TestAPIUtil.get(VISUALIZATION_TEMPLATE_API_ENDPOINT + "?filter=" + context,
                testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertValidResponseTestResult(response, LOGGER, testInfo.getTestMethod().get().getName());

                            testContext.completeNow();

                        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(14)
    void testTemplateDellIDRAC(VertxTestContext testContext, TestInfo testInfo)
    {
        var entityId = ObjectConfigStore.getStore().getItemByIP("*************");

        var context = new JsonObject("{\"entity.type\":\"Monitor\",\"category\":\"Network\"}").put("entity.id", entityId);

        TestAPIUtil.get(VISUALIZATION_TEMPLATE_API_ENDPOINT + "?filter=" + context,
                testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertValidResponseTestResult(response, LOGGER, testInfo.getTestMethod().get().getName());

                            testContext.completeNow();

                        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(15)
    void testTemplateIBMTapeLibrary(VertxTestContext testContext, TestInfo testInfo)
    {
        var entityId = ObjectConfigStore.getStore().getItemByIP("*************");

        var context = new JsonObject("{\"entity.type\":\"Monitor\",\"category\":\"Network\"}").put("entity.id", entityId);

        TestAPIUtil.get(VISUALIZATION_TEMPLATE_API_ENDPOINT + "?filter=" + context,
                testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertValidResponseTestResult(response, LOGGER, testInfo.getTestMethod().get().getName());

                            testContext.completeNow();

                        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(16)
    void testTemplateNutanix(VertxTestContext testContext, TestInfo testInfo)
    {
        var entityId = ObjectConfigStore.getStore().getItemByIP("*************");

        var context = new JsonObject("{\"entity.type\":\"Monitor\",\"category\":\"HCI\"}").put("entity.id", entityId);

        TestAPIUtil.get(VISUALIZATION_TEMPLATE_API_ENDPOINT + "?filter=" + context,
                testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertValidResponseTestResult(response, LOGGER, testInfo.getTestMethod().get().getName());

                            testContext.completeNow();

                        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(17)
    void testTemplatePrism(VertxTestContext testContext, TestInfo testInfo)
    {
        var entityId = ObjectConfigStore.getStore().getItemByIP("*************");

        var context = new JsonObject("{\"entity.type\":\"Monitor\",\"category\":\"HCI\"}").put("entity.id", entityId);

        TestAPIUtil.get(VISUALIZATION_TEMPLATE_API_ENDPOINT + "?filter=" + context,
                testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertValidResponseTestResult(response, LOGGER, testInfo.getTestMethod().get().getName());

                            testContext.completeNow();

                        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(18)
    void testTemplateCiscovManage(VertxTestContext testContext, TestInfo testInfo)
    {
        var entityId = ObjectConfigStore.getStore().getItemByIP("***********");

        var context = new JsonObject("{\"entity.type\":\"Monitor\",\"category\":\"SDN\"}").put("entity.id", entityId);

        TestAPIUtil.get(VISUALIZATION_TEMPLATE_API_ENDPOINT + "?filter=" + context,
                testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertValidResponseTestResult(response, LOGGER, testInfo.getTestMethod().get().getName());

                            testContext.completeNow();

                        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(19)
    void testTemplateCiscovEdge(VertxTestContext testContext, TestInfo testInfo)
    {
        var entityId = ObjectConfigStore.getStore().getItemByIP("**********");

        var context = new JsonObject("{\"entity.type\":\"Monitor\",\"category\":\"SDN\"}").put("entity.id", entityId);

        TestAPIUtil.get(VISUALIZATION_TEMPLATE_API_ENDPOINT + "?filter=" + context,
                testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertValidResponseTestResult(response, LOGGER, testInfo.getTestMethod().get().getName());

                            testContext.completeNow();

                        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(20)
    void testTemplateCiscovSmart(VertxTestContext testContext, TestInfo testInfo)
    {
        var entityId = ObjectConfigStore.getStore().getItemByIP("*********");

        var context = new JsonObject("{\"entity.type\":\"Monitor\",\"category\":\"SDN\"}").put("entity.id", entityId);

        TestAPIUtil.get(VISUALIZATION_TEMPLATE_API_ENDPOINT + "?filter=" + context,
                testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertValidResponseTestResult(response, LOGGER, testInfo.getTestMethod().get().getName());

                            testContext.completeNow();

                        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(21)
    void testTemplateCiscovBond(VertxTestContext testContext, TestInfo testInfo)
    {
        var entityId = ObjectConfigStore.getStore().getItemByIP("*********");

        var context = new JsonObject("{\"entity.type\":\"Monitor\",\"category\":\"SDN\"}").put("entity.id", entityId);

        TestAPIUtil.get(VISUALIZATION_TEMPLATE_API_ENDPOINT + "?filter=" + context,
                testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertValidResponseTestResult(response, LOGGER, testInfo.getTestMethod().get().getName());

                            testContext.completeNow();

                        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(22)
    void testTemplateCiscoMeraki(VertxTestContext testContext, TestInfo testInfo)
    {
        var entityId = ObjectConfigStore.getStore().getItemByIP("n149.meraki.com");

        var context = new JsonObject("{\"entity.type\":\"Monitor\",\"category\":\"SDN\"}").put("entity.id", entityId);

        TestAPIUtil.get(VISUALIZATION_TEMPLATE_API_ENDPOINT + "?filter=" + context,
                testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertValidResponseTestResult(response, LOGGER, testInfo.getTestMethod().get().getName());

                            testContext.completeNow();

                        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(23)
    void testTemplateCiscoMerakiSecurity(VertxTestContext testContext, TestInfo testInfo)
    {
        var entityId = ObjectConfigStore.getStore().getItemByIP("************");

        var context = new JsonObject("{\"entity.type\":\"Monitor\",\"category\":\"SDN\"}").put("entity.id", entityId);

        TestAPIUtil.get(VISUALIZATION_TEMPLATE_API_ENDPOINT + "?filter=" + context,
                testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertValidResponseTestResult(response, LOGGER, testInfo.getTestMethod().get().getName());

                            testContext.completeNow();

                        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(24)
    void testTemplateCiscoMerakiSwitch(VertxTestContext testContext, TestInfo testInfo)
    {
        var entityId = ObjectConfigStore.getStore().getItemByIP("************");

        var context = new JsonObject("{\"entity.type\":\"Monitor\",\"category\":\"SDN\"}").put("entity.id", entityId);

        TestAPIUtil.get(VISUALIZATION_TEMPLATE_API_ENDPOINT + "?filter=" + context,
                testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertValidResponseTestResult(response, LOGGER, testInfo.getTestMethod().get().getName());

                            testContext.completeNow();

                        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(25)
    void testTemplateCiscoMerakiRadio(VertxTestContext testContext, TestInfo testInfo)
    {
        var entityId = ObjectConfigStore.getStore().getItemByIP("************");

        var context = new JsonObject("{\"entity.type\":\"Monitor\",\"category\":\"SDN\"}").put("entity.id", entityId);

        TestAPIUtil.get(VISUALIZATION_TEMPLATE_API_ENDPOINT + "?filter=" + context,
                testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertValidResponseTestResult(response, LOGGER, testInfo.getTestMethod().get().getName());

                            testContext.completeNow();

                        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(26)
    void testRenderLogTemplate(VertxTestContext testContext, TestInfo testInfo)
    {
        LOGGER.info("Running " + testInfo.getTestMethod().get().getName());

        var context = new JsonObject("{\"entity.type\":\"Monitor\",\"category\":\"Virtualization\"}").put(AIOpsConstants.ENTITY_ID, entityID);

        TestAPIUtil.get(VISUALIZATION_TEMPLATE_API_ENDPOINT + "?filter=" + context,
                testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertValidResponseTestResult(response, LOGGER, testInfo.getTestMethod().get().getName());

                            var result = response.bodyAsJsonObject().getJsonObject(RESULT);

                            LOGGER.info("Template API Response of :: " + testInfo.getTestMethod().get().getName() + " :: " + result.encode());

                            assertTrue(result.containsKey(Template.TEMPLATE_TABS));

                            var items = result.getJsonArray(Template.TEMPLATE_TABS);

                            for (var i = 0; i <= items.size(); i++)
                            {
                                var item = items.getJsonObject(i);

                                if (item.getString("text").equalsIgnoreCase(VisualizationConstants.Template.LOG_ANALYTICS.getName()))
                                {
                                    testContext.completeNow();

                                    break;
                                }
                            }

                        })));

    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(27)
    void testRenderTrapTemplate(VertxTestContext testContext, TestInfo testInfo)
    {
        LOGGER.info("Running -> " + testInfo.getTestMethod().get().getName());

        var context = new JsonObject("{\"entity.type\":\"Monitor\",\"category\":\"Network\"}").put(AIOpsConstants.ENTITY_ID, entityID);

        TestAPIUtil.get(VISUALIZATION_TEMPLATE_API_ENDPOINT + "?filter=" + context,
                testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertValidResponseTestResult(response, LOGGER, testInfo.getTestMethod().get().getName());

                            var result = response.bodyAsJsonObject().getJsonObject(RESULT);

                            LOGGER.info("Template API Response of :: " + testInfo.getTestMethod().get().getName() + " :: " + result.encode());

                            assertTrue(result.containsKey(Template.TEMPLATE_TABS));

                            assertFalse(result.getJsonArray(Template.TEMPLATE_TABS).isEmpty());

                            var items = result.getJsonArray(Template.TEMPLATE_TABS);

                            for (var i = 0; i <= items.size(); i++)
                            {
                                var item = items.getJsonObject(i);

                                if (item.getString("text").equalsIgnoreCase(VisualizationConstants.Template.TRAP_ANALYTICS.getName()))
                                {
                                    testContext.completeNow();

                                    break;
                                }
                            }

                        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(29)
    void testTemplateNetAppONTAPCluster(VertxTestContext testContext, TestInfo testInfo)
    {
        var entityId = ObjectConfigStore.getStore().getItemByIP("************");

        var context = new JsonObject("{\"entity.type\":\"Monitor\",\"category\":\"Storage\"}").put("entity.id", entityId);

        TestAPIUtil.get(VISUALIZATION_TEMPLATE_API_ENDPOINT + "?filter=" + context,
                testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertValidResponseTestResult(response, LOGGER, testInfo.getTestMethod().get().getName());

                            testContext.completeNow();

                        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(30)
    void testTemplateHPEStoreOnce(VertxTestContext testContext, TestInfo testInfo)
    {
        var entityId = ObjectConfigStore.getStore().getItemByIP("************");

        var context = new JsonObject("{\"entity.type\":\"Monitor\",\"category\":\"Storage\"}").put("entity.id", entityId);

        TestAPIUtil.get(VISUALIZATION_TEMPLATE_API_ENDPOINT + "?filter=" + context,
                testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertValidResponseTestResult(response, LOGGER, testInfo.getTestMethod().get().getName());

                            testContext.completeNow();

                        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(31)
    void testTemplateCiscoACI(VertxTestContext testContext, TestInfo testInfo)
    {
        var entityId = ObjectConfigStore.getStore().getItemByIP("***********");

        var context = new JsonObject("{\"entity.type\":\"Monitor\",\"category\":\"SDN\"}").put("entity.id", entityId);

        TestAPIUtil.get(VISUALIZATION_TEMPLATE_API_ENDPOINT + "?filter=" + context,
                testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertValidResponseTestResult(response, LOGGER, testInfo.getTestMethod().get().getName());

                            testContext.completeNow();

                        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(32)
    void testRenderFlowTemplate(VertxTestContext testContext, TestInfo testInfo)
    {
        LOGGER.info("Running -> " + testInfo.getTestMethod().get().getName());

        var context = new JsonObject("{\"entity.type\":\"Monitor\",\"category\":\"Network\"}").put(AIOpsConstants.ENTITY_ID, entityID);

        TestAPIUtil.get(VISUALIZATION_TEMPLATE_API_ENDPOINT + "?filter=" + context,
                testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertValidResponseTestResult(response, LOGGER, testInfo.getTestMethod().get().getName());

                            var result = response.bodyAsJsonObject().getJsonObject(RESULT);

                            LOGGER.info("Template API Response of :: " + testInfo.getTestMethod().get().getName() + " :: " + result.encode());

                            assertTrue(result.containsKey(Template.TEMPLATE_TABS));

                            for (var index = 0; index <= result.getJsonArray(Template.TEMPLATE_TABS).size(); index++)
                            {
                                var tab = result.getJsonArray(Template.TEMPLATE_TABS).getJsonObject(index);

                                if (tab.getString(Template.TEMPLATE_TEXT).equalsIgnoreCase(VisualizationConstants.Template.FLOW_ANALYTICS.getName()))
                                {
                                    assertEquals(10, tab.getJsonArray(Template.TEMPLATE_TABS).size());  // check for the all nested tabs of FLOW_ANALYTICS

                                    testContext.completeNow();

                                    break;
                                }
                            }
                        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(33)
    void testRenderApplicationTemplate(VertxTestContext testContext, TestInfo testInfo)
    {
        LOGGER.info("Running -> " + testInfo.getTestMethod().get().getName());

        var item = ObjectConfigStore.getStore().getItem(ObjectConfigStore.getStore().getItemByIP("************"));

        assertFalse(item.isEmpty());

        var context = new JsonObject("{\"entity.type\":\"Monitor\",\"category\":\"Server\"}");

        context.put("entity.id", item.getLong(ID));

        TestAPIUtil.get(VISUALIZATION_TEMPLATE_API_ENDPOINT + "?filter=" + context,
                testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertValidResponseTestResult(response, LOGGER, testInfo.getTestMethod().get().getName());

                            var result = response.bodyAsJsonObject().getJsonObject(RESULT);

                            LOGGER.info("Template API Response of :: " + testInfo.getTestMethod().get().getName() + " :: " + result.encode());

                            assertTrue(result.containsKey(Template.TEMPLATE_TABS));

                            for (var index = 0; index <= result.getJsonArray(Template.TEMPLATE_TABS).size(); index++)
                            {
                                var tab = result.getJsonArray(Template.TEMPLATE_TABS).getJsonObject(index);

                                if (tab.getString(Template.TEMPLATE_TEXT).equalsIgnoreCase(NMSConstants.Type.ACTIVE_DIRECTORY.getName()))
                                {
                                    testContext.completeNow();

                                    break;
                                }
                            }
                        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(34)
    void testAddCustomTab2(VertxTestContext testContext, TestInfo testInfo)
    {
        var context = new JsonObject("{\"template.name\":\"win1\",\"template.tabs\":[{\"tab.id\":10000000000100,\"text\":\"Overview\",\"user.created\":\"no\"}],\"custom.tabs\":[],\"style\":{\"font.size\":\"large\",\"h.gap\":8,\"v.gap\":8,\"row.height\":85},\"template.widgets\":[]}");

        context.put(TEMPLATE_NAME, "Template Test " + System.currentTimeMillis()).put("_type", 1);

        TestAPIUtil.post(VISUALIZATION_TEMPLATE_API_ENDPOINT, context,
                testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            LOGGER.info(testInfo.getTestMethod().get().getName() + ": POST response: " + response.bodyAsJsonObject().encode());

                            templateId = response.bodyAsJsonObject().getLong(GlobalConstants.ID);

                            Assertions.assertFalse(TemplateConfigStore.getStore().getItem(templateId).isEmpty());

                            var item = TemplateConfigStore.getStore().getItem(10000000000100L);

                            item.getJsonArray("custom.tabs", new JsonArray()).add(new JsonObject().put("tab.id", templateId).put("text", CommonUtil.getString(context.getValue(TEMPLATE_NAME))));

                            TestAPIUtil.put(VISUALIZATION_TEMPLATE_API_ENDPOINT + "/" + 10000000000100L, item,
                                    testContext.succeeding(asyncResult ->
                                            testContext.verify(() ->
                                            {
                                                TestAPIUtil.assertUpdateEntityTestResult(TemplateConfigStore.getStore(), item, asyncResult.bodyAsJsonObject(),
                                                        String.format(InfoMessageConstants.ENTITY_UPDATED, "Template"), LOGGER, testInfo.getTestMethod().get().getName());

                                                testContext.completeNow();
                                            })));
                        })));

    }

}
