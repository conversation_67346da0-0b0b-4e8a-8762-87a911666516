/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

package com.mindarray.api;

import com.mindarray.GlobalConstants;
import com.mindarray.TestAPIConstants;
import com.mindarray.TestAPIUtil;
import com.mindarray.TestUtil;
import com.mindarray.eventbus.EventBusConstants;
import com.mindarray.store.ConfigTemplateConfigStore;
import com.mindarray.util.CodecUtil;
import com.mindarray.util.Logger;
import io.github.artsok.RepeatedIfExceptionsTest;
import io.vertx.core.eventbus.MessageConsumer;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import io.vertx.junit5.Timeout;
import io.vertx.junit5.VertxExtension;
import io.vertx.junit5.VertxTestContext;
import org.apache.http.HttpStatus;
import org.junit.jupiter.api.*;
import org.junit.jupiter.api.condition.EnabledIfSystemProperty;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.api.parallel.Execution;
import org.junit.jupiter.api.parallel.ExecutionMode;

import static com.mindarray.GlobalConstants.*;
import static com.mindarray.eventbus.EventBusConstants.*;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

@ExtendWith(VertxExtension.class)
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
@EnabledIfSystemProperty(named = "test.mode", matches = "^APP|^PRIMARY$")
@Timeout(10 * 1000)
@Execution(ExecutionMode.SAME_THREAD)
public class TestConfigTemplate
{

    private static final Logger LOGGER = new Logger(TestConfigTemplate.class, "CONFIG TEST CASE", "Test Config Template");
    private static final JsonObject CONTEXT = new JsonObject().put(ConfigTemplate.CONFIG_TEMPLATE_NAME, "config-template-test")
            .put(ConfigTemplate.CONFIG_TEMPLATE_DESCRIPTION, "This is a config template test")
            .put(ConfigTemplate.CONFIG_TEMPLATE_VENDOR, "Cisco Systems")
            .put(ConfigTemplate.CONFIG_TEMPLATE_OS_TYPE, "Test OS")
            .put(StorageProfile.StorageProtocol.NONE.getName(), new JsonObject().put("Backup_Running", new JsonArray()
                    .add(new JsonObject().put("operation.command", "terminal length 0").put("operation.delay.time", 1000).put("operation.timeout", 2000).put("operation.prompt", "#").put("operation.prompt.command", "No Command"))
                    .add(new JsonObject().put("operation.command", "show running-config").put("operation.delay.time", 1000).put("operation.timeout", 2000).put("operation.prompt", "#").put("operation.prompt.command", "No Command"))));
    private static MessageConsumer<JsonObject> messageConsumer;
    private static Long CONFIG_TEMPLATE_ID = null;

    @BeforeEach
    void beforeEach(VertxTestContext testContext, TestInfo testInfo)
    {
        LOGGER.info(String.format("running test case %s", testInfo.getTestMethod().get().getName()));

        testContext.completeNow();
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(1)
    void testCreateConfigTemplate(VertxTestContext testContext, TestInfo testInfo)
    {

        LOGGER.info("testConfigTemplateCreate: started");

        var context = CONTEXT.copy();

        context.put(ConfigTemplate.CONFIG_TEMPLATE_NAME, "Cisco-config-template-test");

        LOGGER.info("testConfigTemplateCreate: context: " + context);

        TestAPIUtil.post(TestAPIConstants.CONFIG_TEMPLATE_API_ENDPOINT, context, null);

        TestAPIUtil.post(TestAPIConstants.CONFIG_TEMPLATE_API_ENDPOINT, CONTEXT, testContext.succeeding(response ->
                testContext.verify(() ->
                {

                    LOGGER.info("testConfigTemplateCreate: result: " + response.bodyAsJsonObject());

                    TestAPIUtil.assertCreateEntityTestResult(ConfigTemplateConfigStore.getStore(), response.bodyAsJsonObject(), "Config Template created successfully", LOGGER, testInfo.getTestMethod().get().getName());

                    CONFIG_TEMPLATE_ID = response.bodyAsJsonObject().getLong(GlobalConstants.ID);

                    testContext.completeNow();
                })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(2)
    void testCreateConfigTemplateDuplicateName(VertxTestContext testContext)
    {
        LOGGER.info("testConfigTemplateWithDuplicateName: started");

        TestAPIUtil.post(TestAPIConstants.CONFIG_TEMPLATE_API_ENDPOINT, CONTEXT, testContext.succeeding(response ->
                testContext.verify(() ->
                {

                    LOGGER.info("testConfigTemplateWithDuplicateName: result: " + response.bodyAsJsonObject());

                    TestAPIUtil.assertAlreadyExistedEntityTestResult(response.bodyAsJsonObject(), "Template Name is not unique", ConfigTemplateConfigStore.getStore(), ConfigTemplate.CONFIG_TEMPLATE_NAME, CONTEXT.getString(ConfigTemplate.CONFIG_TEMPLATE_NAME));

                    testContext.completeNow();
                })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(3)
    void testGetConfigTemplate(VertxTestContext testContext, TestInfo testInfo)
    {
        LOGGER.info("testConfigTemplateGet: started");

        LOGGER.info("testConfigTemplateGet: CONFIG_TEMPLATE_ID: " + CONFIG_TEMPLATE_ID);

        assertNotNull(CONFIG_TEMPLATE_ID);

        TestAPIUtil.get(TestAPIConstants.CONFIG_TEMPLATE_API_ENDPOINT + GlobalConstants.PATH_SEPARATOR + CONFIG_TEMPLATE_ID, testContext.succeeding(response ->
                testContext.verify(() ->
                {

                    TestAPIUtil.assertGETRequestTestResult(response, CONFIG_TEMPLATE_ID, ConfigTemplateConfigStore.getStore(), null, LOGGER, testInfo.getTestMethod().get().getName());

                    testContext.completeNow();

                })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(4)
    void testGetAllConfigTemplates(VertxTestContext testContext)
    {
        LOGGER.info("testConfigTemplateGetAll: started");

        TestAPIUtil.get(TestAPIConstants.CONFIG_TEMPLATE_API_ENDPOINT, testContext.succeeding(response ->
                testContext.verify(() ->
                {

                    LOGGER.info("testConfigTemplateGetAll: result: " + response.bodyAsJsonObject());

                    TestAPIUtil.assertGETAllRequestTestResult(response, ConfigTemplateConfigStore.getStore(), null);

                    testContext.completeNow();
                })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(5)
    void testUpdateConfigTemplate(VertxTestContext testContext, TestInfo testInfo)
    {
        LOGGER.info("testConfigTemplateUpdate: started");

        var context = CONTEXT.copy();

        context.put(ConfigTemplate.CONFIG_TEMPLATE_NAME, "Test-1")
                .put(StorageProfile.StorageProtocol.TFTP.getName(), new JsonObject().put("Backup_Running", new JsonArray()
                        .add(new JsonObject().put("operation.command", "copy running-config tftp:").put("operation.delay.time", 1000).put("operation.timeout", 2000).put("operation.prompt", "#").put("operation.prompt.command", "No Command"))
                        .add(new JsonObject().put("operation.command", "&[TransferProtocolServerAddress]").put("operation.delay.time", 1000).put("operation.timeout", 2000).put("operation.prompt", "#").put("operation.prompt.command", "No Command"))
                        .add(new JsonObject().put("operation.command", "&[TransferFileName]").put("operation.delay.time", 1000).put("operation.timeout", 2000).put("operation.prompt", "#").put("operation.prompt.command", "LF"))));

        LOGGER.info("testConfigTemplateUpdate: context: " + context);

        TestAPIUtil.put(TestAPIConstants.CONFIG_TEMPLATE_API_ENDPOINT + GlobalConstants.PATH_SEPARATOR + CONFIG_TEMPLATE_ID, context, testContext.succeeding(response ->
                testContext.verify(() ->
                {

                    LOGGER.info("testConfigTemplateUpdate: result: " + response.bodyAsJsonObject());

                    TestAPIUtil.assertUpdateEntityTestResult(ConfigTemplateConfigStore.getStore(), context, response.bodyAsJsonObject(), "Config Template updated successfully", LOGGER, testInfo.getTestMethod().get().getName());

                    testContext.completeNow();
                })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(6)
    void testDeleteConfigTemplate(VertxTestContext testContext)
    {

        LOGGER.info("testConfigTemplateDelete: started");

        TestAPIUtil.delete(TestAPIConstants.CONFIG_TEMPLATE_API_ENDPOINT + GlobalConstants.PATH_SEPARATOR + CONFIG_TEMPLATE_ID, testContext.succeeding(response ->
                testContext.verify(() ->
                {

                    LOGGER.info("testConfigTemplateDelete: result: " + response.bodyAsJsonObject());

                    TestAPIUtil.assertDeleteEntityTestResult(ConfigTemplateConfigStore.getStore(), response.bodyAsJsonObject(), "Config Template deleted successfully");

                    testContext.completeNow();
                })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(7)
    void testDeleteDefaultConfigTemplate(VertxTestContext testContext)
    {
        TestAPIUtil.delete(TestAPIConstants.CONFIG_TEMPLATE_API_ENDPOINT + GlobalConstants.PATH_SEPARATOR + "100000000000001", testContext.succeeding(response ->
                testContext.verify(() ->
                {

                    TestAPIUtil.assertDeleteDefaultEntityTestResult(response.bodyAsJsonObject(), "Unable to remove default Config Template");

                    testContext.completeNow();
                })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(8)
    void testDeleteConfigTemplateNotExist(VertxTestContext testContext)
    {
        TestAPIUtil.delete(TestAPIConstants.CONFIG_TEMPLATE_API_ENDPOINT + GlobalConstants.PATH_SEPARATOR + "123", testContext.succeeding(response ->
                testContext.verify(() ->
                {

                    TestAPIUtil.assertNotExistEntityDeleteTestResult(response, "Config Template", "Item Config Template not found");

                    testContext.completeNow();
                })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(9)
    void testExportConfigTemplate(VertxTestContext testContext)
    {

        messageConsumer = TestUtil.vertx().eventBus().localConsumer(EVENT_USER + TestUtil.getSessionId(), message ->
        {

            if (message.body().containsKey(EVENT_TYPE) && message.body().getString(EVENT_TYPE).equalsIgnoreCase(UI_NOTIFICATION_CSV_EXPORT_READY) && message.body().getBinary(EVENT_CONTEXT) != null)
            {
                var result = CodecUtil.toJSONObject(message.body().getBinary(EventBusConstants.EVENT_CONTEXT));

                assertEquals(STATUS_SUCCEED, result.getString(STATUS));

                assertEquals(HttpStatus.SC_OK, result.getInteger(APIConstants.RESPONSE_CODE));

                assertEquals("Cisco.json", result.getString(FILE_NAME));

                messageConsumer.unregister(asyncResult -> testContext.completeNow());
            }

        });

        TestUtil.vertx().eventBus().publish(UI_ACTION_CONFIG_TEMPLATE_EXPORT, new JsonObject().put(GlobalConstants.ID, 100000000000001L)
                .put(APIConstants.SESSION_ID, TestUtil.getSessionId())
                .put(User.USER_NAME, "admin"));
    }
}
