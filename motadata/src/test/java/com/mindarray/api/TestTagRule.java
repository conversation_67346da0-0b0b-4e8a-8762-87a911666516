/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

/*
 * Change Logs:
 *  Date			Author			         Notes
 *  28-Feb-2025     Smit Prajapati           MOTADATA-4956: Rule Based Tagging
 */

package com.mindarray.api;

import com.mindarray.GlobalConstants;
import com.mindarray.TestAPIUtil;
import com.mindarray.TestUtil;
import com.mindarray.eventbus.EventBusConstants;
import com.mindarray.nms.NMSConstants;
import com.mindarray.store.MetricConfigStore;
import com.mindarray.store.ObjectConfigStore;
import com.mindarray.store.TagConfigStore;
import com.mindarray.store.TagRuleConfigStore;
import com.mindarray.util.CodecUtil;
import com.mindarray.util.CommonUtil;
import com.mindarray.util.Logger;
import io.github.artsok.RepeatedIfExceptionsTest;
import io.vertx.core.eventbus.MessageConsumer;
import io.vertx.core.json.JsonObject;
import io.vertx.junit5.Timeout;
import io.vertx.junit5.VertxExtension;
import io.vertx.junit5.VertxTestContext;
import org.apache.http.HttpStatus;
import org.junit.jupiter.api.*;
import org.junit.jupiter.api.condition.EnabledIfSystemProperty;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.api.parallel.Execution;
import org.junit.jupiter.api.parallel.ExecutionMode;

import java.util.concurrent.TimeUnit;

import static com.mindarray.GlobalConstants.*;
import static com.mindarray.TestAPIConstants.TAG_RULE_API_ENDPOINT;
import static com.mindarray.api.AIOpsObject.OBJECT_TAGS;
import static com.mindarray.datastore.DatastoreConstants.AggregationType.COUNT;
import static com.mindarray.eventbus.EventBusConstants.*;
import static org.junit.jupiter.api.Assertions.assertEquals;

@ExtendWith(VertxExtension.class)
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
@Timeout(70 * 1000)
@Execution(ExecutionMode.SAME_THREAD)
@EnabledIfSystemProperty(named = "test.mode", matches = "^APP|^PRIMARY|SLAVE$")
public class TestTagRule
{
    private static final Logger LOGGER = new Logger(TestTagRule.class, "tag-rule-test", "Tag Rule Test");
    private static final JsonObject IDS = new JsonObject();
    private static MessageConsumer<JsonObject> messageConsumer;

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(1)
    void testCreateRuleAssignMonitor(VertxTestContext testContext, TestInfo testInfo)
    {
        LOGGER.info(ObjectConfigStore.getStore().getItem(ObjectConfigStore.getStore().getItemByIP("************")));

        LOGGER.info(MetricConfigStore.getStore().getItem(MetricConfigStore.getStore().getItemByMetricPlugin(ObjectConfigStore.getStore().getItemByIP("**********"), NMSConstants.MetricPlugin.SNMP_INTERFACE.getName())));

        var payload = new JsonObject("{\"tag.rule.name\":\"Monitor-Assign\",\"tag.rule.type\":\"monitor\",\"tag.rule.operation\":\"Assign\",\"tag.rule.tags\":[\"monitor-static\",\"monitor-dynamic:{object.name}\"],\"tag.rule.inclusive.condition\":\"yes\",\"tag.rule.conditions\":[{\"data.point\":\"object.ip\",\"operator\":\"contain\",\"value\":\"8.135\",\"filter\":\"NONE\"},{\"data.point\":\"object.type\",\"operator\":\"contain\",\"value\":\"Linux\",\"filter\":\"AND\"}]}");

        TestAPIUtil.post(TAG_RULE_API_ENDPOINT, payload, testContext.succeeding(response ->
                testContext.verify(() ->
                {
                    IDS.put("monitor-assign", response.bodyAsJsonObject().getLong(GlobalConstants.ID));

                    testContext.awaitCompletion(1, TimeUnit.SECONDS);

                    TestUtil.vertx().eventBus().send(UI_ACTION_TAG_RULE_RUN, new JsonObject().put(ID, IDS.getLong("monitor-assign")));

                    testContext.completeNow();
                })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(2)
    void testCreateRuleAssignInterface(VertxTestContext testContext, TestInfo testInfo)
    {
        var payload = new JsonObject("{\"tag.rule.name\":\"Interface-Assign\",\"tag.rule.type\":\"interface\",\"tag.rule.operation\":\"Assign\",\"tag.rule.tags\":[\"interface-static\",\"interface-dynamic:{interface.name}\"],\"tag.rule.inclusive.condition\":\"yes\",\"tag.rule.conditions\":[{\"data.point\":\"interface.description\",\"operator\":\"contain\",\"value\":\"VLAN 04095 (Mgmt)\",\"filter\":\"NONE\"}]}");

        TestAPIUtil.post(TAG_RULE_API_ENDPOINT, payload, testContext.succeeding(response ->
                testContext.verify(() ->
                {
                    IDS.put("interface-assign", response.bodyAsJsonObject().getLong(GlobalConstants.ID));

                    testContext.awaitCompletion(1, TimeUnit.SECONDS);

                    TestUtil.vertx().eventBus().send(UI_ACTION_TAG_RULE_RUN, new JsonObject().put(ID, IDS.getLong("interface-assign")));

                    testContext.completeNow();
                })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(3)
    void testCreateRuleRemoveInterface(VertxTestContext testContext, TestInfo testInfo)
    {
        var payload = new JsonObject("{\"tag.rule.name\":\"Interface-Remove\",\"tag.rule.type\":\"interface\",\"tag.rule.operation\":\"Remove\",\"tag.rule.tags\":[\"interface-dynamic:{interface.name}\"],\"tag.rule.inclusive.condition\":\"yes\",\"tag.rule.conditions\":[{\"data.point\":\"interface.description\",\"operator\":\"contain\",\"value\":\"VLAN 04095 (Mgmt)\",\"filter\":\"NONE\"}]}");

        TestAPIUtil.post(TAG_RULE_API_ENDPOINT, payload, testContext.succeeding(response ->
                testContext.verify(() ->
                {
                    IDS.put("Interface-remove", response.bodyAsJsonObject().getLong(GlobalConstants.ID));

                    testContext.completeNow();
                })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(4)
    void testCreateRuleRemoveMonitor(VertxTestContext testContext, TestInfo testInfo)
    {
        var payload = new JsonObject("{\"tag.rule.name\":\"Monitor-Remove\",\"tag.rule.type\":\"monitor\",\"tag.rule.operation\":\"Remove\",\"tag.rule.tags\":[\"monitor-dynamic:{object.name}\"],\"tag.rule.inclusive.condition\":\"yes\",\"tag.rule.conditions\":[{\"data.point\":\"object.ip\",\"operator\":\"contain\",\"value\":\"8.135\",\"filter\":\"NONE\"},{\"data.point\":\"object.type\",\"operator\":\"contain\",\"value\":\"Linux\",\"filter\":\"AND\"}]}");

        TestAPIUtil.post(TAG_RULE_API_ENDPOINT, payload, testContext.succeeding(response ->
                testContext.verify(() ->
                {
                    IDS.put("monitor-assign", response.bodyAsJsonObject().getLong(GlobalConstants.ID));

                    testContext.awaitCompletion(1, TimeUnit.SECONDS);

                    testContext.completeNow();
                })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(5)
    void testTestMonitorRule(VertxTestContext testContext, TestInfo testInfo)
    {

        messageConsumer = TestUtil.vertx().eventBus().localConsumer(EVENT_USER + TestUtil.getSessionId(), message ->
        {

            if (message.body().containsKey(EVENT_TYPE) && message.body().getString(EVENT_TYPE).equalsIgnoreCase(UI_ACTION_TAG_RULE_TEST) && message.body().getBinary(EVENT_CONTEXT) != null)
            {
                var result = CodecUtil.toJSONObject(message.body().getBinary(EventBusConstants.EVENT_CONTEXT));

                LOGGER.info("testMonitorRulePreview: Result: " + result.encodePrettily());

                assertEquals("Rule Condition satisfied", result.getString(MESSAGE));

                assertEquals(STATUS_SUCCEED, result.getString(STATUS));

                var ids = result.getJsonArray(RESULT);

                for (var index = 0; index < ids.size(); index++)
                {
                    var object = ObjectConfigStore.getStore().getItem(ids.getLong(index));

                    Assertions.assertTrue(object.getJsonArray(OBJECT_TAGS).contains(CommonUtil.getInteger(TagConfigStore.getStore().getTag("monitor-static"))));
                }

                messageConsumer.unregister();

                testContext.completeNow();
            }

        });

        TestUtil.vertx().eventBus().send(UI_ACTION_TAG_RULE_TEST, TagRuleConfigStore.getStore().getItem(IDS.getLong("monitor-assign")).put(APIConstants.SESSION_ID, TestUtil.getSessionId()));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(6)
    void testTestInterfaceRule(VertxTestContext testContext, TestInfo testInfo)
    {

        messageConsumer = TestUtil.vertx().eventBus().localConsumer(EVENT_USER + TestUtil.getSessionId(), message ->
        {

            if (message.body().containsKey(EVENT_TYPE) && message.body().getString(EVENT_TYPE).equalsIgnoreCase(UI_ACTION_TAG_RULE_TEST) && message.body().getBinary(EVENT_CONTEXT) != null)
            {
                var result = CodecUtil.toJSONObject(message.body().getBinary(EventBusConstants.EVENT_CONTEXT));

                LOGGER.info("testStorageProfileFTP: Result: " + result.encodePrettily());

                assertEquals("Rule Condition satisfied", result.getString(MESSAGE));

                assertEquals(STATUS_SUCCEED, result.getString(STATUS));

                var entities = result.getJsonArray(RESULT);

                for (var index = 0; index < entities.size(); index++)
                {
                    Assertions.assertTrue(entities.getJsonObject(index).getJsonArray(INSTANCE_TAGS).contains(CommonUtil.getInteger(TagConfigStore.getStore().getTag("interface-static"))));
                }

                testContext.completeNow();

                messageConsumer.unregister();
            }

        });

        TestUtil.vertx().eventBus().send(UI_ACTION_TAG_RULE_TEST, TagRuleConfigStore.getStore().getItem(IDS.getLong("interface-assign")).put(APIConstants.SESSION_ID, TestUtil.getSessionId()));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(7)
    void testQualifyEntities(VertxTestContext testContext, TestInfo testInfo)
    {
        TestAPIUtil.get(TAG_RULE_API_ENDPOINT, testContext.succeeding(response ->
                testContext.verify(() ->
                {

                    var result = response.bodyAsJsonObject();

                    Assertions.assertEquals(HttpStatus.SC_OK, response.statusCode());

                    Assertions.assertEquals(STATUS_SUCCEED, result.getString(STATUS));

                    var entities = result.getJsonArray(RESULT);

                    for (var index = 0; index < entities.size(); index++)
                    {
                        var entity = entities.getJsonObject(index);

                        if (entity != null)
                        {
                            Assertions.assertTrue(entity.getInteger(COUNT.getName()) >= 1);
                        }
                    }

                    testContext.completeNow();
                })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(8)
    void testRunRemoveRule(VertxTestContext testContext, TestInfo testInfo)
    {

        messageConsumer = TestUtil.vertx().eventBus().localConsumer(EVENT_USER + TestUtil.getSessionId(), message ->
        {

            if (message.body().containsKey(EVENT_TYPE) && message.body().getString(EVENT_TYPE).equalsIgnoreCase(UI_ACTION_TAG_RULE_RUN) && message.body().getBinary(EVENT_CONTEXT) != null)
            {
                var result = CodecUtil.toJSONObject(message.body().getBinary(EventBusConstants.EVENT_CONTEXT));

                LOGGER.info("testMonitorRulePreview: Result: " + result.encodePrettily());

                assertEquals("Tags have been applied Successfully!", result.getString(MESSAGE));

                assertEquals(STATUS_SUCCEED, result.getString(STATUS));

                var entities = result.getJsonObject(RESULT);

                for (var entity : entities)
                {
                    Assertions.assertTrue(entities.getJsonArray(entity.getKey()).getJsonObject(0).getJsonArray(INSTANCE_TAGS).contains(CommonUtil.getInteger(TagConfigStore.getStore().getTag("interface-static"))));
                }

                messageConsumer.unregister();

                testContext.completeNow();
            }

        });

        TestUtil.vertx().eventBus().send(UI_ACTION_TAG_RULE_RUN, new JsonObject().put(ID, IDS.getLong("Interface-remove")).put(APIConstants.SESSION_ID, TestUtil.getSessionId()));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(9)
    void testDeleteRule(VertxTestContext testContext, TestInfo testInfo)
    {
        TestAPIUtil.delete(TAG_RULE_API_ENDPOINT + "/" + IDS.getLong("interface-assign"), testContext.succeeding(response ->
                testContext.verify(() ->
                {
                    Assertions.assertEquals(HttpStatus.SC_OK, response.statusCode());

                    Assertions.assertEquals(STATUS_SUCCEED, response.bodyAsJsonObject().getString(STATUS));

                    Assertions.assertEquals("Tag Rule deleted successfully", response.bodyAsJsonObject().getString(MESSAGE));

                    testContext.completeNow();
                })));
    }

}
