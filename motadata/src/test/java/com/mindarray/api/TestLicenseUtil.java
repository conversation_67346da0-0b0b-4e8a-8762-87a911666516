/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

package com.mindarray.api;

import com.mindarray.Bootstrap;
import com.mindarray.GlobalConstants;
import com.mindarray.TestAPIConstants;
import com.mindarray.TestAPIUtil;
import com.mindarray.util.LicenseUtil;
import io.github.artsok.RepeatedIfExceptionsTest;
import io.vertx.core.buffer.Buffer;
import io.vertx.core.json.JsonObject;
import io.vertx.junit5.Timeout;
import io.vertx.junit5.VertxExtension;
import io.vertx.junit5.VertxTestContext;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.io.IOUtils;
import org.junit.jupiter.api.*;
import org.junit.jupiter.api.condition.EnabledIfSystemProperty;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.api.parallel.Execution;
import org.junit.jupiter.api.parallel.ExecutionMode;

import javax.crypto.Cipher;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

import static com.mindarray.GlobalConstants.RESULT;
import static com.mindarray.api.APIConstants.RESPONSE_CODE;
import static com.mindarray.api.License.LICENSE_ACTIVATION_CODE;
import static org.apache.http.HttpStatus.SC_BAD_REQUEST;
import static org.apache.http.HttpStatus.SC_OK;
import static org.junit.jupiter.api.Assertions.assertEquals;

@ExtendWith(VertxExtension.class)
@Timeout(60 * 1000)
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
@EnabledIfSystemProperty(named = "test.mode", matches = "^APP|^PRIMARY$")
@Execution(ExecutionMode.SAME_THREAD)
public class TestLicenseUtil
{

    private static String licenseActivationCode;

    @AfterAll
    static void afterAll(VertxTestContext testContext) throws InterruptedException
    {
        testContext.awaitCompletion(5, TimeUnit.SECONDS);

        testContext.completeNow();
    }

    private static String encrypt(String value)
    {
        byte[] secretKeySpecBytes;

        byte[] ivParameterSpecBytes;

        String result = null;

        try (var resourceAsStream = Thread.currentThread().getContextClassLoader().getResourceAsStream("aes.key"))
        {
            var buffer = Buffer.buffer(IOUtils.toString(Objects.requireNonNull(resourceAsStream), StandardCharsets.UTF_8));

            secretKeySpecBytes = buffer.toString().split(" ")[0].trim().getBytes(StandardCharsets.UTF_8);

            ivParameterSpecBytes = buffer.toString().split(" ")[1].trim().getBytes(StandardCharsets.UTF_8);

            var cipher = Cipher.getInstance("AES/CBC/PKCS5PADDING");

            cipher.init(Cipher.ENCRYPT_MODE, new SecretKeySpec(secretKeySpecBytes, "AES"), new IvParameterSpec(ivParameterSpecBytes));

            result = Base64.encodeBase64String(cipher.doFinal(value.getBytes()));

        }
        catch (Exception exception)
        {

            exception.getCause();
        }

        return result;
    }

    private static String decrypt(String value)
    {
        byte[] secretKeySpecBytes;

        byte[] ivParameterSpecBytes;

        String result = null;

        try (var resourceAsStream = Thread.currentThread().getContextClassLoader().getResourceAsStream("aes.key"))
        {
            var buffer = Buffer.buffer(IOUtils.toString(Objects.requireNonNull(resourceAsStream), StandardCharsets.UTF_8));

            secretKeySpecBytes = buffer.toString().split(" ")[0].trim().getBytes(StandardCharsets.UTF_8);

            ivParameterSpecBytes = buffer.toString().split(" ")[1].trim().getBytes(StandardCharsets.UTF_8);

            var cipher = Cipher.getInstance("AES/CBC/PKCS5PADDING");

            cipher.init(Cipher.DECRYPT_MODE, new SecretKeySpec(secretKeySpecBytes, "AES"), new IvParameterSpec(ivParameterSpecBytes));

            result = new String(cipher.doFinal(Base64.decodeBase64(value)));
        }

        catch (Exception exception)
        {
            exception.getCause();
        }

        return result;
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(1)
    void getLicenseDetails(VertxTestContext testContext)
    {
        TestAPIUtil.get(TestAPIConstants.LICENSE_API_ENDPOINT, testContext.succeeding(response -> testContext.verify(() ->
        {
            assertEquals(SC_OK, response.statusCode());

            var body = response.bodyAsJsonObject();

            Assertions.assertNotNull(body);

            assertEquals(SC_OK, body.getInteger(RESPONSE_CODE));

            Assertions.assertNotNull(body.getJsonObject(RESULT));

            body = body.getJsonObject(RESULT);

            licenseActivationCode = body.getString(LICENSE_ACTIVATION_CODE);

            Assertions.assertNotNull(body.getValue(LICENSE_ACTIVATION_CODE));

            testContext.completeNow();
        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(2)
    void updateLicenseDetails(VertxTestContext testContext)
    {
        var context = new JsonObject().put(LICENSE_ACTIVATION_CODE, licenseActivationCode);

        TestAPIUtil.put(TestAPIConstants.LICENSE_API_ENDPOINT + 1L, context, testContext.succeeding(response -> testContext.verify(() ->
        {
            assertEquals(SC_OK, response.statusCode());

            testContext.completeNow();
        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(3)
    void updateLicenseDetailsInvalid(VertxTestContext testContext)
    {
        var context = new JsonObject().put(LICENSE_ACTIVATION_CODE, "LICENSE_ACTIVATION_CODE");

        TestAPIUtil.put(TestAPIConstants.LICENSE_API_ENDPOINT + 1L, context, testContext.succeeding(response -> testContext.verify(() ->
        {
            assertEquals(SC_BAD_REQUEST, response.statusCode());

            testContext.completeNow();
        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(4)
    void updateLicenseDetailToEnterprise(VertxTestContext testContext)
    {
        var buffer = Bootstrap.vertx().fileSystem().readFileBlocking(GlobalConstants.CURRENT_DIR + GlobalConstants.PATH_SEPARATOR + "license.lic");

        if (buffer != null && buffer.length() > 0)
        {

            var license = new JsonObject(decrypt(buffer.toString()));

            if (!license.isEmpty())
            {
                license.put("license.edition", LicenseUtil.LicenseEdition.OBSERVABILITY.getCode()).put("license.type", LicenseUtil.LicenseType.SUBSCRIPTION.name()).put("description", "Motadata Enterprise Edition");

                TestAPIUtil.put(TestAPIConstants.LICENSE_API_ENDPOINT + 1L, new JsonObject().put("license.activation.code", encrypt(license.encode())), testContext.succeeding(update -> testContext.verify(() ->
                {
                    assertEquals(SC_OK, update.statusCode());

                    assertEquals(LicenseUtil.getLicenseDetails().getInteger("license.edition"), LicenseUtil.LicenseEdition.OBSERVABILITY.getCode());

                    assertEquals(LicenseUtil.getLicenseDetails().getString("license.type"), LicenseUtil.LicenseType.SUBSCRIPTION.name());

                    testContext.completeNow();
                })));
            }

        }
        else
        {
            testContext.failNow("Failed due to no license File found.");
        }
    }
}