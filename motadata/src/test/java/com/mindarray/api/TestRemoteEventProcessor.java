/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

package com.mindarray.api;


import com.mindarray.InfoMessageConstants;
import com.mindarray.TestAPIUtil;
import com.mindarray.TestUtil;
import com.mindarray.eventbus.EventBusConstants;
import com.mindarray.manager.MotadataAppManager;
import com.mindarray.store.RemoteEventProcessorConfigStore;
import com.mindarray.util.CodecUtil;
import com.mindarray.util.CommonUtil;
import com.mindarray.util.Logger;
import com.mindarray.util.MotadataConfigUtil;
import io.github.artsok.RepeatedIfExceptionsTest;
import io.vertx.core.Handler;
import io.vertx.core.eventbus.MessageConsumer;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import io.vertx.junit5.Timeout;
import io.vertx.junit5.VertxExtension;
import io.vertx.junit5.VertxTestContext;
import org.junit.jupiter.api.*;
import org.junit.jupiter.api.condition.EnabledIfSystemProperty;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.api.parallel.Execution;
import org.junit.jupiter.api.parallel.ExecutionMode;

import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;

import static com.mindarray.GlobalConstants.*;
import static com.mindarray.TestAPIConstants.MOTADATA_APP_ENDPOINT;
import static com.mindarray.TestAPIConstants.REMOTE_EVENT_PROCESSOR_API_ENDPOINT;
import static com.mindarray.api.APIConstants.RESPONSE_CODE;
import static com.mindarray.eventbus.EventBusConstants.*;
import static org.apache.http.HttpStatus.SC_BAD_REQUEST;
import static org.apache.http.HttpStatus.SC_OK;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNull;


@ExtendWith(VertxExtension.class)
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
@EnabledIfSystemProperty(named = "test.mode", matches = "^APP|^PRIMARY$")
@Timeout(300 * 1000)
@Execution(ExecutionMode.SAME_THREAD)
public class TestRemoteEventProcessor
{
    private static final JsonObject CONTEXT = new JsonObject();

    private static final Logger LOGGER = new Logger(TestRemoteEventProcessor.class, MOTADATA_NMS, "Remote Event Processor Test");

    private static MessageConsumer<JsonObject> messageConsumer;

    private static String remoteEventProcessorUUID;

    @AfterAll
    static void clean(VertxTestContext testContext)
    {
        if (messageConsumer != null)
        {
            messageConsumer.unregister(result -> testContext.completeNow());
        }
        else
        {
            testContext.completeNow();
        }
    }

    @AfterEach
    void afterEach(VertxTestContext testContext)
    {
        if (messageConsumer != null)
        {
            messageConsumer.unregister(result -> testContext.completeNow());
        }
        else
        {
            testContext.completeNow();
        }
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(1)
    void testGetAllRemoteEventProcessor(VertxTestContext testContext)
    {

        TestAPIUtil.get(REMOTE_EVENT_PROCESSOR_API_ENDPOINT
                , testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertGETAllRequestTestResult(response, RemoteEventProcessorConfigStore.getStore(), new JsonArray().add(STATUS).add(DURATION).add(EventBusConstants.EVENT_TIMESTAMP).add(DISABLED).add(MotadataAppManager.HEARTBEAT_STATE));

                            CONTEXT.put("remote.event.processor", response.bodyAsJsonObject().getJsonArray(RESULT).getJsonObject(0).getLong(ID));

                            testContext.completeNow();
                        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(2)
    void testGetRemoteEventProcessor(VertxTestContext testContext, TestInfo testInfo)
    {

        TestAPIUtil.get(REMOTE_EVENT_PROCESSOR_API_ENDPOINT + "/" + CONTEXT.getLong("remote.event.processor")
                , testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertGETRequestTestResult(response, CONTEXT.getLong("remote.event.processor"), RemoteEventProcessorConfigStore.getStore(), new JsonArray().add(STATUS).add(DURATION).add(DISABLED).add(EventBusConstants.EVENT_TIMESTAMP).add(MotadataAppManager.HEARTBEAT_STATE), LOGGER, testInfo.getTestMethod().get().getName());

                            testContext.completeNow();
                        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(3)
    void testRemoteEventProcessorRegistration(VertxTestContext testContext, TestInfo testInfo)
    {
        LOGGER.info(String.format("running %s", testInfo.getTestMethod().get().getName()));

        // uuid for collector
        remoteEventProcessorUUID = System.getProperty("build.pipeline.collector.uuid");

        Assertions.assertTrue(remoteEventProcessorUUID != null && !remoteEventProcessorUUID.isEmpty());

        var item = RemoteEventProcessorConfigStore.getStore().getItemByValue(RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_UUID, remoteEventProcessorUUID);

        if (item == null)
        {
            var context = new JsonObject().put(RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_HOST, System.getProperty("build.pipeline.collector.host"))
                    .put(RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_TYPE, BootstrapType.COLLECTOR)
                    .put(RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_IP, System.getProperty("build.pipeline.collector.address"))
                    .put(RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_UUID, remoteEventProcessorUUID)
                    .put(RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_VERSION, 1.0);

            TestUtil.vertx().eventBus().send(EventBusConstants.EVENT_REGISTRATION, context.put(EventBusConstants.EVENT_TYPE, EventBusConstants.EVENT_REGISTRATION));

            var retries = new AtomicInteger(0);

            TestUtil.vertx().setPeriodic(1000, timer ->
            {
                retries.getAndIncrement();

                var id = RemoteEventProcessorConfigStore.getStore().getItemIdByUUID(remoteEventProcessorUUID, null, null);

                if (id != null)
                {
                    TestUtil.vertx().cancelTimer(timer);

                    Assertions.assertTrue(id > 0);

                    testContext.completeNow();
                }
                else if (retries.get() == 10)
                {
                    TestUtil.vertx().cancelTimer(timer);

                    testContext.failNow(new Exception("failed to register remote event processor"));
                }
            });
        }
        else
        {
            testContext.completeNow();
        }
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(4)
    void testUpdateRemoteEventProcessor(VertxTestContext testContext, TestInfo testInfo)
    {
        var item = RemoteEventProcessorConfigStore.getStore().getItemByValue(RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_UUID, remoteEventProcessorUUID).put("REMOTE_EVENT_PROCESSOR_VERSION", 1.0);

        TestAPIUtil.put(REMOTE_EVENT_PROCESSOR_API_ENDPOINT + "/" + item.getLong(ID), item,
                testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertUpdateEntityTestResult(RemoteEventProcessorConfigStore.getStore(), item, response.bodyAsJsonObject(),
                                    String.format(InfoMessageConstants.ENTITY_UPDATED, "Remote Event Processor"), LOGGER, testInfo.getTestMethod().get().getName());

                            testContext.completeNow();
                        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(5)
    @Timeout(480 * 1000)
    void testUpgradeRemoteEventProcessor(VertxTestContext testContext, TestInfo testInfo) throws Exception
    {
        testContext.awaitCompletion(5, TimeUnit.SECONDS);

        var item = RemoteEventProcessorConfigStore.getStore().getItemByValue(RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_UUID, remoteEventProcessorUUID);

        Assertions.assertNotNull(item);

        var currentMajorVersion = CommonUtil.getInteger(MotadataConfigUtil.getVersion().split("\\.")[0].trim());

        TestUtil.createZipFile(new String[]{"upgrade1.json", "upgrade2.json", "VERSION"}, CURRENT_DIR + PATH_SEPARATOR + "uploads" + PATH_SEPARATOR + "rpe-upgrade.zip", "linux", (currentMajorVersion + 1) + ".0.0");

        LOGGER.info(String.format("running %s", testInfo.getTestMethod().get().getName()));

        assertEventTestResult(event ->
        {
            LOGGER.info(String.format("received event %s", event));

            if (event.getJsonObject(EVENT_CONTEXT) != null
                    && event.getJsonObject(EVENT_CONTEXT).getString(EVENT_TYPE).equalsIgnoreCase(EVENT_REMOTE_PROCESSOR_UPGRADE))
            {
                LOGGER.info("rpe upgrade event received");

                assertEquals(STATUS_SUCCEED, event.getJsonObject(EVENT_CONTEXT).getString(STATUS));

                messageConsumer.unregister(result ->
                        TestAPIUtil.post(MOTADATA_APP_ENDPOINT + "/" + 1234L + "/upgrade", new JsonObject().put(MotadataAppManager.PATCH_ARTIFACT_FILE, "rpe-upgrade.zip").put(MotadataApp.ARTIFACT_TYPE, BootstrapType.COLLECTOR.name()),
                                testContext.succeeding(response ->
                                        testContext.verify(() ->
                                        {
                                            LOGGER.info(String.format("response : %s", response.bodyAsJsonObject()));

                                            assertEquals(SC_BAD_REQUEST, response.bodyAsJsonObject().getInteger(RESPONSE_CODE));

                                            assertEquals(STATUS_FAIL, response.bodyAsJsonObject().getString(STATUS));

                                            testContext.completeNow();
                                        }))));

            }
        });

        TestAPIUtil.post(MOTADATA_APP_ENDPOINT + "/" + item.getLong(ID) + "/upgrade", new JsonObject().put(MotadataAppManager.PATCH_ARTIFACT_FILE, "rpe-upgrade.zip").put(MotadataApp.ARTIFACT_TYPE, BootstrapType.COLLECTOR.name()), response ->
                testContext.verify(() -> assertEquals(SC_OK, response.result().statusCode())));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(6)
    void testStopRemoteEventProcessor(VertxTestContext testContext, TestInfo testInfo)
    {
        var item = RemoteEventProcessorConfigStore.getStore().getItemByValue(RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_UUID, remoteEventProcessorUUID);

        Assertions.assertNotNull(item);

        LOGGER.info(String.format("running %s", testInfo.getTestMethod().get().getName()));

        assertEventTestResult(event ->
        {
            if (event.getJsonObject(EVENT_CONTEXT) != null
                    && event.getJsonObject(EVENT_CONTEXT).getString(EVENT_TYPE) != null
                    && event.getJsonObject(EVENT_CONTEXT).getString(EVENT_TYPE).equals(EVENT_REMOTE_PROCESSOR_STOP)
                    && event.getJsonObject(EVENT_CONTEXT).getString(MESSAGE) != null)
            {
                Assertions.assertEquals(event.getJsonObject(EVENT_CONTEXT).getString(MESSAGE),
                        String.format(InfoMessageConstants.STOP_SUCCEEDED, APIConstants.Entity.REMOTE_EVENT_PROCESSOR.getName(), item.getString(RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_UUID)));

                Assertions.assertEquals(STATUS_SUCCEED, event.getJsonObject(EVENT_CONTEXT).getString(STATUS));

                testContext.completeNow();
            }
        });

        TestAPIUtil.post(REMOTE_EVENT_PROCESSOR_API_ENDPOINT + "/" + item.getLong(ID) + "/stop", response ->
                testContext.verify(() -> assertEquals(SC_OK, response.result().statusCode())));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(7)
    void testStartRemoteEventProcessor(VertxTestContext testContext, TestInfo testInfo)
    {
        var item = RemoteEventProcessorConfigStore.getStore().getItemByValue(RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_UUID, remoteEventProcessorUUID);

        Assertions.assertNotNull(item);

        LOGGER.info(String.format("running %s", testInfo.getTestMethod().get().getName()));

        assertEventTestResult(event ->
        {
            if (event.getJsonObject(EVENT_CONTEXT) != null
                    && event.getJsonObject(EVENT_CONTEXT).getString(EVENT_TYPE) != null
                    && event.getJsonObject(EVENT_CONTEXT).getString(EVENT_TYPE).equals(EVENT_REMOTE_PROCESSOR_START)
                    && event.getJsonObject(EVENT_CONTEXT).getString(MESSAGE) != null)
            {
                Assertions.assertEquals(event.getJsonObject(EVENT_CONTEXT).getString(MESSAGE),
                        String.format(InfoMessageConstants.START_SUCCEEDED, APIConstants.Entity.REMOTE_EVENT_PROCESSOR.getName(), item.getString(RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_UUID)));

                Assertions.assertEquals(STATUS_SUCCEED, event.getJsonObject(EVENT_CONTEXT).getString(STATUS));

                testContext.completeNow();
            }
        });

        TestAPIUtil.post(REMOTE_EVENT_PROCESSOR_API_ENDPOINT + "/" + item.getLong(ID) + "/start", response ->
                testContext.verify(() -> assertEquals(SC_OK, response.result().statusCode())));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(8)
    void testRestartRemoteEventProcessor(VertxTestContext testContext, TestInfo testInfo)
    {
        var item = RemoteEventProcessorConfigStore.getStore().getItemByValue(RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_UUID, remoteEventProcessorUUID);

        Assertions.assertNotNull(item);

        LOGGER.info(String.format("running %s", testInfo.getTestMethod().get().getName()));

        assertEventTestResult(event ->
        {
            if (event.getJsonObject(EVENT_CONTEXT) != null
                    && event.getJsonObject(EVENT_CONTEXT).getString(EVENT_TYPE) != null
                    && event.getJsonObject(EVENT_CONTEXT).getString(EVENT_TYPE).equals(EVENT_REMOTE_PROCESSOR_RESTART)
                    && event.getJsonObject(EVENT_CONTEXT).getString(MESSAGE) != null)
            {
                Assertions.assertEquals(event.getJsonObject(EVENT_CONTEXT).getString(MESSAGE),
                        String.format(InfoMessageConstants.RESTART_SUCCEEDED, APIConstants.Entity.REMOTE_EVENT_PROCESSOR.getName(), item.getString(RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_UUID)));

                Assertions.assertEquals(STATUS_SUCCEED, event.getJsonObject(EVENT_CONTEXT).getString(STATUS));

                testContext.completeNow();
            }
        });

        TestAPIUtil.post(REMOTE_EVENT_PROCESSOR_API_ENDPOINT + "/" + item.getLong(ID) + "/restart", response ->
                testContext.verify(() -> assertEquals(SC_OK, response.result().statusCode())));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(9)
    void testStopRemoteEventProcessorInvalidId(VertxTestContext testContext)
    {
        TestAPIUtil.post(REMOTE_EVENT_PROCESSOR_API_ENDPOINT + "/" + 1234L + "/stop"
                , testContext.succeeding(response -> testContext.verify(() ->
                {
                    var result = response.bodyAsJsonObject();

                    assertEquals(SC_BAD_REQUEST, result.getInteger(RESPONSE_CODE));

                    assertEquals(STATUS_FAIL, result.getString(STATUS));

                    testContext.completeNow();
                })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(10)
    void testStartRemoteEventProcessorInvalidId(VertxTestContext testContext)
    {
        TestAPIUtil.post(REMOTE_EVENT_PROCESSOR_API_ENDPOINT + "/" + 1234L + "/start"
                , testContext.succeeding(response -> testContext.verify(() ->
                {
                    var result = response.bodyAsJsonObject();

                    assertEquals(SC_BAD_REQUEST, result.getInteger(RESPONSE_CODE));

                    assertEquals(STATUS_FAIL, result.getString(STATUS));

                    testContext.completeNow();
                })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(11)
    void testRestartRemoteEventProcessorInvalidId(VertxTestContext testContext)
    {
        TestAPIUtil.post(REMOTE_EVENT_PROCESSOR_API_ENDPOINT + "/" + 1234L + "/restart"
                , testContext.succeeding(response -> testContext.verify(() ->
                {
                    var result = response.bodyAsJsonObject();

                    assertEquals(SC_BAD_REQUEST, result.getInteger(RESPONSE_CODE));

                    assertEquals(STATUS_FAIL, result.getString(STATUS));

                    testContext.completeNow();
                })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(12)
    void testDeleteRemoteEventProcessor(VertxTestContext testContext, TestInfo testInfo)
    {
        var item = RemoteEventProcessorConfigStore.getStore().getItemByValue(RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_UUID, remoteEventProcessorUUID);

        LOGGER.info(String.format("running %s", testInfo.getTestMethod().get().getName()));

        TestAPIUtil.delete(REMOTE_EVENT_PROCESSOR_API_ENDPOINT + "/" + item.getLong(ID)
                , testContext.succeeding(response -> testContext.verify(() ->
                {
                    var result = response.bodyAsJsonObject();

                    assertEquals(SC_OK, result.getInteger(RESPONSE_CODE));

                    assertEquals(STATUS_SUCCEED, result.getString(STATUS));

                    testContext.awaitCompletion(10, TimeUnit.SECONDS);

                    assertNull(RemoteEventProcessorConfigStore.getStore().getItemByValue(RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_UUID, remoteEventProcessorUUID));

                    testContext.completeNow();
                })));
    }

    private void assertEventTestResult(Handler<JsonObject> handler)
    {
        messageConsumer = TestUtil.vertx().eventBus().localConsumer(EVENT_USER + TestUtil.getSessionId(), message ->
        {
            if (message.body().getString(EVENT_TYPE) != null)
            {
                var eventContext = message.body().getValue(EVENT_CONTEXT);

                if (eventContext instanceof String)
                {
                    try
                    {
                        message.body().put(EVENT_CONTEXT, CodecUtil.toJSONObject(message.body().getBinary(EventBusConstants.EVENT_CONTEXT)));

                    }
                    catch (Exception exception)
                    {
                        LOGGER.error(exception);
                    }
                }
                handler.handle(message.body());
            }
        });
    }
}