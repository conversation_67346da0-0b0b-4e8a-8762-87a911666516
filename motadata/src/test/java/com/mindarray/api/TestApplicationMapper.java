/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

package com.mindarray.api;

import com.mindarray.ErrorMessageConstants;
import com.mindarray.InfoMessageConstants;
import com.mindarray.TestAPIUtil;
import com.mindarray.store.ApplicationMapperConfigStore;
import com.mindarray.util.Logger;
import io.github.artsok.RepeatedIfExceptionsTest;
import io.vertx.core.json.JsonObject;
import io.vertx.junit5.VertxExtension;
import io.vertx.junit5.VertxTestContext;
import org.junit.jupiter.api.*;
import org.junit.jupiter.api.condition.EnabledIfSystemProperty;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.api.parallel.Execution;
import org.junit.jupiter.api.parallel.ExecutionMode;

import static com.mindarray.GlobalConstants.ID;
import static com.mindarray.TestAPIConstants.APPLICATION_MAPPER_API_ENDPOINT;
import static com.mindarray.api.ApplicationMapper.*;


@ExtendWith(VertxExtension.class)
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
@Execution(ExecutionMode.SAME_THREAD)
@EnabledIfSystemProperty(named = "test.mode", matches = "^APP|^PRIMARY$")

public class TestApplicationMapper
{
    private static final long CURRENT_TIME_MILLIS = System.currentTimeMillis();

    private static final String ENTITY_NAME = "Application Mapper";

    private static final JsonObject CONTEXT = new JsonObject();

    private static final Logger LOGGER = new Logger(TestApplicationMapper.class, "Test Application Mapper", "Test Application Mapper");

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(1)
    void testGetAllApplicationMapper(VertxTestContext testContext)
    {
        TestAPIUtil.get(APPLICATION_MAPPER_API_ENDPOINT,
                testContext.succeeding(response -> testContext.verify(() ->
                {
                    TestAPIUtil.assertGETAllRequestTestResult(response, ApplicationMapperConfigStore.getStore(), null);

                    testContext.completeNow();
                })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(2)
    void testCreateApplicationMapper(VertxTestContext testContext, TestInfo testInfo)
    {
        var context = new JsonObject().put(APPLICATION_MAPPER_NAME, "SSH" + CURRENT_TIME_MILLIS).put(APPLICATION_MAPPER_PROTOCOL, "TCP").put(APPLICATION_MAPPER_PORT, 12);

        TestAPIUtil.post(APPLICATION_MAPPER_API_ENDPOINT, context,
                testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertCreateEntityTestResult(ApplicationMapperConfigStore.getStore(), context, response.bodyAsJsonObject(),
                                    String.format(InfoMessageConstants.ENTITY_CREATED, ENTITY_NAME), null, LOGGER, testInfo.getTestMethod().get().getName());

                            TestApplicationMapper.CONTEXT.put("application.mapper", response.bodyAsJsonObject().getLong(ID));

                            testContext.completeNow();
                        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(3)
    void testCreateDuplicateApplicationMapper(VertxTestContext testContext)
    {
        var context = new JsonObject()
                .put(APPLICATION_MAPPER_NAME, "SSH" + CURRENT_TIME_MILLIS).put(APPLICATION_MAPPER_PROTOCOL, "TCP")
                .put(APPLICATION_MAPPER_PORT, 12);

        TestAPIUtil.post(APPLICATION_MAPPER_API_ENDPOINT, context,
                testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertAlreadyExistedEntityTestResult(response.bodyAsJsonObject(), String.format(ErrorMessageConstants.API_FIELD_UNIQUE_RULE, "Port"),
                                    ApplicationMapperConfigStore.getStore(), APPLICATION_MAPPER_NAME, context.getString(APPLICATION_MAPPER_NAME));

                            testContext.completeNow();
                        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(4)
    void testGetApplicationMapper(VertxTestContext testContext, TestInfo testInfo)
    {
        TestAPIUtil.get(APPLICATION_MAPPER_API_ENDPOINT + CONTEXT.getLong("application.mapper"), testContext.succeeding(response ->
                testContext.verify(() ->
                {
                    TestAPIUtil.assertGETRequestTestResult(response, CONTEXT.getLong("application.mapper"), ApplicationMapperConfigStore.getStore(), null, LOGGER, testInfo.getTestMethod().get().getName());

                    testContext.completeNow();

                })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(5)
    void testUpdateApplicationMapper(VertxTestContext testContext, TestInfo testInfo)
    {

        var context = new JsonObject()
                .put(APPLICATION_MAPPER_NAME, "SSH1Update" + CURRENT_TIME_MILLIS).put(APPLICATION_MAPPER_PROTOCOL, "UDP")
                .put(APPLICATION_MAPPER_PORT, 908);

        TestAPIUtil.put(APPLICATION_MAPPER_API_ENDPOINT + TestApplicationMapper.CONTEXT.getLong("application.mapper"), context,
                testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertUpdateEntityTestResult(ApplicationMapperConfigStore.getStore(), context, response.bodyAsJsonObject(),
                                    String.format(InfoMessageConstants.ENTITY_UPDATED, ENTITY_NAME), LOGGER, testInfo.getTestMethod().get().getName());

                            testContext.completeNow();
                        })));

    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(6)
    void testDeleteApplicationMapper(VertxTestContext testContext)
    {
        TestAPIUtil.delete(APPLICATION_MAPPER_API_ENDPOINT + CONTEXT.getLong("application.mapper"), testContext.succeeding(response -> testContext.verify(() ->
        {
            TestAPIUtil.assertDeleteEntityTestResult(ApplicationMapperConfigStore.getStore(), response.bodyAsJsonObject(),
                    String.format(InfoMessageConstants.ENTITY_DELETED, ENTITY_NAME));

            testContext.completeNow();

        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(7)
    void testDeleteApplicationMapperNotExist(VertxTestContext testContext)
    {
        TestAPIUtil.delete(APPLICATION_MAPPER_API_ENDPOINT + 1234, testContext.succeeding(response -> testContext.verify(() ->
        {
            TestAPIUtil.assertNotExistEntityDeleteTestResult(response, ENTITY_NAME, String.format(ErrorMessageConstants.ITEM_NOT_FOUND_IN_STORE, APIConstants.Entity.APPLICATION_MAPPER.getName()));

            testContext.completeNow();

        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(8)
    void testUpdateApplicationMapperHavingNetworkServiceObjects(VertxTestContext testContext, TestInfo testInfo)
    {
        var item = ApplicationMapperConfigStore.getStore().getItemByValue(APPLICATION_MAPPER_NAME, "RIP");

        Assertions.assertNotNull(item);

        Assertions.assertFalse(item.isEmpty());

        var context = new JsonObject().put(APPLICATION_MAPPER_NAME, "RIPUpdate" + CURRENT_TIME_MILLIS);

        TestAPIUtil.put(APPLICATION_MAPPER_API_ENDPOINT + "/" + item.getLong(ID), context,
                testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertUpdateEntityTestResult(ApplicationMapperConfigStore.getStore(), context, response.bodyAsJsonObject(),
                                    String.format(InfoMessageConstants.ENTITY_UPDATED, ENTITY_NAME), LOGGER, testInfo.getTestMethod().get().getName());

                            testContext.completeNow();
                        })));
    }

}
