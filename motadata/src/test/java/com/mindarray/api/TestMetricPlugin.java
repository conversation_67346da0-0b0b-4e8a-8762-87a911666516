/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

package com.mindarray.api;

import com.mindarray.*;
import com.mindarray.eventbus.EventBusConstants;
import com.mindarray.nms.NMSConstants;
import com.mindarray.plugin.PluginEngineConstants;
import com.mindarray.store.*;
import com.mindarray.util.CodecUtil;
import com.mindarray.util.CommonUtil;
import com.mindarray.util.DateTimeUtil;
import com.mindarray.util.Logger;
import io.github.artsok.RepeatedIfExceptionsTest;
import io.vertx.core.Promise;
import io.vertx.core.eventbus.MessageConsumer;
import io.vertx.core.http.HttpHeaders;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import io.vertx.junit5.Timeout;
import io.vertx.junit5.VertxExtension;
import io.vertx.junit5.VertxTestContext;
import org.apache.http.HttpStatus;
import org.junit.jupiter.api.*;
import org.junit.jupiter.api.condition.EnabledIfSystemProperty;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.api.parallel.Execution;
import org.junit.jupiter.api.parallel.ExecutionMode;

import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

import static com.mindarray.GlobalConstants.*;
import static com.mindarray.TestAPIConstants.*;
import static com.mindarray.api.APIConstants.*;
import static com.mindarray.api.User.USER_NAME;
import static com.mindarray.api.User.USER_PASSWORD;
import static com.mindarray.eventbus.EventBusConstants.*;
import static org.apache.http.HttpStatus.SC_BAD_REQUEST;
import static org.apache.http.HttpStatus.SC_OK;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

@ExtendWith(VertxExtension.class)
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
@Timeout(100 * 1000)
@Execution(ExecutionMode.SAME_THREAD)
@EnabledIfSystemProperty(named = "test.mode", matches = "^APP|^PRIMARY$")
public class TestMetricPlugin
{
    private static final Logger LOGGER = new Logger(TestMetricPlugin.class, MOTADATA_NMS, "Metric Plugin Test");

    private static final Map<String, Long> ENTITY_IDS = new HashMap<>();

    private static final JsonArray OBJECTS = new JsonArray();
    private static final Map<String, Long> CREDENTIAL_PROFILES = new HashMap<>();
    private static String metricName;
    private static MessageConsumer<JsonObject> messageConsumer;

    private static MessageConsumer<JsonObject> byteMessageConsumer;

    @BeforeAll
    static void beforeAll(VertxTestContext testContext)
    {
        OBJECTS.addAll(new JsonArray(ObjectConfigStore.getStore().getItemsByValue(AIOpsObject.OBJECT_CATEGORY, NMSConstants.Category.SERVER.getName())
                .stream().filter(item -> JsonObject.mapFrom(item).getString(AIOpsObject.OBJECT_DISCOVERY_METHOD) != null && JsonObject.mapFrom(item).getString(AIOpsObject.OBJECT_DISCOVERY_METHOD).equalsIgnoreCase(NMSConstants.DiscoveryMethod.REMOTE.name()))
                .map(item -> JsonObject.mapFrom(item).getLong(ID))
                .collect(Collectors.toList())));

        assertNotNull(OBJECTS);

        Assertions.assertFalse(OBJECTS.isEmpty());

        var metrics = MetricConfigStore.getStore().getItemsByValues(Metric.METRIC_OBJECT, OBJECTS);

        assertNotNull(metrics);

        Assertions.assertFalse(metrics.isEmpty());

        for (var index = 0; index < metrics.size(); index++)
        {
            var metric = metrics.getJsonObject(index);

            if (metric.getLong(Metric.METRIC_CREDENTIAL_PROFILE) != NOT_AVAILABLE && !CREDENTIAL_PROFILES.containsKey(metric.getString(Metric.METRIC_TYPE)))
            {
                CREDENTIAL_PROFILES.put(metric.getString(Metric.METRIC_TYPE), metric.getLong(Metric.METRIC_CREDENTIAL_PROFILE));
            }

            var ip = ObjectConfigStore.getStore().getItem(metric.getLong(Metric.METRIC_OBJECT)).getString(AIOpsObject.OBJECT_IP);

            if (CommonUtil.isNotNullOrEmpty(ip) && !CREDENTIAL_PROFILES.containsKey(ip))
            {
                CREDENTIAL_PROFILES.put(ip, metric.getLong(Metric.METRIC_CREDENTIAL_PROFILE));
            }
        }

        testContext.completeNow();

    }

    @AfterAll
    static void afterAll(VertxTestContext testContext)
    {
        if (ENTITY_IDS.containsKey("down.object"))
        {
            ObjectStatusCacheStore.getStore().updateItem(ENTITY_IDS.get("down.object"), STATUS_UP, DateTimeUtil.currentSeconds());
        }

        if (byteMessageConsumer != null)
        {
            byteMessageConsumer.unregister();
        }

        if (messageConsumer != null)
        {
            messageConsumer.unregister(result -> testContext.completeNow());
        }
        else
        {
            testContext.completeNow();
        }
    }

    private static void assertMetricPluginPollResult(JsonObject context, VertxTestContext testContext, String methodName)
    {
        try
        {
            byteMessageConsumer = TestUtil.vertx().eventBus().localConsumer(EVENT_METRIC_POLL_RESPONSE, message ->
            {
                try
                {
                    var event = message.body();

                    if (event.getLong(EVENT_ID).equals(context.getLong(EVENT_ID)))
                    {
                        testContext.verify(() ->
                        {
                            LOGGER.info(methodName + ": event: " + event.encode());

                            byteMessageConsumer.unregister();

                            Assertions.assertTrue(event.getString(STATUS).equalsIgnoreCase(GlobalConstants.STATUS_SUCCEED));

                            testContext.completeNow();
                        });
                    }
                }
                catch (Exception exception)
                {
                    LOGGER.error(exception);

                    testContext.failNow(exception);
                }
            });

            TestUtil.vertx().eventBus().send(EVENT_ROUTER, context);
        }
        catch (Exception exception)
        {
            testContext.failNow(exception);
        }
    }

    @BeforeEach
    void beforeEach(VertxTestContext testContext, TestInfo testInfo)
    {
        LOGGER.info(String.format("running test case %s", testInfo.getTestMethod().get().getName()));

        if (byteMessageConsumer != null)
        {
            byteMessageConsumer.unregister();
        }

        if (messageConsumer != null)
        {
            messageConsumer.unregister(result -> testContext.completeNow());
        }
        else
        {
            testContext.completeNow();
        }
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(1)
    @Timeout(value = 60, timeUnit = TimeUnit.SECONDS)
    void testCreateCustomMetricPluginHavingObject(VertxTestContext testContext, TestInfo testInfo)
    {
        var context = TestConstants.prepareParams(testInfo.getTestMethod().get().getName());

        context.put(MetricPlugin.METRIC_PLUGIN_ENTITIES, OBJECTS);

        if (!context.getJsonObject(MetricPlugin.METRIC_PLUGIN_CONTEXT).containsKey(MetricPlugin.METRIC_PLUGIN_CREDENTIAL_PROFILE))
        {
            context.put(MetricPlugin.METRIC_PLUGIN_CREDENTIAL_PROFILE, CREDENTIAL_PROFILES.get(NMSConstants.Type.LINUX.getName()));
        }

        TestAPIUtil.post(METRIC_PLUGIN_API_ENDPOINT, context, testContext.succeeding(response ->
                testContext.verify(() -> assertCreateMetricTestResult(response.bodyAsJsonObject(), context, "custom", testContext, testInfo.getTestMethod().get().getName()))));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(2)
    void testGetCustomMetricPlugin(VertxTestContext testContext, TestInfo testInfo)
    {
        TestAPIUtil.get(METRIC_PLUGIN_API_ENDPOINT + "/" + ENTITY_IDS.get("custom"), testContext.succeeding(response ->
                testContext.verify(() ->
                {
                    TestAPIUtil.assertGETRequestTestResult(response, ENTITY_IDS.get("custom"), MetricPluginConfigStore.getStore(), null, LOGGER, testInfo.getTestMethod().get().getName());

                    testContext.completeNow();
                })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(3)
    void testCreateCustomMetricHavingGroup(VertxTestContext testContext, TestInfo testInfo)
    {
        var groups = new JsonArray(GroupConfigStore.getStore().getItems().stream().map(item -> JsonObject.mapFrom(item).getLong(ID)).collect(Collectors.toList()));

        assertNotNull(groups);

        Assertions.assertFalse(groups.isEmpty());

        var context = TestConstants.prepareParams(testInfo.getTestMethod().get().getName());

        context.put(MetricPlugin.METRIC_PLUGIN_ENTITIES, groups);

        var objects = new JsonArray(ObjectConfigStore.getStore().getItemsByMultiValueFieldAll(AIOpsObject.OBJECT_GROUPS, groups).stream().map(item -> JsonObject.mapFrom(item).getLong(ID)).collect(Collectors.toList()));

        assertNotNull(objects);

        Assertions.assertFalse(objects.isEmpty());

        if (!context.getJsonObject(MetricPlugin.METRIC_PLUGIN_CONTEXT).containsKey(MetricPlugin.METRIC_PLUGIN_CREDENTIAL_PROFILE))
        {
            context.put(MetricPlugin.METRIC_PLUGIN_CREDENTIAL_PROFILE, CREDENTIAL_PROFILES.get(NMSConstants.Type.WINDOWS.getName()));
        }

        TestAPIUtil.post(METRIC_PLUGIN_API_ENDPOINT, context, testContext.succeeding(response ->
                testContext.verify(() ->
                {
                    assertNotNull(response.bodyAsJsonObject().getLong(ID));

                    ENTITY_IDS.put("python.group", response.bodyAsJsonObject().getLong(ID));

                    TestAPIUtil.assertCreateEntityTestResult(MetricPluginConfigStore.getStore(), context, response.bodyAsJsonObject(),
                            String.format(InfoMessageConstants.ENTITY_CREATED, Entity.METRIC_PLUGIN.getName()), null, LOGGER, testInfo.getTestMethod().get().getName());

                    testContext.completeNow();
                })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(4)
    void testGetCustomMetricPluginHavingGroup(VertxTestContext testContext, TestInfo testInfo)
    {
        TestAPIUtil.get(METRIC_PLUGIN_API_ENDPOINT + "/" + ENTITY_IDS.get("python.group"), testContext.succeeding(response ->
                testContext.verify(() ->
                {
                    TestAPIUtil.assertGETRequestTestResult(response, ENTITY_IDS.get("python.group"), MetricPluginConfigStore.getStore(), null, LOGGER, testInfo.getTestMethod().get().getName());

                    // group metric plugin references
                    TestAPIUtil.get(METRIC_PLUGIN_API_ENDPOINT + "/" + ENTITY_IDS.get("python.group") + "/references",
                            testContext.succeeding(httpResponse -> testContext.verify(() ->
                            {
                                LOGGER.info(testInfo.getTestMethod().get().getName() + ": httpResponse: " + httpResponse.bodyAsJsonObject().encode());

                                assertEquals(SC_OK, httpResponse.statusCode());

                                var body = httpResponse.bodyAsJsonObject();

                                Assertions.assertNotNull(body);

                                assertEquals(SC_OK, body.getInteger(RESPONSE_CODE));

                                Assertions.assertNotNull(body.getJsonObject(RESULT));

                                body = body.getJsonObject(RESULT);

                                Assertions.assertTrue(body.containsKey(Entity.GROUP.getName()));

                                Assertions.assertFalse(body.getJsonArray(Entity.GROUP.getName()).isEmpty());

                                testContext.completeNow();
                            })));
                })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(5)
    void testCreateSSHMetricPluginHavingObject(VertxTestContext testContext, TestInfo testInfo)
    {

        var context = TestConstants.prepareParams(testInfo.getTestMethod().get().getName());

        var objects = ObjectConfigStore.getStore().flatItems(AIOpsObject.OBJECT_IP, "************", ID);

        assertNotNull(objects);

        Assertions.assertFalse(objects.isEmpty());

        context.put(MetricPlugin.METRIC_PLUGIN_ENTITIES, objects);

        ENTITY_IDS.put(EventBusConstants.EVENT_OBJECT_DISABLE, objects.getLong(0));

        metricName = context.getString(MetricPlugin.METRIC_PLUGIN_NAME);

        var metrics = MetricConfigStore.getStore().getItemsByValue(Metric.METRIC_OBJECT, ENTITY_IDS.get(EventBusConstants.EVENT_OBJECT_DISABLE));

        assertNotNull(metrics);

        Assertions.assertFalse(metrics.isEmpty());

        for (var index = 0; index < metrics.size(); index++)
        {
            if (metrics.getJsonObject(index).getString(Metric.METRIC_PLUGIN).equalsIgnoreCase(NMSConstants.MetricPlugin.LINUX.getName()))
            {
                context.put(MetricPlugin.METRIC_PLUGIN_CREDENTIAL_PROFILE, metrics.getJsonObject(index).getLong(Metric.METRIC_CREDENTIAL_PROFILE));
            }
        }
        TestAPIUtil.put(OBJECT_API_ENDPOINT + "/" + ENTITY_IDS.get(EventBusConstants.EVENT_OBJECT_DISABLE) + "/state", new JsonObject().put(AIOpsObject.OBJECT_STATE, NMSConstants.State.DISABLE.name()), testContext.succeeding(result ->
                testContext.verify(() ->
                {
                    TestAPIUtil.assertValidResponseTestResult(result, LOGGER, testInfo.getTestMethod().get().getName());

                    var retries = new AtomicInteger();

                    TestUtil.vertx().setPeriodic(TimeUnit.SECONDS.toMillis(3), timer ->
                    {

                        if (ObjectConfigStore.getStore().getItem(ENTITY_IDS.get(EventBusConstants.EVENT_OBJECT_DISABLE)).getString(AIOpsObject.OBJECT_STATE).equalsIgnoreCase(NMSConstants.State.DISABLE.name()))
                        {
                            TestUtil.vertx().cancelTimer(timer);

                            TestAPIUtil.post(METRIC_PLUGIN_API_ENDPOINT, context, testContext.succeeding(response ->
                                    testContext.verify(() -> assertCreateMetricTestResult(response.bodyAsJsonObject(), context, "ssh", testContext, testInfo.getTestMethod().get().getName()))));

                        }
                        else if (retries.get() > 5)
                        {
                            TestUtil.vertx().cancelTimer(timer);

                            testContext.failNow("object state is not disabled in object config store");
                        }
                        else
                        {
                            retries.incrementAndGet();
                        }

                    });

                })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(6)
        // #24324
    void testPollDisabledMonitor(VertxTestContext testContext, TestInfo testInfo)
    {

        var object = ObjectConfigStore.getStore().getItem(ENTITY_IDS.get(EVENT_OBJECT_DISABLE));

        assertMetricPluginTestResult(testContext, STATUS_FAIL, String.format(ErrorMessageConstants.OBJECT_STATE_IS_SAME_ERROR, "Disable"), ErrorCodes.ERROR_CODE_BAD_REQUEST, testInfo.getTestMethod().get().getName());

        TestUtil.vertx().eventBus().send(UI_ACTION_POLL, new JsonObject().put(ID, object.getLong(ID)).put(User.USER_NAME, object.getString(User.USER_NAME))
                .put(SESSION_ID, TestUtil.getSessionId()));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(7)
    void testGetSSHMetricPluginHavingObject(VertxTestContext testContext, TestInfo testInfo)
    {
        TestAPIUtil.get(METRIC_PLUGIN_API_ENDPOINT + "/" + ENTITY_IDS.get("ssh"), testContext.succeeding(response ->
                testContext.verify(() ->
                {
                    TestAPIUtil.assertGETRequestTestResult(response, ENTITY_IDS.get("ssh"), MetricPluginConfigStore.getStore(), null, LOGGER, testInfo.getTestMethod().get().getName());

                    testContext.completeNow();
                })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(8)
    void testGetCredentialProfileReferences(VertxTestContext testContext, TestInfo testInfo)
    {
        var item = MetricPluginConfigStore.getStore().getItem(ENTITY_IDS.get("ssh"));

        Assertions.assertNotNull(item);

        if (item.containsKey(MetricPlugin.METRIC_PLUGIN_CONTEXT))
        {
            item.mergeIn(item.getJsonObject(MetricPlugin.METRIC_PLUGIN_CONTEXT));
        }

        TestAPIUtil.get(CREDENTIAL_PROFILE_API_ENDPOINT + "/" + item.getLong(MetricPlugin.METRIC_PLUGIN_CREDENTIAL_PROFILE) + "/references",
                testContext.succeeding(response -> testContext.verify(() ->
                {
                    LOGGER.info(testInfo.getTestMethod().get().getName() + ": response: " + response.bodyAsJsonObject().encode());

                    assertEquals(SC_OK, response.statusCode());

                    var body = response.bodyAsJsonObject();

                    Assertions.assertNotNull(body);

                    assertEquals(SC_OK, body.getInteger(RESPONSE_CODE));

                    Assertions.assertNotNull(body.getJsonObject(RESULT));

                    body = body.getJsonObject(RESULT);

                    Assertions.assertTrue(body.containsKey(Entity.METRIC.getName()));

                    Assertions.assertFalse(body.getJsonArray(Entity.METRIC.getName()).isEmpty());

                    testContext.completeNow();
                })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(8)
    void testGetCredentialProfileReferencesDataSecurity(VertxTestContext testContext, TestInfo testInfo)
    {
        var user = UserConfigStore.getStore().getItemByValue(USER_NAME, "MinimalAccess");

        Assertions.assertNotNull(user);

        TestAPIUtil.post(ACCESS_TOKEN_API_ENDPOINT, new JsonObject().put(USER_NAME, user.getString(USER_NAME))
                .put(USER_PASSWORD, user.getString(USER_PASSWORD)), asyncResult ->
        {
            if (asyncResult.succeeded())
            {
                var response = asyncResult.result();

                assertEquals(HttpStatus.SC_OK, response.statusCode());

                var body = response.bodyAsJsonObject();

                Assertions.assertNotNull(body);

                Assertions.assertTrue(body.containsKey(APIConstants.AUTH_ACCESS_TOKEN));

                Assertions.assertTrue(body.containsKey(APIConstants.AUTH_REFRESH_TOKEN));

                var item = MetricPluginConfigStore.getStore().getItem(ENTITY_IDS.get("ssh"));

                Assertions.assertNotNull(item);

                if (item.containsKey(MetricPlugin.METRIC_PLUGIN_CONTEXT))
                {
                    item.mergeIn(item.getJsonObject(MetricPlugin.METRIC_PLUGIN_CONTEXT));
                }

                TestAPIUtil.get(CREDENTIAL_PROFILE_API_ENDPOINT + "/" + item.getLong(MetricPlugin.METRIC_PLUGIN_CREDENTIAL_PROFILE) + "/references",
                        new JsonObject().put(HttpHeaders.AUTHORIZATION.toString(), "Bearer " + body.getString(APIConstants.AUTH_ACCESS_TOKEN)),
                        testContext.succeeding(asyncResponse -> testContext.verify(() ->
                        {
                            LOGGER.info(testInfo.getTestMethod().get().getName() + ": response: " + asyncResponse.bodyAsJsonObject().encode());

                            assertEquals(SC_OK, asyncResponse.statusCode());

                            var responseBody = asyncResponse.bodyAsJsonObject();

                            Assertions.assertNotNull(responseBody);

                            assertEquals(SC_OK, responseBody.getInteger(RESPONSE_CODE));

                            Assertions.assertNotNull(responseBody.getJsonObject(RESULT));

                            responseBody = responseBody.getJsonObject(RESULT);

                            Assertions.assertTrue(responseBody.containsKey(Entity.METRIC.getName()));

                            Assertions.assertFalse(responseBody.getJsonArray(Entity.METRIC.getName()).isEmpty());

                            testContext.completeNow();
                        })));
            }
            else
            {
                LOGGER.error(asyncResult.cause());

                testContext.failNow(asyncResult.cause());
            }

        });
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(9)
        //Task - 4066
    void testCheckDisabledMonitorMetricState(VertxTestContext testContext, TestInfo testInfo)
    {
        TestAPIUtil.get(METRIC_API_ENDPOINT + "/" + ENTITY_IDS.get(EventBusConstants.EVENT_OBJECT_DISABLE), testContext.succeeding(response ->
                testContext.verify(() ->
                {
                    LOGGER.info(testInfo.getTestMethod().get().getName() + ": response: " + response.bodyAsJsonObject().encode());

                    assertEquals(SC_OK, response.statusCode());

                    var body = response.bodyAsJsonObject().getJsonObject(RESULT);

                    assertNotNull(body);

                    JsonArray metrics = body.getJsonArray(NMSConstants.Category.CUSTOM.getName());

                    assertNotNull(metrics);

                    for (var index = 0; index < metrics.size(); index++)
                    {

                        if (metrics.getJsonObject(index).getString(Metric.METRIC_NAME).equalsIgnoreCase(metricName))
                        {

                            Assertions.assertTrue(metrics.getJsonObject(index).getString(Metric.METRIC_STATE).equalsIgnoreCase(NMSConstants.State.DISABLE.name()));

                            testContext.completeNow();
                        }
                    }
                })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(10)
    void testEnableObject(VertxTestContext testContext, TestInfo testInfo)
    {
        TestAPIUtil.put(OBJECT_API_ENDPOINT + "/" + ENTITY_IDS.get(EventBusConstants.EVENT_OBJECT_DISABLE) + "/state", new JsonObject().put(AIOpsObject.OBJECT_STATE, NMSConstants.State.ENABLE.name()), testContext.succeeding(result ->
                testContext.verify(() ->
                {
                    TestAPIUtil.assertValidResponseTestResult(result, LOGGER, testInfo.getTestMethod().get().getName());

                    testContext.completeNow();

                })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(11)
    void testCreatePowershellMetricPluginHavingObject(VertxTestContext testContext, TestInfo testInfo)
    {
        var context = TestConstants.prepareParams(testInfo.getTestMethod().get().getName());

        var objects = ObjectConfigStore.getStore().flatItems(AIOpsObject.OBJECT_TYPE, NMSConstants.Type.WINDOWS.getName(), ID);

        assertNotNull(objects);

        context.put(MetricPlugin.METRIC_PLUGIN_ENTITIES, objects);

        if (!context.getJsonObject(MetricPlugin.METRIC_PLUGIN_CONTEXT).containsKey(MetricPlugin.METRIC_PLUGIN_CREDENTIAL_PROFILE))
        {
            context.put(MetricPlugin.METRIC_PLUGIN_CREDENTIAL_PROFILE, CREDENTIAL_PROFILES.get(NMSConstants.Type.WINDOWS.getName()));
        }

        TestAPIUtil.post(METRIC_PLUGIN_API_ENDPOINT, context, testContext.succeeding(response ->
                testContext.verify(() -> assertCreateMetricTestResult(response.bodyAsJsonObject(), context, "powershell", testContext, testInfo.getTestMethod().get().getName()))));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(12)
    void testGetPowershellMetricPluginHavingObject(VertxTestContext testContext, TestInfo testInfo)
    {
        TestAPIUtil.get(METRIC_PLUGIN_API_ENDPOINT + "/" + ENTITY_IDS.get("powershell"), testContext.succeeding(response ->
                testContext.verify(() ->
                {
                    TestAPIUtil.assertGETRequestTestResult(response, ENTITY_IDS.get("powershell"), MetricPluginConfigStore.getStore(), null, LOGGER, testInfo.getTestMethod().get().getName());

                    testContext.completeNow();
                })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(13)
    void testCreateJDBCMetricPluginHavingObject(VertxTestContext testContext, TestInfo testInfo)
    {
        var context = TestConstants.prepareParams(testInfo.getTestMethod().get().getName());

        context.put(MetricPlugin.METRIC_PLUGIN_ENTITIES, OBJECTS);

        if (!context.getJsonObject(MetricPlugin.METRIC_PLUGIN_CONTEXT).containsKey(MetricPlugin.METRIC_PLUGIN_CREDENTIAL_PROFILE))
        {
            context.put(MetricPlugin.METRIC_PLUGIN_CREDENTIAL_PROFILE, CREDENTIAL_PROFILES.get(NMSConstants.Type.LINUX.getName()));
        }

        TestAPIUtil.post(METRIC_PLUGIN_API_ENDPOINT, context, testContext.succeeding(response ->
                testContext.verify(() -> assertCreateMetricTestResult(response.bodyAsJsonObject(), context, "jdbc", testContext, testInfo.getTestMethod().get().getName()))));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(14)
    void testGetJDBCMetricPluginHavingObject(VertxTestContext testContext, TestInfo testInfo)
    {
        TestAPIUtil.get(METRIC_PLUGIN_API_ENDPOINT + "/" + ENTITY_IDS.get("jdbc"), testContext.succeeding(response ->
                testContext.verify(() ->
                {
                    TestAPIUtil.assertGETRequestTestResult(response, ENTITY_IDS.get("jdbc"), MetricPluginConfigStore.getStore(), null, LOGGER, testInfo.getTestMethod().get().getName());

                    testContext.completeNow();
                })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(15)
    void testGetAllMetric(VertxTestContext testContext, TestInfo testInfo)
    {
        TestAPIUtil.get(METRIC_PLUGIN_API_ENDPOINT, testContext.succeeding(response ->
                testContext.verify(() ->
                {
                    LOGGER.info(testInfo.getTestMethod().get().getName() + ": response: " + response.bodyAsJsonObject().encode());

                    assertEquals(SC_OK, response.statusCode());

                    var items = response.bodyAsJsonObject().getJsonArray(RESULT);

                    assertNotNull(items);

                    Assertions.assertFalse(items.isEmpty());

                    var flag = true;

                    for (var index = 0; index < items.size(); index++)
                    {
                        if (items.getJsonObject(index).containsKey(MetricPlugin.METRIC_PLUGIN_VARIABLES) &&
                                items.getJsonObject(index).getJsonObject(MetricPlugin.METRIC_PLUGIN_VARIABLES).containsKey("$$$password$$$"))
                        {
                            flag = false;

                            break;
                        }
                    }

                    if (flag)
                    {
                        testContext.completeNow();
                    }
                    else
                    {
                        testContext.failNow(new Exception("metric plugin script variables contains sensitive fields...."));
                    }
                })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(16)
    void testUnassignObject(VertxTestContext testContext, TestInfo testInfo)
    {
        var objects = ObjectConfigStore.getStore().flatItems(AIOpsObject.OBJECT_TYPE, NMSConstants.Type.WINDOWS.getName(), ID);

        assertNotNull(objects);

        Assertions.assertFalse(objects.isEmpty());

        TestAPIUtil.put(METRIC_PLUGIN_API_ENDPOINT + "/" + ENTITY_IDS.get("python.group") + "/unassign", new JsonObject().put(REQUEST_PARAM_IDS, new JsonArray().add(objects.getLong(0))), testContext.succeeding(response ->
                testContext.verify(() ->
                {
                    LOGGER.info(testInfo.getTestMethod().get().getName() + ": response: " + response.bodyAsJsonObject().encode());

                    assertEquals(String.format(InfoMessageConstants.UNASSIGNED_SUCCEEDED, Entity.METRIC_PLUGIN.getName()), response.bodyAsJsonObject().getString(GlobalConstants.MESSAGE));

                    assertEquals(SC_OK, response.bodyAsJsonObject().getInteger(APIConstants.RESPONSE_CODE));

                    TimeUnit.SECONDS.sleep(3);

                    var item = MetricPluginConfigStore.getStore().getItem(ENTITY_IDS.get("python.group"));

                    assertNotNull(item);

                    assertNotNull(item.getJsonArray(MetricPlugin.METRIC_PLUGIN_ENTITIES));

                    Assertions.assertFalse(item.getJsonArray(MetricPlugin.METRIC_PLUGIN_ENTITIES).contains(objects.getLong(0)));

                    testContext.completeNow();
                })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(17)
    void testAssignObject(VertxTestContext testContext, TestInfo testInfo)
    {
        var objects = ObjectConfigStore.getStore().flatItems(AIOpsObject.OBJECT_IP, "fd00:1:1:1::132", ID);

        assertNotNull(objects);

        Assertions.assertFalse(objects.isEmpty());

        TestAPIUtil.put(METRIC_PLUGIN_API_ENDPOINT + "/" + ENTITY_IDS.get("python.group") + "/assign", new JsonObject().put(REQUEST_PARAM_IDS, new JsonArray().add(objects.getLong(0))), testContext.succeeding(response ->
                testContext.verify(() ->
                {
                    LOGGER.info(testInfo.getTestMethod().get().getName() + ": response: " + response.bodyAsJsonObject().encode());

                    assertEquals(String.format(InfoMessageConstants.ASSIGN_SUCCEEDED, Entity.METRIC_PLUGIN.getName()), response.bodyAsJsonObject().getString(GlobalConstants.MESSAGE));

                    assertEquals(SC_OK, response.bodyAsJsonObject().getInteger(APIConstants.RESPONSE_CODE));

                    var promise = Promise.<Void>promise();

                    TestUtil.vertx().setPeriodic(2000, timer ->
                    {
                        var metrics = MetricConfigStore.getStore().getItemsByMapValueField(Metric.METRIC_CONTEXT, APIConstants.ENTITY_ID, ENTITY_IDS.get("python.group"));

                        for (var index = 0; index < metrics.size(); index++)
                        {
                            if (metrics.getJsonObject(index).getLong(Metric.METRIC_OBJECT).equals(objects.getLong(0)))
                            {
                                TestUtil.vertx().cancelTimer(timer);

                                var item = MetricPluginConfigStore.getStore().getItem(ENTITY_IDS.get("python.group"));

                                assertNotNull(item);

                                assertNotNull(item.getJsonArray(MetricPlugin.METRIC_PLUGIN_ENTITIES));

                                Assertions.assertTrue(item.getJsonArray(MetricPlugin.METRIC_PLUGIN_ENTITIES).contains(objects.getLong(0)));

                                promise.complete();

                                break;
                            }
                        }
                    });

                    promise.future().onComplete(result -> testContext.completeNow());
                })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    //bug - 4166
    @Order(18)
    void testDeleteAssignMonitorInMetric(VertxTestContext testContext, TestInfo testInfo)
    {
        TestAPIUtil.deleteAll(OBJECT_API_ENDPOINT, new JsonObject()
                .put(REQUEST_PARAM_IDS, OBJECTS), testContext.succeeding(response -> testContext.verify(() ->
        {
            LOGGER.info(testInfo.getTestMethod().get().getName() + ": response: " + response.bodyAsJsonObject().encode());

            assertEquals(String.format(ErrorMessageConstants.ENTITY_DELETE_FAILED_IN_USED, Entity.OBJECT.getName()), response.bodyAsJsonObject().getString(GlobalConstants.MESSAGE));

            assertEquals(SC_BAD_REQUEST, response.bodyAsJsonObject().getInteger(APIConstants.RESPONSE_CODE));

            assertEquals(STATUS_FAIL, response.bodyAsJsonObject().getString(GlobalConstants.STATUS));

            assertNotNull(response.bodyAsJsonObject().getJsonArray(RESULT).getJsonObject(0));

            testContext.completeNow();

        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(19)
    void testPollCustomPythonMetric(VertxTestContext testContext, TestInfo testInfo) throws Exception
    {
        testContext.awaitCompletion(10, TimeUnit.SECONDS);

        var metrics = MetricConfigStore.getStore().getItemsByValue(Metric.METRIC_NAME, "Custom");

        assertNotNull(metrics);

        Assertions.assertFalse(metrics.isEmpty());

        var metric = TestNMSUtil.prepareMetricPollContext(metrics.getJsonObject(0));

        assertMetricPluginPollResult(metric.put(APIConstants.SESSION_ID, TestUtil.getSessionId()), testContext, testInfo.getTestMethod().get().getName());
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(20)
    void testTestCustomMetricScript(VertxTestContext testContext, TestInfo testInfo) throws Exception
    {
        testContext.awaitCompletion(10, TimeUnit.SECONDS);

        var object = ObjectConfigStore.getStore().getItemByValue(AIOpsObject.OBJECT_IP, "************");

        Assertions.assertNotNull(object);

        var context = TestConstants.prepareParams("testCreateCustomMetricPluginHavingObject");

        context.put(MetricPlugin.METRIC_PLUGIN_ENTITIES, new JsonArray().add(object.getLong(ID)));

        if (!context.getJsonObject(MetricPlugin.METRIC_PLUGIN_CONTEXT).containsKey(MetricPlugin.METRIC_PLUGIN_CREDENTIAL_PROFILE))
        {
            Assertions.assertTrue(CREDENTIAL_PROFILES.containsKey(object.getString(AIOpsObject.OBJECT_IP)));

            context.put(MetricPlugin.METRIC_PLUGIN_CREDENTIAL_PROFILE, CREDENTIAL_PROFILES.get(object.getString(AIOpsObject.OBJECT_IP)));
        }

        context.put(ID, object.getLong(ID)).put(APIConstants.SESSION_ID, TestUtil.getSessionId()).put(GlobalConstants.TIMEOUT, 20);

        assertMetricPluginTestResult(testContext, STATUS_SUCCEED, InfoMessageConstants.METRIC_PLUGIN_TEST_SUCCEEDED, ErrorCodes.ERROR_CODE_SUCCESS, testInfo.getTestMethod().get().getName());

        ObjectStatusCacheStore.getStore().updateItem(object.getLong(ID), STATUS_UP, DateTimeUtil.currentSeconds());

        TestUtil.vertx().eventBus().send(UI_ACTION_METRIC_PLUGIN_TEST, context);
    }

    @Disabled
    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(21)
    @Timeout(200 * 1000)
        //bug - 3819
    void testTestCustomMetricPluginInvalidScript(VertxTestContext testContext, TestInfo testInfo) throws Exception
    {
        try
        {
            var objects = ObjectConfigStore.getStore().flatItems(AIOpsObject.OBJECT_IP, "************", ID);

            assertNotNull(objects);

            Assertions.assertFalse(objects.isEmpty());

            var context = TestConstants.prepareParams("testCreateSSHMetricPluginHavingObject");

            context.put(MetricPlugin.METRIC_PLUGIN_ENTITIES, new JsonArray().add(objects.getLong(0)));

            if (!context.getJsonObject(MetricPlugin.METRIC_PLUGIN_CONTEXT).containsKey(MetricPlugin.METRIC_PLUGIN_CREDENTIAL_PROFILE))
            {
                Assertions.assertTrue(CREDENTIAL_PROFILES.containsKey("************"));

                context.put(MetricPlugin.METRIC_PLUGIN_CREDENTIAL_PROFILE, CREDENTIAL_PROFILES.get("************"));
            }

            context.put(PluginEngineConstants.PARSING_SCRIPT, "dummy");

            context.put(PluginEngineConstants.SCRIPT_LANGUAGE, PluginEngineConstants.PluginEngine.PYTHON.getName());

            context.put(ID, objects.getLong(0)).put(APIConstants.SESSION_ID, TestUtil.getSessionId()).put(GlobalConstants.TIMEOUT, 20);

            LOGGER.info("Checking assertion :: ");

            assertMetricPluginTestResult(testContext, STATUS_TIME_OUT, null, ErrorCodes.ERROR_CODE_METRIC_PLUGIN_TEST, testInfo.getTestMethod().get().getName());

            ObjectStatusCacheStore.getStore().updateItem(objects.getLong(0), STATUS_UP, DateTimeUtil.currentSeconds());

            TestUtil.vertx().eventBus().send(UI_ACTION_METRIC_PLUGIN_TEST, context);
        }
        catch (Exception exception)
        {
            LOGGER.info("Error while checking test case :: ");

            LOGGER.error(exception);

            testContext.failNow(exception);
        }
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(22)
    void testCreateHTTPMetricPluginHavingObject(VertxTestContext testContext, TestInfo testInfo)
    {
        var context = TestConstants.prepareParams(testInfo.getTestMethod().get().getName());

        var objects = ObjectConfigStore.getStore().flatItems(AIOpsObject.OBJECT_TYPE, NMSConstants.Type.WINDOWS.getName(), ID);

        assertNotNull(objects);

        context.put(MetricPlugin.METRIC_PLUGIN_ENTITIES, objects);

        if (!context.getJsonObject(MetricPlugin.METRIC_PLUGIN_CONTEXT).containsKey(MetricPlugin.METRIC_PLUGIN_CREDENTIAL_PROFILE))
        {
            context.put(MetricPlugin.METRIC_PLUGIN_CREDENTIAL_PROFILE, CREDENTIAL_PROFILES.get(NMSConstants.Type.WINDOWS.getName()));
        }

        TestAPIUtil.post(METRIC_PLUGIN_API_ENDPOINT, context, testContext.succeeding(response ->
                testContext.verify(() ->
                {
                    assertCreateMetricTestResult(response.bodyAsJsonObject(), context, "http", testContext, testInfo.getTestMethod().get().getName());

                    testContext.completeNow();
                })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(23)
    void testGetHTTPMetricPluginHavingObject(VertxTestContext testContext, TestInfo testInfo)
    {
        TestAPIUtil.get(METRIC_PLUGIN_API_ENDPOINT + "/" + ENTITY_IDS.get("http"), testContext.succeeding(response ->
                testContext.verify(() ->
                {
                    TestAPIUtil.assertGETRequestTestResult(response, ENTITY_IDS.get("http"), MetricPluginConfigStore.getStore(), null, LOGGER, testInfo.getTestMethod().get().getName());

                    testContext.completeNow();
                })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(24)
    void testDeleteMetricPlugin(VertxTestContext testContext)
    {
        TestAPIUtil.delete(METRIC_PLUGIN_API_ENDPOINT + "/" + ENTITY_IDS.get("custom"), testContext.succeeding(response ->
                testContext.verify(() ->
                {
                    TestAPIUtil.assertEntityInUsedDeleteTestResult(response, Entity.METRIC_PLUGIN.getName());

                    testContext.completeNow();
                })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(25)
    void testCheckItemPresentInStore(VertxTestContext testContext)
    {
        var metrics = MetricConfigStore.getStore().getItemsByMapValueField(Metric.METRIC_CONTEXT, APIConstants.ENTITY_ID, ENTITY_IDS.get("custom"));

        assertNotNull(metrics);

        Assertions.assertFalse(metrics.isEmpty());

        testContext.completeNow();
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(26)
    void testDeletePowershellMetricAfterUnassigned(VertxTestContext testContext, TestInfo testInfo)
    {
        var objects = ObjectConfigStore.getStore().flatItems(AIOpsObject.OBJECT_TYPE, NMSConstants.Type.WINDOWS.getName(), ID);

        assertNotNull(objects);

        Assertions.assertFalse(objects.isEmpty());

        TestAPIUtil.put(METRIC_PLUGIN_API_ENDPOINT + "/" + ENTITY_IDS.get("powershell") + "/unassign", new JsonObject().put(REQUEST_PARAM_IDS, objects), testContext.succeeding(response ->
                testContext.verify(() ->
                {
                    LOGGER.info(testInfo.getTestMethod().get().getName() + ": response: " + response.bodyAsJsonObject().encode());

                    assertEquals(String.format(InfoMessageConstants.UNASSIGNED_SUCCEEDED, Entity.METRIC_PLUGIN.getName()), response.bodyAsJsonObject().getString(GlobalConstants.MESSAGE));

                    assertEquals(SC_OK, response.bodyAsJsonObject().getInteger(APIConstants.RESPONSE_CODE));

                    var promise = Promise.<Void>promise();

                    TestUtil.vertx().setPeriodic(2000, timer ->
                    {
                        var metrics = MetricConfigStore.getStore().getItemsByMapValueField(Metric.METRIC_CONTEXT, APIConstants.ENTITY_ID, ENTITY_IDS.get("powershell"));

                        if (metrics.isEmpty())
                        {
                            TestUtil.vertx().cancelTimer(timer);

                            promise.complete();
                        }
                    });

                    promise.future().onComplete(result ->
                            TestAPIUtil.delete(METRIC_PLUGIN_API_ENDPOINT + "/" + ENTITY_IDS.get("powershell"), testContext.succeeding(deleteResponse ->
                                    testContext.verify(() ->
                                    {
                                        TestAPIUtil.assertDeleteEntityTestResult(MetricPluginConfigStore.getStore(), deleteResponse.bodyAsJsonObject(), String.format(InfoMessageConstants.ENTITY_DELETED, Entity.METRIC_PLUGIN.getName()));

                                        testContext.completeNow();
                                    }))));
                })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(27)
    void testCreateDuplicateMetric(VertxTestContext testContext)
    {
        var context = TestConstants.prepareParams("testCreateSSHMetricPluginHavingObject");

        var objects = ObjectConfigStore.getStore().flatItems(AIOpsObject.OBJECT_TYPE, NMSConstants.Type.WINDOWS.getName(), ID);

        assertNotNull(objects);

        context.put(MetricPlugin.METRIC_PLUGIN_ENTITIES, objects);

        if (!context.getJsonObject(MetricPlugin.METRIC_PLUGIN_CONTEXT).containsKey(MetricPlugin.METRIC_PLUGIN_CREDENTIAL_PROFILE))
        {
            context.put(MetricPlugin.METRIC_PLUGIN_CREDENTIAL_PROFILE, CREDENTIAL_PROFILES.get(NMSConstants.Type.WINDOWS.getName()));
        }

        TestAPIUtil.post(METRIC_PLUGIN_API_ENDPOINT, context, testContext.succeeding(response ->
                testContext.verify(() ->
                {
                    TestAPIUtil.assertAlreadyExistedEntityTestResult(response.bodyAsJsonObject(), String.format(ErrorMessageConstants.API_FIELD_UNIQUE_RULE, "Metric Name"),
                            MetricPluginConfigStore.getStore(), MetricPlugin.METRIC_PLUGIN_NAME, context.getString(MetricPlugin.METRIC_PLUGIN_NAME));

                    testContext.completeNow();
                })));
    }

    //#4002
    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(28)
    void testInvalidCustomMetricPluginHavingObjectTest(VertxTestContext testContext, TestInfo testInfo) throws Exception
    {
        testContext.awaitCompletion(10, TimeUnit.SECONDS);

        var context = TestConstants.prepareParams(testInfo.getTestMethod().get().getName());

        var objects = ObjectConfigStore.getStore().flatItems(AIOpsObject.OBJECT_IP, "************", ID);

        Assertions.assertNotNull(objects);

        Assertions.assertFalse(objects.isEmpty());

        context.put(MetricPlugin.METRIC_PLUGIN_ENTITIES, new JsonArray().add(objects.getLong(0)));

        if (!context.getJsonObject(MetricPlugin.METRIC_PLUGIN_CONTEXT).containsKey(MetricPlugin.METRIC_PLUGIN_CREDENTIAL_PROFILE))
        {
            Assertions.assertTrue(CREDENTIAL_PROFILES.containsKey("************"));

            context.put(MetricPlugin.METRIC_PLUGIN_CREDENTIAL_PROFILE, CREDENTIAL_PROFILES.get("************"));
        }

        context.put(ID, objects.getLong(0)).put(APIConstants.SESSION_ID, TestUtil.getSessionId()).put(GlobalConstants.TIMEOUT, 20);

        assertMetricPluginTestResult(testContext, STATUS_FAIL, String.format(ErrorMessageConstants.METRIC_PLUGIN_TEST_FAILED, ErrorMessageConstants.INTERNAL_ERROR), ErrorCodes.ERROR_CODE_METRIC_PLUGIN_TEST, testInfo.getTestMethod().get().getName());

        ObjectStatusCacheStore.getStore().updateItem(objects.getLong(0), STATUS_UP, DateTimeUtil.currentSeconds());

        TestUtil.vertx().eventBus().send(UI_ACTION_METRIC_PLUGIN_TEST, context);
    }

    //#4704
    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(29)
    void testTestCustomMetricScriptDownObject(VertxTestContext testContext, TestInfo testInfo) throws Exception
    {
        testContext.awaitCompletion(10, TimeUnit.SECONDS);

        var objects = ObjectConfigStore.getStore().getItemsByValue(AIOpsObject.OBJECT_TYPE, NMSConstants.Type.LINUX.getName());

        var object = new JsonObject();

        for (var index = 0; index < objects.size(); index++)
        {
            if (!objects.getJsonObject(index).containsKey(AIOpsObject.OBJECT_AGENT))
            {
                object.mergeIn(objects.getJsonObject(index));

                break;
            }
        }

        Assertions.assertFalse(object.isEmpty());

        Assertions.assertNotSame(DUMMY_ID, object.getLong(ID));

        var context = TestConstants.prepareParams("testCreateCustomMetricPluginHavingObject");

        context.put(MetricPlugin.METRIC_PLUGIN_ENTITIES, new JsonArray().add(object.getLong(ID)));

        if (!context.getJsonObject(MetricPlugin.METRIC_PLUGIN_CONTEXT).containsKey(MetricPlugin.METRIC_PLUGIN_CREDENTIAL_PROFILE))
        {
            Assertions.assertTrue(CREDENTIAL_PROFILES.containsKey(object.getString(AIOpsObject.OBJECT_IP)));

            context.put(MetricPlugin.METRIC_PLUGIN_CREDENTIAL_PROFILE, CREDENTIAL_PROFILES.get(object.getString(AIOpsObject.OBJECT_IP)));
        }

        context.put(ID, object.getLong(ID)).put(APIConstants.SESSION_ID, TestUtil.getSessionId()).put(GlobalConstants.TIMEOUT, 20);

        assertMetricPluginTestResult(testContext, STATUS_FAIL, String.format(ErrorMessageConstants.METRIC_PLUGIN_TEST_FAILED, ErrorMessageConstants.OBJECT_ERROR)
                , ErrorCodes.ERROR_CODE_NO_ITEM_FOUND, testInfo.getTestMethod().get().getName());

        ObjectStatusCacheStore.getStore().updateItem(object.getLong(ID), STATUS_DOWN, DateTimeUtil.currentSeconds());

        ENTITY_IDS.put("down.object", object.getLong(ID));

        TestUtil.vertx().eventBus().send(UI_ACTION_METRIC_PLUGIN_TEST, context);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(30)
    void testUpdateMetricPlugin(VertxTestContext testContext, TestInfo testInfo)
    {
        var metricPlugin = MetricPluginConfigStore.getStore().getItem();

        Assertions.assertNotNull(metricPlugin);

        if (!metricPlugin.containsKey(MetricPlugin.METRIC_PLUGIN_CONTEXT))
        {
            metricPlugin.put(MetricPlugin.METRIC_PLUGIN_CONTEXT, new JsonObject());
        }
        metricPlugin.put(MetricPlugin.METRIC_PLUGIN_ENTITIES, ObjectConfigStore.getStore().getItemsByValue(AIOpsObject.OBJECT_TYPE, NMSConstants.Type.LINUX.getName()));

        metricPlugin.getJsonObject(MetricPlugin.METRIC_PLUGIN_CONTEXT).put(MetricPlugin.METRIC_PLUGIN_VARIABLES, new JsonObject().put("$$$count$$$", 2));

        TestAPIUtil.put(METRIC_PLUGIN_API_ENDPOINT + "/" + metricPlugin.getLong(ID), metricPlugin,
                testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertUpdateEntityTestResult(MetricPluginConfigStore.getStore(), metricPlugin, response.bodyAsJsonObject(),
                                    String.format(InfoMessageConstants.ENTITY_UPDATED, Entity.METRIC_PLUGIN.getName()), LOGGER, testInfo.getTestMethod().get().getName());

                            testContext.completeNow();
                        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(31)
    void testProvisionMetricPlugin(VertxTestContext testContext)
    {
        var items = ObjectConfigStore.getStore().getItem(ObjectConfigStore.getStore().getItemByIP("***********"));
        assertNotNull(items);
        Assertions.assertFalse(items.isEmpty());
        var metrics = MetricConfigStore.getStore().getMetricNamesByObject(items.getLong(ID));
        var credential = new JsonObject("{\"credential.profile.name\":\"SSH Credential\",\"credential.profile.protocol\":\"HTTP/HTTPS\",\"credential.profile.context\":{\"username\":\"mind123\",\"password\":\"mind123\"}}");
        credential.put("credential.profile.name", credential.getString("credential.profile.name") + System.currentTimeMillis());
        TestAPIUtil.post(CREDENTIAL_PROFILE_API_ENDPOINT, credential, testContext.succeeding(response -> testContext.verify(() ->
        {
            assertEquals(SC_OK, response.bodyAsJsonObject().getInteger(APIConstants.RESPONSE_CODE));
            var context = new JsonObject()
                    .put(AIOpsObject.OBJECT_VENDOR, items.getString(AIOpsObject.OBJECT_VENDOR))
                    .put(PORT, 44333).put(AIOpsObject.OBJECT_TYPE, items.getString(AIOpsObject.OBJECT_TYPE))
                    .put(MetricPlugin.METRIC_PLUGIN_CREDENTIAL_PROFILE, CredentialProfileConfigStore.getStore().getItemByValue(CredentialProfile.CREDENTIAL_PROFILE_PROTOCOL, NMSConstants.Protocol.HTTP_HTTPS.getName()).getLong(ID))
                    .put(REQUEST_PARAM_IDS, new JsonArray().add(items.getLong(ID)));
            TestAPIUtil.post(METRIC_PLUGIN_API_ENDPOINT + "/provision", context, testContext.succeeding(reply -> testContext.verify(() ->
            {
                testContext.awaitCompletion(5, TimeUnit.SECONDS);

                assertEquals(String.format(InfoMessageConstants.ASSIGN_SUCCEEDED, Entity.METRIC_PLUGIN.getName()), reply.bodyAsJsonObject().getString(GlobalConstants.MESSAGE));

                assertEquals(SC_OK, reply.bodyAsJsonObject().getInteger(APIConstants.RESPONSE_CODE));

                var retries = new AtomicInteger(3);

                TestUtil.vertx().setPeriodic(5000, timer ->
                {

                    if (MetricConfigStore.getStore().getMetricNamesByObject(items.getLong(ID)).size() > metrics.size())
                    {
                        testContext.completeNow();
                    }
                    else
                    {
                        if (retries.decrementAndGet() <= 0)
                        {
                            testContext.failNow("max attempt exceeded..");
                        }
                    }

                });
            })));
        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(32)
    void testUpdateObject(VertxTestContext testContext, TestInfo testInfo)
    {
        var object = ObjectConfigStore.getStore().getItem(ObjectConfigStore.getStore().getItemByIP("fd00:1:1:1::132"));

        TestAPIUtil.put("/api/v1/settings/objects/" + object.getLong(ID), object, response ->
        {

            if (response.succeeded())
            {
                LOGGER.info(testInfo.getTestMethod().get().getName() + ": response: " + response.result().bodyAsJsonObject().encode());

                assertEquals(SC_OK, response.result().statusCode());

                var body = response.result().bodyAsJsonObject();

                Assertions.assertNotNull(body);

                assertEquals(SC_OK, body.getInteger(RESPONSE_CODE));

                testContext.completeNow();
            }
            else
            {
                testContext.failNow(response.cause());
            }
        });
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(33)
    void testBulkTestCustomMetricScript(VertxTestContext testContext, TestInfo testInfo) throws Exception
    {
        var objects = new HashSet<Long>();

        var object = ObjectConfigStore.getStore().getItemByValue(AIOpsObject.OBJECT_IP, "************");

        Assertions.assertNotNull(object);

        objects.add(object.getLong(ID));

        var networkObject = ObjectConfigStore.getStore().getItemByValue(AIOpsObject.OBJECT_IP, "**********");

        Assertions.assertNotNull(networkObject);

        objects.add(networkObject.getLong(ID));

        var context = TestConstants.prepareParams("testCreateCustomMetricPluginHavingObject");

        context.put(MetricPlugin.METRIC_PLUGIN_NAME, "Bulk Test Custom Metric").put(MetricPlugin.METRIC_PLUGIN_ENTITIES, new JsonArray().add(object.getLong(ID)).add(networkObject.getLong(ID)));

        if (!context.getJsonObject(MetricPlugin.METRIC_PLUGIN_CONTEXT).containsKey(MetricPlugin.METRIC_PLUGIN_CREDENTIAL_PROFILE))
        {
            Assertions.assertTrue(CREDENTIAL_PROFILES.containsKey(object.getString(AIOpsObject.OBJECT_IP)));

            context.put(MetricPlugin.METRIC_PLUGIN_CREDENTIAL_PROFILE, CREDENTIAL_PROFILES.get(object.getString(AIOpsObject.OBJECT_IP)));
        }

        context.put(APIConstants.SESSION_ID, TestUtil.getSessionId()).put(GlobalConstants.TIMEOUT, 20);

        messageConsumer = TestUtil.vertx().eventBus().localConsumer(EVENT_USER + TestUtil.getSessionId(), message ->
        {
            try
            {
                if (message.body() != null && message.body().getBinary(EVENT_CONTEXT) != null && message.body().containsKey(EVENT_COMPRESSION_TYPE) && message.body().getInteger(EVENT_COMPRESSION_TYPE) == COMPRESSION_TYPE_ONE && message.body().getString(EVENT_TYPE) != null && (message.body().getString(EVENT_TYPE).equalsIgnoreCase(UI_ACTION_METRIC_PLUGIN_TEST)))
                {
                    var eventContext = CodecUtil.toJSONObject(message.body().getBinary(EventBusConstants.EVENT_CONTEXT));

                    LOGGER.info(testInfo.getTestMethod().get().getName() + ": eventContext: " + eventContext.encode());

                    if (eventContext.containsKey(ID) && objects.contains(eventContext.getLong(ID)))
                    {
                        objects.remove(eventContext.getLong(ID));

                        Assertions.assertEquals(STATUS_SUCCEED, eventContext.getString(STATUS));

                        Assertions.assertNotNull(eventContext.getJsonObject(RESULT));

                        Assertions.assertFalse(eventContext.getJsonObject(RESULT).isEmpty());
                    }

                    if (objects.isEmpty())
                    {
                        messageConsumer.unregister(result -> testContext.completeNow());
                    }
                }
            }
            catch (Exception exception)
            {
                LOGGER.debug("Error while doing assertion :: ");

                LOGGER.error(exception);

                testContext.failNow(exception);
            }
        });

        ObjectStatusCacheStore.getStore().updateItem(object.getLong(ID), STATUS_UP, DateTimeUtil.currentSeconds());

        ObjectStatusCacheStore.getStore().updateItem(networkObject.getLong(ID), STATUS_UP, DateTimeUtil.currentSeconds());

        TestUtil.vertx().eventBus().send(UI_ACTION_METRIC_PLUGIN_TEST, context);
    }

    private void assertMetricPluginTestResult(VertxTestContext testContext, String status, String value, String errorCode, String methodName)
    {
        try
        {
            messageConsumer = TestUtil.vertx().eventBus().localConsumer(EVENT_USER + TestUtil.getSessionId(), message ->
            {
                try
                {
                    LOGGER.debug(message != null ? " Null Message : " : message.body());

                    if (message.body() != null && message.body().getBinary(EVENT_CONTEXT) != null && message.body().containsKey(EVENT_COMPRESSION_TYPE) && message.body().getInteger(EVENT_COMPRESSION_TYPE) == COMPRESSION_TYPE_ONE && message.body().getString(EVENT_TYPE) != null && (message.body().getString(EVENT_TYPE).equalsIgnoreCase(UI_ACTION_METRIC_PLUGIN_TEST) || message.body().getString(EVENT_TYPE).equalsIgnoreCase(UI_ACTION_POLL)))
                    {
                        var eventContext = CodecUtil.toJSONObject(message.body().getBinary(EventBusConstants.EVENT_CONTEXT));

                        if (eventContext.containsKey(STATUS))
                        {
                            testContext.verify(() ->
                            {
                                LOGGER.info(methodName + ": eventContext: " + eventContext.encode());

                                Assertions.assertEquals(eventContext.getString(STATUS), status);

                                if (value != null)
                                {
                                    Assertions.assertEquals(eventContext.getString(MESSAGE), value);
                                }

                                Assertions.assertEquals(eventContext.getString(ERROR_CODE), errorCode);

                                messageConsumer.unregister(result -> testContext.completeNow());
                            });

                        }
                    }
                }
                catch (Exception exception)
                {
                    LOGGER.debug("Error while doing assertion :: ");

                    LOGGER.error(exception);

                    testContext.failNow(exception);
                }
            });
        }
        catch (Exception exception)
        {
            LOGGER.debug("Error while doing assertion - ");

            LOGGER.error(exception);

            testContext.failNow(exception);
        }
    }

    private void assertCreateMetricTestResult(JsonObject response, JsonObject context, String key, VertxTestContext testContext, String methodName)
    {
        TestAPIUtil.assertCreateEntityTestResult(MetricPluginConfigStore.getStore(), context, response, String.format(InfoMessageConstants.ENTITY_CREATED, Entity.METRIC_PLUGIN.getName()), null, LOGGER, methodName);

        assertNotNull(response.getLong(ID));

        ENTITY_IDS.put(key, response.getLong(ID));

        testContext.completeNow();

    }
}
