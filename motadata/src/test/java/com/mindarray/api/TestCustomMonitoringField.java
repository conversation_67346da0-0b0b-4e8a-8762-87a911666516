/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

package com.mindarray.api;

import com.mindarray.ErrorMessageConstants;
import com.mindarray.GlobalConstants;
import com.mindarray.InfoMessageConstants;
import com.mindarray.TestAPIUtil;
import com.mindarray.store.CustomMonitoringFieldConfigStore;
import com.mindarray.util.Logger;
import io.github.artsok.RepeatedIfExceptionsTest;
import io.vertx.core.json.JsonObject;
import io.vertx.junit5.VertxExtension;
import io.vertx.junit5.VertxTestContext;
import org.junit.jupiter.api.MethodOrderer;
import org.junit.jupiter.api.Order;
import org.junit.jupiter.api.TestInfo;
import org.junit.jupiter.api.TestMethodOrder;
import org.junit.jupiter.api.condition.EnabledIfSystemProperty;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.api.parallel.Execution;
import org.junit.jupiter.api.parallel.ExecutionMode;

import static com.mindarray.GlobalConstants.MOTADATA_API;
import static com.mindarray.GlobalConstants.RESULT;
import static com.mindarray.TestAPIConstants.CUSTOM_MONITORING_FIELD_API_ENDPOINT;
import static com.mindarray.api.CustomMonitoringField.CUSTOM_MONITORING_FIELD_NAME;


@ExtendWith(VertxExtension.class)
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
@Execution(ExecutionMode.SAME_THREAD)
@EnabledIfSystemProperty(named = "test.mode", matches = "^APP|^PRIMARY$")

public class TestCustomMonitoringField
{
    private static final Logger LOGGER = new Logger(TestCustomMonitoringField.class, MOTADATA_API, "Test Custom Monitoring Field");
    private static long ID;

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(1)
    void testGetAllCustomMonitoringField(VertxTestContext testContext)
    {

        TestAPIUtil.get(CUSTOM_MONITORING_FIELD_API_ENDPOINT
                , testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertGETAllRequestTestResult(response, CustomMonitoringFieldConfigStore.getStore(), null);

                            ID = response.bodyAsJsonObject().getJsonArray(RESULT).getJsonObject(0).getLong(GlobalConstants.ID);

                            testContext.completeNow();
                        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(2)
    void testUpdateCustomMonitoringField(VertxTestContext testContext, TestInfo testInfo)
    {
        var item = new JsonObject().put(CUSTOM_MONITORING_FIELD_NAME, "Location1");

        TestAPIUtil.put(CUSTOM_MONITORING_FIELD_API_ENDPOINT + "/" + ID, item,
                testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertUpdateEntityTestResult(CustomMonitoringFieldConfigStore.getStore(), item, response.bodyAsJsonObject(),
                                    String.format(InfoMessageConstants.ENTITY_UPDATED, "Custom Monitoring Field"), LOGGER, testInfo.getTestMethod().get().getName());

                            testContext.completeNow();
                        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(3)
    void testUpdateDuplicateCustomField(VertxTestContext testContext)
    {
        TestAPIUtil.put(CUSTOM_MONITORING_FIELD_API_ENDPOINT + "/" + ID, new JsonObject()
                        .put(CUSTOM_MONITORING_FIELD_NAME, "Floor"),
                testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertAlreadyExistedEntityTestResult(response.bodyAsJsonObject(), String.format(ErrorMessageConstants.API_FIELD_UNIQUE_RULE, "Field Name"),
                                    CustomMonitoringFieldConfigStore.getStore(), CUSTOM_MONITORING_FIELD_NAME, "Floor");

                            testContext.completeNow();
                        })));
    }
}


