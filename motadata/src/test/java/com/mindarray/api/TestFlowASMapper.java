/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

package com.mindarray.api;

import com.mindarray.ErrorMessageConstants;
import com.mindarray.InfoMessageConstants;
import com.mindarray.TestAPIUtil;
import com.mindarray.store.FlowASMapperConfigStore;
import com.mindarray.util.Logger;
import io.github.artsok.RepeatedIfExceptionsTest;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import io.vertx.junit5.VertxExtension;
import io.vertx.junit5.VertxTestContext;
import org.junit.jupiter.api.MethodOrderer;
import org.junit.jupiter.api.Order;
import org.junit.jupiter.api.TestInfo;
import org.junit.jupiter.api.TestMethodOrder;
import org.junit.jupiter.api.condition.EnabledIfSystemProperty;
import org.junit.jupiter.api.extension.ExtendWith;

import static com.mindarray.GlobalConstants.ID;
import static com.mindarray.TestAPIConstants.FLOW_AS_MAPPER_API_ENDPOINT;
import static com.mindarray.api.FlowASMapper.*;


@ExtendWith(VertxExtension.class)
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
@EnabledIfSystemProperty(named = "test.mode", matches = "^APP|^PRIMARY$")

public class TestFlowASMapper
{
    private static final String ENTITY_NAME = "Flow AS Mapper";

    private static final JsonObject CONTEXT = new JsonObject();

    private static final Logger LOGGER = new Logger(TestFlowASMapper.class, "Test Flow AS Mapper", "Test Flow AS Mapper");

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(1)
    void testCreateFlowASMapper(VertxTestContext testContext, TestInfo testInfo)
    {
        var context = new JsonObject().put(FLOW_AS_MAPPER_NAME, "Stanford").put(FLOW_AS_MAPPER_GROUP, new JsonArray().add("172.16.8.0-255")).put(FLOW_AS_MAPPER_NUMBER, 85).put(FLOW_AS_MAPPER_ORGANIZATION, "Stanford University");

        TestAPIUtil.post(FLOW_AS_MAPPER_API_ENDPOINT, context,
                testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertCreateEntityTestResult(FlowASMapperConfigStore.getStore(), context, response.bodyAsJsonObject(),
                                    String.format(InfoMessageConstants.ENTITY_CREATED, ENTITY_NAME), null, LOGGER, testInfo.getTestMethod().get().getName());

                            TestFlowASMapper.CONTEXT.put("flow.as.mapper", response.bodyAsJsonObject().getLong(ID));

                            testContext.completeNow();
                        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(3)
    void testGetFlowASMapper(VertxTestContext testContext, TestInfo testInfo)
    {
        TestAPIUtil.get(FLOW_AS_MAPPER_API_ENDPOINT + CONTEXT.getLong("flow.as.mapper"), testContext.succeeding(response ->
                testContext.verify(() ->
                {
                    TestAPIUtil.assertGETRequestTestResult(response, CONTEXT.getLong("flow.as.mapper"), FlowASMapperConfigStore.getStore(), null, LOGGER, testInfo.getTestMethod().get().getName());

                    testContext.completeNow();

                })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(4)
    void testGetAllFlowASMapper(VertxTestContext testContext)
    {
        TestAPIUtil.get(FLOW_AS_MAPPER_API_ENDPOINT,
                testContext.succeeding(response -> testContext.verify(() ->
                {
                    TestAPIUtil.assertGETAllRequestTestResult(response, FlowASMapperConfigStore.getStore(), null);

                    testContext.completeNow();
                })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(5)
    void testDeleteFlowASMapper(VertxTestContext testContext)
    {
        TestAPIUtil.delete(FLOW_AS_MAPPER_API_ENDPOINT + CONTEXT.getLong("flow.as.mapper"), testContext.succeeding(response -> testContext.verify(() ->
        {
            TestAPIUtil.assertDeleteEntityTestResult(FlowASMapperConfigStore.getStore(), response.bodyAsJsonObject(),
                    String.format(InfoMessageConstants.ENTITY_DELETED, ENTITY_NAME));

            testContext.completeNow();

        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(6)
    void testDeleteFlowASMapperNotExist(VertxTestContext testContext)
    {
        TestAPIUtil.delete(FLOW_AS_MAPPER_API_ENDPOINT + 1234, testContext.succeeding(response -> testContext.verify(() ->
        {
            TestAPIUtil.assertNotExistEntityDeleteTestResult(response, ENTITY_NAME, String.format(ErrorMessageConstants.ITEM_NOT_FOUND_IN_STORE, APIConstants.Entity.FLOW_AS_MAPPER.getName()));

            testContext.completeNow();

        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(7)
    void testCreateSecondFlowASMapper(VertxTestContext testContext, TestInfo testInfo)
    {
        var context = new JsonObject().put(FLOW_AS_MAPPER_NAME, "Stanford Test 2").put(FLOW_AS_MAPPER_GROUP, new JsonArray().add("1.1.1.1")).put(FLOW_AS_MAPPER_NUMBER, 80).put(FLOW_AS_MAPPER_ORGANIZATION, "Oxford University");

        TestAPIUtil.post(FLOW_AS_MAPPER_API_ENDPOINT, context,
                testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertCreateEntityTestResult(FlowASMapperConfigStore.getStore(), context, response.bodyAsJsonObject(),
                                    String.format(InfoMessageConstants.ENTITY_CREATED, ENTITY_NAME), null, LOGGER, testInfo.getTestMethod().get().getName());

                            testContext.completeNow();
                        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(7)
    void testCreateThirdFlowASMapper(VertxTestContext testContext, TestInfo testInfo)
    {
        var context = new JsonObject().put(FLOW_AS_MAPPER_NAME, "Stanford Test 3").put(FLOW_AS_MAPPER_GROUP, new JsonArray().add("1.1.1.1-1.1.1.10")).put(FLOW_AS_MAPPER_NUMBER, 80).put(FLOW_AS_MAPPER_ORGANIZATION, "Oxford University");

        TestAPIUtil.post(FLOW_AS_MAPPER_API_ENDPOINT, context,
                testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertCreateEntityTestResult(FlowASMapperConfigStore.getStore(), context, response.bodyAsJsonObject(),
                                    String.format(InfoMessageConstants.ENTITY_CREATED, ENTITY_NAME), null, LOGGER, testInfo.getTestMethod().get().getName());

                            testContext.completeNow();
                        })));
    }
}
