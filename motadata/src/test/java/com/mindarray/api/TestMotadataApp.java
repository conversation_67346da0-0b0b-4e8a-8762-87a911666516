/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

package com.mindarray.api;

import com.mindarray.*;
import com.mindarray.datastore.DatastoreConstants;
import com.mindarray.db.DBConstants;
import com.mindarray.eventbus.EventBusConstants;
import com.mindarray.job.JobScheduler;
import com.mindarray.manager.MotadataAppManager;
import com.mindarray.store.AgentCacheStore;
import com.mindarray.store.AgentConfigStore;
import com.mindarray.store.BackupProfileConfigStore;
import com.mindarray.store.SchedulerConfigStore;
import com.mindarray.util.*;
import io.github.artsok.RepeatedIfExceptionsTest;
import io.vertx.core.Future;
import io.vertx.core.Promise;
import io.vertx.core.buffer.Buffer;
import io.vertx.core.eventbus.MessageConsumer;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import io.vertx.junit5.Timeout;
import io.vertx.junit5.VertxExtension;
import io.vertx.junit5.VertxTestContext;
import org.apache.commons.io.FileUtils;
import org.apache.http.HttpStatus;
import org.junit.jupiter.api.*;
import org.junit.jupiter.api.condition.EnabledIfSystemProperty;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.api.parallel.Execution;
import org.junit.jupiter.api.parallel.ExecutionMode;
import org.zeromq.SocketType;
import org.zeroturnaround.zip.ZipUtil;

import java.io.File;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;

import static com.mindarray.GlobalConstants.*;
import static com.mindarray.TestAPIConstants.*;
import static com.mindarray.api.APIConstants.RESPONSE_CODE;
import static com.mindarray.api.Scheduler.*;
import static com.mindarray.db.DBConstants.*;
import static com.mindarray.eventbus.EventBusConstants.*;
import static com.mindarray.manager.MotadataAppManager.PATCH_ARTIFACT_FILE;
import static com.mindarray.nms.NMSConstants.OBJECTS;
import static com.mindarray.nms.NMSConstants.STATE_NOT_REACHABLE;
import static org.apache.http.HttpStatus.SC_BAD_REQUEST;
import static org.apache.http.HttpStatus.SC_OK;
import static org.junit.jupiter.api.Assertions.*;

@ExtendWith(VertxExtension.class)
@Timeout(60 * 1000)
@Execution(ExecutionMode.SAME_THREAD)
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
@EnabledIfSystemProperty(named = "test.mode", matches = "^APP|^PRIMARY$")
public class TestMotadataApp
{
    private static final Logger LOGGER = new Logger(TestMotadataApp.class, MOTADATA_API, "Test Motadata App");

    private static MessageConsumer<JsonObject> messageConsumer;

    @BeforeEach
    void beforeEach(VertxTestContext testContext, TestInfo testInfo)
    {
        LOGGER.info(String.format("running test case %s", testInfo.getTestMethod().get().getName()));

        testContext.completeNow();
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(1)
    void testGetMotadataAppVersion(VertxTestContext testContext)
    {
        TestAPIUtil.get(MOTADATA_APP_ENDPOINT + "/version",
                testContext.succeeding(response ->
                {
                    assertEquals(SC_OK, response.statusCode());

                    assertEquals(STATUS_SUCCEED, response.bodyAsJsonObject().getString(STATUS));

                    assertEquals(MotadataConfigUtil.getVersion(), response.bodyAsJsonObject().getString(RESULT));

                    testContext.completeNow();
                }));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(2)
    void testGetAllArtifacts(VertxTestContext testContext)
    {
        TestAPIUtil.get(MOTADATA_APP_ENDPOINT + "/artifacts" + "?filter=" + new JsonObject().put(MotadataApp.ARTIFACT_TYPE, new JsonArray().add(BootstrapType.APP.name().toLowerCase())).put(MotadataApp.ARTIFACT_MODE, new JsonArray().add(MotadataConfigUtil.getInstallationMode().toLowerCase())),
                testContext.succeeding(response ->
                {
                    assertEquals(SC_OK, response.statusCode());

                    assertEquals(STATUS_SUCCEED, response.bodyAsJsonObject().getString(STATUS));

                    assertFalse(response.bodyAsJsonObject().getJsonArray(RESULT).isEmpty());

                    testContext.completeNow();
                }));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(3)
    void testGetAllCollectors(VertxTestContext testContext)
    {
        TestAPIUtil.get(MOTADATA_APP_ENDPOINT + "/artifacts" + "?filter=" + new JsonObject().put(MotadataApp.ARTIFACT_TYPE, new JsonArray().add(BootstrapType.COLLECTOR.name().toLowerCase())).put(MotadataApp.ARTIFACT_MODE, new JsonArray().add(InstallationMode.STANDALONE.name().toLowerCase())),
                testContext.succeeding(response ->
                {
                    assertEquals(SC_OK, response.statusCode());

                    assertEquals(STATUS_SUCCEED, response.bodyAsJsonObject().getString(STATUS));

                    assertTrue(response.bodyAsJsonObject().containsKey(RESULT));

                    testContext.completeNow();
                }));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(4)
    void testGetAllAgents(VertxTestContext testContext)
    {
        TestAPIUtil.get(MOTADATA_APP_ENDPOINT + "/artifacts" + "?filter=" + new JsonObject().put(MotadataApp.ARTIFACT_TYPE, new JsonArray().add(BootstrapType.AGENT.name().toLowerCase())).put(MotadataApp.ARTIFACT_MODE, new JsonArray().add(InstallationMode.STANDALONE.name().toLowerCase())),
                testContext.succeeding(response ->
                {
                    assertEquals(SC_OK, response.statusCode());

                    assertEquals(STATUS_SUCCEED, response.bodyAsJsonObject().getString(STATUS));

                    assertTrue(response.bodyAsJsonObject().containsKey(RESULT));

                    testContext.completeNow();
                }));
    }

    // MOTADATA-1314
    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(5)
    @Timeout(100 * 1000)
    void testNotReachableAgentUpgrade(VertxTestContext testContext)
    {
        var cipherUtil = new CipherUtil();

        TestUtil.createZipFile(new String[]{"VERSION"}, CURRENT_DIR + PATH_SEPARATOR + "uploads" + PATH_SEPARATOR + "not-reachable.zip", "linux", "10.10.11");

        try (var producer = Bootstrap.zcontext().socket(SocketType.PUSH))
        {
            producer.connect("tcp://*:" + CommonUtil.getEventSubscriberPort());

            var context = new JsonObject().put("object.ip", "************")
                    .put("agent.version", "10.10.10")
                    .put("agent.os.name", "linux")
                    .put("objects", new JsonArray().add("************"))
                    .put("agent.id", "AABBCCDD")
                    .put("event.type", "agent.registration")
                    .put("object.host", "agent-test-146")
                    .put("agent.configs", new JsonObject().put("agent", new JsonObject()
                            .put("agent.id", "WEFGHI")
                            .put("agent.deactivation.status", "no")
                            .put("metric.agent.status", "no")
                            .put("agent.state", "ENABLE")));

            producer.send(Buffer.buffer().appendShortLE(CommonUtil.getShort(EventBusConstants.AGENT_TOPIC.length())).appendString(EventBusConstants.AGENT_TOPIC).appendBytes(cipherUtil.encrypt(CodecUtil.compress(context.encode()))).getBytes());

            testContext.awaitCompletion(10, TimeUnit.SECONDS);

            var id = AgentConfigStore.getStore().getAgentId("AABBCCDD");

            AgentCacheStore.getStore().updateItem(id, AgentCacheStore.getStore().getItem(id).put(MotadataAppManager.HEARTBEAT_STATE, STATE_NOT_REACHABLE));

            TestAPIUtil.post(String.format("/api/v1/system/motadata-app/%s/upgrade", id), new JsonObject().put(MotadataApp.ARTIFACT_TYPE, BootstrapType.AGENT.name()).put(PATCH_ARTIFACT_FILE, "not-reachable.zip"), testContext.succeeding((response) ->
            {
                assertEquals(SC_BAD_REQUEST, response.statusCode());

                assertEquals(STATUS_FAIL, response.bodyAsJsonObject().getString(STATUS));

                assertTrue(response.bodyAsJsonObject().containsKey(MESSAGE));

                assertTrue(response.bodyAsJsonObject().getString(MESSAGE).contains(String.format(ErrorMessageConstants.AGENT_NOT_REACHABLE, "agent-test-146")));

                testContext.completeNow();
            }));
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);

            testContext.failNow(exception);
        }
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(6)
    @Timeout(100 * 1000)
    void testRestoreConfigDB(VertxTestContext testContext)
    {
        try
        {
            FileUtils.copyFileToDirectory(new File(CURRENT_DIR + PATH_SEPARATOR + VERSION_FILE), new File(CURRENT_DIR + PATH_SEPARATOR + CONFIG_DIR));

            ZipUtil.pack(new File(CURRENT_DIR + PATH_SEPARATOR + CONFIG_DIR), new File(CURRENT_DIR + PATH_SEPARATOR + UPLOADS + PATH_SEPARATOR + "Config-Backup-123.zip"), false);

            FileUtils.deleteQuietly(new File(CURRENT_DIR + PATH_SEPARATOR + CONFIG_DIR + PATH_SEPARATOR + VERSION_FILE));

            TestAPIUtil.post(MOTADATA_APP_ENDPOINT + "/restore", new JsonObject().put(DBConstants.BACKUP_FILE, "Config-Backup-123.zip").put(User.USER_PASSWORD, "admin"),
                    testContext.succeeding(response ->
                    {
                        assertEquals(SC_OK, response.statusCode());

                        assertEquals(STATUS_SUCCEED, response.bodyAsJsonObject().getString(STATUS));

                        testContext.completeNow();
                    }));
        }
        catch (Exception exception)
        {
            testContext.failNow(exception.getMessage());

            LOGGER.error(exception);
        }
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(7)
    @Timeout(100 * 1000)
    void testRestoreConfigDBInvalidPassword(VertxTestContext testContext)
    {
        try
        {
            ZipUtil.createEmpty(new File(CURRENT_DIR + PATH_SEPARATOR + UPLOADS + PATH_SEPARATOR + "Config-Backup-1234.zip"));

            TestAPIUtil.post(MOTADATA_APP_ENDPOINT + "/restore", new JsonObject().put(DBConstants.BACKUP_FILE, "Config-Backup-1234.zip").put(User.USER_PASSWORD, "admin123"),
                    testContext.succeeding(response ->
                    {
                        assertEquals(SC_BAD_REQUEST, response.statusCode());

                        assertEquals(STATUS_FAIL, response.bodyAsJsonObject().getString(STATUS));

                        testContext.completeNow();
                    }));
        }
        catch (Exception exception)
        {
            testContext.failNow(exception.getMessage());

            LOGGER.error(exception);
        }
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(8)
    @Timeout(100 * 1000)
    void testRestoreConfigDBInvalidVersionFile(VertxTestContext testContext)
    {
        try
        {
            FileUtils.deleteQuietly(new File(CURRENT_DIR + PATH_SEPARATOR + CONFIG_DIR + PATH_SEPARATOR + VERSION_FILE));

            var tempDirectory = new File(CURRENT_DIR + PATH_SEPARATOR + "temp");

            FileUtils.forceMkdir(tempDirectory);

            FileUtils.copyFileToDirectory(new File(CURRENT_DIR + PATH_SEPARATOR + CONFIG_DIR + PATH_SEPARATOR + MOTADATA_CONFIG_FILE), tempDirectory);

            ZipUtil.pack(new File(CURRENT_DIR + PATH_SEPARATOR + "temp"), new File(CURRENT_DIR + PATH_SEPARATOR + UPLOADS + PATH_SEPARATOR + "Config-Backup-12345.zip"), false);

            TestAPIUtil.post(MOTADATA_APP_ENDPOINT + "/restore", new JsonObject().put(DBConstants.BACKUP_FILE, "Config-Backup-12345.zip").put(User.USER_PASSWORD, "admin"),
                    testContext.succeeding(response ->
                    {
                        assertEquals(SC_BAD_REQUEST, response.statusCode());

                        assertEquals(STATUS_FAIL, response.bodyAsJsonObject().getString(STATUS));

                        testContext.completeNow();
                    }));
        }
        catch (Exception exception)
        {
            testContext.failNow(exception.getMessage());

            LOGGER.error(exception);
        }
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(9)
    @Timeout(120 * 1000)
    void testRunConfigDBBackupJob(VertxTestContext testContext)
    {
        Assertions.assertEquals(2, SchedulerConfigStore.getStore().getItemsByValue(Scheduler.SCHEDULER_JOB_TYPE, JobScheduler.JobType.DATABASE_BACKUP.getName()).size());

        var item = BackupProfileConfigStore.getStore().getItemByValue(BackupProfile.BACKUP_PROFILE_TYPE, BackupProfile.BackupProfileType.CONFIG_DB.getName());

        var dateTime = DateTimeUtil.getScheduledTimestamp(System.currentTimeMillis() + (5 * 1000L));

        var scheduler = new JsonObject()
                .put(SCHEDULER_START_DATE, new SimpleDateFormat("dd-MM-yyyy").format(new Date()))
                .put(SCHEDULER_TIMELINE, CronExpressionUtil.CRON_ONCE)
                .put(Scheduler.SCHEDULER_TIMES, new JsonArray().add(dateTime.split(" ")[1]))
                .put(Scheduler.SCHEDULER_START_DATE, dateTime.split(" ")[0])
                .put(SCHEDULER_JOB_TYPE, JobScheduler.JobType.DATABASE_BACKUP.getName())
                .put(SCHEDULER_CONTEXT, new JsonObject().put(OBJECTS, new JsonArray().add(item.getLong(ID))));

        TestAPIUtil.post(SCHEDULER_API_ENDPOINT, scheduler, response ->
        {
            if (response.succeeded())
            {
                Assertions.assertNotNull(response.result());

                Assertions.assertEquals(HttpStatus.SC_OK, response.result().statusCode());

                assertBackupJobTestResult().onComplete(result ->
                {
                    if (result.succeeded())
                    {
                        LOGGER.debug(String.format("backup file: %s created ", result.result()));

                        send(result.result(), item);

                        TestUtil.vertx().setTimer(5000, id ->
                                assertBackupJobTestResult(item.getLong(ID), result.result()).onComplete(asyncResult ->
                                {
                                    if (asyncResult.succeeded())
                                    {
                                        testContext.completeNow();
                                    }
                                    else
                                    {
                                        testContext.failNow(asyncResult.cause());
                                    }
                                }));
                    }
                    else
                    {
                        testContext.failNow(result.cause());
                    }
                });
            }
            else
            {
                testContext.failNow(response.cause());
            }
        });
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(10)
    @Timeout(200 * 1000)
    void testRunConfigDBBackupJob2(VertxTestContext testContext)
    {
        var item = BackupProfileConfigStore.getStore().getItemByValue(BackupProfile.BACKUP_PROFILE_TYPE, BackupProfile.BackupProfileType.CONFIG_DB.getName());

        var dateTime = DateTimeUtil.getScheduledTimestamp(System.currentTimeMillis() + (5 * 1000L));

        var scheduler = new JsonObject()
                .put(SCHEDULER_START_DATE, new SimpleDateFormat("dd-MM-yyyy").format(new Date()))
                .put(SCHEDULER_TIMELINE, CronExpressionUtil.CRON_ONCE)
                .put(Scheduler.SCHEDULER_TIMES, new JsonArray().add(dateTime.split(" ")[1]))
                .put(Scheduler.SCHEDULER_START_DATE, dateTime.split(" ")[0])
                .put(SCHEDULER_JOB_TYPE, JobScheduler.JobType.DATABASE_BACKUP.getName())
                .put(SCHEDULER_CONTEXT, new JsonObject().put(OBJECTS, new JsonArray().add(item.getLong(ID))));

        TestAPIUtil.post(SCHEDULER_API_ENDPOINT, scheduler, response ->
        {
            if (response.succeeded())
            {
                Assertions.assertNotNull(response.result());

                Assertions.assertEquals(HttpStatus.SC_OK, response.result().statusCode());

                assertBackupJobTestResult().onComplete(result ->
                {
                    if (result.succeeded())
                    {
                        LOGGER.debug(String.format("backup file: %s created ", result.result()));

                        var backupSize = new AtomicInteger(0);

                        Bootstrap.configDBService().getAll(TBL_BACKUP_SNAPSHOT + item.getLong(ID), asyncResult ->
                        {
                            if (asyncResult.succeeded())
                            {
                                if (asyncResult.result() != null)
                                {
                                    send(result.result(), item);

                                    assertBackupTestResult(backupSize, result.result(), item, testContext, new AtomicBoolean(false), new AtomicInteger(0));
                                }
                                else
                                {
                                    testContext.failNow("result it null");
                                }
                            }
                            else
                            {
                                testContext.failNow(asyncResult.cause());
                            }
                        });
                    }
                    else
                    {
                        testContext.failNow(result.cause());
                    }
                });
            }
            else
            {
                testContext.failNow(response.cause());
            }
        });
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(11)
    void testRunDatastoreBackupJob(VertxTestContext testContext)
    {
        var item = BackupProfileConfigStore.getStore().getItemByValue(BackupProfile.BACKUP_PROFILE_TYPE, BackupProfile.BackupProfileType.REPORT_DB.getName());

        var dateTime = DateTimeUtil.getScheduledTimestamp(System.currentTimeMillis() + (61 * 1000L));

        var scheduler = new JsonObject()
                .put(SCHEDULER_START_DATE, new SimpleDateFormat("dd-MM-yyyy").format(new Date()))
                .put(SCHEDULER_TIMELINE, CronExpressionUtil.CRON_ONCE)
                .put(Scheduler.SCHEDULER_TIMES, new JsonArray().add(dateTime.split(" ")[1]))
                .put(Scheduler.SCHEDULER_START_DATE, dateTime.split(" ")[0])
                .put(SCHEDULER_JOB_TYPE, JobScheduler.JobType.DATABASE_BACKUP.getName())
                .put(SCHEDULER_CONTEXT, new JsonObject().put(OBJECTS, new JsonArray().add(item.getLong(ID))));

        TestAPIUtil.post(SCHEDULER_API_ENDPOINT, scheduler, result ->
        {
            try
            {
                if (result.succeeded())
                {
                    Assertions.assertNotNull(result.result());

                    Assertions.assertEquals(HttpStatus.SC_OK, result.result().statusCode());

                    send("/dummy-backup.zip", item);

                    assertBackupJobTestResult(item.getLong(ID), "/dummy-backup.zip").onComplete(asyncResult ->
                    {
                        if (asyncResult.succeeded())
                        {
                            testContext.completeNow();
                        }
                        else
                        {
                            testContext.failNow(asyncResult.cause());
                        }
                    });
                }
                else
                {
                    testContext.failNow(result.cause());
                }
            }
            catch (Exception exception)
            {
                LOGGER.error(exception);

                testContext.failNow(exception);
            }
        });
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(12)
    @Timeout(value = 60 * 1000)
    void testVerifyAgentArtifactCompatibility(VertxTestContext testContext, TestInfo testInfo)
    {
        LOGGER.info(String.format("running %s", testInfo.getTestMethod().get().getName()));

        try
        {
            var srcDir = new File(CURRENT_DIR + PATH_SEPARATOR + UPLOADS + PATH_SEPARATOR + "new-artifacts");

            FileUtils.copyFileToDirectory(new File(CURRENT_DIR + PATH_SEPARATOR + VERSION_FILE), srcDir);

            ZipUtil.pack(srcDir, new File(CURRENT_DIR + PATH_SEPARATOR + UPLOADS + PATH_SEPARATOR + "new-artifacts.zip"));

            FileUtils.deleteQuietly(srcDir);

            messageConsumer = TestUtil.vertx().eventBus().localConsumer(EVENT_USER + TestUtil.getSessionId(), message ->
            {
                if (message.body().containsKey(EVENT_TYPE) && message.body().getString(EVENT_TYPE).equalsIgnoreCase(UI_ACTION_ARTIFACT_COMPATIBILITY_CHECK) && message.body().getBinary(EVENT_CONTEXT) != null)
                {
                    var context = CodecUtil.toJSONObject(message.body().getBinary(EventBusConstants.EVENT_CONTEXT));

                    LOGGER.debug(String.format("artifact compatibility check response : %s", context.encodePrettily()));

                    assertFalse(context.getJsonArray(RESULT).isEmpty());

                    Assertions.assertEquals(STATUS_SUCCEED, context.getString(STATUS));

                    messageConsumer.unregister(asyncResult -> testContext.completeNow());
                }
            });

            TestUtil.vertx().eventBus().send(UI_ACTION_ARTIFACT_COMPATIBILITY_CHECK, new JsonObject().put(EVENT_TYPE, UI_ACTION_ARTIFACT_COMPATIBILITY_CHECK).put(PATCH_ARTIFACT_FILE, "new-artifacts.zip").put(MotadataApp.ARTIFACT_TYPE, new JsonArray().add(BootstrapType.AGENT.name())).put(APIConstants.SESSION_ID, TestUtil.getSessionId()).put(User.USER_NAME, "admin"));
        }
        catch (Exception exception)
        {
            testContext.failNow(exception.getMessage());

            LOGGER.error(exception);
        }
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(13)
    @Timeout(value = 60 * 1000)
    void testVerifyRemoteEventProcessorArtifactCompatibility(VertxTestContext testContext, TestInfo testInfo)
    {
        LOGGER.info(String.format("running %s", testInfo.getTestMethod().get().getName()));

        try
        {
            var srcDir = new File(CURRENT_DIR + PATH_SEPARATOR + UPLOADS + PATH_SEPARATOR + "new-artifacts");

            FileUtils.copyFileToDirectory(new File(CURRENT_DIR + PATH_SEPARATOR + VERSION_FILE), srcDir);

            ZipUtil.pack(srcDir, new File(CURRENT_DIR + PATH_SEPARATOR + UPLOADS + PATH_SEPARATOR + "new-artifacts.zip"));

            FileUtils.deleteQuietly(srcDir);

            messageConsumer = TestUtil.vertx().eventBus().localConsumer(EVENT_USER + TestUtil.getSessionId(), message ->
            {
                if (message.body().containsKey(EVENT_TYPE) && message.body().getString(EVENT_TYPE).equalsIgnoreCase(UI_ACTION_ARTIFACT_COMPATIBILITY_CHECK) && message.body().getBinary(EVENT_CONTEXT) != null)
                {
                    var context = CodecUtil.toJSONObject(message.body().getBinary(EventBusConstants.EVENT_CONTEXT));

                    LOGGER.debug(String.format("artifact compatibility check response : %s", context.encodePrettily()));

                    assertFalse(context.getJsonArray(RESULT).isEmpty());

                    Assertions.assertEquals(STATUS_SUCCEED, context.getString(STATUS));

                    messageConsumer.unregister(asyncResult -> testContext.completeNow());
                }
            });

            TestUtil.vertx().eventBus().send(UI_ACTION_ARTIFACT_COMPATIBILITY_CHECK, new JsonObject().put(EVENT_TYPE, UI_ACTION_ARTIFACT_COMPATIBILITY_CHECK).put(PATCH_ARTIFACT_FILE, "new-artifacts.zip").put(MotadataApp.ARTIFACT_TYPE, new JsonArray().add(BootstrapType.APP.name())).put(APIConstants.SESSION_ID, TestUtil.getSessionId()).put(User.USER_NAME, "admin"));
        }
        catch (Exception exception)
        {
            testContext.failNow(exception.getMessage());

            LOGGER.error(exception);
        }
    }

    private Future<String> assertBackupJobTestResult()
    {
        // once scheduler trigger , it will create backup zip file in backup folder

        var promise = Promise.<String>promise();

        var retries = new AtomicInteger(0);

        TestUtil.vertx().setPeriodic(5000, timer ->
        {
            try
            {
                var backupDir = new File(DBConstants.CONFIG_DB_BACKUP_PATH);

                var backupZips = backupDir.listFiles();

                if (backupDir.exists() && backupZips != null && backupZips.length > 0)
                {
                    var fileName = EMPTY_VALUE;

                    for (var file : backupZips)
                    {
                        if (file.getAbsolutePath().endsWith("zip"))
                        {
                            fileName = file.getAbsolutePath();

                            break;
                        }
                    }

                    if (!fileName.isEmpty() && !promise.future().isComplete())
                    {
                        TestUtil.vertx().cancelTimer(timer);

                        promise.complete(fileName);
                    }
                    else
                    {
                        retries.getAndIncrement();
                    }
                }
                else if (retries.get() > 15)
                {
                    TestUtil.vertx().cancelTimer(timer);

                    promise.fail("failed to create backup");
                }
                else
                {
                    retries.getAndIncrement();
                }
            }
            catch (Exception exception)
            {
                LOGGER.error(exception);
            }
        });

        return promise.future();
    }

    private Future<Void> assertBackupJobTestResult(long id, String zipFile)
    {
        var promise = Promise.<Void>promise();

        var retries = new AtomicInteger(0);

        var fileName = zipFile.split(PATH_SEPARATOR)[zipFile.split(PATH_SEPARATOR).length - 1];

        LOGGER.info(String.format("checking %s in backup result ", zipFile));

        TestUtil.vertx().setPeriodic(7000, timer ->
        {
            try
            {
                TestAPIUtil.get(MOTADATA_APP_PROFILE_API_ENDPOINT + "/backup-snapshots/" + id, asyncResult ->
                {
                    if (asyncResult.succeeded())
                    {
                        var result = asyncResult.result().bodyAsJsonObject();

                        Assertions.assertEquals(HttpStatus.SC_OK, result.getInteger(RESPONSE_CODE));

                        if (result.containsKey(RESULT) && !result.getJsonArray(RESULT).isEmpty())
                        {
                            var snapshot = result.getJsonArray(RESULT).getJsonObject(0);

                            if (snapshot.getString(GlobalConstants.SRC_FILE_PATH).equalsIgnoreCase(fileName))
                            {
                                TestUtil.vertx().cancelTimer(timer);

                                promise.complete();
                            }
                            else if (retries.get() > 10)
                            {
                                TestUtil.vertx().cancelTimer(timer);

                                promise.fail("failed to update backup profile");

                                LOGGER.warn("failed to update backup profile");
                            }
                            else
                            {
                                retries.getAndIncrement();
                            }
                        }
                        else
                        {
                            retries.getAndIncrement();
                        }
                    }
                    else
                    {
                        retries.getAndIncrement();
                    }

                });
            }
            catch (Exception exception)
            {
                LOGGER.error(exception);
            }
        });

        return promise.future();
    }

    private void assertBackupTestResult(AtomicInteger currentBackupSize, String fileName, JsonObject context, VertxTestContext testContext, AtomicBoolean thresholdReached, AtomicInteger retries)
    {
        TestUtil.vertx().setTimer(TimeUnit.SECONDS.toMillis(7), id ->
                Bootstrap.configDBService().getAll(TBL_BACKUP_SNAPSHOT + context.getLong(ID), asyncResult ->
                {
                    if (asyncResult.succeeded())
                    {
                        var backupSize = asyncResult.result().size();

                        LOGGER.info("backupSize :" + backupSize);

                        if (backupSize > DBConstants.MAX_LOCAL_BACKUP_COPIES)
                        {
                            testContext.failNow("backup size exceeds the max local backup copies");
                        }
                        else if (thresholdReached.get() && asyncResult.result().size() == MAX_LOCAL_BACKUP_COPIES)
                        {
                            testContext.completeNow();
                        }
                        else if (asyncResult.result().size() == MAX_LOCAL_BACKUP_COPIES)
                        {
                            currentBackupSize.set(asyncResult.result().size());

                            thresholdReached.set(true);

                            send(fileName, context);

                            retries.set(0);

                            assertBackupTestResult(currentBackupSize, fileName, context, testContext, thresholdReached, retries);
                        }
                        else if (asyncResult.result().size() > currentBackupSize.get())
                        {
                            currentBackupSize.set(asyncResult.result().size());

                            send(fileName, context);

                            retries.set(0);

                            assertBackupTestResult(currentBackupSize, fileName, context, testContext, thresholdReached, retries);
                        }
                        else if (retries.get() > 2)
                        {
                            testContext.failNow("config db failed to update in even 21 seconds!");
                        }
                        else
                        {
                            retries.set(retries.incrementAndGet());

                            assertBackupTestResult(currentBackupSize, fileName, context, testContext, thresholdReached, retries);
                        }
                    }
                }));
    }

    private void send(String fileName, JsonObject context)
    {
        // send dummy response as it will come from manager

        context.put(BACKUP_END_TIME, System.currentTimeMillis() + 4)
                .put(BACKUP_START_TIME, System.currentTimeMillis())
                .put(STATUS, STATUS_SUCCEED)
                .put(DatastoreConstants.BACKUP_SIZE_BYTES, 10)
                .put(GlobalConstants.SRC_FILE_PATH, fileName)
                .put(SYSTEM_BOOTSTRAP_TYPE, BootstrapType.APP.name())
                .put(EVENT_SCHEDULER, 12345L)
                .put(EVENT_TYPE, EVENT_DATABASE_BACKUP);

        Bootstrap.vertx().eventBus().send(EVENT_MANAGER_RESPONSE_PROCESSOR, context);

        LOGGER.debug(String.format("response send for %s ", fileName));
    }
}
