/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

package com.mindarray.api;

import com.mindarray.*;
import com.mindarray.eventbus.EventBusConstants;
import com.mindarray.store.SMSGatewayConfigStore;
import com.mindarray.util.CodecUtil;
import com.mindarray.util.Logger;
import io.github.artsok.RepeatedIfExceptionsTest;
import io.vertx.core.eventbus.MessageConsumer;
import io.vertx.core.json.JsonObject;
import io.vertx.junit5.Timeout;
import io.vertx.junit5.VertxExtension;
import io.vertx.junit5.VertxTestContext;
import org.junit.jupiter.api.*;
import org.junit.jupiter.api.condition.EnabledIfSystemProperty;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.api.parallel.Execution;
import org.junit.jupiter.api.parallel.ExecutionMode;

import static com.mindarray.ErrorMessageConstants.SMS_GATEWAY_TEST_FAILED;
import static com.mindarray.GlobalConstants.*;
import static com.mindarray.TestAPIConstants.SMS_GATEWAY_CONFIGURATION_API_ENDPOINT;
import static com.mindarray.api.APIConstants.SESSION_ID;
import static com.mindarray.api.SMSGatewayConfiguration.SMS_SERVER_GATEWAY_URL;
import static com.mindarray.eventbus.EventBusConstants.*;
import static org.junit.jupiter.api.Assertions.assertEquals;

@ExtendWith(VertxExtension.class)
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
@Timeout(50 * 1000)
@Disabled
@Execution(ExecutionMode.SAME_THREAD)
@EnabledIfSystemProperty(named = "test.mode", matches = "^APP|^PRIMARY$")
public class TestSMSConfiguration
{
    private static final JsonObject SMS_CONTEXT = new JsonObject().put(SMS_SERVER_GATEWAY_URL, "http://api.textlocal.in/send/?username=<EMAIL>&hash=b3fb8f0b0e5d2c0ca467faf2a4baac0fc794acb7e4f7b3873e874efd3d4134c0&sender=TXTLCL&numbers=$$number$$&message=$$message$$")
            .put(TARGET, "7990311324")
            .put(MESSAGE, "Test message from motadata");
    private static final JsonObject FAIL_SMS_CONTEXT = new JsonObject().put(SMS_SERVER_GATEWAY_URL, "http://api.textlocal.in/send/?username=<EMAIL>&hash=b3fb8f00fc794acb7e4f7b3873e874efd3d4134c0&sender=TXTLCL&numbers=$$number$$&message=$$message$$")
            .put(TARGET, "7990311324")
            .put(MESSAGE, "Test message from motadata");
    private static final Logger LOGGER = new Logger(TestSMSConfiguration.class, MOTADATA_API, "Test SMS Configuration");
    private static MessageConsumer<JsonObject> messageConsumer;

    @AfterAll
    static void cleanUp(VertxTestContext testContext)
    {
        messageConsumer.unregister(result ->
                testContext.completeNow());
    }

    private static void assertSMSTestResult(VertxTestContext testContext, String value, String status, String errorCode)
    {
        messageConsumer = Bootstrap.vertx().eventBus().localConsumer(EVENT_USER + TestUtil.getSessionId(), message ->
        {
            try
            {
                var eventContext = CodecUtil.toJSONObject(message.body().getBinary(EventBusConstants.EVENT_CONTEXT));

                if (eventContext.getString(STATUS) != null && eventContext.getString(MESSAGE) != null && message.body().containsKey(EVENT_TYPE))
                {
                    testContext.verify(() ->
                    {

                        assertEquals(status, eventContext.getString(STATUS));

                        assertEquals(errorCode, eventContext.getString(ERROR_CODE));

                        assertEquals(value, eventContext.getString(MESSAGE));

                        messageConsumer.unregister(result -> testContext.completeNow());
                    });
                }
            }
            catch (Exception exception)
            {
                messageConsumer.unregister(result -> testContext.failNow(exception));
            }
        });
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(1)
    void testGetSMSServer(VertxTestContext testContext, TestInfo testInfo)
    {
        TestAPIUtil.get(SMS_GATEWAY_CONFIGURATION_API_ENDPOINT + GlobalConstants.DEFAULT_ID, testContext.succeeding(response ->
                testContext.verify(() ->
                {
                    TestAPIUtil.assertGETRequestTestResult(response, GlobalConstants.DEFAULT_ID, SMSGatewayConfigStore.getStore(), null, LOGGER, testInfo.getTestMethod().get().getName());

                    testContext.completeNow();

                })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(2)
    void testUpdateSMSGatewayConfiguration(VertxTestContext testContext, TestInfo testInfo)
    {
        var context = new JsonObject()
                .put(SMS_SERVER_GATEWAY_URL, "http://api.textlocal.in/send/?username=<EMAIL>&hash=qSi5JdhO4tc-xQFfwCUTRbW82FbTQ0nSZtOecVDbWCd&sender=TXTLCL&numbers=$$number$$&message=$$message$$");

        TestAPIUtil.put(SMS_GATEWAY_CONFIGURATION_API_ENDPOINT + GlobalConstants.DEFAULT_ID, context,
                testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertUpdateEntityTestResult(SMSGatewayConfigStore.getStore(), context, response.bodyAsJsonObject(),
                                    String.format(InfoMessageConstants.ENTITY_UPDATED, "SMS Gateway Configuration"), LOGGER, testInfo.getTestMethod().get().getName());

                            testContext.completeNow();

                        })));

    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(3)
    @EnabledIfSystemProperty(named = "env.type", matches = "prod")
    void testSendSMS(VertxTestContext testContext)
    {
        assertSMSTestResult(testContext, InfoMessageConstants.SMS_GATEWAY_TEST_SUCCEEDED, STATUS_SUCCEED, ErrorCodes.ERROR_CODE_SUCCESS);

        Bootstrap.vertx().eventBus().send(EVENT_SERVER, new JsonObject().put(EVENT_TYPE, UI_ACTION_SMS_GATEWAY_CONFIGURATION_TEST).put(SESSION_ID, TestUtil.getSessionId())
                .put(EVENT_CONTEXT, SMS_CONTEXT.put(SESSION_ID, TestUtil.getSessionId())));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(4)
    void testSendSMSInvalid(VertxTestContext testContext)
    {
        assertSMSTestResult(testContext, String.format(SMS_GATEWAY_TEST_FAILED, "{\"errors\":[{\"code\":3,\"message\":\"Invalid login details\"}],\"status\":\"failure\"}"), STATUS_FAIL, ErrorCodes.ERROR_CODE_SMS_CONFIG_TEST);

        Bootstrap.vertx().eventBus().send(EVENT_SERVER, new JsonObject().put(SESSION_ID, TestUtil.getSessionId()).put(EVENT_TYPE, UI_ACTION_SMS_GATEWAY_CONFIGURATION_TEST)
                .put(EVENT_CONTEXT, FAIL_SMS_CONTEXT.put(SESSION_ID, TestUtil.getSessionId())));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(5)
    void testSendSMSInvalidTarget(VertxTestContext testContext)
    {
        assertSMSTestResult(testContext, String.format(SMS_GATEWAY_TEST_FAILED, "Cannot invoke \"String.length()\" because \"s\" is null"), STATUS_FAIL, ErrorCodes.ERROR_CODE_INTERNAL_ERROR);

        var context = FAIL_SMS_CONTEXT.put(SESSION_ID, TestUtil.getSessionId());

        context.remove(TARGET);

        Bootstrap.vertx().eventBus().send(EVENT_SERVER, new JsonObject().put(SESSION_ID, TestUtil.getSessionId()).put(EVENT_TYPE, UI_ACTION_SMS_GATEWAY_CONFIGURATION_TEST)
                .put(EVENT_CONTEXT, context));
    }
}
