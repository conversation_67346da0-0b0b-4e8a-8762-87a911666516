/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

package com.mindarray.service;

import com.mindarray.Bootstrap;
import com.mindarray.GlobalConstants;
import com.mindarray.TestUtil;
import com.mindarray.eventbus.EventBusConstants;
import com.mindarray.nms.NMSConstants;
import com.mindarray.util.HealthUtil;
import com.mindarray.util.Logger;
import com.mindarray.visualization.VisualizationManager;
import io.github.artsok.RepeatedIfExceptionsTest;
import io.vertx.core.json.JsonObject;
import io.vertx.junit5.Timeout;
import io.vertx.junit5.VertxExtension;
import io.vertx.junit5.VertxTestContext;
import org.junit.jupiter.api.*;
import org.junit.jupiter.api.condition.EnabledIfSystemProperty;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.api.parallel.Execution;
import org.junit.jupiter.api.parallel.ExecutionMode;

import java.util.concurrent.TimeUnit;

import static com.mindarray.GlobalConstants.RESULT;
import static com.mindarray.api.Metric.METRIC_CATEGORY;
import static com.mindarray.api.RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_UUID;
import static com.mindarray.eventbus.EventBusConstants.EVENT_MOTADATA_STATS_QUERY;

@ExtendWith(VertxExtension.class)
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
@Execution(ExecutionMode.SAME_THREAD)
@Timeout(80 * 1000)
@EnabledIfSystemProperty(named = "test.mode", matches = "^APP|^PRIMARY|SLAVE$")
public class TestAutoScaler
{


    private static final Logger LOGGER = new Logger(TestAutoScaler.class, GlobalConstants.MOTADATA_NMS, "Auto Scaler test");

    @BeforeAll
    static void beforeAll(VertxTestContext testContext)
    {
        var deploymentId = Bootstrap.getDeployedVerticles().get(VisualizationManager.class.getSimpleName());

        Assertions.assertNotNull(deploymentId);

        // to avoid unnecessary visualizationManager responses
        Bootstrap.vertx().undeploy(deploymentId, result ->
        {
            if (result.succeeded())
            {
                testContext.completeNow();
            }
        });
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(1)
    void testAutoScaleMetricPoller(VertxTestContext testContext)
    {

        try
        {
            var response = new JsonObject("{\"event.type\":\"engine.statistics\",\"engine.type\":\"metric.poll\",\"remote.event.processor.uuid\":\"ABC\",\"health.stats\":{\"pending.events\":0,\"metric.category\":[{\"metric.category\":\"Network\",\"metric.category.pending.events\":0,\"metric.category.pending.batch.events\":0,\"metric.category.idle.workers\":3},{\"metric.category\":\"Server\",\"metric.category.pending.events\":0,\"metric.category.pending.batch.events\":0,\"metric.category.idle.workers\":2},{\"metric.category\":\"Virtualization\",\"metric.category.pending.events\":0,\"metric.category.pending.batch.events\":0,\"metric.category.idle.workers\":1},{\"metric.category\":\"Cloud\",\"metric.category.pending.events\":0,\"metric.category.pending.batch.events\":0,\"metric.category.idle.workers\":1},{\"metric.category\":\"Service Check\",\"metric.category.pending.events\":0,\"metric.category.pending.batch.events\":0,\"metric.category.idle.workers\":1},{\"metric.category\":\"Middleware\",\"metric.category.pending.events\":0,\"metric.category.pending.batch.events\":0,\"metric.category.idle.workers\":1},{\"metric.category\":\"Database\",\"metric.category.pending.events\":0,\"metric.category.pending.batch.events\":0,\"metric.category.idle.workers\":1},{\"metric.category\":\"Web Server\",\"metric.category.pending.events\":0,\"metric.category.pending.batch.events\":0,\"metric.category.idle.workers\":1},{\"metric.category\":\"Other\",\"metric.category.pending.events\":0,\"metric.category.pending.batch.events\":0,\"metric.category.idle.workers\":1},{\"metric.category\":\"Custom\",\"metric.category.pending.events\":0,\"metric.category.pending.batch.events\":0,\"metric.category.idle.workers\":1},{\"metric.category\":\"availability\",\"metric.category.pending.events\":0,\"metric.category.idle.workers\":2},{\"metric.category\":\"network.service\",\"metric.category.pending.events\":0,\"metric.category.idle.workers\":2},{\"metric.category\":\"cloud.status\",\"metric.category.pending.events\":0,\"metric.category.idle.workers\":2}],\"pending.batch.events\":0,\"dropped.events\":0,\"idle.workers\":13}}").put(REMOTE_EVENT_PROCESSOR_UUID, Bootstrap.getRegistrationId());

            for (var index = 0; index < 10; index++)
            {
                var stats = response.getJsonObject(HealthUtil.HEALTH_STATS);

                var category = stats.getJsonArray(METRIC_CATEGORY).getJsonObject(0);

                category.put("metric.category.pending.batch.events", 2000 + index);

                Bootstrap.vertx().eventBus().publish(EventBusConstants.EVENT_ENGINE_STATS_RESPONSE, response); // adding more pending events of network to qualify that category ...

                testContext.awaitCompletion(500, TimeUnit.MILLISECONDS);
            }

            TestUtil.vertx().eventBus().<JsonObject>localConsumer(EventBusConstants.EVENT_METRIC_POLLER_TEST, message ->
            {
                LOGGER.trace(message.body());

                var event = message.body();

                Assertions.assertNotNull(event.getJsonArray("categories"));

                Assertions.assertEquals(NMSConstants.Category.NETWORK.getName(), event.getJsonArray("categories").getString(0));

                Assertions.assertTrue(event.getInteger("workers") > 0); //qualified executors is greater than 0 due to deployment type is 0, and cpu:17%, memory:15%

                testContext.completeNow();
            });

            //triggering condition, cpu:17%, memory:15%
            TestUtil.vertx().eventBus().send(EVENT_MOTADATA_STATS_QUERY, new JsonObject().put(RESULT, "4WDPEHQBAADiYM8QdAEAAGQUAAAAAAAAAAYAAAAAAAAAAQQFAQAAAB0AAAAAsAcAbW9uaXRvcgcAbW9uaXRvcgEAAAAxOgAAAAGgFgBzeXN0ZW0uY3B1LnBlcmNlbnReYXZnEgBzeXN0ZW0uY3B1LnBlcmNlbnQAAAAAAAAsQEQAAAABoBsAc3lzdGVtLmNwdS51c2VyLnBlcmNlbnReYXZnFwBzeXN0ZW0uY3B1LnVzZXIucGVyY2VudGZmZmZmZvY_TgAAAAGgIABzeXN0ZW0uY3B1LmludGVycnVwdC5wZXJjZW50XmF2ZxwAc3lzdGVtLmNwdS5pbnRlcnJ1cHQucGVyY2VudAAAAAAAAAAASgAAAAGgHgBzeXN0ZW0ubWVtb3J5LnVzZWQucGVyY2VudF5hdmcaAHN5c3RlbS5tZW1vcnkudXNlZC5wZXJjZW50rkfhehQOREA"));

        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }

}
