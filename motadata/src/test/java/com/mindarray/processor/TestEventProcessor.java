/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

package com.mindarray.processor;

import com.mindarray.Bootstrap;
import com.mindarray.TestUtil;
import com.mindarray.datastore.DatastoreConstants;
import com.mindarray.eventbus.EventBusConstants;
import com.mindarray.eventbus.RemoteEventForwarder;
import io.github.artsok.RepeatedIfExceptionsTest;
import io.vertx.core.eventbus.MessageConsumer;
import io.vertx.core.json.JsonObject;
import io.vertx.junit5.Timeout;
import io.vertx.junit5.VertxExtension;
import io.vertx.junit5.VertxTestContext;
import org.junit.jupiter.api.*;
import org.junit.jupiter.api.condition.EnabledIfSystemProperty;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.api.parallel.Execution;
import org.junit.jupiter.api.parallel.ExecutionMode;

import java.util.concurrent.TimeUnit;

import static com.mindarray.GlobalConstants.PLUGIN_ID;
import static com.mindarray.eventbus.EventBusConstants.*;

@ExtendWith(VertxExtension.class)
@Execution(ExecutionMode.SAME_THREAD)
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
@Timeout(70 * 1000)
@EnabledIfSystemProperty(named = "test.mode", matches = "EVENT_PROCESSOR")
public class TestEventProcessor
{
    private static MessageConsumer<JsonObject> eventDBWriteTestConsumer;

    @BeforeAll
    static void beforeAll(VertxTestContext testContext)
    {
        try
        {
            testContext.awaitCompletion(10, TimeUnit.SECONDS);

            var deploymentId = Bootstrap.getDeployedVerticles().get(RemoteEventForwarder.class.getSimpleName() + " 9450");

            Bootstrap.vertx().undeploy(deploymentId, result ->
            {
                if (result.succeeded())
                {
                    var id = Bootstrap.getDeployedVerticles().get(RemoteEventForwarder.class.getSimpleName() + " 9449");

                    Bootstrap.vertx().undeploy(id, asyncResult ->
                    {
                        if (asyncResult.succeeded())
                        {
                            //config init to collector
                            Bootstrap.vertx().eventBus().publish(EVENT_CHANGE_NOTIFICATION, new JsonObject("{\"event.type\":\"change.notification\",\"event.topic\":\"remote.event.processor \",\"remote.event.processor.uuid\":\"collebctor\",\"change.notification.type\":\"CONFIG_INIT\",\"result\":{\"log.parser.plugin\":[{\"id\":10000000000010,\"log.parser.plugin.name\":\"PostgreSQLLogParserPlugin\",\"log.parser.plugin.context\":{\"log.parser.plugin.script\":\"PostgreSQLLogParserPlugin.java\"},\"_type\":\"0\"},{\"id\":10000000000011,\"log.parser.plugin.name\":\"SonicWallLogParserPlugin\",\"log.parser.plugin.context\":{\"log.parser.plugin.script\":\"SonicWallLogParserPlugin.java\"},\"_type\":\"0\"},{\"id\":10000000000008,\"log.parser.plugin.name\":\"OracleDatabaseLogParserPlugin\",\"log.parser.plugin.context\":{\"log.parser.plugin.script\":\"OracleDatabaseLogParserPlugin.java\"},\"_type\":\"0\"},{\"id\":10000000000009,\"log.parser.plugin.name\":\"PaloAltoLogParserPlugin\",\"log.parser.plugin.context\":{\"log.parser.plugin.script\":\"PaloAltoLogParserPlugin.java\"},\"_type\":\"0\"},{\"id\":10000000000014,\"log.parser.plugin.name\":\"WindowsLogParserPlugin\",\"log.parser.plugin.context\":{\"log.parser.plugin.script\":\"WindowsLogParserPlugin.java\"},\"_type\":\"0\"},{\"id\":10000000000015,\"log.parser.plugin.name\":\"WindowsUpdateLogParserPlugin\",\"log.parser.plugin.context\":{\"log.parser.plugin.script\":\"WindowsUpdateLogParserPlugin.java\"},\"_type\":\"0\"},{\"id\":10000000000012,\"log.parser.plugin.name\":\"SophosLogParserPlugin\",\"log.parser.plugin.context\":{\"log.parser.plugin.script\":\"SophosLogParserPlugin.java\"},\"_type\":\"0\"},{\"id\":10000000000013,\"log.parser.plugin.name\":\"WindowsFirewallLogParserPlugin\",\"log.parser.plugin.context\":{\"log.parser.plugin.script\":\"WindowsFirewallLogParserPlugin.java\"},\"_type\":\"0\"},{\"id\":10000000000002,\"log.parser.plugin.name\":\"CiscoSwitchLogParserPlugin\",\"log.parser.plugin.context\":{\"log.parser.plugin.script\":\"CiscoSwitchLogParserPlugin.java\"},\"_type\":\"0\"},{\"id\":10000000000003,\"log.parser.plugin.name\":\"FortinetLogParserPlugin\",\"log.parser.plugin.context\":{\"log.parser.plugin.script\":\"FortinetLogParserPlugin.java\"},\"_type\":\"0\"},{\"id\":10000000000001,\"log.parser.plugin.name\":\"CiscoASALogParserPlugin\",\"log.parser.plugin.context\":{\"log.parser.plugin.script\":\"CiscoASALogParserPlugin.java\"},\"_type\":\"0\"},{\"id\":10000000000006,\"log.parser.plugin.name\":\"MySQLLogParserPlugin\",\"log.parser.plugin.context\":{\"log.parser.plugin.script\":\"MySQLLogParserPlugin.java\"},\"_type\":\"0\"},{\"id\":10000000000007,\"log.parser.plugin.name\":\"MySQLSlowQueryLogParserPlugin\",\"log.parser.plugin.context\":{\"log.parser.plugin.script\":\"MySQLSlowQueryLogParserPlugin.java\"},\"_type\":\"0\"},{\"id\":10000000000004,\"log.parser.plugin.name\":\"LinuxLogParserPlugin\",\"log.parser.plugin.context\":{\"log.parser.plugin.script\":\"LinuxLogParserPlugin.java\"},\"_type\":\"0\"},{\"id\":10000000000005,\"log.parser.plugin.name\":\"MicrosoftExchangeLogParserPlugin\",\"log.parser.plugin.context\":{\"log.parser.plugin.script\":\"MicrosoftExchangeLogParserPlugin.java\"},\"_type\":\"0\"},{\"id\":10000000000016,\"log.parser.plugin.name\":\"NutanixLogParserPlugin\",\"log.parser.plugin.context\":{\"log.parser.plugin.script\":\"NutanixLogParserPlugin.java\"},\"_type\":\"0\"}]},\"license\":{\"description\":\"Motadata Observability Edition\",\"license.issue.time\":1705487954030,\"license.expiry.time\":1721240999999,\"license.type\":\"FREE\",\"license.edition\":2,\"monitoring.enabled\":\"yes\",\"licensed.monitors\":1000,\"config.management.enabled\":\"yes\",\"licensed.config.devices\":1000,\"log.enabled\":\"yes\",\"licensed.log.quota.bytes\":**********,\"licensed.eps\":200,\"flow.enabled\":\"yes\",\"licensed.flow.quota.bytes\":**********,\"licensed.fps\":200,\"license.activation.code\":\"n45WcGx/mxSLdI8A39YXF8mxAc2WmAkP4r/hdLi1eWcSt/i5/YqQLcaQPvlBBTIhdXjjVEBI2DoDrbr1utha2Dr62rtM7J9mfMXlzby5GizQ5ajqjWbpENa2ndR7zaoGUBBiRBMDh5zTvP56kJji28ELyIMYBcHlUIuUxyW3l1/XjvYSb+yqz6dpZNazkdYyfQv7bQWfKU9henJYho8Ytt/YGV5gLIuFzvbbi3EPh3Ea7F4v0+XI3ZuuHZPIjyjBIWBFUfd74RKF6JsqBj8chf85a78g0tzBeWl0O3Wcua/e8EujqKl2HqOwSCQhomduKZjw9J9OCW5Bu3UGvnlpxlEJqClOt0eYT9JUP8DWOISKfjDkgxxQIScXXpxknvSre2bLlgkHlM671pP17iGojL0czmn/v/OlpV7GCiiLoYBUY6JQFNdE3BNOwwHzi1W6D4kY7FBnHSCOJhD25nmmUNTc4GqbCQZe6A/3GXXG8O8D94sClhsBiH0ztSGus39hfT2J65ZDZZT4977GgP3CVp6eerZC7qzbteOvqTB2zaoDMylJZMyQGUN0n2fe6Q1tpuQcRfY87FTWJrNEyCvMOsiu6LGyH5pLTLVHeg53hhMsIQmo8SXRTfQ0LgJ9szpp\",\"remaining.days\":154,\"used.monitors\":0,\"used.applications\":0,\"used.vms\":0,\"used.access.points\":0,\"used.agents\":0,\"used.config.devices\":0,\"used.log.quota.bytes\":0,\"used.flow.quota.bytes\":0}}"));

                            testContext.completeNow();
                        }
                    });
                }
            });


        }
        catch (Exception exception)
        {
            testContext.failNow(exception);
        }
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(1)
    @Timeout(5000)
    void testLogCollector(VertxTestContext testContext) throws InterruptedException
    {

        testContext.awaitCompletion(5, TimeUnit.SECONDS);

        eventDBWriteTestConsumer = TestUtil.vertx().eventBus().localConsumer(EventBusConstants.EVENT_REMOTE, message -> //this event will be
        {
            try
            {
                var event = message.body();

                if (EVENT_LOG_PROCESS.equalsIgnoreCase(event.getString(EVENT_TYPE)))
                {
                    Assertions.assertNotNull(event);

                    Assertions.assertEquals(EVENT_LOG_PROCESS, event.getString(EVENT_TYPE));

                    Assertions.assertEquals(105, event.getInteger(EVENT_VOLUME_BYTES));

                    Assertions.assertEquals(DatastoreConstants.PluginId.UNKNOWN_LOG_EVENT.getName(), event.getInteger(PLUGIN_ID));

                    eventDBWriteTestConsumer.unregister();

                    testContext.completeNow();
                }
            }
            catch (Exception exception)
            {
                testContext.failNow(exception);
            }
        });

        var log = new JsonObject("{\"event.volume.bytes\":105,\"event\":\"<8>Jan  30 233443 dfefer 11:26:44 mindarray-pc1 unix_chkpwd]: password check failed for user (mindarray)\",\"event.source\":\"10.20.41.35\",\"event.received.time\":1706614241,\"event.type\":\"log\"}");

        TestUtil.vertx().eventBus().send(EVENT_LOG + ".1.consume", log.toString().getBytes()); //processor will receives log at event.engine

        Assertions.assertTrue(testContext.awaitCompletion(15, TimeUnit.SECONDS), "could not complete the task, reason: timed out.");
    }


    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(2)
    @Timeout(5000)
    @DisplayName("ParseTrafficLogToFlow")
    void testParseTrafficLog(VertxTestContext testContext) throws InterruptedException
    {
        //config change event to collector
        Bootstrap.vertx().eventBus().publish(EVENT_CHANGE_NOTIFICATION, new JsonObject("{\"event.type\":\"change.notification\",\"event.topic\":\"remote.event.processor \",\"table\":\"tbl_config_log_parser\",\"change.notification.type\":\"CONFIG_CHANGE\",\"request\":\"create\",\"id\":10000000000005,\"result\":{\"log.parser.name\":\"Fortinet\",\"log.parser.event\":\"<189>date=2016-09-19 time=18:06:43 devname=DTSPL-FW devid=FW60CA3913000813 logid=0000000013 type=traffic subtype=forward level=notice vd=root srcip=*********** srcport=28055 srcintf=\\\\\\\"internal6\\\\\\\" dstip=******* dstport=53 dstintf=\\\\\\\"internal5\\\\\\\" poluuid=9e973e02-6e65-51e6-2fde-e6238f5acd57 sessionid=1300543 proto=17 action=accept policyid=4 dstcountry=\\\\\\\"United States\\\\\\\" srccountry=\\\\\\\"Reserved\\\\\\\" trandisp=snat transip=************** transport=28055 service=\\\\\\\"DNS\\\\\\\" duration=90 sentbyte=67 rcvdbyte=83 sentpkt=1 rcvdpkt=1 appcat=\\\\\\\"unscanned\\\\\\\" devtype=\\\\\\\"Linux PC\\\\\\\" osname=\\\\\\\"Linux\\\\\\\" osversion=\\\\\\\"2.6, 3.3, 3.6, 3.8, 3.9\\\\\\\" mastersrcmac=00:0c:29:70:d5:63 srcmac=00:0c:29:70:d5:63\",\"log.parser.condition.keywords\":[],\"log.parser.condition\":\"all\",\"log.parser.fields\":[{\"log.parser.field.value\":1669395871,\"log.parser.field.name\":\"event.received.time\",\"log.parser.field.type\":\"none\"},{\"log.parser.field.value\":\"189\",\"log.parser.field.name\":\"priority\",\"log.parser.field.type\":\"none\"},{\"log.parser.field.value\":\"local use 7\",\"log.parser.field.name\":\"facility\",\"log.parser.field.type\":\"none\"},{\"log.parser.field.value\":\"Notice\",\"log.parser.field.name\":\"severity\",\"log.parser.field.type\":\"none\"},{\"log.parser.field.value\":\"traffic\",\"log.parser.field.name\":\"type\",\"log.parser.field.type\":\"none\"},{\"log.parser.field.value\":\"notice\",\"log.parser.field.name\":\"log.level\",\"log.parser.field.type\":\"none\"},{\"log.parser.field.value\":\"***********\",\"log.parser.field.name\":\"source.ip\",\"log.parser.field.type\":\"none\"},{\"log.parser.field.value\":\"28055\",\"log.parser.field.name\":\"source.port\",\"log.parser.field.type\":\"none\"},{\"log.parser.field.value\":\"\\\\internal6\\\\\",               \"log.parser.field.name\": \"source.interface\",               \"log.parser.field.type\": \"none\"             },             {               \"log.parser.field.value\": \"*******\",               \"log.parser.field.name\": \"destination.ip\",               \"log.parser.field.type\": \"none\"             },             {               \"log.parser.field.value\": \"53\",               \"log.parser.field.name\": \"destination.port\",               \"log.parser.field.type\": \"none\"             },             {               \"log.parser.field.value\": \"\\\\internal5\\\\\",               \"log.parser.field.name\": \"destination.interface\",               \"log.parser.field.type\": \"none\"             },             {               \"log.parser.field.value\": \"17\",               \"log.parser.field.name\": \"protocol\",               \"log.parser.field.type\": \"none\"             },             {               \"log.parser.field.value\": \"accept\",               \"log.parser.field.name\": \"action\",               \"log.parser.field.type\": \"none\"             },             {               \"log.parser.field.value\": \"\\\\UnitedStates\\\\\",               \"log.parser.field.name\": \"destination.country\",               \"log.parser.field.type\": \"none\"             },             {               \"log.parser.field.value\": \"\\\\Reserved\\\\\",               \"log.parser.field.name\": \"source.country\",               \"log.parser.field.type\": \"none\"             },             {               \"log.parser.field.value\": \"snat\",               \"log.parser.field.name\": \"translation.type\",               \"log.parser.field.type\": \"none\"             },             {               \"log.parser.field.value\": \"**************\",               \"log.parser.field.name\": \"nat.source.ip\",               \"log.parser.field.type\": \"none\"             },             {               \"log.parser.field.value\": \"28055\",               \"log.parser.field.name\": \"nat.source.port\",               \"log.parser.field.type\": \"none\"             },             {               \"log.parser.field.value\": \"\\\\DNS\\\\\",               \"log.parser.field.name\": \"service\",               \"log.parser.field.type\": \"none\"             },             {               \"log.parser.field.value\": \"90\",               \"log.parser.field.name\": \"duration\",               \"log.parser.field.type\": \"none\"             },             {               \"log.parser.field.value\": \"67\",               \"log.parser.field.name\": \"sent.bytes\",               \"log.parser.field.type\": \"none\"             },             {               \"log.parser.field.value\": \"83\",               \"log.parser.field.name\": \"received.bytes\",               \"log.parser.field.type\": \"none\"             },             {               \"log.parser.field.value\": \"1\",               \"log.parser.field.name\": \"sent.packets\",               \"log.parser.field.type\": \"none\"             },             {               \"log.parser.field.value\": \"1\",               \"log.parser.field.name\": \"received.packets\",               \"log.parser.field.type\": \"none\"             },             {               \"log.parser.field.value\": \"\\\\unscanned\\\\\",               \"log.parser.field.name\": \"application.category\",               \"log.parser.field.type\": \"none\"             },             {               \"log.parser.field.value\": \"\\\\Linux\\\\\",               \"log.parser.field.name\": \"os.name\",               \"log.parser.field.type\": \"none\"             },             {               \"log.parser.field.value\": \"\\\\2.6,3.3,3.6,3.8,3.9\\\\\",               \"log.parser.field.name\": \"os.version\",               \"log.parser.field.type\": \"none\"             },             {               \"log.parser.field.value\": \"00:0c:29:70:d5:63\",               \"log.parser.field.name\": \"source.mac\",               \"log.parser.field.type\": \"none\"             },             {               \"log.parser.field.value\": \"Traffic\",               \"log.parser.field.name\": \"event.category\",               \"log.parser.field.type\": \"none\"             },             {               \"log.parser.field.value\": 150,               \"log.parser.field.name\": \"bytes\",               \"log.parser.field.type\": \"none\"             },             {               \"log.parser.field.value\": 1474288603,               \"log.parser.field.name\": \"timestamp\",               \"log.parser.field.type\": \"timestamp\"             }           ],           \"log.parser.type\": \"custom\",           \"log.parser.plugin\": 10000000000003,           \"log.parser.source.type\": \"Firewall\",           \"log.parser.upload\": \"no\",           \"plugin.id\": 600005,           \"log.parser.entities\": [\"127.0.0.1\"],           \"id\": 10000000000005,           \"log.parser.date.time.formatter.type\": \"numeric\",           \"log.parser.date.time.format\": \"seconds\",           \"log.parser.source.vendor\": \"Fortinet\"         } }"));

        testContext.awaitCompletion(2, TimeUnit.SECONDS);

        eventDBWriteTestConsumer = TestUtil.vertx().eventBus().localConsumer(EventBusConstants.EVENT_REMOTE, message -> //this event will be
        {
            try
            {
                var event = message.body();

                if (EVENT_FLOW.equalsIgnoreCase(event.getString(EVENT_TYPE)))
                {
                    Assertions.assertNotNull(event);

                    Assertions.assertEquals(EVENT_FLOW, event.getString(EVENT_TYPE));

                    Assertions.assertEquals(261, event.getInteger(EVENT_VOLUME_BYTES));

                    Assertions.assertEquals(DatastoreConstants.PluginId.FLOW_EVENT.getName(), event.getInteger(PLUGIN_ID));

                    Assertions.assertTrue(event.containsKey("source.ip") && "***********".equalsIgnoreCase(event.getString("source.ip")));

                    eventDBWriteTestConsumer.unregister();

                    testContext.completeNow();
                }
            }
            catch (Exception exception)
            {
                testContext.failNow(exception);
            }
        });

        var log = new JsonObject("{\"event.volume.bytes\":275,\"event\":\"\",\"event.source\":\"127.0.0.1\",\"event.received.time\":1706614241,\"event.type\":\"log\"}").put(EVENT, "<189>date=2021-09-19 time=18:06:43 devname=DTSPL-FW devid=FW60CA3913000813 logid=0000000013 type=traffic subtype=forward level=notice vd=root srcip=*********** srcport=28055 srcintf=\"internal6\" dstip=******* dstport=53 dstintf=\"internal5\" poluuid=9e973e02-6e65-51e6-2fde-e6238f5acd57 sessionid=1300543 proto=17 action=accept policyid=4 dstcountry=\"United States\" srccountry=\"Reserved\" trandisp=snat transip=************** transport=28055 service=\"DNS\" duration=90 sentbyte=67 rcvdbyte=83 sentpkt=1 rcvdpkt=1 appcat=\"unscanned\" devtype=\"Linux PC\" osname=\"Linux\" osversion=\"2.6, 3.3, 3.6, 3.8, 3.9\" mastersrcmac=00:0c:29:70:d5:63 srcmac=00:0c:29:70:d5:63");

        TestUtil.vertx().eventBus().send(EVENT_LOG + ".1.consume", log.toString().getBytes()); //processor will receives log at event.engine

        Assertions.assertTrue(testContext.awaitCompletion(20, TimeUnit.SECONDS), "could not complete the task, reason: timed out.");

    }
}
