/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

package com.mindarray.processor;

import com.mindarray.Bootstrap;
import com.mindarray.GlobalConstants;
import com.mindarray.TestAPIUtil;
import com.mindarray.TestUtil;
import com.mindarray.api.AIOpsObject;
import com.mindarray.api.APIConstants;
import com.mindarray.api.Agent;
import com.mindarray.api.RemoteEventProcessor;
import com.mindarray.datastore.DatastoreConstants;
import com.mindarray.eventbus.EventBusConstants;
import com.mindarray.log.LogEngineConstants;
import com.mindarray.log.LogPatternDetector;
import com.mindarray.store.AgentConfigStore;
import com.mindarray.store.EventSourceConfigStore;
import com.mindarray.store.LogParserConfigStore;
import com.mindarray.store.ObjectConfigStore;
import com.mindarray.util.*;
import com.mindarray.visualization.VisualizationConstants;
import io.github.artsok.RepeatedIfExceptionsTest;
import io.vertx.core.buffer.Buffer;
import io.vertx.core.eventbus.MessageConsumer;
import io.vertx.core.json.JsonObject;
import io.vertx.junit5.Timeout;
import io.vertx.junit5.VertxExtension;
import io.vertx.junit5.VertxTestContext;
import org.apache.http.HttpStatus;
import org.joda.time.DateTimeZone;
import org.joda.time.format.DateTimeFormat;
import org.junit.jupiter.api.*;
import org.junit.jupiter.api.condition.EnabledIfSystemProperty;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.api.parallel.Execution;
import org.junit.jupiter.api.parallel.ExecutionMode;
import org.xerial.snappy.Snappy;
import org.zeromq.SocketType;
import org.zeromq.ZMQ;

import java.net.DatagramPacket;
import java.net.DatagramSocket;
import java.net.InetAddress;
import java.net.Socket;
import java.time.Duration;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.locks.LockSupport;

import static com.mindarray.GlobalConstants.MOTADATA_API;
import static com.mindarray.TestAPIConstants.LOG_PARSER_API_ENDPOINT;
import static com.mindarray.TestAPIConstants.MISC_COLUMN_MAPPER_API_ENDPOINT;
import static org.apache.http.HttpStatus.SC_OK;
import static org.junit.jupiter.api.Assertions.assertEquals;

@ExtendWith(VertxExtension.class)
@Execution(ExecutionMode.SAME_THREAD)
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
@Timeout(70 * 1000)
@EnabledIfSystemProperty(named = "test.mode", matches = "^APP|^PRIMARY|SLAVE$")
public class TestLogParser
{
    private static final ZMQ.Socket FORWARDER = Bootstrap.zcontext().socket(SocketType.PUSH);
    private static final Logger LOGGER = new Logger(TestLogParser.class, MOTADATA_API, "Test Log Parser");
    private static MessageConsumer<byte[]> eventDBWriteTestConsumer;

    @BeforeAll
    static void beforeAll(VertxTestContext testContext)
    {
        AgentConfigStore.getStore().updateItem(1L, new JsonObject().put(Agent.AGENT_STATE, "enable").put(Agent.AGENT_UUID, "7FB44D1EFE254F43938469F2F53D6D09"));

        ObjectConfigStore.getStore().addItem(2L, new JsonObject().put(AIOpsObject.OBJECT_IP, "127.0.0.1").put(AIOpsObject.OBJECT_CATEGORY, LogEngineConstants.Category.SERVER.getName()));

        try
        {
            // reflection to mock inject the required objects for test cases.

            var field = AgentConfigStore.getStore().getClass().getDeclaredField("itemsByUUID");

            field.setAccessible(true);

            var remoteEventProcessors = (Map<String, Long>) field.get(AgentConfigStore.getStore());

            remoteEventProcessors.put("7FB44D1EFE254F43938469F2F53D6D09", 1L);

            field.set(AgentConfigStore.getStore(), remoteEventProcessors);

            field = ObjectConfigStore.getStore().getClass().getDeclaredField("itemsByAgent");

            field.setAccessible(true);

            var agents = (Map<Long, Long>) field.get(ObjectConfigStore.getStore());

            agents.put(1L, 2L);

            field.set(ObjectConfigStore.getStore(), agents);

            FORWARDER.setSndHWM(MotadataConfigUtil.getEventBacklogSize());

            FORWARDER.setHWM(MotadataConfigUtil.getEventBacklogSize());

            FORWARDER.setSendTimeOut(0);

            FORWARDER.connect("tcp://" + "localhost" + ":" + 9449);

            LockSupport.parkNanos(Duration.ofMillis(100).toNanos());

            testContext.completeNow();
        }
        catch (Exception exception)
        {
            testContext.failNow(exception);
        }
    }

    @BeforeEach
    void beforeEach(VertxTestContext testContext, TestInfo testInfo)
    {
        LOGGER.info(String.format("running test case %s", testInfo.getTestMethod().get().getName()));

        testContext.completeNow();
    }

    @BeforeEach
    void beforeEach(VertxTestContext testContext)
    {
        if (eventDBWriteTestConsumer != null)
        {
            eventDBWriteTestConsumer.unregister(result -> testContext.completeNow());
        }
        else
        {
            testContext.completeNow();
        }
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(1)
    @Timeout(5000)
    void testParseDateTime(VertxTestContext testContext)
    {
        var output = LogPatternDetector.detectTimePattern("Feb 2 2017 16:42:04 smit-HP-EliteBook-840-G2 su: pam_unix(su:auth): authentication failure; logname= uid=1000 euid=0 tty=pts/1 ruser=smit rhost=  user=root");

        Assertions.assertEquals(DateTimeFormat.forPattern("MMM dd yyyy HH:mm:ss").parseDateTime("Feb 2 2017 16:42:04").getMillis() / 1000, DateTimeUtil.timestampToSeconds(output.getString("format"), output.getString("timestamp"), ""));

        testContext.completeNow();
    }

    @Test
    @Order(2)
    @Timeout(5000)
    void testParseDateTimeTimeWithTimezone(VertxTestContext testContext)
    {
        var output = LogPatternDetector.detectTimePattern("Feb 2 2017 16:42:04 smit-HP-EliteBook-840-G2 su: pam_unix(su:auth): authentication failure; logname= uid=1000 euid=0 tty=pts/1 ruser=smit rhost=  user=root");

        Assertions.assertEquals(DateTimeFormat.forPattern("MMM dd yyyy HH:mm:ss").withZone(DateTimeZone.forID("America/Chicago")).parseDateTime("Feb 2 2017 16:42:04").getMillis() / 1000, DateTimeUtil.timestampToSeconds(output.getString("format"), output.getString("timestamp"), "America/Chicago"));

        testContext.completeNow();
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(3)
    @Timeout(5000)
    void testParserLogOtherCategory(VertxTestContext testContext) throws InterruptedException
    {
        var values = new JsonObject("{\"source\":\"127.0.0.1\",\"event.category\":\"Other\",\"event.source.type\":\"Other\",\"event.severity\":\"Informational\"}");

        try (var socket = new Socket("localhost", MotadataConfigUtil.getTCPPort()))
        {
            var outputStream = socket.getOutputStream();

            eventDBWriteTestConsumer = TestUtil.vertx().eventBus().localConsumer(EventBusConstants.EVENT_DATASTORE_WRITE + "." + DatastoreConstants.DatastoreCategory.EVENT.getName(), message ->
            {

                try
                {
                    var event = TestUtil.decodeEventBufferSingleRow(Buffer.buffer(message.body()));

                    if (event.getString(GlobalConstants.PLUGIN_ID).equalsIgnoreCase(DatastoreConstants.PluginId.UNKNOWN_LOG_EVENT.getName() + "-" + "other"))
                    {
                        testContext.verify(() ->
                        {

                            for (var entry : values)
                            {
                                Assertions.assertTrue(event.containsKey(entry.getKey()) && CommonUtil.getString(entry.getValue()).equalsIgnoreCase(event.getString(entry.getKey())));
                            }

                            var retries = new AtomicInteger(10);

                            TestUtil.vertx().setPeriodic(2000, timer ->
                            {

                                var item = EventSourceConfigStore.getStore().getItemByValue(EventBusConstants.EVENT_SOURCE, "127.0.0.1");

                                if (item != null)
                                {
                                    Assertions.assertTrue(item.getJsonArray(GlobalConstants.PLUGIN_ID).contains(DatastoreConstants.PluginId.UNKNOWN_LOG_EVENT.getName()), "should contain plugin id: " + DatastoreConstants.PluginId.UNKNOWN_LOG_EVENT.getName());

                                    testContext.completeNow();
                                }
                                else
                                {
                                    if (retries.decrementAndGet() <= 0)
                                    {
                                        testContext.failNow("max attempt exceeded..");
                                    }
                                }

                            });
                        });
                    }
                }
                catch (Exception exception)
                {
                    testContext.failNow(exception);
                }
            });

            outputStream.write("<85>Sep  7 16:42:04 smit-HP-EliteBook-840-G2 su: pam_unix(su:auth): authentication failure; logname= uid=1000 euid=0 tty=pts/1 ruser=smit rhost=  user=root".getBytes());

            outputStream.flush();
        }
        catch (Exception exception)
        {
            testContext.failNow(exception);
        }

        Assertions.assertTrue(testContext.awaitCompletion(50, TimeUnit.SECONDS), "could not complete the task, reason: timed out.");
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(4)
    @Timeout(20 * 1000)
    @DisplayName("CustomLogViaTCPListener")
    void testTCPLogListener1(VertxTestContext testContext, TestInfo testInfo) throws InterruptedException
    {
        var values = new JsonObject("{\"source\":\"127.0.0.1\",\"linux.syslog.event.type\":\"log\",\"linux.syslog.priority\":85,\"linux.syslog.logsource\":\"smit-HP-EliteBook-840-G2\",\"linux.syslog.program\":\"su\",\"linux.syslog.facility\":\"security/authorization messages\",\"linux.syslog.severity\":\"Notice\",\"event.category\":\"Linux Syslog\",\"event.source.type\":\"Linux\",\"event.severity\":\"Notice\"}");

        TestAPIUtil.put("/api/v1/settings/log-parsers/10000000000004/assign", new JsonObject("{\"params\":[\"127.0.0.1\"]}"), testContext.succeeding(response -> testContext.verify(() ->
        {
            LOGGER.info(testInfo.getTestMethod().get().getName() + ": response: " + response.bodyAsJsonObject().encode());

            Assertions.assertEquals(SC_OK, response.statusCode());

            try (var socket = new Socket("localhost", MotadataConfigUtil.getTCPPort()))
            {
                var outputStream = socket.getOutputStream();

                assertLogProcessorTestResult(testContext, values, LogParserConfigStore.getStore().getItem(10000000000004L).getInteger(GlobalConstants.PLUGIN_ID) + "-linux.syslog", testInfo.getTestMethod().get().getName());

                outputStream.write("<85>Sep  7 16:42:04 smit-HP-EliteBook-840-G2 su: pam_unix(su:auth): authentication failure; logname= uid=1000 euid=0 tty=pts/1 ruser=smit rhost=  user=root".getBytes());

                outputStream.flush();

            }
            catch (Exception exception)
            {
                testContext.failNow(exception);
            }
        })));

        Assertions.assertTrue(testContext.awaitCompletion(50, TimeUnit.SECONDS), "could not complete the task, reason: timed out.");
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(5)
    @Timeout(20 * 1000)
    @DisplayName("CustomLogViaUDPListener")
    void testUDPLogListener(VertxTestContext testContext, TestInfo testInfo) throws InterruptedException
    {
        var parser = LogParserConfigStore.getStore().getItem(10000000000004L);

        var values = new JsonObject("{\"linux.syslog.event.type\":\"log\",\"linux.syslog.priority\":85,\"linux.syslog.logsource\":\"smit-HP-EliteBook-840-G2\",\"linux.syslog.program\":\"su\",\"linux.syslog.facility\":\"security/authorization messages\",\"linux.syslog.severity\":\"Notice\",\"event.category\":\"Linux Syslog\",\"event.source.type\":\"Linux\",\"event.severity\":\"Notice\"}");

        try (var udpSocket = new DatagramSocket())
        {
            var log = "<85>Sep  7 16:42:04 smit-HP-EliteBook-840-G2 su: pam_unix(su:auth): authentication failure; logname= uid=1000 euid=0 tty=pts/1 ruser=smit rhost=  user=root";

            assertLogProcessorTestResult(testContext, values, parser.getInteger(GlobalConstants.PLUGIN_ID) + "-linux.syslog", testInfo.getTestMethod().get().getName());

            udpSocket.send(new DatagramPacket(log.getBytes(), log.length(), InetAddress.getByName("127.0.0.1"), MotadataConfigUtil.getUDPPort()));
        }
        catch (Exception exception)
        {
            testContext.failNow(exception);
        }

        Assertions.assertTrue(testContext.awaitCompletion(50, TimeUnit.SECONDS), "could not complete the task, reason: timed out.");
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(6)
    @Timeout(20 * 1000)
    @DisplayName("CustomLogViaAgent")
    void testAgentLogListener(VertxTestContext testContext, TestInfo testInfo) throws InterruptedException
    {
        var parser = LogParserConfigStore.getStore().getItem(10000000000004L);

        var values = new JsonObject("{\"linux.syslog.priority\":85,\"linux.syslog.logsource\":\"smit-HP-EliteBook-840-G2\",\"linux.syslog.program\":\"su\",\"linux.syslog.facility\":\"security/authorization messages\",\"linux.syslog.severity\":\"Notice\",\"event.category\":\"Linux Syslog\",\"event.source.type\":\"Linux\",\"event.severity\":\"Notice\"}");

        var cipherUtil = new CipherUtil();

        try (var producer = Bootstrap.zcontext().socket(SocketType.PUSH))
        {
            producer.connect("tcp://*:" + CommonUtil.getEventSubscriberPort());

            //<85>Sep  7 16:42:04 smit-HP-EliteBook-840-G2 su: pam_unix(su:auth): authentication failure; logname= uid=1000 euid=0 tty=pts/1 ruser=smit rhost=  user=root

            var agentLogEvent = """
                    {
                       "event.type" : "agent.response.stream",
                       "agent.id" : "7FB44D1EFE254F43938469F2F53D6D09",
                       "agent.type" : "log",
                       "event.context" : "mwHwQzw4NT5TZXAgIDcgMTY6NDI6MDQgc21pdC1IUC1FbGl0ZUJvb2stODQwLUcyIHN1OiBwYW1fdW5peChzdTphdXRoKTogAQeYZW50aWNhdGlvbiBmYWlsdXJlOyBsb2duYW1lPSB1aWQ9MTAwMCBlAQpEMCB0dHk9cHRzLzEgcnVzZXI9AXFEIHJob3N0PSAgdXNlcj1yb290"
                     }""";

            assertLogProcessorTestResult(testContext, values, parser.getInteger(GlobalConstants.PLUGIN_ID) + "-linux.syslog", testInfo.getTestMethod().get().getName());

            producer.send(Buffer.buffer().appendShortLE(CommonUtil.getShort(EventBusConstants.AGENT_TOPIC.length())).appendString(EventBusConstants.AGENT_TOPIC).appendBytes(cipherUtil.encrypt(Snappy.compress(agentLogEvent))).getBytes());
        }
        catch (Exception exception)
        {
            testContext.failNow(exception);
        }

        Assertions.assertTrue(testContext.awaitCompletion(50, TimeUnit.SECONDS), "could not complete the task, reason: timed out.");
    }


    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(7)
    @Timeout(20 * 1000)
    @DisplayName("DelimiterLogViaTCPListener")
    void testTCPLogListener2(VertxTestContext testContext, TestInfo testInfo) throws InterruptedException
    {
        var payload = new JsonObject("{\"log.parser.name\":\"Test Separator Parser With\",\"log.parser.event\":\"2017/06/15 14:47:34,TRAFFIC,***********,***********,session opened for user root by (uid=0)\",\"log.parser.log.positions\":[0,1,2,3,4],\"log.parser.condition.keywords\":[],\"log.parser.condition\":\"all\",\"log.parser.fields\":[{\"log.parser.field.value\":\"2017/06/15 14:47:34\",\"log.parser.field.name\":\"timestamp\",\"log.parser.field.type\":\"timestamp\"},{\"log.parser.field.value\":\"TRAFFIC\",\"log.parser.field.name\":\"type\",\"log.parser.field.type\":\"none\"},{\"log.parser.field.value\":\"***********\",\"log.parser.field.name\":\"src.ip\",\"log.parser.field.type\":\"none\"},{\"log.parser.field.value\":\"***********\",\"log.parser.field.name\":\"dest.ip\",\"log.parser.field.type\":\"none\"},{\"log.parser.field.value\":\"session opened for user root by (uid=0)\",\"log.parser.field.name\":\"msg\",\"log.parser.field.type\":\"none\"}],\"log.parser.type\":\"delimiter\",\"log.parser.delimiter\":\",\",\"log.parser.source.type\":\"Linux\",\"log.parser.upload\":\"no\",\"log.parser.date.time.formatter.type\":\"formatter\",\"log.parser.date.time.format\":\"yyyy/MM/dd HH:mm:ss\",\"plugin.id\":600021,\"log.parser.entities\":[],\"log.parser.prefix\":\"test.separator.parser.with.\",\"event.source\":\"127.0.0.1\"}");

        var values = new JsonObject("{\"test.separator.parser.with.type\":\"TRAFFIC\",\"test.separator.parser.with.src.ip\":\"***********\",\"test.separator.parser.with.dest.ip\":\"***********\",\"test.separator.parser.with.msg\":\"session opened for user root by (uid=0)\",\"event.category\":\"Test Separator Parser With\",\"event.source.type\":\"Linux\",\"event.severity\":\"Informational\"}");

        TestAPIUtil.post(LOG_PARSER_API_ENDPOINT, payload, testContext.succeeding(response -> testContext.verify(() ->
        {
            LOGGER.info(testInfo.getTestMethod().get().getName() + ": response: " + response.bodyAsJsonObject().encode());

            var parserId = response.bodyAsJsonObject().getLong(GlobalConstants.ID);

            try (var socket = new Socket("localhost", MotadataConfigUtil.getTCPPort()))
            {
                var outputStream = socket.getOutputStream();

                assertLogProcessorTestResult(testContext, values, LogParserConfigStore.getStore().getItem(parserId).getInteger(GlobalConstants.PLUGIN_ID) + "-test.separator.parser.with", testInfo.getTestMethod().get().getName());

                outputStream.write("2021/06/15 14:47:34,TRAFFIC,***********,***********,session opened for user root by (uid=0)".getBytes());

                outputStream.flush();
            }
            catch (Exception exception)
            {
                testContext.failNow(exception);
            }
        })));

        Assertions.assertTrue(testContext.awaitCompletion(50, TimeUnit.SECONDS), "could not complete the task, reason: timed out.");
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(8)
    @Timeout(20 * 1000)
    @DisplayName("JsonLogViaTCPListener")
    void testTCPLogListener3(VertxTestContext testContext, TestInfo testInfo) throws InterruptedException
    {
        var payload = new JsonObject("{\"log.parser.name\":\"Test Json Parser With Local Assign\",\"log.parser.event\":\"{\\\"timestamp\\\": \\\"2017-03-22 16:13:35\\\",   \\\"message\\\": \\\"The DHCP Server service entered the running state.\\\",   \\\"level-description\\\": \\\"Informational\\\",   \\\"id\\\": 7036,   \\\"audit\\\": \\\"Classic\\\",   \\\"source\\\": \\\"System\\\" }\",\"log.parser.log.positions\":[\"timestamp\",\"message\",\"level-description\",\"id\",\"audit\",\"source\"],\"log.parser.condition.keywords\":[],\"log.parser.condition\":\"all\",\"log.parser.fields\":[{\"log.parser.field.value\":\"2017-03-22 16:13:35\",\"log.parser.field.name\":\"timestamp\",\"log.parser.field.type\":\"timestamp\"},{\"log.parser.field.value\":\"The DHCP Server service entered the running state.\",\"log.parser.field.name\":\"message\",\"log.parser.field.type\":\"Any\"},{\"log.parser.field.value\":\"Informational\",\"log.parser.field.name\":\"level-description\",\"log.parser.field.type\":\"Any\"},{\"log.parser.field.value\":\"7036\",\"log.parser.field.name\":\"id\",\"log.parser.field.type\":\"Any\"},{\"log.parser.field.value\":\"Classic\",\"log.parser.field.name\":\"audit\",\"log.parser.field.type\":\"Any\"},{\"log.parser.field.value\":\"System\",\"log.parser.field.name\":\"source\",\"log.parser.field.type\":\"Any\"}],\"log.parser.type\":\"json\",\"log.parser.source.type\":\"Linux\",\"event.source\":\"127.0.0.1\",\"log.parser.upload\":\"no\"}");

        var values = new JsonObject("{\"test.json.parser.with.local.assign.level.description\":\"Informational\",\"test.json.parser.with.local.assign.id\":7036,\"test.json.parser.with.local.assign.audit\":\"Classic\",\"test.json.parser.with.local.assign.source\":\"System\",\"event.category\":\"Test Json Parser With Local Assign\",\"event.source.type\":\"Linux\",\"event.severity\":\"Informational\"}");

        TestAPIUtil.post(LOG_PARSER_API_ENDPOINT, payload, testContext.succeeding(response -> testContext.verify(() ->
        {

            LOGGER.info(testInfo.getTestMethod().get().getName() + ": response: " + response.bodyAsJsonObject().encode());

            var parserId = response.bodyAsJsonObject().getLong(GlobalConstants.ID);

            try (var socket = new Socket("localhost", MotadataConfigUtil.getTCPPort()))
            {
                var outputStream = socket.getOutputStream();

                assertLogProcessorTestResult(testContext, values, LogParserConfigStore.getStore().getItem(parserId).getInteger(GlobalConstants.PLUGIN_ID) + "-test.json.parser.with.local.assign", testInfo.getTestMethod().get().getName());

                outputStream.write("{\"timestamp\":\"2021-03-22 16:13:35\",\"rowMessage\":\"The DHCP Server service entered the running state.\",\"level-description\":\"Informational\",\"id\":7036,\"audit\":\"Classic\",\"source\":\"System\"}".getBytes());

                outputStream.flush();
            }
            catch (Exception exception)
            {
                testContext.failNow(exception);
            }

        })));

        Assertions.assertTrue(testContext.awaitCompletion(50, TimeUnit.SECONDS), "could not complete the task, reason: timed out.");
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(9)
    @Timeout(20 * 1000)
    @DisplayName("RegExLogViaTCPListener")
    void testTCPLogListener4(VertxTestContext testContext, TestInfo testInfo) throws InterruptedException
    {
        var payload = new JsonObject("{\"log.parser.name\":\"TestRegexParserWithLocalAssign\",\"log.parser.event\":\"Sep 11 07:40:38 mindarray-pc7 rsyslogd-2039: Could no open output pipe '/dev/xconsole': No such file or directory [try http://www.rsyslog.com/e/2039 ]\",\"log.parser.log.positions\":[{\"start.position\":0,\"end.position\":15},{\"start.position\":30,\"end.position\":38},{\"start.position\":45,\"end.position\":150}],\"log.parser.condition.keywords\":[],\"log.parser.condition\":\"all\",\"log.parser.fields\":[{\"log.parser.field.value\":\"Sep 11 07:40:38\",\"log.parser.field.name\":\"timestamp\",\"log.parser.field.type\":\"timestamp\"},{\"log.parser.field.value\":\"rsyslogd\",\"log.parser.field.name\":\"process\",\"log.parser.field.type\":\"none\"},{\"log.parser.field.value\":\"Could no open output pipe '/dev/xconsole': No such file or directory [try http://www.rsyslog.com/e/2039\",\"log.parser.field.name\":\"log.message\",\"log.parser.field.type\":\"none\"}],\"log.parser.type\":\"regex\",\"log.parser.source.type\":\"Linux\",\"regex\":\"^([JFMAMJSONDjfmamjsond]\\\\w+\\\\s\\\\d{1,2}\\\\s\\\\d{1,2}:\\\\d{1,2}:\\\\d{1,2})(?:[^\\\\-]*\\\\-){1}\\\\w+\\\\d\\\\ ([^\\\\-]*)(?:[^\\\\:]*\\\\:){1}\\\\ (.*)\",\"event.source\":\"127.0.0.1\",\"log.parser.upload\":\"no\"}");

        var values = new JsonObject("{\"testregexparserwithlocalassign.process\":\"rsyslogd\",\"testregexparserwithlocalassign.log.message\":\"Could no open output pipe '/dev/xconsole': No such file or directory [try http://www.rsyslog.com/e/2039 ]\",\"event.category\":\"TestRegexParserWithLocalAssign\",\"event.source.type\":\"Linux\",\"event.severity\":\"Informational\"}");

        TestAPIUtil.post(LOG_PARSER_API_ENDPOINT, payload, testContext.succeeding(response -> testContext.verify(() ->
        {
            LOGGER.info(testInfo.getTestMethod().get().getName() + ": response: " + response.bodyAsJsonObject().encode());

            var parserId = response.bodyAsJsonObject().getLong(GlobalConstants.ID);

            try (var socket = new Socket("localhost", MotadataConfigUtil.getTCPPort()))
            {
                var outputStream = socket.getOutputStream();

                assertLogProcessorTestResult(testContext, values, LogParserConfigStore.getStore().getItem(parserId).getInteger(GlobalConstants.PLUGIN_ID) + "-testregexparserwithlocalassign", testInfo.getTestMethod().get().getName());

                outputStream.write("Sep 11 07:40:38 mindarray-pc7 rsyslogd-2039: Could no open output pipe '/dev/xconsole': No such file or directory [try http://www.rsyslog.com/e/2039 ]".getBytes());

                outputStream.flush();
            }
            catch (Exception exception)
            {
                testContext.failNow(exception);
            }
        })));

        Assertions.assertTrue(testContext.awaitCompletion(50, TimeUnit.SECONDS), "could not complete the task, reason: timed out.");
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(10)
    @Timeout(20 * 1000)
    @DisplayName("RegExLogViaTCPListenerWithAVGField")
    void testTCPLogListener5(VertxTestContext testContext, TestInfo testInfo) throws InterruptedException
    {
        var payload = new JsonObject("{\"log.parser.name\":\"TestWithField\",\"log.parser.event\":\"[24-07-08 07:45:08.9854] warn: [23]  Failed to send notification since no session exists to use as context. Notification was dropped\",\"log.parser.log.positions\":[{\"start.position\":1,\"end.position\":18},{\"start.position\":25,\"end.position\":29},{\"start.position\":32,\"end.position\":34}],\"log.parser.condition.keywords\":[],\"log.parser.condition\":\"all\",\"log.parser.fields\":[{\"log.parser.field.value\":\"24-07-08 07:45:08\",\"log.parser.field.name\":\"timestamp\",\"log.parser.field.type\":\"timestamp\"},{\"log.parser.field.value\":\"warn\",\"log.parser.field.name\":\"severity\",\"log.parser.field.type\":\"none\"},{\"log.parser.field.value\":\"23\",\"log.parser.field.name\":\"byte.avg\",\"log.parser.field.type\":\"none\"}],\"log.parser.type\":\"regex\",\"log.parser.source.type\":\"Azure Cloud\",\"regex\":\"^(?:[^\\\\[]*\\\\[){1}(\\\\d{2}\\\\-\\\\d{1,2}\\\\-\\\\d{1,2}\\\\s\\\\d{1,2}:\\\\d{1,2}:\\\\d{1,2})(?:[^\\\\]]*\\\\]){1}\\\\ ([^\\\\:]*)\\\\:\\\\ \\\\[(\\\\d+)\",\"event.source\":\"127.0.0.1\",\"log.parser.upload\":\"no\"}");

        var values = new JsonObject("{\"testwithfield.severity\":\"warn\",\"testwithfield.byte.avg\":45,\"event.category\":\"TestWithField\",\"event.source.type\":\"Azure Cloud\",\"event.severity\":\"Warning\"}");

        TestAPIUtil.post(LOG_PARSER_API_ENDPOINT, payload, testContext.succeeding(response -> testContext.verify(() ->
        {
            LOGGER.info(testInfo.getTestMethod().get().getName() + ": response: " + response.bodyAsJsonObject().encode());

            var parserId = response.bodyAsJsonObject().getLong(GlobalConstants.ID);

            try (var socket = new Socket("localhost", MotadataConfigUtil.getTCPPort()))
            {
                var outputStream = socket.getOutputStream();

                assertLogProcessorTestResult(testContext, values, LogParserConfigStore.getStore().getItem(parserId).getInteger(GlobalConstants.PLUGIN_ID) + "-testwithfield", testInfo.getTestMethod().get().getName());

                outputStream.write("[24-07-08 07:45:08.9854] warn: [45]  Failed to send notification since no session exists to use as context. Notification was dropped".getBytes());

                outputStream.flush();
            }
            catch (Exception exception)
            {
                testContext.failNow(exception);
            }
        })));

        Assertions.assertTrue(testContext.awaitCompletion(50, TimeUnit.SECONDS), "could not complete the task, reason: timed out.");
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(11)
    @Timeout(20 * 1000)
    @DisplayName("ParseTrafficLogToFlow")
    void testParseTrafficLog(VertxTestContext testContext, TestInfo testInfo) throws InterruptedException
    {
        var context = new JsonObject("{\"log.parser.name\":\"test\",\"log.parser.event\":\"<189>date=2016-09-19 time=18:06:43 devname=DTSPL-FW devid=FW60CA3913000813 logid=0000000013 type=traffic subtype=forward level=notice vd=root srcip=*********** srcport=28055 srcintf=\\\"internal6\\\" dstip=******* dstport=53 dstintf=\\\"internal5\\\" poluuid=9e973e02-6e65-51e6-2fde-e6238f5acd57 sessionid=1300543 proto=17 action=accept policyid=4 dstcountry=\\\"United States\\\" srccountry=\\\"Reserved\\\" trandisp=snat transip=************** transport=28055 service=\\\"DNS\\\" duration=90 sentbyte=67 rcvdbyte=83 sentpkt=1 rcvdpkt=1 appcat=\\\"unscanned\\\" devtype=\\\"Linux PC\\\" osname=\\\"Linux\\\" osversion=\\\"2.6, 3.3, 3.6, 3.8, 3.9\\\" mastersrcmac=00:0c:29:70:d5:63 srcmac=00:0c:29:70:d5:63\",\"log.parser.condition.keywords\":[],\"log.parser.condition\":\"all\",\"log.parser.fields\":[{\"log.parser.field.value\":1669989491,\"log.parser.field.name\":\"event.received.time\",\"log.parser.field.type\":\"none\"},{\"log.parser.field.value\":\"189\",\"log.parser.field.name\":\"priority\",\"log.parser.field.type\":\"none\"},{\"log.parser.field.value\":\"local use 7\",\"log.parser.field.name\":\"facility\",\"log.parser.field.type\":\"none\"},{\"log.parser.field.value\":\"Notice\",\"log.parser.field.name\":\"severity\",\"log.parser.field.type\":\"none\"},{\"log.parser.field.value\":\"traffic\",\"log.parser.field.name\":\"type\",\"log.parser.field.type\":\"none\"},{\"log.parser.field.value\":\"notice\",\"log.parser.field.name\":\"log.level\",\"log.parser.field.type\":\"none\"},{\"log.parser.field.value\":\"***********\",\"log.parser.field.name\":\"source.ip\",\"log.parser.field.type\":\"none\"},{\"log.parser.field.value\":\"28055\",\"log.parser.field.name\":\"source.port\",\"log.parser.field.type\":\"none\"},{\"log.parser.field.value\":\"internal6\",\"log.parser.field.name\":\"source.interface\",\"log.parser.field.type\":\"none\"},{\"log.parser.field.value\":\"*******\",\"log.parser.field.name\":\"destination.ip\",\"log.parser.field.type\":\"none\"},{\"log.parser.field.value\":\"53\",\"log.parser.field.name\":\"destination.port\",\"log.parser.field.type\":\"none\"},{\"log.parser.field.value\":\"internal5\",\"log.parser.field.name\":\"destination.interface\",\"log.parser.field.type\":\"none\"},{\"log.parser.field.value\":\"17\",\"log.parser.field.name\":\"protocol\",\"log.parser.field.type\":\"none\"},{\"log.parser.field.value\":\"accept\",\"log.parser.field.name\":\"action\",\"log.parser.field.type\":\"none\"},{\"log.parser.field.value\":\"United States\",\"log.parser.field.name\":\"destination.country\",\"log.parser.field.type\":\"none\"},{\"log.parser.field.value\":\"Reserved\",\"log.parser.field.name\":\"source.country\",\"log.parser.field.type\":\"none\"},{\"log.parser.field.value\":\"snat\",\"log.parser.field.name\":\"translation.type\",\"log.parser.field.type\":\"none\"},{\"log.parser.field.value\":\"**************\",\"log.parser.field.name\":\"nat.source.ip\",\"log.parser.field.type\":\"none\"},{\"log.parser.field.value\":\"28055\",\"log.parser.field.name\":\"nat.source.port\",\"log.parser.field.type\":\"none\"},{\"log.parser.field.value\":\"DNS\",\"log.parser.field.name\":\"service\",\"log.parser.field.type\":\"none\"},{\"log.parser.field.value\":\"90\",\"log.parser.field.name\":\"duration\",\"log.parser.field.type\":\"none\"},{\"log.parser.field.value\":\"67\",\"log.parser.field.name\":\"sent.bytes\",\"log.parser.field.type\":\"none\"},{\"log.parser.field.value\":\"83\",\"log.parser.field.name\":\"received.bytes\",\"log.parser.field.type\":\"none\"},{\"log.parser.field.value\":\"1\",\"log.parser.field.name\":\"sent.packets\",\"log.parser.field.type\":\"none\"},{\"log.parser.field.value\":\"1\",\"log.parser.field.name\":\"received.packets\",\"log.parser.field.type\":\"none\"},{\"log.parser.field.value\":\"unscanned\",\"log.parser.field.name\":\"application.category\",\"log.parser.field.type\":\"none\"},{\"log.parser.field.value\":\"Linux\",\"log.parser.field.name\":\"os.name\",\"log.parser.field.type\":\"none\"},{\"log.parser.field.value\":\"2.6, 3.3, 3.6, 3.8, 3.9\",\"log.parser.field.name\":\"os.version\",\"log.parser.field.type\":\"none\"},{\"log.parser.field.value\":\"00:0c:29:70:d5:63\",\"log.parser.field.name\":\"source.mac\",\"log.parser.field.type\":\"none\"},{\"log.parser.field.value\":\"Traffic\",\"log.parser.field.name\":\"event.category\",\"log.parser.field.type\":\"none\"},{\"log.parser.field.value\":150,\"log.parser.field.name\":\"bytes\",\"log.parser.field.type\":\"none\"},{\"log.parser.field.value\":2,\"log.parser.field.name\":\"packets\",\"log.parser.field.type\":\"none\"},{\"log.parser.field.value\":1474288603,\"log.parser.field.name\":\"timestamp\",\"log.parser.field.type\":\"timestamp\"}],\"log.parser.type\":\"custom\",\"log.parser.plugin\":10000000000003,\"log.parser.source.type\":\"Firewall\",\"log.parser.upload\":\"no\",\"log.parser.date.time.formatter.type\":\"numeric\",\"log.parser.date.time.format\":\"seconds\",\"event.source\":\"127.0.0.1\",\"log.parser.prefix\":\"test.\",\"id\":9591037285666}");

        var values = new JsonObject("{\"volume.bytes\":150,\"source.if.index\":\"interface-index-0\",\"destination.if.index\":\"interface-index-0\",\"source.port\": 0,\"destination.port\": 53,\"protocol\":\"17 (UDP)\",\"packets\":2,\"original.source.port\":28055,\"original.destination.port\":53}");

        TestAPIUtil.post(LOG_PARSER_API_ENDPOINT, context, testContext.succeeding(response -> testContext.verify(() ->
        {
            LOGGER.info(testInfo.getTestMethod().get().getName() + ": response: " + response.bodyAsJsonObject().encode());

            try (var socket = new Socket("localhost", MotadataConfigUtil.getTCPPort()))
            {
                var outputStream = socket.getOutputStream();

                assertTrafficLogProcessorTestResult(testContext, values, testInfo.getTestMethod().get().getName());

                outputStream.write("<189>date=2021-09-19 time=18:06:43 devname=DTSPL-FW devid=FW60CA3913000813 logid=0000000013 type=traffic subtype=forward level=notice vd=root srcip=*********** srcport=28055 srcintf=\"internal6\" dstip=******* dstport=53 dstintf=\"internal5\" poluuid=9e973e02-6e65-51e6-2fde-e6238f5acd57 sessionid=1300543 proto=17 action=accept policyid=4 dstcountry=\"United States\" srccountry=\"Reserved\" trandisp=snat transip=************** transport=28055 service=\"DNS\" duration=90 sentbyte=67 rcvdbyte=83 sentpkt=1 rcvdpkt=1 appcat=\"unscanned\" devtype=\"Linux PC\" osname=\"Linux\" osversion=\"2.6, 3.3, 3.6, 3.8, 3.9\" mastersrcmac=00:0c:29:70:d5:63 srcmac=00:0c:29:70:d5:63".getBytes());

                outputStream.flush();
            }
            catch (Exception exception)
            {
                testContext.failNow(exception);
            }
        })));

        Assertions.assertTrue(testContext.awaitCompletion(50, TimeUnit.SECONDS), "could not complete the task, reason: timed out.");

    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(12)
    @Timeout(20 * 1000)
    void testLogCollectorResponse(VertxTestContext testContext, TestInfo testInfo)
    {

        var cipherUtil = new CipherUtil();

        eventDBWriteTestConsumer = TestUtil.vertx().eventBus().localConsumer(EventBusConstants.EVENT_DATASTORE_WRITE + "." + DatastoreConstants.DatastoreCategory.EVENT.getName(), message ->
        {

            try
            {
                var event = TestUtil.decodeEventBufferSingleRow(Buffer.buffer(message.body()));

                var values = new JsonObject("{\"plugin.id\":\"500009-other\",\"datastore.format\":\"1\",\"datastore.type\":\"11\",\"source\":\"127.0.0.1\",\"message\":\"<85>Jan  23 11:26:44 mindarray-pc1 unix_chkpwd[21237]: password check failed for user (mindarray)\",\"event.category\":\"Other\",\"event.source.type\":\"Other\",\"event.severity\":\"Informational\"}");

                if (event.getString(GlobalConstants.PLUGIN_ID).equalsIgnoreCase("500009-other"))
                {
                    testContext.verify(() ->
                    {
                        LOGGER.info(testInfo.getTestMethod().get().getName() + ": event: " + event.encode());

                        for (var entry : values)
                        {
                            Assertions.assertTrue(event.containsKey(entry.getKey()) && CommonUtil.getString(entry.getValue()).equalsIgnoreCase(event.getString(entry.getKey())));
                        }
                    });

                    eventDBWriteTestConsumer.unregister();

                    testContext.completeNow();
                }
            }
            catch (Exception exception)
            {
                testContext.failNow(exception);
            }
        });

        var collectorResult = new JsonObject("{\"event.source\":\"127.0.0.1\",\"event.received.time\":1705979523,\"message\":\"<85>Jan  23 11:26:44 mindarray-pc1 unix_chkpwd[21237]: password check failed for user (mindarray)\",\"event.category\":\"Other\",\"plugin.id\":500009,\"event.source.type\":\"Other\",\"event.timestamp\":1705979523,\"event.volume.bytes\":98}").
                put(EventBusConstants.EVENT_TYPE, EventBusConstants.EVENT_LOG).
                put(RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_UUID, "collectorUUID");

        FORWARDER.send(Buffer.buffer().appendShortLE(CommonUtil.getShort((EventBusConstants.REMOTE_EVENT_PROCESSOR_TOPIC + " ").length())).appendString(EventBusConstants.REMOTE_EVENT_PROCESSOR_TOPIC + " ").appendBytes(cipherUtil.encrypt(CodecUtil.compress(collectorResult.encode()))).getBytes());
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(13)
    void testColumnMapperEntries(VertxTestContext testContext)
    {
        TestAPIUtil.get(MISC_COLUMN_MAPPER_API_ENDPOINT + "?filter=" + new JsonObject().put(VisualizationConstants.VISUALIZATION_GROUP_TYPE, VisualizationConstants.VisualizationDataSource.LOG.getName()),
                testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            var body = response.bodyAsJsonObject();

                            assertEquals(HttpStatus.SC_OK, body.getInteger(APIConstants.RESPONSE_CODE));

                            Assertions.assertNotNull(body.getJsonObject(GlobalConstants.RESULT));

                            for (var entry : body.getJsonObject("result"))
                            {
                                Assertions.assertFalse(entry.getKey().contains("_"));
                            }

                            testContext.completeNow();

                        })));
    }

    private void assertTrafficLogProcessorTestResult(VertxTestContext testContext, JsonObject values, String methodName)
    {
        eventDBWriteTestConsumer = TestUtil.vertx().eventBus().localConsumer(EventBusConstants.EVENT_DATASTORE_WRITE + "." + DatastoreConstants.DatastoreCategory.EVENT.getName(), message -> testContext.verify(() ->
        {
            try
            {
                var event = TestUtil.decodeEventBufferSingleRow(Buffer.buffer(message.body()));

                if ((DatastoreConstants.PluginId.FLOW_EVENT.getName() + "-flow").equalsIgnoreCase(event.getString(GlobalConstants.PLUGIN_ID)))
                {
                    LOGGER.info(methodName + ": event: " + event.encode());

                    for (var entry : values)
                    {
                        Assertions.assertTrue(event.containsKey(entry.getKey()) && CommonUtil.getString(entry.getValue()).equalsIgnoreCase(event.getString(entry.getKey())));
                    }

                    testContext.completeNow();
                }
            }
            catch (Exception exception)
            {
                testContext.failNow(exception);
            }
        }));
    }

    private void assertLogProcessorTestResult(VertxTestContext testContext, JsonObject values, String pluginId, String methodName)
    {
        eventDBWriteTestConsumer = TestUtil.vertx().eventBus().localConsumer(EventBusConstants.EVENT_DATASTORE_WRITE + "." + DatastoreConstants.DatastoreCategory.EVENT.getName(), message ->
        {

            try
            {
                var event = TestUtil.decodeEventBufferSingleRow(Buffer.buffer(message.body()));

                if (event.getString(GlobalConstants.PLUGIN_ID).equalsIgnoreCase(pluginId))
                {
                    testContext.verify(() ->
                    {
                        LOGGER.info(methodName + ": event: " + event.encode());

                        for (var entry : values)
                        {
                            Assertions.assertTrue(event.containsKey(entry.getKey()) && CommonUtil.getString(entry.getValue()).equalsIgnoreCase(event.getString(entry.getKey())));
                        }

                        var retries = new AtomicInteger(0);

                        TestUtil.vertx().setPeriodic(3 * 1000, timer ->
                        {
                            if (retries.get() == 3)
                            {
                                TestUtil.vertx().cancelTimer(timer);

                                testContext.failNow("max attempt reached.");
                            }
                            else
                            {
                                var item = EventSourceConfigStore.getStore().getItemByValue(EventBusConstants.EVENT_SOURCE, "127.0.0.1");

                                LOGGER.info("EventSourceConfigStore: " + EventSourceConfigStore.getStore().getItems());

                                if (item != null && item.getJsonArray(GlobalConstants.PLUGIN_ID).contains(CommonUtil.getInteger(pluginId.split("-")[0])))
                                {
                                    testContext.completeNow();
                                }

                                retries.incrementAndGet();
                            }
                        });
                    });
                }
            }
            catch (Exception exception)
            {
                testContext.failNow(exception);
            }
        });
    }
}
