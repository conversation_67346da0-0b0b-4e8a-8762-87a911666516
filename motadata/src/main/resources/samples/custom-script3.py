#  Copyright (c) Motadata 2022. All rights reserved.

import argparse
import json
import socket
from base64 import b64encode

import netmiko
from motadatasdk.constants import Constant
from motadatasdk.logger import Logger
from motadatasdk.util import Util


class PaloAltoFirewallSiteVPNPlugin:

    def __init__(self):

        self.errors = []

    def collect(self, context):

        result = {Constant.STATUS: Constant.STATUS_FAIL, Constant.RESULT: {}}

        tunnels = {}

        try:

            if context.get(Constant.OBJECT_VENDOR) is not None and context.get(Constant.OBJECT_VENDOR) == Constant.VENDOR_PALO_ALTO_NETWORKS:

                client = self._init(context)

                if client is not None:

                    output = client.send_command("show vpn flow")

                    if output is not None and len(output) > 0:

                        tunnels = self._set_tunnel_metrics(output)

                        for tunnel_id in tunnels.keys():

                            output = client.send_command("show vpn flow tunnel-id {}".format(tunnel_id))

                            self._set_tunnel_traffic_metrics(output, tunnels, tunnel_id)

                        result['result'] = list(tunnels.values())

                        if tunnels is not None and len(tunnels) > 0:

                            output = client.send_command("show interface all")

                            self._set_tunnel_interface_metrics(output, tunnels)

                    result[Constant.RESULT] = list(tunnels.values())

            else:
                result[Constant.STATUS] = Constant.STATUS_FAIL

                result[Constant.ERROR_CODE] = Constant.ERROR_CODE_BAD_REQUEST

                result[Constant.MESSAGE] = "Invalid Vendor Type"

        except Exception as exception:

            self.errors.append({Constant.ERROR: str(Logger.get_stack_trace()), Constant.MESSAGE: str(exception), Constant.ERROR_CODE: Constant.ERROR_CODE_INTERNAL_ERROR})

        finally:

            if len(self.errors) > 0:

                result[Constant.ERRORS] = self.errors

            if len(result.get(Constant.RESULT)) > 0:

                result[Constant.STATUS] = Constant.STATUS_SUCCEED

        return result

    def _init(self, context):

        client = None

        try:

            credential = {
                "ip": context.get(Constant.OBJECT_IP),
                "device_type": 'paloalto_panos',
                "username": context.get(Constant.USER_NAME),
                "secret": context.get(Constant.PASSWORD),
                "timeout": context.get(Constant.TIMEOUT),
                "port": context.get(Constant.PORT),
                "password":context.get(Constant.PASSWORD)}

            client = netmiko.ConnectHandler(**credential)

        except netmiko.NetMikoAuthenticationException as exception:

            self.errors.append(
                {Constant.ERROR: str(exception), Constant.MESSAGE: Constant.ERROR_MESSAGE_INVALID_CREDENTIALS.format(str(context.get(Constant.OBJECT_IP)), str(context.get(Constant.PORT))), Constant.ERROR_CODE: Constant.ERROR_CODE_INVALID_CREDENTIALS})

        except (netmiko.NetMikoTimeoutException, socket.timeout) as exception:

            self.errors.append(
                {Constant.ERROR: str(exception), Constant.MESSAGE: Constant.ERROR_MESSAGE_CONNECTION_TIMEOUT.format("SSH",str(context.get(Constant.OBJECT_IP)), str(context.get(Constant.PORT))), Constant.ERROR_CODE: Constant.ERROR_CODE_TIMEOUT})

        except Exception as exception:

            self.errors.append(
                {Constant.ERROR: str(Logger.get_stack_trace()),  Constant.MESSAGE: str(exception), Constant.ERROR_CODE: Constant.ERROR_CODE_INTERNAL_ERROR})

        return client

    def _set_tunnel_metrics(self, output):

        output = output.split("id")[1].split("\n")

        tunnels = {}

        for index in range(2, len(output)):

            tunnel = {}

            if output[index] is not None and len(output[index]) > 0:

                tunnel_id = int(output[index].split()[0].strip())

                tunnels[tunnel_id] = tunnel

                tunnel['tunnel.id'] = tunnel_id

                tunnel['tunnel.name'] = output[index].split()[1].strip()

                if output[index].split()[2].strip() == "active":

                    tunnel['tunnel.status'] = "Up"

                elif output[index].split()[2].strip() == "inactive":

                    tunnel['tunnel.status'] = "Down"

                else:

                    tunnel['tunnel.status'] = "Unknown"

                tunnel['tunnel'] = output[index].split()[4].strip()+":"+output[index].split()[5].strip()

                tunnel['tunnel.interface'] = output[index].split()[6].strip()

        return tunnels

    def _set_tunnel_traffic_metrics(self, output, tunnels, tunnel_id):

        tunnel_metrics = output.strip().split("\n")

        tunnel = tunnels.get(tunnel_id)

        for tunnel_metric in tunnel_metrics:

            if "auth algorithm" in tunnel_metric:

                tunnel["tunnel.authentication.algorithm"] = tunnel_metric.split(":")[1].strip()

            elif "enc  algorithm" in tunnel_metric:

                tunnel["tunnel.encryption.algorithm"] = tunnel_metric.split(":")[1].strip()

            elif "encap packets" in tunnel_metric:

                tunnel["tunnel.encrypted.packets.rate"] = tunnel_metric.split(":")[1].strip()

            elif "decap packets" in tunnel_metric:

                tunnel["tunnel.decrypted.packets.rate"] = tunnel_metric.split(":")[1].strip()

            elif "monitor packets sent" in tunnel_metric:

                tunnel["tunnel.out.packets.rate"] = tunnel_metric.split(":")[1].strip()

            elif "monitor packets recv" in tunnel_metric:

                tunnel["tunnel.in.packets.rate"] = tunnel_metric.split(":")[1].strip()

        tunnels[tunnel_id] = tunnel

    def _set_tunnel_interface_metrics(self, output, tunnels):

        tokens = output.strip().split("logical interfaces")[1].split("\n")

        tunnel_zone = {}

        if "zone" in tokens[2]:

            for index in range(4,len(tokens)):

                token = tokens[index].split()

                interface_name = token[0].strip()

                if interface_name is not None and len(interface_name) > 0:

                    tunnel_zone[interface_name] = token[3].strip()

            for tunnel_id in tunnels.keys():

                tunnel = tunnels.get(tunnel_id)

                if tunnel.get("tunnel.interface") is not None and len(tunnel.get("tunnel.interface")) > 0:

                    tunnel["tunnel.security.zone"] = tunnel_zone.get(tunnel.get("tunnel.interface"))

        return tunnels


if __name__ == '__main__':
    parser = argparse.ArgumentParser()

    parser.add_argument("--context")

    args, leftovers = parser.parse_known_args()

    print(b64encode(json.dumps(PaloAltoFirewallSiteVPNPlugin().collect(Util.load_plugin_context(str(args.context))).encode()).decode(), end=Constant.BLANK_STRING)