#  Copyright (c) Motadata 2022. All rights reserved.

import argparse
import json
from base64 import b64encode

from motadatasdk.constants import Constant
from motadatasdk.logger import Logger
from motadatasdk.util import Util


#"command" : "select TOP 10 con.host, con.port, con.user_name, con.connection_status, con.connection_id, con.transaction_id, TO_VARCHAR(s.LAST_EXECUTED_TIME) LAST_EXECUTED_TIME, s.ALLOCATED_MEMORY_SIZE, s.used_memory_size , s.statement_string from m_connections con, m_prepared_statements s where s.connection_id = con.connection_id and con.connection_status != 'IDLE' order by s.allocated_memory_size desc ",

class SAPHANASessionByMemoryMetricPlugin:

    def collect(self, context):

        result = {Constant.STATUS: Constant.STATUS_FAIL, Constant.RESULT: {}}

        try:

            sessions = []

            for row in context.get(Constant.RESULT):

                session = {}

                if row.get("STATEMENT_STRING") != context.get(Constant.COMMAND):

                    if row.get("HOST") is not None:
                        session["sap.hana.session.host"] = row.get("HOST")

                    if row.get("PORT") is not None:
                        session["sap.hana.session.port"] = row.get("PORT")

                    if row.get("USER_NAME") is not None:
                        session["sap.hana.session.user.name"] = row.get("USER_NAME")

                    if row.get("CONNECTION_STATUS") is not None:
                        session["sap.hana.session.connection.status"] = row.get("CONNECTION_STATUS")

                    if row.get("CONNECTION_ID") is not None and row.get("CONNECTION_ID") >= 0:
                        session["sap.hana.session.connection.id"] = row.get("CONNECTION_ID")

                    if row.get("TRANSACTION_ID") is not None:
                        session["sap.hana.session.transaction.id"] = row.get("TRANSACTION_ID")

                    if row.get("LAST_EXECUTED_TIME") is not None:
                        session["sap.hana.session.last.executed.time"] = Util.convert_to_string(
                            row.get("LAST_EXECUTED_TIME"))

                    if row.get("ALLOCATED_MEMORY_SIZE") is not None:
                        session["sap.hana.session.memory.allocated.bytes"] = Util.convert_to_int(
                            row.get("ALLOCATED_MEMORY_SIZE"))

                    if row.get("USED_MEMORY_SIZE") is not None:
                        session["sap.hana.session.memory.used.bytes"] = Util.convert_to_int(row.get("USED_MEMORY_SIZE"))

                    if row.get("STATEMENT_STRING") is not None:
                        session["sap.hana.session.query"] = Util.convert_to_string(row.get("STATEMENT_STRING"))

                    if len(session) > 0:
                        sessions.append(session)

            if len(sessions) > 0:
                result[Constant.STATUS] = Constant.STATUS_SUCCEED

                result[Constant.RESULT]["sap.hana.session"] = sessions

        except Exception as exception:

            result[Constant.ERRORS] = [
                {Constant.ERROR: str(Logger.get_stack_trace()), Constant.MESSAGE: str(exception),
                 Constant.ERROR_CODE: Constant.ERROR_CODE_INTERNAL_ERROR}]

        return result

if __name__ == '__main__':
    parser = argparse.ArgumentParser()

    parser.add_argument("--context")

    args, leftovers = parser.parse_known_args()

    print(b64encode(json.dumps(SAPHANASessionByMemoryMetricPlugin().collect(
        Util.load_plugin_context(str(args.context))).encode()).decode(), end=Constant.BLANK_STRING)