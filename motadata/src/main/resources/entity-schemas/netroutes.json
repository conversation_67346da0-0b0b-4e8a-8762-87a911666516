{"entity": "NetRoute", "table": "tbl_config_netroute", "version": "1.0", "author": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "props": [{"name": "netroute.name", "title": "NetRoute Name", "type": "string", "rules": ["required", "unique"]}, {"name": "netroute.source", "title": "NetRoute Source", "type": "numeric", "rules": ["required"]}, {"name": "netroute.destination", "title": "NetRoute Destination", "type": "string", "rules": ["required"]}, {"name": "netroute.destination.port", "title": "NetRoute Destination Port", "type": "numeric", "rules": ["required"]}, {"name": "netroute.polling.time", "title": "NetRoute Polling Time", "type": "numeric", "rules": ["required"]}, {"name": "netroute.state", "title": "NetRoute State", "type": "string", "rules": ["required"]}, {"name": "netroute.tags", "title": "NetRoute Tags", "type": "list", "rules": []}], "entries": []}