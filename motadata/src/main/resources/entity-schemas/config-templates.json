{"entity": "Config <PERSON>late", "table": "tbl_config_config_template", "version": "1.0", "author": "", "props": [{"name": "config.template.name", "title": "Template Name", "type": "string", "rules": ["required", "unique"]}, {"name": "config.template.description", "title": "Template Description", "type": "string"}, {"name": "config.template.vendor", "title": "<PERSON><PERSON><PERSON>", "type": "string", "rules": ["required"]}, {"name": "config.template.os.type", "title": "OS Type", "type": "string"}, {"name": "config.template.catalog.ids", "title": "Catalog id (s)", "type": "list"}], "entries": [{"type": "inline", "records": [{"id": 100000000000001, "config.template.name": "Cisco", "config.template.description": "This is an Config template for Cisco Devices", "config.template.vendor": "Cisco Systems", "config.template.os.type": "Cisco", "config.template.delay.time": 1000, "NONE": {"Backup_Running": [{"operation.command": "terminal length 0", "operation.delay.time": 1000, "operation.timeout": 2000, "operation.prompt": "#", "operation.prompt.command": "No Command"}, {"operation.command": "show running-config", "operation.delay.time": 1000, "operation.timeout": 5000, "operation.prompt": "#", "operation.prompt.command": "LF", "operation.response.required": "yes"}], "Backup_Startup": [{"operation.command": "terminal length 0", "operation.delay.time": 1000, "operation.timeout": 2000, "operation.prompt": "#", "operation.prompt.command": "No Command"}, {"operation.command": "show startup-config", "operation.delay.time": 1000, "operation.timeout": 5000, "operation.prompt": "#", "operation.prompt.command": "LF", "operation.response.required": "yes"}], "Sync": [{"operation.command": "copy running-config startup-config", "operation.delay.time": 1000, "operation.timeout": 10000, "operation.prompt": "]?", "operation.prompt.command": "LF followed by LF"}]}, "TFTP": {"Backup_Running": [{"operation.command": "copy running-config tftp:", "operation.delay.time": 1000, "operation.timeout": 2000, "operation.prompt": "]?", "operation.prompt.command": "No Command"}, {"operation.command": "&[TransferProtocolServerAddress]", "operation.delay.time": 1000, "operation.timeout": 2000, "operation.prompt": "]?", "operation.prompt.command": "No Command"}, {"operation.command": "&[TransferFileName]", "operation.delay.time": 1000, "operation.timeout": 5000, "operation.prompt": "#", "operation.prompt.command": "LF"}], "Backup_Startup": [{"operation.command": "copy startup-config tftp:", "operation.delay.time": 1000, "operation.timeout": 2000, "operation.prompt": "]?", "operation.prompt.command": "No Command"}, {"operation.command": "&[TransferProtocolServerAddress]", "operation.delay.time": 1000, "operation.timeout": 2000, "operation.prompt": "]?", "operation.prompt.command": "No Command"}, {"operation.command": "&[TransferFileName]", "operation.delay.time": 1000, "operation.timeout": 5000, "operation.prompt": "#", "operation.prompt.command": "LF"}], "Sync": [{"operation.command": "copy running-config startup-config", "operation.delay.time": 1000, "operation.timeout": 10000, "operation.prompt": "]?", "operation.prompt.command": "LF followed by LF"}], "Restore": [{"operation.command": "copy tftp: running-config", "operation.delay.time": 1000, "operation.timeout": 2000, "operation.prompt": "]?", "operation.prompt.command": "No Command"}, {"operation.command": "&[TransferProtocolServerAddress]", "operation.delay.time": 1000, "operation.timeout": 2000, "operation.prompt": "]?", "operation.prompt.command": "No Command"}, {"operation.command": "&[TransferFileName]", "operation.delay.time": 1000, "operation.timeout": 2000, "operation.prompt": "]?", "operation.prompt.command": "No Command"}, {"operation.command": "running-config", "operation.delay.time": 1000, "operation.timeout": 5000, "operation.prompt": "#", "operation.prompt.command": "LF"}]}, "SCP/SFTP": {"Backup_Running": [{"operation.command": "copy running-config scp:", "operation.delay.time": 1000, "operation.timeout": 2000, "operation.prompt": "]?", "operation.prompt.command": "No Command"}, {"operation.command": "&[TransferProtocolServerAddress]", "operation.delay.time": 1000, "operation.timeout": 2000, "operation.prompt": "]?", "operation.prompt.command": "No Command"}, {"operation.command": "&[TransferProtocolServerUser]", "operation.delay.time": 1000, "operation.timeout": 2000, "operation.prompt": "]?", "operation.prompt.command": "No Command"}, {"operation.command": "&[TransferFileName]", "operation.delay.time": 1000, "operation.timeout": 2000, "operation.prompt": "Password:", "operation.prompt.command": "No Command"}, {"operation.command": "&[TransferProtocolServerPassword]", "operation.delay.time": 1000, "operation.timeout": 5000, "operation.prompt": "#", "operation.prompt.command": "LF"}], "Backup_Startup": [{"operation.command": "copy startup-config scp:", "operation.delay.time": 1000, "operation.timeout": 2000, "operation.prompt": "]?", "operation.prompt.command": "No Command"}, {"operation.command": "&[TransferProtocolServerAddress]", "operation.delay.time": 1000, "operation.timeout": 2000, "operation.prompt": "]?", "operation.prompt.command": "No Command"}, {"operation.command": "&[TransferProtocolServerUser]", "operation.delay.time": 1000, "operation.timeout": 2000, "operation.prompt": "]?", "operation.prompt.command": "No Command"}, {"operation.command": "&[TransferFileName]", "operation.delay.time": 1000, "operation.timeout": 2000, "operation.prompt": "Password:", "operation.prompt.command": "No Command"}, {"operation.command": "&[TransferProtocolServerPassword]", "operation.delay.time": 1000, "operation.timeout": 5000, "operation.prompt": "#", "operation.prompt.command": "LF"}], "Sync": [{"operation.command": "copy running-config startup-config", "operation.delay.time": 1000, "operation.timeout": 10000, "operation.prompt": "]?", "operation.prompt.command": "LF followed by LF"}], "Restore": [{"operation.command": "copy scp: running-config", "operation.delay.time": 100, "operation.timeout": 2000, "operation.prompt": "]?", "operation.prompt.command": "No Command"}, {"operation.command": "&[TransferProtocolServerAddress]", "operation.delay.time": 100, "operation.timeout": 2000, "operation.prompt": "]?", "operation.prompt.command": "No Command"}, {"operation.command": "&[TransferProtocolServerUser]", "operation.delay.time": 100, "operation.timeout": 2000, "operation.prompt": "]?", "operation.prompt.command": "No Command"}, {"operation.command": "&[TransferFileName]", "operation.delay.time": 100, "operation.timeout": 2000, "operation.prompt": "]?", "operation.prompt.command": "No Command"}, {"operation.command": "running-config", "operation.delay.time": 100, "operation.timeout": 2000, "operation.prompt": "Password:", "operation.prompt.command": "No Command"}, {"operation.command": "&[TransferProtocolServerPassword]", "operation.delay.time": 100, "operation.timeout": 5000, "operation.prompt": "#", "operation.prompt.command": "LF"}]}, "config.template.catalog.ids": [], "_type": "0"}, {"id": 100000000000003, "config.template.name": "D-Link", "config.template.description": "This is an Config template for D-Link Devices", "config.template.vendor": "D-Link", "config.template.os.type": "DROS", "config.template.delay.time": 1000, "TFTP": {"Backup_Running": [{"operation.command": "upload cfg_toTFTP &[TransferProtocolServerAddress] &[TransferFileName]", "operation.delay.time": 1000, "operation.timeout": 2000, "operation.prompt": ">", "operation.prompt.command": "LF"}], "Sync": [{"operation.command": "save", "operation.delay.time": 1000, "operation.timeout": 10000, "operation.prompt": ">", "operation.prompt.command": "LF"}], "Restore": [{"operation.command": "download cfg_fromTFTP &[TransferProtocolServerAddress] &[TransferFileName]", "operation.delay.time": 1000, "operation.timeout": 2000, "operation.prompt": ">", "operation.prompt.command": "LF"}]}, "_type": "0"}, {"id": 100000000000004, "config.template.name": "Fortinet", "config.template.description": "This is an Config template for Fortinet Fortigate Devices", "config.template.vendor": "Fortinet", "config.template.os.type": "FortiOS", "config.template.delay.time": 1000, "NONE": {"Backup_Running": [{"operation.command": "config system console", "operation.delay.time": 1000, "operation.timeout": 2000, "operation.prompt": "#", "operation.prompt.command": "LF"}, {"operation.command": "set output standard", "operation.delay.time": 1000, "operation.timeout": 2000, "operation.prompt": "#", "operation.prompt.command": "LF"}, {"operation.command": "end", "operation.delay.time": 1000, "operation.timeout": 2000, "operation.prompt": "#", "operation.prompt.command": "LF"}, {"operation.command": "show", "operation.delay.time": 1000, "operation.timeout": 5000, "operation.prompt": "#", "operation.prompt.command": "LF", "operation.response.required": "yes"}]}, "TFTP": {"Backup_Running": [{"operation.command": "execute backup config tftp &[TransferFileName] &[TransferProtocolServerAddress]", "operation.delay.time": 1000, "operation.timeout": 5000, "operation.prompt": "#", "operation.prompt.command": "LF"}], "Restore": [{"operation.command": "execute restore config tftp &[TransferFileName] &[TransferProtocolServerAddress]", "operation.delay.time": 1000, "operation.timeout": 5000, "operation.prompt": "(y/n)", "operation.prompt.command": "y followed by LF"}]}, "_type": "0"}, {"id": 100000000000005, "config.template.name": "HPE", "config.template.description": "This is an Config template for HPE Devices", "config.template.vendor": "Hewlett Packard Enterprise", "config.template.os.type": "Comware Software", "config.template.delay.time": 1000, "NONE": {"Backup_Running": [{"operation.command": "terminal length 0", "operation.delay.time": 1000, "operation.timeout": 2000, "operation.prompt": "#", "operation.prompt.command": "No Command"}, {"operation.command": "show running-config", "operation.delay.time": 1000, "operation.timeout": 5000, "operation.prompt": "#", "operation.prompt.command": "LF", "operation.response.required": "yes"}], "Backup_Startup": [{"operation.command": "terminal length 0", "operation.delay.time": 1000, "operation.timeout": 2000, "operation.prompt": "#", "operation.prompt.command": "No Command"}, {"operation.command": "show startup-config", "operation.delay.time": 1000, "operation.timeout": 5000, "operation.prompt": "#", "operation.prompt.command": "LF", "operation.response.required": "yes"}], "Sync": [{"operation.command": "copy system:running-config nvram:startup-config", "operation.delay.time": 1000, "operation.timeout": 10000, "operation.prompt": "(y/n)", "operation.prompt.command": "y"}]}, "TFTP": {"Backup_Startup": [{"operation.command": "copy nvram:startup-config tftp://&[TransferProtocolServerAddress]/&[TransferFileName]", "operation.delay.time": 1000, "operation.timeout": 5000, "operation.prompt": "(y/n)", "operation.prompt.command": "y"}], "Sync": [{"operation.command": "copy system:running-config nvram:startup-config", "operation.delay.time": 1000, "operation.timeout": 10000, "operation.prompt": "]?", "operation.prompt.command": "LF"}]}, "_type": "0"}, {"id": 100000000000006, "config.template.name": "<PERSON><PERSON><PERSON>", "config.template.description": "This is an Config template for Huawei Devices", "config.template.vendor": "<PERSON><PERSON><PERSON>", "config.template.os.type": "VRP", "config.template.delay.time": 1000, "NONE": {"Backup_Running": [{"operation.command": "screen-length 0 temporary", "operation.delay.time": 1000, "operation.timeout": 2000, "operation.prompt": ">", "operation.prompt.command": "No Command"}, {"operation.command": "display current-configuration", "operation.delay.time": 1000, "operation.timeout": 5000, "operation.prompt": ">", "operation.prompt.command": "LF", "operation.response.required": "yes"}], "Backup_Startup": [{"operation.command": "screen-length 0 temporary", "operation.delay.time": 1000, "operation.timeout": 2000, "operation.prompt": ">", "operation.prompt.command": "No Command"}, {"operation.command": "display saved-configuration", "operation.delay.time": 1000, "operation.timeout": 5000, "operation.prompt": ">", "operation.prompt.command": "LF", "operation.response.required": "yes"}], "Sync": [{"operation.command": "save", "operation.delay.time": 1000, "operation.timeout": 10000, "operation.prompt": "[Y/N]", "operation.prompt.command": "y followed by LF"}]}, "TFTP": {"Backup_Running": [{"operation.command": "save &[TransferFileName]", "operation.delay.time": 1000, "operation.timeout": 4000, "operation.prompt": "[Y/N]", "operation.prompt.command": "y followed by LF"}, {"operation.command": "tftp &[TransferProtocolServerAddress] put &[TransferFileName]", "operation.delay.time": 1000, "operation.timeout": 5000, "operation.prompt": ">", "operation.prompt.command": "LF"}], "Sync": [{"operation.command": "save", "operation.delay.time": 1000, "operation.timeout": 10000, "operation.prompt": "[Y/N]", "operation.prompt.command": "y followed by LF"}], "Restore": [{"operation.command": "tftp &[TransferProtocolServerAddress] get &[TransferFileName]", "operation.delay.time": 1000, "operation.timeout": 3000, "operation.prompt": "[Y/N]:", "operation.prompt.command": "y followed by LF"}, {"operation.command": "startup saved-configuration &[TransferFileName]", "operation.delay.time": 1000, "operation.timeout": 5000, "operation.prompt": ">", "operation.prompt.command": "LF"}]}, "SCP/SFTP": {"Backup_Running": [{"operation.command": "save &[LocalFileName]", "operation.delay.time": 1000, "operation.timeout": 10000, "operation.prompt": "[Y/N]", "operation.prompt.command": "y followed by LF"}, {"operation.command": "system-view", "operation.delay.time": 1000, "operation.timeout": 2000, "operation.prompt": "]", "operation.prompt.command": "LF"}, {"operation.command": "sftp client-transfile put host-ip &[TransferProtocolServerAddress] username &[TransferProtocolServerUser] password &[TransferProtocolServerPassword] sourcefile &[LocalFileName]", "operation.delay.time": 1000, "operation.timeout": 10000, "operation.prompt": "]", "operation.prompt.command": "LF"}, {"operation.command": "quit", "operation.delay.time": 1000, "operation.timeout": 2000, "operation.prompt": ">", "operation.prompt.command": "LF"}, {"operation.command": "delete &[LocalFileName]", "operation.delay.time": 1000, "operation.timeout": 2000, "operation.prompt": "[Y/N]", "operation.prompt.command": "y followed by LF"}], "Sync": [{"operation.command": "save", "operation.delay.time": 1000, "operation.timeout": 10000, "operation.prompt": "[Y/N]", "operation.prompt.command": "y followed by LF"}]}, "_type": "0"}, {"_type": "0", "id": 100000000000007, "config.template.name": "Juniper", "config.template.description": "This is an Config template for Juniper Devices", "config.template.os.type": "JunOS", "config.template.delay.time": 1000, "config.template.vendor": "Juniper Networks", "config.template.catalog.ids": [], "NONE": {"Backup_Running": [{"operation.command": "set cli screen-length 0", "operation.timeout": 2000, "operation.delay.time": 1000, "operation.prompt": ">", "operation.prompt.command": "No Command", "operation.response.required": "no"}, {"operation.command": "show configuration", "operation.timeout": 5000, "operation.delay.time": 1000, "operation.prompt": ">", "operation.prompt.command": "LF", "operation.response.required": "yes"}]}, "TFTP": {"Backup_Running": [{"operation.command": "configure", "operation.timeout": 2000, "operation.delay.time": 1000, "operation.prompt": "#", "operation.prompt.command": "No Command"}, {"operation.command": "save /tmp/&[TransferFileName]", "operation.timeout": 5000, "operation.delay.time": 1000, "operation.prompt": "#", "operation.prompt.command": "No Command"}, {"operation.command": "exit", "operation.timeout": 5000, "operation.delay.time": 1000, "operation.prompt": ">", "operation.prompt.command": "No Command"}, {"operation.command": "start shell", "operation.timeout": 5000, "operation.delay.time": 1000, "operation.prompt": "%", "operation.prompt.command": "No Command"}, {"operation.command": "cd /tmp/", "operation.timeout": 5000, "operation.delay.time": 1000, "operation.prompt": "%", "operation.prompt.command": "No Command"}, {"operation.command": "tftp &[TransferProtocolServerAddress]", "operation.timeout": 5000, "operation.delay.time": 1000, "operation.prompt": ">", "operation.prompt.command": "No Command"}, {"operation.command": "put &[TransferFileName]", "operation.timeout": 5000, "operation.delay.time": 1000, "operation.prompt": ">", "operation.prompt.command": "No Command"}, {"operation.command": "quit", "operation.timeout": 5000, "operation.delay.time": 1000, "operation.prompt": "%", "operation.prompt.command": "No Command"}, {"operation.command": "exit", "operation.timeout": 5000, "operation.delay.time": 1000, "operation.prompt": ">", "operation.prompt.command": "LF"}]}, "SCP/SFTP": {"Backup_Running": [{"operation.command": "configure", "operation.timeout": 2000, "operation.delay.time": 1000, "operation.prompt": "#", "operation.prompt.command": "No Command"}, {"operation.command": "save /tmp/&[LocalFileName]", "operation.timeout": 5000, "operation.delay.time": 1000, "operation.prompt": "#", "operation.prompt.command": "No Command"}, {"operation.command": "exit", "operation.timeout": 5000, "operation.delay.time": 1000, "operation.prompt": ">", "operation.prompt.command": "No Command"}, {"operation.command": "start shell", "operation.timeout": 5000, "operation.delay.time": 1000, "operation.prompt": "%", "operation.prompt.command": "No Command"}, {"operation.command": "cd /tmp/", "operation.timeout": 5000, "operation.delay.time": 1000, "operation.prompt": "%", "operation.prompt.command": "No Command"}, {"operation.command": "sftp &[TransferProtocolServerUser]@&[TransferProtocolServerAddress]", "operation.timeout": 5000, "operation.delay.time": 1000, "operation.prompt": "password:", "operation.prompt.command": "No Command"}, {"operation.command": "&[TransferProtocolServerPassword]", "operation.timeout": 5000, "operation.delay.time": 1000, "operation.prompt": ">", "operation.prompt.command": "No Command"}, {"operation.command": "cd /motadata/motadata/config-management", "operation.timeout": 5000, "operation.delay.time": 1000, "operation.prompt": ">", "operation.prompt.command": "No Command"}, {"operation.command": "put /tmp/&[LocalFileName]", "operation.timeout": 5000, "operation.delay.time": 1000, "operation.prompt": ">", "operation.prompt.command": "No Command"}, {"operation.command": "quit", "operation.timeout": 5000, "operation.delay.time": 1000, "operation.prompt": "%", "operation.prompt.command": "No Command"}, {"operation.command": "exit", "operation.timeout": 5000, "operation.prompt": ">", "operation.prompt.command": "LF"}]}}, {"id": 100000000000008, "config.template.name": "Extreme Networks", "config.template.description": "This is a device template for all Extreme Summit switches running with ExtremewareXOS.", "config.template.os.type": "ExtremeXOS", "config.template.delay.time": 1000, "config.template.vendor": "Extreme Networks", "config.template.catalog.ids": [], "NONE": {"Backup_Running": [{"operation.command": "disable clipaging", "operation.timeout": 2000, "operation.prompt": "#", "operation.prompt.command": "No Command", "operation.response.required": "no"}, {"operation.command": "show config detail", "operation.timeout": 20000, "operation.prompt": "#", "operation.prompt.command": "LF", "operation.response.required": "yes"}]}, "TFTP": {"Backup_Running": [{"operation.command": "tftp &[TransferProtocolServerAddress] -p -l primary.cfg -r &[TransferFileName]", "operation.timeout": 2000, "operation.prompt": "#", "operation.prompt.command": "LF"}], "Restore": [{"operation.command": "tftp &[TransferProtocolServerAddress] -g -l primary.cfg -r &[TransferFileName]", "operation.timeout": 2000, "operation.prompt": "#", "operation.prompt.command": "LF"}]}, "SCP/SFTP": {"Backup_Running": [{"operation.command": "scp2 port 22 primary.cfg &[TransferProtocolServerUser]@&[TransferProtocolServerAddress]:&[TransferFileName]", "operation.timeout": 2000, "operation.prompt": "password:", "operation.prompt.command": "No Command"}, {"operation.command": "&[TransferProtocolServerPassword]", "operation.timeout": 2000, "operation.prompt": "#", "operation.prompt.command": "LF"}], "Restore": [{"operation.command": "scp2 port 22 &[TransferProtocolServerUser]@&[TransferProtocolServerAddress]:&[TransferFileName] primary.cfg", "operation.timeout": 2000, "operation.prompt": "password:", "operation.prompt.command": "No Command"}, {"operation.command": "&[TransferProtocolServerPassword]", "operation.timeout": 2000, "operation.prompt": "#", "operation.prompt.command": "LF"}]}, "_type": "0"}, {"_type": "0", "id": 100000000000009, "config.template.name": "Palo Alto Firewall", "config.template.description": "This is a template for Palo Alto Firewall for series 3000 and 4000", "config.template.os.type": "PanOS", "config.template.delay.time": 1000, "config.template.vendor": "Palo Alto Networks", "config.template.catalog.ids": [], "NONE": {"Backup_Running": [{"operation.command": "set cli pager off", "operation.timeout": 2000, "operation.prompt": ">", "operation.prompt.command": "No Command", "operation.response.required": "no"}, {"operation.command": "show config running", "operation.timeout": 2000, "operation.prompt": ">", "operation.prompt.command": "LF", "operation.response.required": "yes"}]}, "TFTP": {"Backup_Running": [{"operation.command": "configure", "operation.timeout": 10000, "operation.prompt": "#", "operation.prompt.command": "No Command"}, {"operation.command": "save config to &[TransferFileName]", "operation.timeout": 10000, "operation.prompt": "#", "operation.prompt.command": "No Command"}, {"operation.command": "exit", "operation.timeout": 10000, "operation.prompt": ">", "operation.prompt.command": "No Command"}, {"operation.command": "tftp export configuration from &[TransferFileName] to &[TransferProtocolServerAddress]", "operation.timeout": 10000, "operation.prompt": ">", "operation.prompt.command": "No Command"}, {"operation.command": "delete config saved &[TransferFileName]", "operation.timeout": 10000, "operation.prompt": ">", "operation.prompt.command": "LF"}], "Restore": [{"operation.command": "tftp import configuration source-ip &[DeviceIP] from &[TransferProtocolServerAddress] file &[TransferFileName]", "operation.timeout": 10000, "operation.prompt": ">", "operation.prompt.command": "No Command"}, {"operation.command": "configure", "operation.timeout": 10000, "operation.prompt": "#", "operation.prompt.command": "No Command"}, {"operation.command": "load config from &[TransferFileName]", "operation.timeout": 10000, "operation.prompt": "#", "operation.prompt.command": "No Command"}, {"operation.command": "commit", "operation.timeout": 10000, "operation.prompt": "#", "operation.prompt.command": "No Command"}, {"operation.command": "exit", "operation.timeout": 10000, "operation.prompt": ">", "operation.prompt.command": "LF"}]}, "SCP/SFTP": {"Backup_Running": [{"operation.command": "configure", "operation.timeout": 10000, "operation.prompt": "#", "operation.prompt.command": "No Command"}, {"operation.command": "save config to &[LocalFileName]", "operation.timeout": 10000, "operation.prompt": "#", "operation.prompt.command": "No Command"}, {"operation.command": "exit", "operation.timeout": 10000, "operation.prompt": ">", "operation.prompt.command": "No Command"}, {"operation.command": "scp export configuration from &[LocalFileName] to &[TransferProtocolServerUser]@&[TransferProtocolServerAddress]:&[TransferFileName]", "operation.timeout": 10000, "operation.prompt": "password:", "operation.prompt.command": "No Command"}, {"operation.command": "&[TransferProtocolServerPassword]", "operation.timeout": 10000, "operation.prompt": ">", "operation.prompt.command": "No Command"}, {"operation.command": "delete config saved &[LocalFileName]", "operation.timeout": 10000, "operation.prompt": ">", "operation.prompt.command": "No Command"}], "Restore": [{"operation.command": "scp import configuration from &[TransferProtocolServerUser]@&[TransferProtocolServerAddress]:&[TransferFileName]", "operation.timeout": 10000, "operation.prompt": "password:", "operation.prompt.command": "No Command"}, {"operation.command": "&[TransferProtocolServerPassword]", "operation.timeout": 10000, "operation.prompt": ">", "operation.prompt.command": "No Command"}, {"operation.command": "configure", "operation.timeout": 10000, "operation.prompt": "#", "operation.prompt.command": "No Command"}, {"operation.command": "load config from &[LocalFileName]", "operation.timeout": 10000, "operation.prompt": "#", "operation.prompt.command": "No Command"}, {"operation.command": "commit", "operation.timeout": 10000, "operation.prompt": "#", "operation.prompt.command": "No Command"}, {"operation.command": "exit", "operation.timeout": 10000, "operation.prompt": ">", "operation.prompt.command": "No Command"}]}}, {"_type": "0", "id": 100000000000010, "config.template.name": "3Com", "config.template.description": "This is a template for all 3Com devices", "config.template.os.type": "3Com", "config.template.delay.time": 1000, "config.template.vendor": "3Com Corporation", "config.template.catalog.ids": [], "NONE": {"Backup_Running": [{"operation.command": "screen-length disable", "operation.timeout": 2000, "operation.prompt": ">", "operation.prompt.command": "No Command", "operation.response.required": "no"}, {"operation.command": "display current-configuration", "operation.timeout": 10000, "operation.prompt": ">", "operation.prompt.command": "No Command", "operation.response.required": "yes"}], "Backup_Startup": [{"operation.command": "screen-length disable", "operation.timeout": 2000, "operation.prompt": ">", "operation.prompt.command": "No Command", "operation.response.required": "no"}, {"operation.command": "display saved-configuration", "operation.timeout": 10000, "operation.prompt": ">", "operation.prompt.command": "No Command", "operation.response.required": "yes"}], "Sync": [{"operation.command": "copy current-configuration saved-configuration", "operation.delay.time": 1000, "operation.timeout": 10000, "operation.prompt": ">", "operation.prompt.command": "LF followed by LF"}]}, "TFTP": {"Backup_Running": [{"operation.command": "system", "operation.timeout": 2000, "operation.prompt": "]", "operation.prompt.command": "No Command"}, {"operation.command": "backup fabric current-configuration to &[TransferProtocolServerAddress] &[TransferFileName]", "operation.timeout": 10000, "operation.prompt": "]", "operation.prompt.command": "No Command"}, {"operation.command": "exit", "operation.timeout": 2000, "operation.prompt": "]", "operation.prompt.command": "No Command"}], "Backup_Startup": [{"operation.command": "system", "operation.timeout": 2000, "operation.prompt": "]", "operation.prompt.command": "No Command"}, {"operation.command": "backup fabric saved-configuration to &[TransferProtocolServerAddress] &[TransferFileName]", "operation.timeout": 10000, "operation.prompt": "]", "operation.prompt.command": "No Command"}, {"operation.command": "exit", "operation.timeout": 2000, "operation.prompt": "]", "operation.prompt.command": "No Command"}], "Restore": [{"operation.command": "system", "operation.timeout": 2000, "operation.prompt": "]", "operation.prompt.command": "No Command"}, {"operation.command": "restore fabric current-configuration from &[TransferProtocolServerAddress]", "operation.timeout": 10000, "operation.prompt": "]", "operation.prompt.command": "LF"}, {"operation.command": "exit", "operation.timeout": 2000, "operation.prompt": "]", "operation.prompt.command": "No Command"}], "Sync": [{"operation.command": "copy current-configuration saved-configuration", "operation.delay.time": 1000, "operation.timeout": 10000, "operation.prompt": ">", "operation.prompt.command": "LF followed by LF"}]}}, {"_type": "0", "id": 100000000000011, "config.template.name": "A10Networks", "config.template.description": "This is a template for all A10Networks devices", "config.template.os.type": "ACOS", "config.template.delay.time": 1000, "config.template.vendor": "A10 Networks", "config.template.catalog.ids": [], "NONE": {"Backup_Running": [{"operation.command": "terminal length 0", "operation.timeout": 2000, "operation.prompt": "#", "operation.prompt.command": "No Command", "operation.response.required": "no"}, {"operation.command": "show running-config", "operation.timeout": 10000, "operation.prompt": "#", "operation.prompt.command": "No Command", "operation.response.required": "yes"}], "Backup_Startup": [{"operation.command": "terminal length 0", "operation.timeout": 2000, "operation.prompt": "#", "operation.prompt.command": "No Command", "operation.response.required": "no"}, {"operation.command": "show startup-config", "operation.timeout": 10000, "operation.prompt": "#", "operation.prompt.command": "No Command", "operation.response.required": "yes"}], "Sync": [{"operation.command": "copy running-config startup-config", "operation.delay.time": 1000, "operation.timeout": 10000, "operation.prompt": "#", "operation.prompt.command": "LF followed by LF"}]}, "TFTP": {"Backup_Running": [{"operation.command": "copy running-config tftp://&[TransferProtocolServerAddress]/&[TransferFileName]", "operation.timeout": 10000, "operation.prompt": "#", "operation.prompt.command": "No Command"}], "Backup_Startup": [{"operation.command": "copy startup-config tftp://&[TransferProtocolServerAddress]/&[TransferFileName]", "operation.timeout": 10000, "operation.prompt": "#", "operation.prompt.command": "No Command"}], "Restore": [{"operation.command": "copy tftp://&[TransferProtocolServerAddress]/&[TransferFileName] running-config", "operation.timeout": 10000, "operation.prompt": "#", "operation.prompt.command": "No Command"}], "Sync": [{"operation.command": "copy running-config startup-config", "operation.delay.time": 1000, "operation.timeout": 10000, "operation.prompt": "#", "operation.prompt.command": "LF followed by LF"}]}, "SCP/SFTP": {"Backup_Running": [{"operation.command": "copy running-config scp://&[TransferProtocolServerUser]@&[TransferProtocolServerAddress]://&[TransferFileName]\n\n\n&[TransferProtocolServerPassword]", "operation.delay.time": 1000, "operation.timeout": 10000, "operation.prompt": "#", "operation.prompt.command": "No Command"}], "Backup_Startup": [{"operation.command": "copy startup-config scp://&[TransferProtocolServerUser]@&[TransferProtocolServerAddress]://&[TransferFileName]\n\n\n&[TransferProtocolServerPassword]", "operation.delay.time": 1000, "operation.timeout": 10000, "operation.prompt": "#", "operation.prompt.command": "No Command"}], "Sync": [{"operation.command": "copy running-config startup-config", "operation.delay.time": 1000, "operation.timeout": 10000, "operation.prompt": "#", "operation.prompt.command": "LF followed by LF"}], "Restore": [{"operation.command": "copy scp://&[TransferProtocolServerUser]@&[TransferProtocolServerAddress]://&[TransferFileName] running-config\n\n&[TransferProtocolServerPassword]", "operation.delay.time": 1000, "operation.timeout": 10000, "operation.prompt": "#", "operation.prompt.command": "No Command"}]}}, {"_type": "0", "id": 100000000000012, "config.template.name": "AcmePacket", "config.template.description": "This is an Config template for all Acme Packet devices", "config.template.os.type": "Acme Packet", "config.template.delay.time": 1000, "config.template.vendor": "Acme Packet", "config.template.catalog.ids": [], "NONE": {"Backup_Running": [{"operation.command": "no paging", "operation.timeout": 2000, "operation.prompt": "#", "operation.prompt.command": "No Command", "operation.response.required": "no"}, {"operation.command": "show running-config", "operation.timeout": 10000, "operation.prompt": "#", "operation.prompt.command": "No Command", "operation.response.required": "yes"}]}}, {"_type": "0", "id": 100000000000013, "config.template.name": "Adtran", "config.template.description": "This is an Config template for all Adtran devices", "config.template.os.type": "AOS", "config.template.delay.time": 1000, "config.template.vendor": "Adtran", "config.template.catalog.ids": [], "NONE": {"Backup_Running": [{"operation.command": "terminal length 0", "operation.timeout": 2000, "operation.prompt": "#", "operation.prompt.command": "No Command", "operation.response.required": "no"}, {"operation.command": "show running-config", "operation.timeout": 10000, "operation.prompt": "#", "operation.prompt.command": "No Command", "operation.response.required": "yes"}], "Backup_Startup": [{"operation.command": "terminal length 0", "operation.timeout": 2000, "operation.prompt": "#", "operation.prompt.command": "No Command", "operation.response.required": "no"}, {"operation.command": "show startup-config", "operation.timeout": 10000, "operation.prompt": "#", "operation.prompt.command": "No Command", "operation.response.required": "yes"}], "Sync": [{"operation.command": "copy running-config startup-config", "operation.delay.time": 1000, "operation.timeout": 10000, "operation.prompt": "$", "operation.prompt.command": "LF followed by LF"}]}, "TFTP": {"Backup_Running": [{"operation.command": "copy running-config tftp\n&[TransferProtocolServerAddress]\n&[TransferFileName]", "operation.timeout": 10000, "operation.prompt": "#", "operation.prompt.command": "No Command"}], "Backup_Startup": [{"operation.command": "copy startup-config tftp\n&[TransferProtocolServerAddress]\n&[TransferFileName]", "operation.timeout": 10000, "operation.prompt": "#", "operation.prompt.command": "No Command"}], "Restore": [{"operation.command": "copy tftp running-config\n&[TransferProtocolServerAddress]\n]&[TransferFileName]", "operation.timeout": 10000, "operation.prompt": "#", "operation.prompt.command": "No Command"}], "Sync": [{"operation.command": "copy running-config startup-config", "operation.delay.time": 1000, "operation.timeout": 10000, "operation.prompt": "$", "operation.prompt.command": "LF followed by LF"}]}, "SCP/SFTP": {"Backup_Running": [{"operation.command": "copy running-config scp://&[TransferProtocolServerUser]@&[TransferProtocolServerAddress]://&[TransferFileName]\n\n\n&[TransferProtocolServerPassword]", "operation.timeout": 10000, "operation.prompt": "#", "operation.prompt.command": "No Command"}], "Backup_Startup": [{"operation.command": "copy startup-config scp://&[TransferProtocolServerUser]@&[TransferProtocolServerAddress]://&[TransferFileName]\n\n\n&[TransferProtocolServerPassword]", "operation.timeout": 10000, "operation.prompt": "#", "operation.prompt.command": "No Command"}], "Restore": [{"operation.command": "copy scp://&[TransferProtocolServerUser]@&[TransferProtocolServerAddress]://&[TransferFileName] running-config\n\n\n&[TransferProtocolServerPassword]", "operation.timeout": 10000, "operation.prompt": "#", "operation.prompt.command": "No Command"}], "Sync": [{"operation.command": "copy running-config startup-config", "operation.delay.time": 1000, "operation.timeout": 10000, "operation.prompt": "$", "operation.prompt.command": "LF followed by LF"}]}}, {"_type": "0", "id": 100000000000014, "config.template.name": "ADVA", "config.template.description": "This is an Config template for all ADVA devices", "config.template.os.type": "ADVA", "config.template.delay.time": 1000, "config.template.vendor": "ADVA GmbH", "config.template.catalog.ids": [], "NONE": {"Backup_Running": [{"operation.command": "config-user root cli-paging disabled", "operation.timeout": 2000, "operation.prompt": "#", "operation.prompt.command": "No Command", "operation.response.required": "no"}, {"operation.command": "show running-config", "operation.timeout": 10000, "operation.prompt": "#", "operation.prompt.command": "No Command", "operation.response.required": "yes"}]}}, {"_type": "0", "id": 100000000000015, "config.template.name": "Alaxala", "config.template.description": "This is an Config template for all Alaxala devices", "config.template.os.type": "", "config.template.delay.time": 1000, "config.template.vendor": "ALAXALA Networks Corporation", "config.template.catalog.ids": [], "NONE": {"Backup_Running": [{"operation.command": "set terminal pager disable", "operation.timeout": 2000, "operation.prompt": "#", "operation.prompt.command": "No Command", "operation.response.required": "no"}, {"operation.command": "show running-config", "operation.timeout": 10000, "operation.prompt": "#", "operation.prompt.command": "No Command", "operation.response.required": "yes"}], "Backup_Startup": [{"operation.command": "set terminal pager disable", "operation.timeout": 2000, "operation.prompt": "#", "operation.prompt.command": "No Command", "operation.response.required": "no"}, {"operation.command": "show startup-config", "operation.timeout": 10000, "operation.prompt": "#", "operation.prompt.command": "No Command", "operation.response.required": "yes"}], "Sync": [{"operation.command": "copy running-config startup-config", "operation.delay.time": 1000, "operation.timeout": 10000, "operation.prompt": "#", "operation.prompt.command": "LF followed by LF"}]}, "TFTP": {"Backup_Running": [{"operation.command": "copy running-config tftp\n&[TransferProtocolServerAddress]\n&[TransferFileName]", "operation.timeout": 10000, "operation.prompt": "#", "operation.prompt.command": "No Command"}], "Backup_Startup": [{"operation.command": "copy startup-config tftp\n&[TransferProtocolServerAddress]\n&[TransferFileName]", "operation.timeout": 10000, "operation.prompt": "#", "operation.prompt.command": "No Command"}], "Restore": [{"operation.command": "copy tftp running-config\n&[TransferProtocolServerAddress]\n]&[TransferFileName]", "operation.timeout": 10000, "operation.prompt": "#", "operation.prompt.command": "No Command"}], "Sync": [{"operation.command": "copy running-config startup-config", "operation.delay.time": 1000, "operation.timeout": 10000, "operation.prompt": "#", "operation.prompt.command": "LF followed by LF"}]}, "SCP/SFTP": {"Backup_Running": [{"operation.command": "copy running-config scp://&[TransferProtocolServerUser]@&[TransferProtocolServerAddress]://&[TransferFileName]\n\n\n&[TransferProtocolServerPassword]", "operation.timeout": 10000, "operation.prompt": "#", "operation.prompt.command": "No Command"}], "Backup_Startup": [{"operation.command": "copy startup-config scp://&[TransferProtocolServerUser]@&[TransferProtocolServerAddress]://&[TransferFileName]\n\n\n&[TransferProtocolServerPassword]", "operation.timeout": 10000, "operation.prompt": "#", "operation.prompt.command": "No Command"}], "Restore": [{"operation.command": "copy scp://&[TransferProtocolServerUser]@&[TransferProtocolServerAddress]://&[TransferFileName] running-config\n\n\n&[TransferProtocolServerPassword]", "operation.timeout": 10000, "operation.prompt": "#", "operation.prompt.command": "No Command"}], "Sync": [{"operation.command": "copy running-config startup-config", "operation.delay.time": 1000, "operation.timeout": 10000, "operation.prompt": "#", "operation.prompt.command": "LF followed by LF"}]}}, {"_type": "0", "id": 100000000000016, "config.template.name": "Alcatel", "config.template.description": "This is an Config template for all Alcatel devices", "config.template.os.type": "IOS", "config.template.delay.time": 1000, "config.template.vendor": "Alcatel", "config.template.catalog.ids": [], "NONE": {"Backup_Running": [{"operation.command": "show working/vcboot.cfg", "operation.timeout": 10000, "operation.prompt": "#", "operation.prompt.command": "No Command", "operation.response.required": "yes"}], "Backup_Startup": [{"operation.command": "show certified/vcboot.cfg", "operation.timeout": 10000, "operation.prompt": "#", "operation.prompt.command": "No Command", "operation.response.required": "yes"}], "Sync": [{"operation.command": "copy working/vcboot.cfg certified/vcboot.cfg", "operation.delay.time": 1000, "operation.timeout": 10000, "operation.prompt": "#", "operation.prompt.command": "No Command"}]}, "TFTP": {"Backup_Running": [{"operation.command": "tftp -p -l working/vcboot.cfg -r &[TransferFileName] &[TransferProtocolServerAddress]", "operation.timeout": 10000, "operation.prompt": "#", "operation.prompt.command": "No Command"}], "Backup_Startup": [{"operation.command": "tftp -p -l certified/vcboot.cfg -r &[TransferFileName] &[TransferProtocolServerAddress]", "operation.timeout": 10000, "operation.prompt": "#", "operation.prompt.command": "No Command"}], "Restore": [{"operation.command": "tftp -g -l &[TransferFileName] -r working/vcboot.cfg &[TransferProtocolServerAddress]", "operation.timeout": 10000, "operation.prompt": "#", "operation.prompt.command": "No Command"}], "Sync": [{"operation.command": "copy working/vcboot.cfg certified/vcboot.cfg", "operation.delay.time": 1000, "operation.timeout": 10000, "operation.prompt": "#", "operation.prompt.command": "No Command"}]}}, {"_type": "0", "id": 100000000000017, "config.template.name": "Arista", "config.template.description": "This is an Config template for all Arista devices", "config.template.os.type": "EOS", "config.template.delay.time": 1000, "config.template.vendor": "Arista Networks", "config.template.catalog.ids": [], "NONE": {"Backup_Running": [{"operation.command": "terminal length 0", "operation.timeout": 2000, "operation.prompt": "#", "operation.prompt.command": "No Command", "operation.response.required": "no"}, {"operation.command": "show running-config", "operation.timeout": 10000, "operation.prompt": "#", "operation.prompt.command": "No Command", "operation.response.required": "yes"}], "Backup_Startup": [{"operation.command": "terminal length 0", "operation.timeout": 2000, "operation.prompt": "#", "operation.prompt.command": "No Command", "operation.response.required": "no"}, {"operation.command": "show startup-config", "operation.timeout": 10000, "operation.prompt": "#", "operation.prompt.command": "No Command", "operation.response.required": "yes"}], "Sync": [{"operation.command": "copy running-config startup-config", "operation.delay.time": 1000, "operation.timeout": 10000, "operation.prompt": "#", "operation.prompt.command": "No Command"}]}, "SCP/SFTP": {"Backup_Running": [{"operation.command": "copy running-config scp://&[TransferProtocolServerUser]:&[TransferProtocolServerPassword]@&[TransferProtocolServerAddress]://&[TransferFileName]&\n\n\n", "operation.timeout": 10000, "operation.prompt": "#", "operation.prompt.command": "No Command"}], "Backup_Startup": [{"operation.command": "copy startup-config scp://&[TransferProtocolServerUser]:&[TransferProtocolServerPassword]@&[TransferProtocolServerAddress]://&[TransferFileName]&\n\n\n", "operation.timeout": 10000, "operation.prompt": "#", "operation.prompt.command": "No Command"}], "Sync": [{"operation.command": "copy running-config startup-config", "operation.delay.time": 1000, "operation.timeout": 10000, "operation.prompt": "#", "operation.prompt.command": "No Command"}]}}, {"_type": "0", "id": 100000000000018, "config.template.name": "<PERSON><PERSON><PERSON>", "config.template.description": "This is an Config template for all Arris Devices", "config.template.os.type": "Linux", "config.template.delay.time": 1000, "config.template.vendor": "Arris Interactive", "config.template.catalog.ids": [], "NONE": {"Backup_Running": [{"operation.command": "terminal length 0", "operation.timeout": 2000, "operation.prompt": "#", "operation.prompt.command": "No Command", "operation.response.required": "no"}, {"operation.command": "show running-config", "operation.timeout": 10000, "operation.prompt": "#", "operation.prompt.command": "No Command", "operation.response.required": "yes"}], "Backup_Startup": [{"operation.command": "terminal length 0", "operation.timeout": 2000, "operation.prompt": "#", "operation.prompt.command": "No Command", "operation.response.required": "no"}, {"operation.command": "show startup-config", "operation.timeout": 10000, "operation.prompt": "#", "operation.prompt.command": "No Command", "operation.response.required": "yes"}], "Sync": [{"operation.command": "copy running-config startup-config", "operation.delay.time": 1000, "operation.timeout": 10000, "operation.prompt": "#", "operation.prompt.command": "LF followed by LF"}]}, "TFTP": {"Backup_Running": [{"operation.command": "copy running-config tftp://&[TransferProtocolServerAddress]/&[TransferFileName]\n\n\n", "operation.timeout": 10000, "operation.prompt": "#", "operation.prompt.command": "No Command"}], "Backup_Startup": [{"operation.command": "copy startup-config tftp://&[TransferProtocolServerAddress]/&[TransferFileName]\n\n\n", "operation.timeout": 10000, "operation.prompt": "#", "operation.prompt.command": "No Command"}], "Restore": [{"operation.command": "copy tftp://&[TransferProtocolServerAddress]/&[TransferFileName] running-config\n\n", "operation.timeout": 10000, "operation.prompt": "#", "operation.prompt.command": "No Command"}], "Sync": [{"operation.command": "copy running-config startup-config", "operation.delay.time": 1000, "operation.timeout": 10000, "operation.prompt": "#", "operation.prompt.command": "LF followed by LF"}]}}, {"_type": "0", "id": 100000000000019, "config.template.name": "<PERSON><PERSON><PERSON>", "config.template.description": "This is an Config template for all Avya Devices", "config.template.os.type": "", "config.template.delay.time": 1000, "config.template.vendor": "Avaya Communications", "config.template.catalog.ids": [], "NONE": {"Backup_Running": [{"operation.command": "terminal length 0", "operation.timeout": 2000, "operation.prompt": "#", "operation.prompt.command": "No Command", "operation.response.required": "no"}, {"operation.command": "show running-config", "operation.timeout": 10000, "operation.prompt": "#", "operation.prompt.command": "No Command", "operation.response.required": "yes"}], "Backup_Startup": [{"operation.command": "terminal length 0", "operation.timeout": 2000, "operation.prompt": "#", "operation.prompt.command": "No Command", "operation.response.required": "no"}, {"operation.command": "show startup-config", "operation.timeout": 10000, "operation.prompt": "#", "operation.prompt.command": "No Command", "operation.response.required": "yes"}], "Sync": [{"operation.command": "copy running-config startup-config", "operation.delay.time": 1000, "operation.timeout": 10000, "operation.prompt": "#", "operation.prompt.command": "LF followed by LF"}]}, "TFTP": {"Backup_Running": [{"operation.command": "copy running-config tftp &[TransferFileName] &[TransferProtocolServerAddress]", "operation.timeout": 10000, "operation.prompt": "#", "operation.prompt.command": "No Command"}], "Backup_Startup": [{"operation.command": "copy startup-config tftp &[TransferFileName] &[TransferProtocolServerAddress]", "operation.timeout": 10000, "operation.prompt": "#", "operation.prompt.command": "No Command"}], "Restore": [{"operation.command": "copy tftp &[TransferFileName] &[TransferProtocolServerAddress] running-config", "operation.timeout": 10000, "operation.prompt": "#", "operation.prompt.command": "No Command"}], "Sync": [{"operation.command": "copy running-config startup-config", "operation.delay.time": 1000, "operation.timeout": 10000, "operation.prompt": "#", "operation.prompt.command": "LF followed by LF"}]}}, {"_type": "0", "id": 100000000000020, "config.template.name": "Blue coat", "config.template.description": "This is an Config template for all Blue Coat Devices", "config.template.os.type": "Bluecoat", "config.template.delay.time": 1000, "config.template.vendor": "Blue Coat Systems", "config.template.catalog.ids": [], "NONE": {"Backup_Running": [{"operation.command": "terminal length 0", "operation.timeout": 2000, "operation.prompt": "#", "operation.prompt.command": "No Command", "operation.response.required": "no"}, {"operation.command": "show running-config", "operation.timeout": 10000, "operation.prompt": "#", "operation.prompt.command": "No Command", "operation.response.required": "yes"}], "Backup_Startup": [{"operation.command": "terminal length 0", "operation.timeout": 2000, "operation.prompt": "#", "operation.prompt.command": "No Command", "operation.response.required": "no"}, {"operation.command": "show startup-config", "operation.timeout": 10000, "operation.prompt": "#", "operation.prompt.command": "No Command", "operation.response.required": "yes"}], "Sync": [{"operation.command": "copy running-config startup-config", "operation.delay.time": 1000, "operation.timeout": 10000, "operation.prompt": "#", "operation.prompt.command": "LF followed by LF"}]}, "TFTP": {"Backup_Running": [{"operation.command": "copy running-config tftp &[TransferFileName] &[TransferProtocolServerAddress]", "operation.timeout": 10000, "operation.prompt": "#", "operation.prompt.command": "No Command"}], "Backup_Startup": [{"operation.command": "copy startup-config tftp &[TransferFileName] &[TransferProtocolServerAddress]", "operation.timeout": 10000, "operation.prompt": "#", "operation.prompt.command": "No Command"}], "Restore": [{"operation.command": "copy tftp &[TransferFileName] &[TransferProtocolServerAddress] running-config", "operation.timeout": 10000, "operation.prompt": "#", "operation.prompt.command": "No Command"}], "Sync": [{"operation.command": "copy running-config startup-config", "operation.delay.time": 1000, "operation.timeout": 10000, "operation.prompt": "#", "operation.prompt.command": "LF followed by LF"}]}}, {"_type": "0", "id": 100000000000021, "config.template.name": "Brocade", "config.template.description": "This is an Config template for all Brocade Devices", "config.template.os.type": "Fabric OS", "config.template.delay.time": 1000, "config.template.vendor": "Brocade Communications Systems", "config.template.catalog.ids": [], "NONE": {"Backup_Running": [{"operation.command": "skip-page-display", "operation.timeout": 2000, "operation.prompt": "#", "operation.prompt.command": "No Command", "operation.response.required": "no"}, {"operation.command": "show running-config", "operation.timeout": 10000, "operation.prompt": "#", "operation.prompt.command": "No Command", "operation.response.required": "yes"}], "Backup_Startup": [{"operation.command": "skip-page-display", "operation.timeout": 2000, "operation.prompt": "#", "operation.prompt.command": "No Command", "operation.response.required": "no"}, {"operation.command": "show startup-config", "operation.timeout": 10000, "operation.prompt": "#", "operation.prompt.command": "No Command", "operation.response.required": "yes"}], "Sync": [{"operation.command": "copy running-config startup-config", "operation.delay.time": 1000, "operation.timeout": 10000, "operation.prompt": "#", "operation.prompt.command": "LF followed by LF"}]}, "TFTP": {"Backup_Running": [{"operation.command": "copy running-config tftp &[TransferProtocolServerAddress] &[TransferFileName]\n\n\n", "operation.timeout": 10000, "operation.prompt": "#", "operation.prompt.command": "No Command"}], "Backup_Startup": [{"operation.command": "copy startup-config tftp &[TransferProtocolServerAddress] &[TransferFileName]\n\n\n", "operation.timeout": 10000, "operation.prompt": "#", "operation.prompt.command": "No Command"}], "Restore": [{"operation.command": "copy tftp running-config &[TransferProtocolServerAddress] &[TransferFileName]", "operation.timeout": 10000, "operation.prompt": "#", "operation.prompt.command": "No Command"}], "Sync": [{"operation.command": "copy running-config startup-config", "operation.delay.time": 1000, "operation.timeout": 10000, "operation.prompt": "#", "operation.prompt.command": "LF followed by LF"}]}}, {"_type": "0", "id": 100000000000022, "config.template.name": "Cabletron", "config.template.description": "This is an Config template for all Cabletron Devices", "config.template.os.type": "Cabletron", "config.template.delay.time": 1000, "config.template.vendor": "Cabletron Systems", "config.template.catalog.ids": [], "NONE": {"Backup_Running": [{"operation.command": "config terminal", "operation.timeout": 2000, "operation.prompt": "#", "operation.prompt.command": "No Command", "operation.response.required": "no"}, {"operation.command": "show active", "operation.timeout": 10000, "operation.prompt": "#", "operation.prompt.command": "No Command", "operation.response.required": "yes"}], "Backup_Startup": [{"operation.command": "config terminal", "operation.timeout": 2000, "operation.prompt": "#", "operation.prompt.command": "No Command", "operation.response.required": "no"}, {"operation.command": "show startup", "operation.timeout": 10000, "operation.prompt": "#", "operation.prompt.command": "No Command", "operation.response.required": "yes"}], "Sync": [{"operation.command": "copy active startup", "operation.delay.time": 1000, "operation.timeout": 10000, "operation.prompt": "#", "operation.prompt.command": "LF followed by LF"}]}, "TFTP": {"Backup_Running": [{"operation.command": "copy active tftp://&[TransferProtocolServerAddress]/&[TransferFileName]\n\n\n", "operation.timeout": 10000, "operation.prompt": "#", "operation.prompt.command": "No Command"}], "Backup_Startup": [{"operation.command": "copy active tftp://&[TransferProtocolServerAddress]/&[TransferFileName]\n\n\n", "operation.timeout": 10000, "operation.prompt": "#", "operation.prompt.command": "No Command"}], "Restore": [{"operation.command": "copy tftp://&[TransferProtocolServerAddress]/&[TransferFileName] active\n\n", "operation.timeout": 10000, "operation.prompt": "#", "operation.prompt.command": "No Command"}], "Sync": [{"operation.command": "copy active startup", "operation.delay.time": 1000, "operation.timeout": 10000, "operation.prompt": "#", "operation.prompt.command": "LF followed by LF"}]}}, {"_type": "0", "id": 100000000000023, "config.template.name": "Checkpoint", "config.template.description": "This is an Config template for all Checkpoint Devices", "config.template.os.type": "Edge X", "config.template.delay.time": 1000, "config.template.vendor": "Check Point", "config.template.catalog.ids": [], "NONE": {"Backup_Running": [{"operation.command": "terminal length 0", "operation.timeout": 2000, "operation.prompt": "$", "operation.prompt.command": "No Command", "operation.response.required": "no"}, {"operation.command": "show active", "operation.timeout": 10000, "operation.prompt": "$", "operation.prompt.command": "No Command", "operation.response.required": "yes"}], "Backup_Startup": [{"operation.command": "config terminal", "operation.timeout": 2000, "operation.prompt": "$", "operation.prompt.command": "No Command", "operation.response.required": "no"}, {"operation.command": "show startup", "operation.timeout": 10000, "operation.prompt": "$", "operation.prompt.command": "No Command", "operation.response.required": "yes"}], "Sync": [{"operation.command": "copy active startup", "operation.delay.time": 1000, "operation.timeout": 10000, "operation.prompt": "$", "operation.prompt.command": "LF followed by LF"}]}, "TFTP": {"Backup_Running": [{"operation.command": "copy active tftp://&[TransferProtocolServerAddress]/&[TransferFileName]\n\n\n", "operation.timeout": 10000, "operation.prompt": "$", "operation.prompt.command": "No Command"}], "Backup_Startup": [{"operation.command": "copy active tftp://&[TransferProtocolServerAddress]/&[TransferFileName]\n\n\n", "operation.timeout": 10000, "operation.prompt": "$", "operation.prompt.command": "No Command"}], "Restore": [{"operation.command": "copy tftp://&[TransferProtocolServerAddress]/&[TransferFileName] active\n\n", "operation.timeout": 10000, "operation.prompt": "$", "operation.prompt.command": "No Command"}], "Sync": [{"operation.command": "copy active startup", "operation.delay.time": 1000, "operation.timeout": 10000, "operation.prompt": "$", "operation.prompt.command": "LF followed by LF"}]}}, {"_type": "0", "id": 100000000000024, "config.template.name": "Citrix", "config.template.description": "This is an Config template for all Citrix Devices", "config.template.os.type": "Citrix", "config.template.delay.time": 1000, "config.template.vendor": "Citrix", "config.template.catalog.ids": [], "NONE": {"Backup_Running": [{"operation.command": "set cli mode -page OFF", "operation.timeout": 2000, "operation.prompt": "#", "operation.prompt.command": "No Command", "operation.response.required": "no"}, {"operation.command": "show running-config", "operation.timeout": 10000, "operation.prompt": "#", "operation.prompt.command": "No Command", "operation.response.required": "yes"}]}, "SCP/SFTP": {"Backup_Running": [{"operation.command": "scp /nsconfig/ns.conf &[TransferProtocolServerUser]:&[TransferProtocolServerPassword]@&[TransferProtocolServerAddress]:&[TransferFileName]\n\n\n", "operation.timeout": 10000, "operation.prompt": "#", "operation.prompt.command": "No Command"}], "Restore": [{"operation.command": "scp &[TransferProtocolServerUser]:&[TransferProtocolServerPassword]@&[TransferProtocolServerAddress]:&[TransferFileName] /nsconfig/ns.conf \n\n", "operation.timeout": 10000, "operation.prompt": "#", "operation.prompt.command": "No Command"}]}}, {"_type": "0", "id": 100000000000025, "config.template.name": "Dell", "config.template.description": "This is an Config template for all Dell Devices", "config.template.os.type": "NOS", "config.template.delay.time": 1000, "config.template.vendor": "Dell", "config.template.catalog.ids": [], "NONE": {"Backup_Running": [{"operation.command": "terminal length 0", "operation.timeout": 2000, "operation.prompt": "#", "operation.prompt.command": "No Command", "operation.response.required": "no"}, {"operation.command": "show running-config", "operation.timeout": 10000, "operation.prompt": "#", "operation.prompt.command": "No Command", "operation.response.required": "yes"}], "Backup_Startup": [{"operation.command": "terminal length 0", "operation.timeout": 2000, "operation.prompt": "#", "operation.prompt.command": "No Command", "operation.response.required": "no"}, {"operation.command": "show startup-config", "operation.timeout": 10000, "operation.prompt": "#", "operation.prompt.command": "No Command", "operation.response.required": "yes"}], "Sync": [{"operation.command": "copy running-config startup-config", "operation.delay.time": 1000, "operation.timeout": 10000, "operation.prompt": "#", "operation.prompt.command": "LF followed by LF"}]}, "TFTP": {"Backup_Running": [{"operation.command": "copy running-config tftp", "operation.timeout": 2000, "operation.prompt": "]?", "operation.prompt.command": "No Command"}, {"operation.command": "&[TransferProtocolServerAddress]", "operation.timeout": 2000, "operation.prompt": "]?", "operation.prompt.command": "No Command"}, {"operation.command": "&[TransferFileName]", "operation.timeout": 10000, "operation.prompt": "#", "operation.prompt.command": "No Command"}], "Backup_Startup": [{"operation.command": "copy startup-config tftp", "operation.timeout": 2000, "operation.prompt": "]?", "operation.prompt.command": "No Command"}, {"operation.command": "&[TransferProtocolServerAddress]", "operation.timeout": 2000, "operation.prompt": "]?", "operation.prompt.command": "No Command"}, {"operation.command": "&[TransferFileName]", "operation.timeout": 10000, "operation.prompt": "#", "operation.prompt.command": "No Command"}], "Sync": [{"operation.command": "copy running-config startup-config", "operation.delay.time": 1000, "operation.timeout": 10000, "operation.prompt": "#", "operation.prompt.command": "LF followed by LF"}], "Restore": [{"operation.command": "copy tftp running-config", "operation.timeout": 2000, "operation.prompt": "]?", "operation.prompt.command": "No Command"}, {"operation.command": "&[TransferProtocolServerAddress]", "operation.timeout": 2000, "operation.prompt": "]?", "operation.prompt.command": "No Command"}, {"operation.command": "&[TransferFileName]", "operation.timeout": 10000, "operation.prompt": "#", "operation.prompt.command": "No Command"}]}}, {"_type": "0", "id": 100000000000026, "config.template.name": "Foundary", "config.template.description": "This is an Config template for all Foundary Devices", "config.template.os.type": "Foundary", "config.template.delay.time": 1000, "config.template.vendor": "Foundary", "config.template.catalog.ids": [], "NONE": {"Backup_Running": [{"operation.command": "terminal length 0", "operation.timeout": 2000, "operation.prompt": "#", "operation.prompt.command": "No Command", "operation.response.required": "no"}, {"operation.command": "show running-config", "operation.timeout": 10000, "operation.prompt": "#", "operation.prompt.command": "No Command", "operation.response.required": "yes"}], "Backup_Startup": [{"operation.command": "terminal length 0", "operation.timeout": 2000, "operation.prompt": "#", "operation.prompt.command": "No Command", "operation.response.required": "no"}, {"operation.command": "show startup-config", "operation.timeout": 10000, "operation.prompt": "#", "operation.prompt.command": "No Command", "operation.response.required": "yes"}], "Sync": [{"operation.command": "copy running-config startup-config", "operation.delay.time": 1000, "operation.timeout": 10000, "operation.prompt": "#", "operation.prompt.command": "LF followed by LF"}]}, "TFTP": {"Backup_Running": [{"operation.command": "copy running-config tftp", "operation.timeout": 2000, "operation.prompt": "]?", "operation.prompt.command": "No Command"}, {"operation.command": "&[TransferProtocolServerAddress]", "operation.timeout": 2000, "operation.prompt": "]?", "operation.prompt.command": "No Command"}, {"operation.command": "&[TransferFileName]", "operation.timeout": 10000, "operation.prompt": "#", "operation.prompt.command": "No Command"}], "Backup_Startup": [{"operation.command": "copy startup-config tftp", "operation.timeout": 2000, "operation.prompt": "]?", "operation.prompt.command": "No Command"}, {"operation.command": "&[TransferProtocolServerAddress]", "operation.timeout": 2000, "operation.prompt": "]?", "operation.prompt.command": "No Command"}, {"operation.command": "&[TransferFileName]", "operation.timeout": 10000, "operation.prompt": "#", "operation.prompt.command": "No Command"}], "Sync": [{"operation.command": "copy running-config startup-config", "operation.delay.time": 1000, "operation.timeout": 10000, "operation.prompt": "#", "operation.prompt.command": "LF followed by LF"}], "Restore": [{"operation.command": "copy tftp running-config", "operation.timeout": 2000, "operation.prompt": "]?", "operation.prompt.command": "No Command"}, {"operation.command": "&[TransferProtocolServerAddress]", "operation.timeout": 2000, "operation.prompt": "]?", "operation.prompt.command": "No Command"}, {"operation.command": "&[TransferFileName]", "operation.timeout": 10000, "operation.prompt": "#", "operation.prompt.command": "No Command"}]}}, {"_type": "0", "id": 100000000000027, "config.template.name": "H3C", "config.template.description": "This is an Config template for all H3C Devices", "config.template.os.type": "Comware", "config.template.delay.time": 1000, "config.template.vendor": "H3C", "config.template.catalog.ids": [], "NONE": {"Backup_Running": [{"operation.command": "screen-length 0", "operation.timeout": 2000, "operation.prompt": "#", "operation.prompt.command": "No Command", "operation.response.required": "no"}, {"operation.command": "display current-configuration", "operation.timeout": 10000, "operation.prompt": "#", "operation.prompt.command": "No Command", "operation.response.required": "yes"}], "Backup_Startup": [{"operation.command": "screen-length 0", "operation.timeout": 2000, "operation.prompt": "#", "operation.prompt.command": "No Command", "operation.response.required": "no"}, {"operation.command": "display saved-configuration", "operation.timeout": 10000, "operation.prompt": "#", "operation.prompt.command": "No Command", "operation.response.required": "yes"}], "Sync": [{"operation.command": "copy current-configuration saved-configuration", "operation.delay.time": 1000, "operation.timeout": 10000, "operation.prompt": "#", "operation.prompt.command": "LF followed by LF"}]}, "TFTP": {"Backup_Running": [{"operation.command": "tftp &[TransferProtocolServerAddress] put current-configuration &[TransferFileName]", "operation.timeout": 10000, "operation.prompt": "#", "operation.prompt.command": "No Command"}], "Backup_Startup": [{"operation.command": "tftp &[TransferProtocolServerAddress] put saved-configuration &[TransferFileName]", "operation.timeout": 10000, "operation.prompt": "#", "operation.prompt.command": "No Command"}], "Sync": [{"operation.command": "copy current-configuration saved-configuration", "operation.delay.time": 1000, "operation.timeout": 10000, "operation.prompt": "#", "operation.prompt.command": "LF followed by LF"}], "Restore": [{"operation.command": "tftp &[TransferProtocolServerAddress] get current-configuration &[TransferFileName]", "operation.timeout": 10000, "operation.prompt": "#", "operation.prompt.command": "No Command"}]}}, {"_type": "0", "id": 100000000000028, "config.template.name": "IBM", "config.template.description": "This is an Config template for all IBM Devices", "config.template.os.type": "IBM", "config.template.delay.time": 1000, "config.template.vendor": "IBM", "config.template.catalog.ids": [], "NONE": {"Backup_Running": [{"operation.command": "terminal length 0", "operation.timeout": 2000, "operation.prompt": "#", "operation.prompt.command": "No Command", "operation.response.required": "no"}, {"operation.command": "show running-config", "operation.timeout": 10000, "operation.prompt": "#", "operation.prompt.command": "No Command", "operation.response.required": "yes"}], "Backup_Startup": [{"operation.command": "terminal length 0", "operation.timeout": 2000, "operation.prompt": "#", "operation.prompt.command": "No Command", "operation.response.required": "no"}, {"operation.command": "show startup-config", "operation.timeout": 10000, "operation.prompt": "#", "operation.prompt.command": "No Command", "operation.response.required": "yes"}], "Sync": [{"operation.command": "copy running-config startup-config", "operation.delay.time": 1000, "operation.timeout": 10000, "operation.prompt": "#", "operation.prompt.command": "LF followed by LF"}]}}, {"_type": "0", "id": 100000000000029, "config.template.name": "Motorola", "config.template.description": "This is an Config template for Motorola Devices", "config.template.vendor": "Motorola", "config.template.os.type": "Motorola", "config.template.delay.time": 1000, "NONE": {"Backup_Running": [{"operation.command": "terminal length 0", "operation.delay.time": 1000, "operation.timeout": 2000, "operation.prompt": "#", "operation.prompt.command": "No Command"}, {"operation.command": "show running-config", "operation.delay.time": 1000, "operation.timeout": 10000, "operation.prompt": "#", "operation.prompt.command": "LF", "operation.response.required": "yes"}], "Backup_Startup": [{"operation.command": "terminal length 0", "operation.delay.time": 1000, "operation.timeout": 2000, "operation.prompt": "#", "operation.prompt.command": "No Command"}, {"operation.command": "show startup-config", "operation.delay.time": 1000, "operation.timeout": 10000, "operation.prompt": "#", "operation.prompt.command": "LF", "operation.response.required": "yes"}], "Sync": [{"operation.command": "copy running-config startup-config", "operation.delay.time": 1000, "operation.timeout": 10000, "operation.prompt": "#", "operation.prompt.command": "LF followed by LF"}]}, "TFTP": {"Backup_Running": [{"operation.command": "copy running-config tftp://&[TransferProtocolServerAddress]/&[TransferFileName]", "operation.delay.time": 1000, "operation.timeout": 10000, "operation.prompt": "#", "operation.prompt.command": "No Command"}], "Backup_Startup": [{"operation.command": "copy startup-config tftp://&[TransferProtocolServerAddress]/&[TransferFileName]", "operation.delay.time": 1000, "operation.timeout": 10000, "operation.prompt": "#", "operation.prompt.command": "No Command"}], "Sync": [{"operation.command": "copy running-config startup-config", "operation.delay.time": 1000, "operation.timeout": 10000, "operation.prompt": "#", "operation.prompt.command": "LF followed by LF"}], "Restore": [{"operation.command": "copy tftp://&[TransferProtocolServerAddress]/&[TransferFileName] running-config", "operation.delay.time": 1000, "operation.timeout": 10000, "operation.prompt": "#", "operation.prompt.command": "No Command"}]}, "config.template.catalog.ids": []}, {"id": 100000000000030, "config.template.name": "MRV", "config.template.description": "This is an Config template for MRV Devices", "config.template.vendor": "MRV Communications", "config.template.os.type": "LambdaDriver", "config.template.delay.time": 1000, "NONE": {"Backup_Running": [{"operation.command": "terminal length 0", "operation.delay.time": 1000, "operation.timeout": 2000, "operation.prompt": "#", "operation.prompt.command": "LF", "operation.response.required": "no"}, {"operation.command": "show running-config", "operation.delay.time": 1000, "operation.timeout": 10000, "operation.prompt": "#", "operation.prompt.command": "LF", "operation.response.required": "yes"}], "Backup_Startup": [{"operation.command": "terminal length 0", "operation.delay.time": 1000, "operation.timeout": 2000, "operation.prompt": "#", "operation.prompt.command": "LF", "operation.response.required": "no"}, {"operation.command": "show startup-config", "operation.delay.time": 1000, "operation.timeout": 10000, "operation.prompt": "#", "operation.prompt.command": "LF", "operation.response.required": "yes"}], "Sync": [{"operation.command": "copy running-config startup-config", "operation.delay.time": 1000, "operation.timeout": 10000, "operation.prompt": "#", "operation.prompt.command": "LF followed by LF"}]}, "TFTP": {"Backup_Running": [{"operation.command": "copy running-config tftp &[TransferProtocolServerAddress] &[TransferFileName] &[TransferProtocolServerUser]\n\n&[TransferProtocolServerPassword]", "operation.delay.time": 1000, "operation.timeout": 10000, "operation.prompt": "#", "operation.prompt.command": "No Command"}], "Backup_Startup": [{"operation.command": "copy startup-config tftp &[TransferProtocolServerAddress] &[TransferFileName] &[TransferProtocolServerUser]\n\n&[TransferProtocolServerPassword]", "operation.delay.time": 1000, "operation.timeout": 10000, "operation.prompt": "#", "operation.prompt.command": "No Command"}], "Sync": [{"operation.command": "copy running-config startup-config", "operation.delay.time": 1000, "operation.timeout": 10000, "operation.prompt": "#", "operation.prompt.command": "LF followed by LF"}], "Restore": [{"operation.command": "copy tftp running-config &[TransferProtocolServerAddress] &[TransferFileName] &[TransferProtocolServerUser]\n\n&[TransferProtocolServerPassword]", "operation.delay.time": 1000, "operation.timeout": 10000, "operation.prompt": "#", "operation.prompt.command": "No Command"}]}, "config.template.catalog.ids": [], "_type": "0"}, {"id": 100000000000031, "config.template.name": "Netgear", "config.template.description": "This is an Config template for Netgear Devices", "config.template.vendor": "Netgear", "config.template.os.type": "Netgear", "config.template.delay.time": 1000, "NONE": {"Backup_Running": [{"operation.command": "terminal length 0", "operation.delay.time": 1000, "operation.timeout": 2000, "operation.prompt": "#", "operation.prompt.command": "LF", "operation.response.required": "no"}, {"operation.command": "show system:running-config", "operation.delay.time": 1000, "operation.timeout": 10000, "operation.prompt": "#", "operation.prompt.command": "LF", "operation.response.required": "yes"}], "Backup_Startup": [{"operation.command": "terminal length 0", "operation.delay.time": 1000, "operation.timeout": 2000, "operation.prompt": "#", "operation.prompt.command": "LF", "operation.response.required": "no"}, {"operation.command": "show nvram:startup-config", "operation.delay.time": 1000, "operation.timeout": 10000, "operation.prompt": "#", "operation.prompt.command": "LF", "operation.response.required": "yes"}], "Sync": [{"operation.command": "copy system:running-config nvram:startup-config", "operation.delay.time": 1000, "operation.timeout": 10000, "operation.prompt": "#", "operation.prompt.command": "LF followed by LF"}]}, "TFTP": {"Backup_Running": [{"operation.command": "copy system:running-config tftp://&[TransferProtocolServerAddress]/&[TransferFileName]", "operation.delay.time": 1000, "operation.timeout": 10000, "operation.prompt": "#", "operation.prompt.command": "No Command"}], "Backup_Startup": [{"operation.command": "copy nvram:startup-config tftp://&[TransferProtocolServerAddress]/&[TransferFileName]", "operation.delay.time": 1000, "operation.timeout": 10000, "operation.prompt": "#", "operation.prompt.command": "No Command"}], "Sync": [{"operation.command": "copy system:running-config nvram:startup-config", "operation.delay.time": 1000, "operation.timeout": 10000, "operation.prompt": "#", "operation.prompt.command": "LF followed by LF"}], "Restore": [{"operation.command": "copy tftp://&[TransferProtocolServerAddress]/&[TransferFileName] system:running-config", "operation.delay.time": 1000, "operation.timeout": 10000, "operation.prompt": "#", "operation.prompt.command": "No Command"}]}, "config.template.catalog.ids": [], "_type": "0"}, {"id": 100000000000032, "config.template.name": "Redback", "config.template.description": "This is an Config template for Redback Devices", "config.template.vendor": "RedBack Networks", "config.template.os.type": "Redback", "config.template.delay.time": 1000, "NONE": {"Backup_Running": [{"operation.command": "terminal length 0", "operation.delay.time": 1000, "operation.timeout": 2000, "operation.prompt": "#", "operation.prompt.command": "LF", "operation.response.required": "no"}, {"operation.command": "show configuration", "operation.delay.time": 1000, "operation.timeout": 10000, "operation.prompt": "#", "operation.prompt.command": "LF", "operation.response.required": "yes"}]}, "TFTP": {"Backup_Running": [{"operation.command": "copy configuration tftp://&[TransferProtocolServerAddress]/&[TransferFileName]\n\n\n", "operation.delay.time": 1000, "operation.timeout": 10000, "operation.prompt": "#", "operation.prompt.command": "No Command"}], "Restore": [{"operation.command": "copy tftp://&[TransferProtocolServerAddress]/&[TransferFileName] configuration", "operation.delay.time": 1000, "operation.timeout": 10000, "operation.prompt": "#", "operation.prompt.command": "No Command"}]}, "config.template.catalog.ids": [], "_type": "0"}, {"id": 100000000000033, "config.template.name": "SMC", "config.template.description": "This is an Config template for SMC Devices", "config.template.vendor": "SMC", "config.template.os.type": "SMC", "config.template.delay.time": 1000, "NONE": {"Backup_Running": [{"operation.command": "terminal length 0", "operation.delay.time": 1000, "operation.timeout": 2000, "operation.prompt": "#", "operation.prompt.command": "No Command", "operation.response.required": "no"}, {"operation.command": "show running-config", "operation.delay.time": 1000, "operation.timeout": 10000, "operation.prompt": "#", "operation.prompt.command": "No Command", "operation.response.required": "yes"}], "Backup_Startup": [{"operation.command": "terminal length 0", "operation.delay.time": 1000, "operation.timeout": 2000, "operation.prompt": "#", "operation.prompt.command": "No Command", "operation.response.required": "no"}, {"operation.command": "show startup-config", "operation.delay.time": 1000, "operation.timeout": 10000, "operation.prompt": "#", "operation.prompt.command": "No Command", "operation.response.required": "yes"}], "Sync": [{"operation.command": "copy running-config startup-config", "operation.delay.time": 1000, "operation.timeout": 10000, "operation.prompt": "#", "operation.prompt.command": "LF followed by LF"}]}, "config.template.catalog.ids": [], "_type": "0"}, {"id": 100000000000034, "config.template.name": "Steelhead", "config.template.description": "This is an Config template for Steelhead Devices", "config.template.vendor": "Riverbed Technology", "config.template.os.type": "RiOS", "config.template.delay.time": 1000, "NONE": {"Backup_Running": [{"operation.command": "no cli session paging enable", "operation.delay.time": 1000, "operation.timeout": 2000, "operation.prompt": "#", "operation.prompt.command": "No Command", "operation.response.required": "no"}, {"operation.command": "show running-config", "operation.delay.time": 1000, "operation.timeout": 10000, "operation.prompt": "#", "operation.prompt.command": "No Command", "operation.response.required": "yes"}], "Backup_Startup": [{"operation.command": "no cli session paging enable", "operation.delay.time": 1000, "operation.timeout": 2000, "operation.prompt": "#", "operation.prompt.command": "No Command", "operation.response.required": "no"}, {"operation.command": "show startup-config", "operation.delay.time": 1000, "operation.timeout": 10000, "operation.prompt": "#", "operation.prompt.command": "No Command", "operation.response.required": "yes"}], "Sync": [{"operation.command": "copy running-config configuration", "operation.delay.time": 1000, "operation.timeout": 10000, "operation.prompt": "#", "operation.prompt.command": "LF followed by LF"}]}, "SCP/SFTP": {"Backup_Running": [{"operation.command": "configuration upload running-config scp://&[TransferProtocolServerUser]:&[TransferProtocolServerPassword]@&[TransferProtocolServerAddress]:&[TransferFileName] running-config \n\n", "operation.delay.time": 1000, "operation.timeout": 10000, "operation.prompt": "#", "operation.prompt.command": "No Command"}], "Backup_Startup": [{"operation.command": "configuration upload configuration scp://&[TransferProtocolServerUser]:&[TransferProtocolServerPassword]@&[TransferProtocolServerAddress]:&[TransferFileName] configuration \n\n", "operation.delay.time": 1000, "operation.timeout": 10000, "operation.prompt": "#", "operation.prompt.command": "No Command"}], "Sync": [{"operation.command": "copy running-config configuration", "operation.delay.time": 1000, "operation.timeout": 10000, "operation.prompt": "#", "operation.prompt.command": "LF followed by LF"}], "Restore": [{"operation.command": "configuration fetch running-config &[TransferProtocol]://&[TransferProtocolServerUser]:&[TransferProtocolServerPassword]@&[TransferProtocolServerAddress]:&[TransferFileName] running-config", "operation.delay.time": 1000, "operation.timeout": 10000, "operation.prompt": "#", "operation.prompt.command": "LF followed by LF"}]}, "config.template.catalog.ids": [], "_type": "0"}, {"id": 100000000000035, "config.template.name": "<PERSON><PERSON>", "config.template.description": "This is an Config template for Tejas Devices", "config.template.vendor": "Tejas Networks", "config.template.os.type": "<PERSON><PERSON>", "config.template.delay.time": 1000, "NONE": {"Backup_Running": [{"operation.command": "terminal length 0", "operation.delay.time": 1000, "operation.timeout": 2000, "operation.prompt": "#", "operation.prompt.command": "No Command", "operation.response.required": "no"}, {"operation.command": "show running-config", "operation.delay.time": 1000, "operation.timeout": 10000, "operation.prompt": "#", "operation.prompt.command": "No Command", "operation.response.required": "yes"}], "Backup_Startup": [{"operation.command": "terminal length 0", "operation.delay.time": 1000, "operation.timeout": 2000, "operation.prompt": "#", "operation.prompt.command": "No Command", "operation.response.required": "no"}, {"operation.command": "show startup-config", "operation.delay.time": 1000, "operation.timeout": 10000, "operation.prompt": "#", "operation.prompt.command": "No Command", "operation.response.required": "yes"}], "Sync": [{"operation.command": "copy running-config startup-config", "operation.delay.time": 1000, "operation.timeout": 10000, "operation.prompt": "#", "operation.prompt.command": "LF followed by LF"}]}, "TFTP": {"Backup_Running": [{"operation.command": "copy running-config tftp://&[TransferProtocolServerAddress]/&[TransferFileName]\n\n\n", "operation.delay.time": 1000, "operation.timeout": 10000, "operation.prompt": "#", "operation.prompt.command": "No Command"}], "Backup_Startup": [{"operation.command": "copy startup-config tftp://&[TransferProtocolServerAddress]/&[TransferFileName]\n\n\n", "operation.delay.time": 1000, "operation.timeout": 10000, "operation.prompt": "#", "operation.prompt.command": "No Command"}], "Sync": [{"operation.command": "copy running-config startup-config", "operation.delay.time": 1000, "operation.timeout": 10000, "operation.prompt": "#", "operation.prompt.command": "LF followed by LF"}], "Restore": [{"operation.command": "copy tftp://&[TransferProtocolServerAddress]/&[TransferFileName] running-config \n\n", "operation.delay.time": 1000, "operation.timeout": 10000, "operation.prompt": "#", "operation.prompt.command": "LF followed by LF"}]}, "SCP/SFTP": {"Backup_Running": [{"operation.command": "copy running-config scp://&[TransferProtocolServerUser]@&[TransferProtocolServerAddress]:&[TransferFileName]\n\n\n&[TransferProtocolServerPassword]", "operation.delay.time": 1000, "operation.timeout": 10000, "operation.prompt": "#", "operation.prompt.command": "No Command"}], "Backup_Startup": [{"operation.command": "copy startup-config scp://&[TransferProtocolServerUser]@&[TransferProtocolServerAddress]:&[TransferFileName]\n\n\n&[TransferProtocolServerPassword]", "operation.delay.time": 1000, "operation.timeout": 10000, "operation.prompt": "#", "operation.prompt.command": "No Command"}], "Sync": [{"operation.command": "copy running-config startup-config", "operation.delay.time": 1000, "operation.timeout": 10000, "operation.prompt": "#", "operation.prompt.command": "LF followed by LF"}], "Restore": [{"operation.command": "copy scp://&[TransferProtocolServerUser]@&[TransferProtocolServerAddress]:&[TransferFileName] running-config\n\n&[TransferProtocolServerPassword]", "operation.delay.time": 1000, "operation.timeout": 10000, "operation.prompt": "#", "operation.prompt.command": "LF followed by LF"}]}, "config.template.catalog.ids": [], "_type": "0"}, {"_type": "0", "id": 100000000000036, "config.template.name": "<PERSON><PERSON>r", "config.template.description": "This is an Config template for Load Balancer Devices", "config.template.delay.time": 1000, "config.template.os.type": "BIG-IP", "config.template.vendor": "F5 Networks", "config.template.catalog.ids": [], "NONE": {"Backup_Running": [{"operation.command": "show full-configuration", "operation.timeout": 10000, "operation.prompt": "#", "operation.prompt.command": "No Command", "operation.response.required": "yes"}]}}, {"_type": "0", "id": 100000000000037, "config.template.name": "Nortel", "config.template.description": "This is NCM template for Nortel", "config.template.delay.time": 1000, "config.template.os.type": "Nortel", "config.template.vendor": "Nortel", "config.template.catalog.ids": [], "NONE": {"Backup_Running": [{"operation.command": "terminal length 0", "operation.timeout": 2000, "operation.prompt": "#", "operation.prompt.command": "No Command", "operation.response.required": "no"}, {"operation.command": "show running-config", "operation.timeout": 10000, "operation.prompt": "#", "operation.prompt.command": "No Command", "operation.response.required": "yes"}], "Backup_Startup": [{"operation.command": "terminal length 0", "operation.timeout": 2000, "operation.prompt": "#", "operation.prompt.command": "No Command", "operation.response.required": "no"}, {"operation.command": "show config", "operation.timeout": 10000, "operation.prompt": "#", "operation.prompt.command": "No Command", "operation.response.required": "yes"}], "Sync": [{"operation.command": "copy running-config config", "operation.delay.time": 1000, "operation.timeout": 10000, "operation.prompt": "#", "operation.prompt.command": "LF followed by LF"}]}, "TFTP": {"Backup_Running": [{"operation.command": "copy running-config tftp address &[TransferProtocolServerAddress] filename &[TransferFileName]", "operation.delay.time": 1000, "operation.timeout": 10000, "operation.prompt": "#", "operation.prompt.command": "No Command"}], "Backup_Startup": [{"operation.command": "copy config tftp address &[TransferProtocolServerAddress] filename &[TransferFileName]", "operation.delay.time": 1000, "operation.timeout": 10000, "operation.prompt": "#", "operation.prompt.command": "No Command"}], "Sync": [{"operation.command": "copy running-config config", "operation.delay.time": 1000, "operation.timeout": 10000, "operation.prompt": "#", "operation.prompt.command": "LF followed by LF"}], "Restore": [{"operation.command": "copy tftp running-config address &[TransferProtocolServerAddress] filename &[TransferFileName]", "operation.delay.time": 1000, "operation.timeout": 10000, "operation.prompt": "#", "operation.prompt.command": "No Command"}]}}, {"_type": "0", "id": 100000000000038, "config.template.name": "Proximic", "config.template.description": "This is an Config template for Proximic Devices", "config.template.delay.time": 1000, "config.template.os.type": "Proximic", "config.template.vendor": "Proximic Inc", "config.template.catalog.ids": [], "NONE": {"Backup_Running": [{"operation.command": "show config", "operation.timeout": 10000, "operation.prompt": "#", "operation.prompt.command": "No Command", "operation.response.required": "yes"}]}, "TFTP": {"Backup_Running": [{"operation.command": "upload [TransferProtocolServerAddress] &[TransferFileName] config", "operation.delay.time": 1000, "operation.timeout": 10000, "operation.prompt": "#", "operation.prompt.command": "No Command"}], "Restore": [{"operation.command": "download [TransferProtocolServerAddress] &[TransferFileName] config", "operation.delay.time": 1000, "operation.timeout": 10000, "operation.prompt": "#", "operation.prompt.command": "No Command"}]}}, {"_type": "0", "id": 100000000000039, "config.template.name": "HP", "config.template.description": "This is an Config template for HP Devices", "config.template.os.type": "HP", "config.template.delay.time": 1000, "config.template.vendor": "Hewlett Packard Enterprise", "config.template.catalog.ids": [], "NONE": {"Backup_Running": [{"operation.command": "screen-length disable", "operation.timeout": 2000, "operation.prompt": ">", "operation.prompt.command": "LF", "operation.response.required": "no"}, {"operation.command": "display current-configuration", "operation.timeout": 20000, "operation.prompt": ">", "operation.prompt.command": "LF", "operation.response.required": "yes"}]}, "TFTP": {"Backup_Running": [{"operation.command": "display current-configuration  > &[TransferFileName]", "operation.timeout": 20000, "operation.prompt": ">", "operation.prompt.command": "LF"}, {"operation.command": "tftp &[TransferProtocolServerAddress] put &[TransferFileName]", "operation.timeout": 20000, "operation.prompt": ">", "operation.prompt.command": "LF"}]}, "SCP/SFTP": {"Backup_Running": [{"operation.command": "display current-configuration > running-config.cfg", "operation.timeout": 10000, "operation.prompt": ">", "operation.prompt.command": "No Command"}, {"operation.command": "scp &[TransferProtocolServerAddress] put running-config.cfg &[TransferFileName] user &[TransferProtocolServerUser] password &[TransferProtocolServerPassword]", "operation.timeout": 10000, "operation.prompt": ">", "operation.prompt.command": "No Command"}, {"operation.command": "delete running-config.cfg", "operation.timeout": 10000, "operation.prompt": "[Y/N]", "operation.prompt.command": "y followed by LF"}]}}, {"_type": "0", "id": 100000000000040, "config.template.name": "Fortigate", "config.template.description": "This is an Config template for Fortigate Devices", "config.template.os.type": "FortiOS", "config.template.delay.time": 1000, "config.template.vendor": "Fortinet", "config.template.catalog.ids": [], "NONE": {"Backup_Running": [{"operation.command": "config system console", "operation.timeout": 2000, "operation.prompt": "#", "operation.prompt.command": "No Command", "operation.response.required": "no"}, {"operation.command": "set output standard", "operation.timeout": 2000, "operation.prompt": "#", "operation.prompt.command": "No Command", "operation.response.required": "no"}, {"operation.command": "end", "operation.timeout": 2000, "operation.prompt": "#", "operation.prompt.command": "No Command", "operation.response.required": "no"}, {"operation.command": "show full-configuration", "operation.timeout": 2000, "operation.prompt": "#", "operation.prompt.command": "No Command", "operation.response.required": "yes"}]}, "TFTP": {"Backup_Running": [{"operation.command": "execute backup config tftp &[TransferFileName] &[TransferProtocolServerAddress]", "operation.timeout": 10000, "operation.prompt": "#", "operation.prompt.command": "No Command"}], "Restore": [{"operation.command": "execute restore config tftp &[TransferFileName] &[TransferProtocolServerAddress]", "operation.timeout": 10000, "operation.prompt": "#", "operation.prompt.command": "No Command"}]}, "SCP/SFTP": {"Backup_Running": [{"operation.command": "execute backup config sftp &[TransferFileName] &[TransferProtocolServerAddress] &[TransferProtocolServerUser] &[TransferProtocolServerPassword]", "operation.timeout": 10000, "operation.prompt": "#", "operation.prompt.command": "No Command"}]}}], "version": "1.3"}, {"type": "inline", "records": [{"_type": "0", "id": 100000000000036, "config.template.name": "<PERSON><PERSON>r", "config.template.description": "This is an Config template for Load Balancer Devices", "config.template.delay.time": 1000, "config.template.os.type": "BIG-IP", "config.template.vendor": "F5 Networks", "config.template.catalog.ids": [], "NONE": {"Backup_Running": [{"operation.command": "tmsh show running-config", "operation.timeout": 5000, "operation.prompt": "(y/n)", "operation.prompt.command": "No Command", "operation.response.required": "no"}, {"operation.command": "y", "operation.timeout": 10000, "operation.prompt": "#", "operation.prompt.command": "No Command", "operation.response.required": "yes"}]}}], "version": "1.4"}, {"type": "inline", "records": [{"_type": "0", "id": 100000000000041, "config.template.name": "Cisco NetScaler", "config.template.description": "Netscaler without protocol", "config.template.os.type": "Cisco NetScaler", "config.template.delay.time": 1000, "config.template.vendor": "Citrix Systems", "config.template.catalog.ids": [], "NONE": {"Backup_Running": [{"operation.command": "show ns runningConfig", "operation.timeout": 10000, "operation.prompt": ">", "operation.prompt.command": "LF", "operation.response.required": "yes"}]}}], "version": "1.5"}, {"type": "inline", "records": [{"id": 100000000000001, "config.template.name": "Cisco", "config.template.description": "This is an Config template for Cisco Devices", "config.template.vendor": "Cisco Systems", "config.template.os.type": "Cisco", "config.template.delay.time": 1000, "NONE": {"Backup_Running": [{"operation.command": "terminal length 0", "operation.delay.time": 1000, "operation.timeout": 2000, "operation.prompt": "#", "operation.prompt.command": "No Command"}, {"operation.command": "show running-config", "operation.delay.time": 1000, "operation.timeout": 5000, "operation.prompt": "#", "operation.prompt.command": "LF", "operation.response.required": "yes"}], "Backup_Startup": [{"operation.command": "terminal length 0", "operation.delay.time": 1000, "operation.timeout": 2000, "operation.prompt": "#", "operation.prompt.command": "No Command"}, {"operation.command": "show startup-config", "operation.delay.time": 1000, "operation.timeout": 5000, "operation.prompt": "#", "operation.prompt.command": "LF", "operation.response.required": "yes"}], "Sync": [{"operation.command": "copy running-config startup-config", "operation.delay.time": 1000, "operation.timeout": 10000, "operation.prompt": "]?", "operation.prompt.command": "LF followed by LF"}], "Info": [{"operation.command": "terminal length 0", "operation.delay.time": 1000, "operation.timeout": 3000, "operation.prompt": "#", "operation.prompt.command": "No Command"}, {"operation.command": "show version", "operation.delay.time": 1000, "operation.timeout": 3000, "operation.prompt": "#", "operation.prompt.command": "No Command", "attributes": [{"attribute.name": "CPU", "result.pattern": "(\\S*\\((\\S*)\\))\\s*processor"}, {"attribute.name": "<PERSON>", "result.pattern": "(\\S*)\\s*bytes\\s*of\\s*Flash"}, {"attribute.name": "CPU Revision", "result.pattern": "(\\((revision\\s*\\S*)\\))"}, {"attribute.name": "Configuration Register", "result.pattern": "Configuration register is (\\S+)"}, {"attribute.name": "NVRAM Size", "result.pattern": "(\\S*)\\s*bytes\\s*of\\s*NVRAM"}, {"attribute.name": "Image File Name", "result.pattern": "System\\s*image\\s*file\\s*is\\s*\"(.*)\""}, {"attribute.name": "Serial Number", "result.pattern": "Processor\\s*board\\s*ID\\s*(\\w*)"}, {"attribute.name": "DRAM Size", "result.pattern": "(\\S+)\\s*bytes\\s*of\\s*memory"}]}]}, "TFTP": {"Backup_Running": [{"operation.command": "copy running-config tftp:", "operation.delay.time": 1000, "operation.timeout": 2000, "operation.prompt": "]?", "operation.prompt.command": "No Command"}, {"operation.command": "&[TransferProtocolServerAddress]", "operation.delay.time": 1000, "operation.timeout": 2000, "operation.prompt": "]?", "operation.prompt.command": "No Command"}, {"operation.command": "&[TransferFileName]", "operation.delay.time": 1000, "operation.timeout": 5000, "operation.prompt": "#", "operation.prompt.command": "LF"}], "Backup_Startup": [{"operation.command": "copy startup-config tftp:", "operation.delay.time": 1000, "operation.timeout": 2000, "operation.prompt": "]?", "operation.prompt.command": "No Command"}, {"operation.command": "&[TransferProtocolServerAddress]", "operation.delay.time": 1000, "operation.timeout": 2000, "operation.prompt": "]?", "operation.prompt.command": "No Command"}, {"operation.command": "&[TransferFileName]", "operation.delay.time": 1000, "operation.timeout": 5000, "operation.prompt": "#", "operation.prompt.command": "LF"}], "Sync": [{"operation.command": "copy running-config startup-config", "operation.delay.time": 1000, "operation.timeout": 10000, "operation.prompt": "]?", "operation.prompt.command": "LF followed by LF"}], "Restore": [{"operation.command": "copy tftp: running-config", "operation.delay.time": 1000, "operation.timeout": 2000, "operation.prompt": "]?", "operation.prompt.command": "No Command"}, {"operation.command": "&[TransferProtocolServerAddress]", "operation.delay.time": 1000, "operation.timeout": 2000, "operation.prompt": "]?", "operation.prompt.command": "No Command"}, {"operation.command": "&[TransferFileName]", "operation.delay.time": 1000, "operation.timeout": 2000, "operation.prompt": "]?", "operation.prompt.command": "No Command"}, {"operation.command": "running-config", "operation.delay.time": 1000, "operation.timeout": 5000, "operation.prompt": "#", "operation.prompt.command": "LF"}], "Info": [{"operation.command": "terminal length 0", "operation.delay.time": 1000, "operation.timeout": 3000, "operation.prompt": "#", "operation.prompt.command": "No Command"}, {"operation.command": "show version", "operation.delay.time": 1000, "operation.timeout": 3000, "operation.prompt": "#", "operation.prompt.command": "No Command", "attributes": [{"attribute.name": "CPU", "result.pattern": "(\\S*\\((\\S*)\\))\\s*processor"}, {"attribute.name": "<PERSON>", "result.pattern": "(\\S*)\\s*bytes\\s*of\\s*Flash"}, {"attribute.name": "CPU Revision", "result.pattern": "(\\((revision\\s*\\S*)\\))"}, {"attribute.name": "Configuration Register", "result.pattern": "Configuration register is (\\S+)"}, {"attribute.name": "NVRAM Size", "result.pattern": "(\\S*)\\s*bytes\\s*of\\s*NVRAM"}, {"attribute.name": "Image File Name", "result.pattern": "System\\s*image\\s*file\\s*is\\s*\"(.*)\""}, {"attribute.name": "Serial Number", "result.pattern": "Processor\\s*board\\s*ID\\s*(\\w*)"}, {"attribute.name": "DRAM Size", "result.pattern": "(\\S+)\\s*bytes\\s*of\\s*memory"}]}]}, "SCP/SFTP": {"Backup_Running": [{"operation.command": "copy running-config scp:", "operation.delay.time": 1000, "operation.timeout": 2000, "operation.prompt": "]?", "operation.prompt.command": "No Command"}, {"operation.command": "&[TransferProtocolServerAddress]", "operation.delay.time": 1000, "operation.timeout": 2000, "operation.prompt": "]?", "operation.prompt.command": "No Command"}, {"operation.command": "&[TransferProtocolServerUser]", "operation.delay.time": 1000, "operation.timeout": 2000, "operation.prompt": "]?", "operation.prompt.command": "No Command"}, {"operation.command": "&[TransferFileName]", "operation.delay.time": 1000, "operation.timeout": 2000, "operation.prompt": "Password:", "operation.prompt.command": "No Command"}, {"operation.command": "&[TransferProtocolServerPassword]", "operation.delay.time": 1000, "operation.timeout": 5000, "operation.prompt": "#", "operation.prompt.command": "LF"}], "Backup_Startup": [{"operation.command": "copy startup-config scp:", "operation.delay.time": 1000, "operation.timeout": 2000, "operation.prompt": "]?", "operation.prompt.command": "No Command"}, {"operation.command": "&[TransferProtocolServerAddress]", "operation.delay.time": 1000, "operation.timeout": 2000, "operation.prompt": "]?", "operation.prompt.command": "No Command"}, {"operation.command": "&[TransferProtocolServerUser]", "operation.delay.time": 1000, "operation.timeout": 2000, "operation.prompt": "]?", "operation.prompt.command": "No Command"}, {"operation.command": "&[TransferFileName]", "operation.delay.time": 1000, "operation.timeout": 2000, "operation.prompt": "Password:", "operation.prompt.command": "No Command"}, {"operation.command": "&[TransferProtocolServerPassword]", "operation.delay.time": 1000, "operation.timeout": 5000, "operation.prompt": "#", "operation.prompt.command": "LF"}], "Sync": [{"operation.command": "copy running-config startup-config", "operation.delay.time": 1000, "operation.timeout": 10000, "operation.prompt": "]?", "operation.prompt.command": "LF followed by LF"}], "Restore": [{"operation.command": "copy scp: running-config", "operation.delay.time": 100, "operation.timeout": 2000, "operation.prompt": "]?", "operation.prompt.command": "No Command"}, {"operation.command": "&[TransferProtocolServerAddress]", "operation.delay.time": 100, "operation.timeout": 2000, "operation.prompt": "]?", "operation.prompt.command": "No Command"}, {"operation.command": "&[TransferProtocolServerUser]", "operation.delay.time": 100, "operation.timeout": 2000, "operation.prompt": "]?", "operation.prompt.command": "No Command"}, {"operation.command": "&[TransferFileName]", "operation.delay.time": 100, "operation.timeout": 2000, "operation.prompt": "]?", "operation.prompt.command": "No Command"}, {"operation.command": "running-config", "operation.delay.time": 100, "operation.timeout": 2000, "operation.prompt": "Password:", "operation.prompt.command": "No Command"}, {"operation.command": "&[TransferProtocolServerPassword]", "operation.delay.time": 100, "operation.timeout": 5000, "operation.prompt": "#", "operation.prompt.command": "LF"}], "Info": [{"operation.command": "terminal length 0", "operation.delay.time": 1000, "operation.timeout": 3000, "operation.prompt": "#", "operation.prompt.command": "No Command"}, {"operation.command": "show version", "operation.delay.time": 1000, "operation.timeout": 3000, "operation.prompt": "#", "operation.prompt.command": "No Command", "attributes": [{"attribute.name": "CPU", "result.pattern": "(\\S*\\((\\S*)\\))\\s*processor"}, {"attribute.name": "<PERSON>", "result.pattern": "(\\S*)\\s*bytes\\s*of\\s*Flash"}, {"attribute.name": "CPU Revision", "result.pattern": "(\\((revision\\s*\\S*)\\))"}, {"attribute.name": "Configuration Register", "result.pattern": "Configuration register is (\\S+)"}, {"attribute.name": "NVRAM Size", "result.pattern": "(\\S*)\\s*bytes\\s*of\\s*NVRAM"}, {"attribute.name": "Image File Name", "result.pattern": "System\\s*image\\s*file\\s*is\\s*\"(.*)\""}, {"attribute.name": "Serial Number", "result.pattern": "Processor\\s*board\\s*ID\\s*(\\w*)"}, {"attribute.name": "DRAM Size", "result.pattern": "(\\S+)\\s*bytes\\s*of\\s*memory"}]}]}, "upgrade": {"Get Current Firmware Image": [{"operation.command": "terminal length 0", "operation.delay.time": 1000, "operation.timeout": 2000, "operation.prompt": "#", "operation.prompt.command": "No Command"}, {"operation.command": "show version", "operation.delay.time": 1000, "operation.timeout": 2000, "operation.prompt": "#", "operation.prompt.command": "LF", "attributes": [{"attribute.name": "config.firmware.device.backup.file.name", "result.pattern": "System\\s+image\\s+file\\s+is\\s+\\\"\\S+\\:(\\S+)\\\""}]}], "Get Configuration Register Info": [{"operation.command": "show version", "operation.delay.time": 1000, "operation.timeout": 2000, "operation.prompt": "#", "operation.prompt.command": "No Command", "attributes": [{"attribute.name": "config.firmware.register.info", "result.pattern": "Configuration register is 0xF", "expected.value": "Configuration register is 0xF"}]}], "Get Free Space": [{"operation.command": "dir flash:", "operation.delay.time": 1000, "operation.timeout": 2000, "operation.prompt": "#", "operation.prompt.command": "No Command", "attributes": [{"attribute.name": "config.firmware.image.free.space", "result.pattern": "\\d+\\s*bytes\\s*total\\s*\\((\\d+)\\s+bytes\\s*free\\)"}]}], "Backup Existing Firmware Image": [{"operation.command": "copy flash: tftp:", "operation.delay.time": 1000, "operation.timeout": 2000, "operation.prompt": "]?", "operation.prompt.command": "No Command"}, {"operation.command": "&[FirmwareDeviceBackupFileName]", "operation.delay.time": 1000, "operation.timeout": 2000, "operation.prompt": "]?", "operation.prompt.command": "No Command"}, {"operation.command": "&[TransferProtocolServerAddress]", "operation.delay.time": 1000, "operation.timeout": 60000, "operation.prompt": "]?", "operation.prompt.command": "LF"}, {"operation.command": "&[FirmwareDeviceBackupFilePath]", "operation.delay.time": 1000, "operation.timeout": 600000, "operation.prompt": "#", "operation.prompt.command": "LF"}], "Transfer Firmware Image to Device": [{"operation.command": "copy tftp: flash:", "operation.delay.time": 1000, "operation.timeout": 2000, "operation.prompt": "]?", "operation.prompt.command": "No Command"}, {"operation.command": "&[TransferProtocolServerAddress]", "operation.delay.time": 1000, "operation.timeout": 2000, "operation.prompt": "]?", "operation.prompt.command": "No Command"}, {"operation.command": "&[ConfigFirmwareUpgradeTransferFileName]", "operation.delay.time": 1000, "operation.timeout": 6000, "operation.prompt": "]?", "operation.prompt.command": "LF"}, {"operation.command": "&[TransferFileName]", "operation.delay.time": 1000, "operation.timeout": 600000, "operation.prompt": "#", "operation.prompt.command": "LF"}], "Update Boot Variable (Mount)": [{"operation.command": "config terminal", "operation.delay.time": 1000, "operation.timeout": 2000, "operation.prompt": "#", "operation.prompt.command": "No Command"}, {"operation.command": "no boot system", "operation.delay.time": 1000, "operation.timeout": 2000, "operation.prompt": "#", "operation.prompt.command": "No Command"}, {"operation.command": "boot system flash &[TransferFileName]", "operation.delay.time": 1000, "operation.timeout": 2000, "operation.prompt": "#", "operation.prompt.command": "No Command"}, {"operation.command": "end", "operation.delay.time": 1000, "operation.timeout": 2000, "operation.prompt": "#", "operation.prompt.command": "No Command"}], "Verify Uploaded Firmware Image Integrity": [{"operation.command": "verify flash:&[TransferFileName]", "operation.delay.time": 1000, "operation.timeout": 600000, "operation.prompt": "#", "operation.prompt.command": "No Command"}]}, "config.template.catalog.ids": [], "_type": "0"}, {"id": 100000000000006, "config.template.name": "<PERSON><PERSON><PERSON>", "config.template.description": "This is an Config template for Huawei Devices", "config.template.vendor": "<PERSON><PERSON><PERSON>", "config.template.os.type": "VRP", "config.template.delay.time": 1000, "NONE": {"Backup_Running": [{"operation.command": "screen-length 0 temporary", "operation.delay.time": 1000, "operation.timeout": 2000, "operation.prompt": ">", "operation.prompt.command": "No Command"}, {"operation.command": "display current-configuration", "operation.delay.time": 1000, "operation.timeout": 5000, "operation.prompt": ">", "operation.prompt.command": "LF", "operation.response.required": "yes"}], "Backup_Startup": [{"operation.command": "screen-length 0 temporary", "operation.delay.time": 1000, "operation.timeout": 2000, "operation.prompt": ">", "operation.prompt.command": "No Command"}, {"operation.command": "display saved-configuration", "operation.delay.time": 1000, "operation.timeout": 5000, "operation.prompt": ">", "operation.prompt.command": "LF", "operation.response.required": "yes"}], "Sync": [{"operation.command": "save", "operation.delay.time": 1000, "operation.timeout": 10000, "operation.prompt": "[Y/N]", "operation.prompt.command": "y followed by LF"}], "Info": [{"operation.command": "screen-length 0 temporary", "operation.delay.time": 1000, "operation.timeout": 3000, "operation.prompt": ">", "operation.prompt.command": "No Command"}, {"operation.command": "display version", "operation.delay.time": 1000, "operation.timeout": 3000, "operation.prompt": ">", "operation.prompt.command": "No Command", "attributes": [{"attribute.name": "Manufacturer", "result.pattern": "HUAWEI\\s*(\\S*-\\S*)\\s*"}, {"attribute.name": "DDR Memory Size", "result.pattern": "DDR\\s*Memory\\s*Size\\s*:\\s*(.*)"}, {"attribute.name": "FLASH Memory size", "result.pattern": "FLASH\\s*Memory\\s*Size\\s*:\\s*(.*)"}, {"attribute.name": "Pcb Version", "result.pattern": "Pcb\\s*Version\\s*:\\s*(.*)"}, {"attribute.name": "BootROM Version", "result.pattern": "BootROM\\s*Version\\s*:\\s*(.*)"}, {"attribute.name": "BootLoad version", "result.pattern": "BootLoad\\s*\\S*\\s*:\\s*(.*)"}, {"attribute.name": "Software version", "result.pattern": "Software\\s*Version\\s*:\\s*(.*)"}, {"attribute.name": "CPLD Version", "result.pattern": "CPLD\\s*Version\\s*:\\s*(.*)"}]}, {"operation.command": "display memory-usage", "operation.delay.time": 1000, "operation.timeout": 3000, "operation.prompt": ">", "operation.prompt.command": "No Command", "attributes": [{"attribute.name": "System total Memory", "result.pattern": "System\\s*Total\\s*Memory\\s*Is:\\s*(.*)"}, {"attribute.name": "Total Memory Used", "result.pattern": "Total\\s*Memory\\s*Used\\s*Is:\\s*(.*)"}, {"attribute.name": "Memory Using percentage", "result.pattern": "Memory\\s*Using\\s*Percentage\\s*Is:\\s*(.*)"}]}, {"operation.command": "display device manufacture-info", "operation.delay.time": 1000, "operation.timeout": 3000, "operation.prompt": ">", "operation.prompt.command": "No Command", "attributes": [{"attribute.name": "Serial number", "result.pattern": ".\\s*-\\s*(\\w*)\\s*\\d*-\\d*-\\d*"}]}, {"operation.command": "dir", "operation.delay.time": 1000, "operation.timeout": 3000, "operation.prompt": ">", "operation.prompt.command": "No Command", "attributes": [{"attribute.name": "File Name", "result.pattern": "\\d+:\\d+:\\d+\\s+(\\S+\\.(?:bin|cc))"}]}]}, "TFTP": {"Backup_Running": [{"operation.command": "save &[TransferFileName]", "operation.delay.time": 1000, "operation.timeout": 4000, "operation.prompt": "[Y/N]", "operation.prompt.command": "y followed by LF"}, {"operation.command": "tftp &[TransferProtocolServerAddress] put &[TransferFileName]", "operation.delay.time": 1000, "operation.timeout": 5000, "operation.prompt": ">", "operation.prompt.command": "LF"}], "Sync": [{"operation.command": "save", "operation.delay.time": 1000, "operation.timeout": 10000, "operation.prompt": "[Y/N]", "operation.prompt.command": "y followed by LF"}], "Restore": [{"operation.command": "tftp &[TransferProtocolServerAddress] get &[TransferFileName]", "operation.delay.time": 1000, "operation.timeout": 3000, "operation.prompt": "[Y/N]:", "operation.prompt.command": "y followed by LF"}, {"operation.command": "startup saved-configuration &[TransferFileName]", "operation.delay.time": 1000, "operation.timeout": 5000, "operation.prompt": ">", "operation.prompt.command": "LF"}], "Info": [{"operation.command": "screen-length 0 temporary", "operation.delay.time": 1000, "operation.timeout": 3000, "operation.prompt": ">", "operation.prompt.command": "No Command"}, {"operation.command": "display version", "operation.delay.time": 1000, "operation.timeout": 3000, "operation.prompt": ">", "operation.prompt.command": "No Command", "attributes": [{"attribute.name": "Manufacturer", "result.pattern": "HUAWEI\\s*(\\S*-\\S*)\\s*"}, {"attribute.name": "DDR Memory Size", "result.pattern": "DDR\\s*Memory\\s*Size\\s*:\\s*(.*)"}, {"attribute.name": "FLASH Memory size", "result.pattern": "FLASH\\s*Memory\\s*Size\\s*:\\s*(.*)"}, {"attribute.name": "Pcb Version", "result.pattern": "Pcb\\s*Version\\s*:\\s*(.*)"}, {"attribute.name": "BootROM Version", "result.pattern": "BootROM\\s*Version\\s*:\\s*(.*)"}, {"attribute.name": "BootLoad version", "result.pattern": "BootLoad\\s*\\S*\\s*:\\s*(.*)"}, {"attribute.name": "Software version", "result.pattern": "Software\\s*Version\\s*:\\s*(.*)"}, {"attribute.name": "CPLD Version", "result.pattern": "CPLD\\s*Version\\s*:\\s*(.*)"}]}, {"operation.command": "display memory-usage", "operation.delay.time": 1000, "operation.timeout": 3000, "operation.prompt": ">", "operation.prompt.command": "No Command", "attributes": [{"attribute.name": "System total Memory", "result.pattern": "System\\s*Total\\s*Memory\\s*Is:\\s*(.*)"}, {"attribute.name": "Total Memory Used", "result.pattern": "Total\\s*Memory\\s*Used\\s*Is:\\s*(.*)"}, {"attribute.name": "Memory Using percentage", "result.pattern": "Memory\\s*Using\\s*Percentage\\s*Is:\\s*(.*)"}]}, {"operation.command": "display device manufacture-info", "operation.delay.time": 1000, "operation.timeout": 3000, "operation.prompt": ">", "operation.prompt.command": "No Command", "attributes": [{"attribute.name": "Serial number", "result.pattern": ".\\s*-\\s*(\\w*)\\s*\\d*-\\d*-\\d*"}]}, {"operation.command": "dir", "operation.delay.time": 1000, "operation.timeout": 3000, "operation.prompt": ">", "operation.prompt.command": "No Command", "attributes": [{"attribute.name": "File Name", "result.pattern": "\\d+:\\d+:\\d+\\s+(\\S+\\.(?:bin|cc))"}]}]}, "SCP/SFTP": {"Backup_Running": [{"operation.command": "save &[LocalFileName]", "operation.delay.time": 1000, "operation.timeout": 10000, "operation.prompt": "[Y/N]", "operation.prompt.command": "y followed by LF"}, {"operation.command": "system-view", "operation.delay.time": 1000, "operation.timeout": 2000, "operation.prompt": "]", "operation.prompt.command": "LF"}, {"operation.command": "sftp client-transfile put host-ip &[TransferProtocolServerAddress] username &[TransferProtocolServerUser] password &[TransferProtocolServerPassword] sourcefile &[LocalFileName]", "operation.delay.time": 1000, "operation.timeout": 10000, "operation.prompt": "]", "operation.prompt.command": "LF"}, {"operation.command": "quit", "operation.delay.time": 1000, "operation.timeout": 2000, "operation.prompt": ">", "operation.prompt.command": "LF"}, {"operation.command": "delete &[LocalFileName]", "operation.delay.time": 1000, "operation.timeout": 2000, "operation.prompt": "[Y/N]", "operation.prompt.command": "y followed by LF"}], "Sync": [{"operation.command": "save", "operation.delay.time": 1000, "operation.timeout": 10000, "operation.prompt": "[Y/N]", "operation.prompt.command": "y followed by LF"}], "Info": [{"operation.command": "screen-length 0 temporary", "operation.delay.time": 1000, "operation.timeout": 3000, "operation.prompt": ">", "operation.prompt.command": "No Command"}, {"operation.command": "display version", "operation.delay.time": 1000, "operation.timeout": 3000, "operation.prompt": ">", "operation.prompt.command": "No Command", "attributes": [{"attribute.name": "Manufacturer", "result.pattern": "HUAWEI\\s*(\\S*-\\S*)\\s*"}, {"attribute.name": "DDR Memory Size", "result.pattern": "DDR\\s*Memory\\s*Size\\s*:\\s*(.*)"}, {"attribute.name": "FLASH Memory size", "result.pattern": "FLASH\\s*Memory\\s*Size\\s*:\\s*(.*)"}, {"attribute.name": "Pcb Version", "result.pattern": "Pcb\\s*Version\\s*:\\s*(.*)"}, {"attribute.name": "BootROM Version", "result.pattern": "BootROM\\s*Version\\s*:\\s*(.*)"}, {"attribute.name": "BootLoad version", "result.pattern": "BootLoad\\s*\\S*\\s*:\\s*(.*)"}, {"attribute.name": "Software version", "result.pattern": "Software\\s*Version\\s*:\\s*(.*)"}, {"attribute.name": "CPLD Version", "result.pattern": "CPLD\\s*Version\\s*:\\s*(.*)"}]}, {"operation.command": "display memory-usage", "operation.delay.time": 1000, "operation.timeout": 3000, "operation.prompt": ">", "operation.prompt.command": "No Command", "attributes": [{"attribute.name": "System total Memory", "result.pattern": "System\\s*Total\\s*Memory\\s*Is:\\s*(.*)"}, {"attribute.name": "Total Memory Used", "result.pattern": "Total\\s*Memory\\s*Used\\s*Is:\\s*(.*)"}, {"attribute.name": "Memory Using percentage", "result.pattern": "Memory\\s*Using\\s*Percentage\\s*Is:\\s*(.*)"}]}, {"operation.command": "display device manufacture-info", "operation.delay.time": 1000, "operation.timeout": 3000, "operation.prompt": ">", "operation.prompt.command": "No Command", "attributes": [{"attribute.name": "Serial number", "result.pattern": ".\\s*-\\s*(\\w*)\\s*\\d*-\\d*-\\d*"}]}, {"operation.command": "dir", "operation.delay.time": 1000, "operation.timeout": 3000, "operation.prompt": ">", "operation.prompt.command": "No Command", "attributes": [{"attribute.name": "File Name", "result.pattern": "\\d+:\\d+:\\d+\\s+(\\S+\\.(?:bin|cc))"}]}]}, "_type": "0"}], "version": "1.9"}]}