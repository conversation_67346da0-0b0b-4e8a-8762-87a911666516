{"entity": "Metric", "table": "tbl_config_metric", "version": "1.0", "author": "<PERSON><PERSON>", "props": [{"name": "metric.name", "title": "Metric Name", "type": "string", "rules": ["required"]}, {"name": "metric.type", "title": "Metric Type", "type": "string", "rules": ["required"]}, {"name": "metric.category", "title": "Metric Category", "type": "string", "rules": ["required"]}, {"name": "metric.plugin", "title": "<PERSON><PERSON>", "type": "string", "rules": ["required"]}, {"name": "metric.polling.time", "title": "Metric Polling Time", "type": "numeric", "rules": ["required"]}, {"name": "metric.state", "title": "Metric State", "type": "string", "rules": ["required"]}, {"name": "metric.credential.profile", "title": "Metric Credential Profile", "type": "numeric", "rules": ["required"]}]}