{"entity": "Two Factor Authentication", "table": "tbl_config_two_factor_authentication", "version": "1.0", "author": "<PERSON><PERSON><PERSON>", "props": [{"name": "two.factor.authentication.enable", "title": "Two Factor Authentication Enable", "type": "string", "rules": ["required"], "values": ["yes", "no"]}, {"name": "two.factor.authentication.mode", "title": "Two Factor Authentication Mode", "type": "string", "rules": ["required"], "values": ["Email", "Authenticator App"]}], "entries": [{"type": "inline", "records": [{"two.factor.authentication.enable": "no", "two.factor.authentication.mode": "Email", "id": 10000000000001, "_type": "0"}], "version": "1.0"}]}