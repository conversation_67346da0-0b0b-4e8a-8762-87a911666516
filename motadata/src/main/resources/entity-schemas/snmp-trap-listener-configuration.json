{"entity": "SNMP Trap Listener", "table": "tbl_config_snmp_trap_listener", "version": "1.0", "author": "<PERSON><PERSON>", "props": [{"name": "snmp.trap.listener.v1.v2.port", "title": "Port", "type": "numeric"}, {"name": "snmp.trap.listener.v1.v2.status", "title": "Listener Status", "type": "string", "values": ["yes", "no"]}, {"name": "snmp.community", "title": "SNMP Community", "type": "string"}, {"name": "snmp.trap.listener.v3.port", "title": "Port", "type": "numeric"}, {"name": "snmp.trap.listener.v3.status", "title": "Listener Status", "type": "string", "values": ["yes", "no"]}, {"name": "snmp.security.user.name", "title": "Security User Name", "type": "string"}, {"name": "snmp.security.level", "title": "Security Level", "type": "string", "values": ["No Authentication No Privacy", "Authentication No Privacy", "Authentication Privacy"]}, {"name": "snmp.authentication.protocol", "title": "Authentication Protocol", "type": "string", "values": ["MD5", "SHA", "SHA224", "SHA256", "SHA384", "SHA512"]}, {"name": "snmp.authentication.password", "title": "Authentication Password", "type": "password"}, {"name": "snmp.privacy.protocol", "title": "Privacy Protocol", "type": "string", "values": ["DES", "3DES", "AES128", "AES192", "AES192C", "AES192G", "AES256", "AES256C", "AES256G"]}, {"name": "snmp.private.password", "title": "Private Password", "type": "password"}], "entries": [{"type": "inline", "records": [{"id": 10000000000001, "snmp.trap.listener.v1.v2.port": 1620, "snmp.trap.listener.v1.v2.status": "yes", "snmp.community": "public", "snmp.trap.listener.v3.status": "no", "snmp.trap.listener.v3.port": 1630}], "version": "1.0"}]}