{"entity": "Compliance Weighted Calculation", "table": "tbl_config_compliance_weighted_calculation", "version": "1.0", "author": "<PERSON><PERSON>", "props": [{"name": "rule.context", "title": "Rule", "type": "map", "rules": ["required"]}, {"name": "object.state.context", "title": "Object State", "type": "map", "rules": ["required"]}], "entries": [{"type": "inline", "records": [{"_type": "0", "id": 10000000000001, "rule.weightage": {"CRITICAL": 5, "HIGH": 4, "MEDIUM": 3, "LOW": 2, "INFO": 1}, "object.criticality.weightage": {"P-5": {"weightage": 5, "tags": []}, "P-4": {"weightage": 4, "tags": []}, "P-3": {"weightage": 3, "tags": []}, "P-2": {"weightage": 2, "tags": []}, "P-1": {"weightage": 1, "tags": []}}, "object.state.weightage": {"VULNERABLE": {"start.range": 0, "end.range": 20}, "MODERATE": {"start.range": 21, "end.range": 40}, "POOR": {"start.range": 41, "end.range": 60}, "SECURE": {"start.range": 61, "end.range": 100}}}], "version": "1.1"}]}