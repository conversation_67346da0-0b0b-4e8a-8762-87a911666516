{"entity": "Proxy Server", "table": "tbl_config_proxy_server", "version": "1.0", "author": "<PERSON><PERSON>", "props": [{"name": "proxy.server.host", "title": "Host", "type": "string", "rules": ["required"]}, {"name": "proxy.server.port", "title": "Port", "type": "numeric", "rules": ["required"]}, {"name": "proxy.server.type", "title": "Type", "type": "string", "rules": ["required"], "values": ["http", "socks4", "socks5"]}, {"name": "proxy.server.username", "title": "Username", "type": "string"}, {"name": "proxy.server.password", "title": "Password", "type": "string"}, {"name": "proxy.server.timeout", "title": "Timeout", "type": "numeric"}], "entries": [{"type": "inline", "records": [], "version": "1.0"}]}