/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

package com.mindarray.store;

import com.mindarray.Bootstrap;
import com.mindarray.GlobalConstants;
import com.mindarray.eventbus.EventBusConstants;
import com.mindarray.util.Logger;
import io.vertx.core.Future;
import io.vertx.core.Promise;
import io.vertx.core.json.JsonObject;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

import static com.mindarray.GlobalConstants.ACKNOWLEDGED;
import static com.mindarray.GlobalConstants.NO;
import static com.mindarray.nms.NMSConstants.PREVIOUS_FLAP_TIMESTAMP;
import static com.mindarray.policy.PolicyEngineConstants.POLICY_ACKNOWLEDGE;

public class MetricPolicyFlapDurationCacheStore extends AbstractCacheStore
{

    private static final MetricPolicyFlapDurationCacheStore STORE = new MetricPolicyFlapDurationCacheStore();

    private static final Logger LOGGER = new Logger(MetricPolicyFlapDurationCacheStore.class, GlobalConstants.MOTADATA_STORE, "Metric Policy Flap Duration Cache Store");

    private final Map<String, Long> flaps = new ConcurrentHashMap<>();  // policy with its previous.flap.timestamp

    private final Map<String, String> acks = new ConcurrentHashMap<>();  // policy with its acknowledgment

    private MetricPolicyFlapDurationCacheStore()
    {
    }

    public static MetricPolicyFlapDurationCacheStore getStore()
    {
        return STORE;
    }

    @Override
    public Future<Void> initStore()
    {
        var promise = Promise.<Void>promise();

        try
        {
            Bootstrap.vertx().eventBus().<JsonObject>request(EventBusConstants.EVENT_METRIC_POLICY_TRIGGER_DURATION_QUERY, new JsonObject(), reply ->
            {
                try
                {
                    reply.result().body().getMap().forEach((key, value) ->
                    {
                        var item = (JsonObject) value;

                        flaps.put(key, item.getLong(PREVIOUS_FLAP_TIMESTAMP));

                        var ack = item.containsKey(POLICY_ACKNOWLEDGE) ? new JsonObject(item.getString(POLICY_ACKNOWLEDGE)) : null;

                        acks.put(key, ack != null ? ack.containsKey(ACKNOWLEDGED) ? ack.getString(ACKNOWLEDGED) : NO : NO);
                    });

                    LOGGER.info("Metric Policy Flap Duration Cache Store initialized successfully");

                    promise.complete();
                }
                catch (Exception exception)
                {
                    LOGGER.error(exception);

                    promise.fail(exception);
                }

            });
        }
        catch (Exception exception)
        {
            LOGGER.info(exception);

            promise.fail(exception);
        }

        return promise.future();
    }


    public void update(String key, JsonObject objectPolicyStatus, boolean updateTimestamp)
    {
        var ack = objectPolicyStatus.containsKey(POLICY_ACKNOWLEDGE) ? new JsonObject(objectPolicyStatus.getString(POLICY_ACKNOWLEDGE)) : null;

        acks.put(key, ack != null ? ack.containsKey(ACKNOWLEDGED) ? ack.getString(ACKNOWLEDGED) : NO : NO);

        if (updateTimestamp)
        {
            flaps.put(key, objectPolicyStatus.getLong(PREVIOUS_FLAP_TIMESTAMP));
        }
    }

    public Long getFlapTick(String key)
    {
        return flaps.getOrDefault(key, 0L);
    }

    public String getAcknowledgment(String key)
    {
        return acks.get(key);
    }

}

