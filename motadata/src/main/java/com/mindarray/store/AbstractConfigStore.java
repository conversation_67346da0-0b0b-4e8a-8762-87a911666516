/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

/*
 * Change Logs:
 *  Date			Author			         Notes
 *  28-Feb-2025     Smit Prajapati           MOTADATA-4956: TagRuleConfigStore for Rule Based Tagging
 *  5-Mar-2025      Bharat                   MOTADATA-4740: Two factor authentication 2FA
 *  2-Apr-2025      Bharat                   MOTADATA-5637: Domain Mapping in Flow
 *	20-Feb-2025		Pruthviraj		         NetRoute ,NetRoute policy config store added
 *  22-Apr-2025     Bharat                   MOTADATA-5822: Metric Explorer Enhancements
 * */

package com.mindarray.store;


import com.mindarray.Bootstrap;
import com.mindarray.GlobalConstants;
import com.mindarray.db.DBConstants;
import com.mindarray.eventbus.EventBusConstants;
import com.mindarray.util.CommonUtil;
import com.mindarray.util.Logger;
import io.vertx.core.Future;
import io.vertx.core.Promise;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.stream.Collectors;

import static com.mindarray.GlobalConstants.VALUE;
import static com.mindarray.api.APIConstants.*;


public abstract class AbstractConfigStore
{

    protected final boolean notify;

    protected final Map<Long, JsonObject> items = new ConcurrentHashMap<>();
    private final String table;
    private final Logger logger;
    private final JsonArray filters;
    private final AtomicBoolean initDone = new AtomicBoolean(false); // used to prevent multiple times init ....
    private final List<Map<String, Object>> referenceEntities;

    protected AbstractConfigStore(String table, Logger logger, boolean notify)
    {
        this.table = table;

        this.logger = logger;

        this.referenceEntities = null;

        this.notify = notify;

        this.filters = null;
    }

    protected AbstractConfigStore(Logger logger)
    {
        this.table = null;

        this.logger = logger;

        this.referenceEntities = null;

        this.notify = false;

        this.filters = null;
    }

    protected AbstractConfigStore(String table, Logger logger, boolean notify, List<Map<String, Object>> referenceEntities)
    {
        this.table = table;

        this.logger = logger;

        this.referenceEntities = Collections.unmodifiableList(referenceEntities);

        this.notify = notify;

        this.filters = null;
    }

    protected AbstractConfigStore(String table, Logger logger, boolean notify, JsonArray filters, List<Map<String, Object>> referenceEntities)
    {
        this.table = table;

        this.logger = logger;

        this.referenceEntities = Collections.unmodifiableList(referenceEntities);

        this.notify = notify;

        this.filters = filters;
    }

    public static AbstractConfigStore getConfigStore(ConfigStore store)
    {
        if (store != null)
        {
            return switch (store)
            {
                case AGENT -> AgentConfigStore.getStore();

                case APPLICATION_MAPPER -> ApplicationMapperConfigStore.getStore();

                case USER, MY_ACCOUNT -> UserConfigStore.getStore();

                case USER_ROLE -> UserRoleConfigStore.getStore();

                case SYSTEM_FILE -> SystemFileConfigStore.getStore();

                case SYSTEM_PROCESS -> SystemProcessConfigStore.getStore();

                case SYSTEM_SERVICE -> SystemServiceConfigStore.getStore();

                case SNMP_OID_GROUP -> SNMPOIDGroupConfigStore.getStore();

                case SNMP_TRAP_LISTENER -> SNMPTrapListenerConfigStore.getStore();

                case SNMP_DEVICE_CATALOG -> SNMPDeviceCatalogConfigStore.getStore();

                case SNMP_TRAP_PROFILE -> SNMPTrapProfileConfigStore.getStore();

                case SNMP_TRAP_FORWARDER -> SNMPTrapForwarderConfigStore.getStore();

                case SMS_GATEWAY -> SMSGatewayConfigStore.getStore();

                case MAIL_SERVER -> MailServerConfigStore.getStore();

                case AUTH_TOKEN -> AuthTokenConfigStore.getStore();

                case BUSINESS_HOUR -> BusinessHourConfigStore.getStore();

                case GROUP -> GroupConfigStore.getStore();

                case SCHEDULER -> SchedulerConfigStore.getStore();

                case REMOTE_EVENT_PROCESSOR -> RemoteEventProcessorConfigStore.getStore();

                case PASSWORD_POLICY -> PasswordPolicyConfigStore.getStore();

                case OBJECT -> ObjectConfigStore.getStore();

                case ARCHIVED_OBJECT -> ArchivedObjectConfigStore.getStore();

                case METRIC -> MetricConfigStore.getStore();

                case METRIC_EXPLORER -> MetricExplorerConfigStore.getStore();

                case METRIC_PLUGIN -> MetricPluginConfigStore.getStore();

                case RUNBOOK_PLUGIN -> RunbookPluginConfigStore.getStore();

                case TOPOLOGY_PLUGIN -> TopologyPluginConfigStore.getStore();

                case MAC_SCANNER -> MACScannerConfigStore.getStore();

                case PROTOCOL_MAPPER -> ProtocolMapperConfigStore.getStore();

                case PROXY_SERVER -> ProxyServerConfigStore.getStore();

                case CREDENTIAL_PROFILE -> CredentialProfileConfigStore.getStore();

                case CUSTOM_MONITORING_FIELD -> CustomMonitoringFieldConfigStore.getStore();

                case DISCOVERY -> DiscoveryConfigStore.getStore();

                case LDAP_SERVER -> LDAPServerConfigStore.getStore();

                case LOG_PARSER -> LogParserConfigStore.getStore();

                case LOG_PARSER_PLUGIN -> LogParserPluginConfigStore.getStore();

                case REBRANDING -> RebrandingConfigStore.getStore();

                case MISC -> MiscConfigStore.getStore();

                case DEPENDENCY_MAPPER -> DependencyMapperConfigStore.getStore();

                case DASHBOARD -> DashboardConfigStore.getStore();

                case WIDGET -> WidgetConfigStore.getStore();

                case TEMPLATE -> TemplateConfigStore.getStore();

                case FLOW_SAMPLING_RATE -> FlowSamplingRateConfigStore.getStore();

                case FLOW_IP_GROUP -> FlowIPGroupConfigStore.getStore();

                case EVENT_SOURCE -> EventSourceConfigStore.getStore();

                case METRIC_POLICY -> MetricPolicyConfigStore.getStore();

                case FLOW_SETTINGS -> FlowSettingsConfigStore.getStore();

                case REPORT -> ReportConfigStore.getStore();

                case EVENT_POLICY -> EventPolicyConfigStore.getStore();

                case TAG_RULE -> TagRuleConfigStore.getStore();

                case DATA_RETENTION_POLICY -> DataRetentionPolicyConfigStore.getStore();

                case CONFIG_TEMPLATE -> ConfigTemplateConfigStore.getStore();

                case CONFIGURATION -> ConfigurationConfigStore.getStore();

                case BACKUP_PROFILE -> BackupProfileConfigStore.getStore();

                case STORAGE_PROFILE -> StorageProfileConfigStore.getStore();

                case ARTIFACT -> ArtifactConfigStore.getStore();

                case INTEGRATION -> IntegrationConfigStore.getStore();

                case PERSONAL_ACCESS_TOKEN -> PersonalAccessTokenConfigStore.getStore();

                case LOG_FORWARDER -> LogForwarderConfigStore.getStore();

                case FLOW_AS_MAPPER -> FlowASMapperConfigStore.getStore();

                case FLOW_DOMAIN_MAPPER -> FlowDomainMapperConfigStore.getStore();

                case FLOW_GEOLOCATION_MAPPER -> FlowGeolocationMapperConfigStore.getStore();

                case FLOW_IP_MAPPER -> FlowIPMapperConfigStore.getStore();

                case LOG_COLLECTOR -> LogCollectorConfigStore.getStore();

                case SINGLE_SIGN_ON -> SingleSignOnConfigStore.getStore();

                case TAG -> TagConfigStore.getStore();

                case INTEGRATION_PROFILE -> IntegrationProfileConfigStore.getStore();

                case COMPLIANCE_RULE -> ComplianceRuleConfigStore.getStore();

                case COMPLIANCE_BENCHMARK -> ComplianceBenchmarkConfigStore.getStore();

                case COMPLIANCE_POLICY -> CompliancePolicyConfigStore.getStore();

                case COMPLIANCE_WEIGHTED_CALCULATION -> ComplianceWeightedCalculationConfigStore.getStore();

                case TWO_FACTOR_AUTHENTICATION -> TwoAuthenticationConfigStore.getStore();

                case NETROUTE -> NetRouteConfigStore.getStore();

                case NETROUTE_POLICY -> NetRoutePolicyConfigStore.getStore();

                case SLO_PROFILE -> SLOProfileConfigStore.getStore();
            };
        }

        return null;
    }

    public Future<Void> initStore()
    {
        var promise = Promise.<Void>promise();

        try
        {
            if (!initDone.getAndSet(true) && table != null)
            {
                Bootstrap.configDBService().getAll(table, result ->
                {
                    if (result.succeeded())
                    {
                        items.clear();

                        if (result.result() != null && !result.result().isEmpty())
                        {
                            for (var index = 0; index < result.result().size(); index++)
                            {
                                var item = result.result().getJsonObject(index);

                                items.put(item.getLong(GlobalConstants.ID), item);

                                if (CommonUtil.debugEnabled())
                                {
                                    logger.debug(String.format("item added %s into the store", CommonUtil.removeSensitiveFields(item, false).encodePrettily()));
                                }
                            }
                        }

                        promise.complete();

                        logger.info(String.format("store %s initialized...", this.getClass().getSimpleName()));

                    }

                    else
                    {
                        promise.fail(result.cause());

                        logger.fatal(String.format("failed to init store %s , reason : %s...", this.getClass().getSimpleName(), result.cause()));
                    }

                });
            }

            else
            {
                logger.warn(table != null ? "store already initialized..." : "table is missing...");

                promise.complete();
            }

        }

        catch (Exception exception)
        {
            promise.fail(exception);

            logger.error(exception);
        }

        return promise.future();
    }

    public JsonObject getItem(long id)
    {
        return items.containsKey(id) ? items.get(id).copy() : null;
    }

    public JsonObject getItem(long id, boolean copy)//in some cases we do not require to copy of item as we just get information from it
    {
        return items.containsKey(id) ? (copy ? items.get(id).copy() : items.get(id)) : null;
    }

    public JsonObject getFilteredItem(long id)
    {
        return items.containsKey(id) ? filter(items.get(id).copy()) : null;
    }

    public boolean existItem(long id)
    {
        return items.containsKey(id);
    }

    public boolean existItems()
    {
        return !items.isEmpty();
    }

    public JsonObject getItem()
    {
        return !items.isEmpty() ? items.values().stream().findFirst().get().copy() : null;
    }

    public JsonArray getItems(JsonArray ids)
    {
        JsonArray items = null;

        if (ids != null && !ids.isEmpty())
        {
            items = new JsonArray(ids.stream().filter(this.items::containsKey).map(id -> this.items.get(id).copy()).collect(Collectors.toList()));

        }

        return items;
    }

    public JsonArray getItems()
    {
        return new JsonArray(this.items.values().parallelStream().map(JsonObject::copy).collect(Collectors.toList()));
    }

    public JsonArray getFilteredItems()
    {
        return new JsonArray(this.items.values().parallelStream().map(item -> filter(new JsonObject().mergeIn(item))).collect(Collectors.toList()));
    }

    protected JsonObject filter(JsonObject item)
    {
        var iterator = item.iterator();

        while (iterator.hasNext())
        {
            if (this.filters != null && !this.filters.contains(iterator.next().getKey()))
            {
                iterator.remove();
            }
        }

        return item;
    }

    public Map<Long, String> flatMap(String field)
    {
        var items = new HashMap<Long, String>();

        this.items.forEach((id, item) -> items.put(id, item.getString(field)));

        return items;
    }

    public Map<Long, String> flatMap(String field, JsonArray ids)
    {
        var items = new HashMap<Long, String>();

        ids.forEach(id -> items.put((long) id, this.items.get(id).getString(field)));

        return items;
    }

    public Map<Long, JsonObject> flatMap()
    {
        var items = new HashMap<Long, JsonObject>();

        this.items.forEach((id, item) -> items.put(id, JsonObject.mapFrom(item)));

        return items;
    }

    public JsonArray flatItems(String field)
    {
        return new JsonArray(items.values().parallelStream().filter(item -> item.getValue(field) != null).map(item -> item.getValue(field)).distinct().collect(Collectors.toList()));
    }

    public void deleteItem(long id)
    {
        items.remove(id);

        notify(REQUEST_DELETE, id, null);
    }

    public void deleteItems(JsonArray ids)
    {
        if (ids != null && !ids.isEmpty())
        {
            for (var index = 0; index < ids.size(); index++)
            {
                var id = ids.getLong(index);

                items.remove(id);

                notify(REQUEST_DELETE, id, null);
            }
        }
    }

    public Future<Void> addItem(long id)
    {
        return update(id, REQUEST_CREATE);
    }

    public void clear()
    {
        this.items.clear();
    }

    public Future<Void> updateItems(JsonArray ids)
    {
        var promise = Promise.<Void>promise();

        Bootstrap.configDBService().get(table,
                new JsonObject().put(DBConstants.FIELD_NAME, GlobalConstants.ID).put(VALUE, ids),
                result ->
                {
                    if (result.succeeded())
                    {
                        for (var index = 0; index < result.result().size(); index++)
                        {
                            var item = result.result().getJsonObject(index);

                            items.put(item.getLong(GlobalConstants.ID), item);

                            notify(REQUEST_UPDATE, item.getLong(GlobalConstants.ID), item);
                        }

                        promise.complete();
                    }

                    else
                    {
                        promise.fail(result.cause());
                    }
                });

        return promise.future();
    }

    public Future<Void> updateItem(long id)
    {
        return update(id, REQUEST_UPDATE);
    }

    public void addItem(long id, JsonObject item)
    {
        if (item != null)
        {
            this.items.put(id, item);
        }
    }

    private Future<Void> update(long id, String request)
    {
        var promise = Promise.<Void>promise();

        try
        {
            Bootstrap.configDBService().getById(table, id, result ->
            {
                if (result.succeeded())
                {
                    if (result.result() != null)
                    {
                        items.put(id, result.result());
                    }

                    promise.complete();

                    notify(request, id, result.result());
                }

                else
                {
                    promise.fail(result.cause());
                }
            });
        }

        catch (Exception exception)
        {
            promise.fail(exception);

            logger.error(exception);
        }

        return promise.future();
    }

    public JsonObject enrich()
    {
        JsonObject event = null;

        if (this.table != null && this.notify)
        {
            var items = new JsonArray();

            for (var item : this.items.values())
            {
                items.add(item);
            }

            event = new JsonObject();

            event.put(this.table, items);
        }

        return event;
    }

    public void notify(String request, long id, JsonObject item)
    {

        if (this.table != null && this.notify)
        {
            if (request.equalsIgnoreCase(REQUEST_DELETE))
            {
                Bootstrap.vertx().eventBus().send(EventBusConstants.EVENT_PUBLICATION,
                        new JsonObject().put(EventBusConstants.EVENT_TYPE, EventBusConstants.EVENT_CHANGE_NOTIFICATION)
                                .put(EventBusConstants.EVENT_TOPIC, EventBusConstants.REMOTE_EVENT_PROCESSOR_TOPIC)
                                .put(ENTITY_TABLE, this.table)
                                .put(EventBusConstants.CHANGE_NOTIFICATION_TYPE, EventBusConstants.ChangeNotificationType.CONFIG_CHANGE.name())
                                .put(REQUEST, request)
                                .put(GlobalConstants.ID, id));
            }
            else
            {
                Bootstrap.vertx().eventBus().send(EventBusConstants.EVENT_PUBLICATION,
                        new JsonObject().put(EventBusConstants.EVENT_TYPE, EventBusConstants.EVENT_CHANGE_NOTIFICATION)
                                .put(EventBusConstants.EVENT_TOPIC, EventBusConstants.REMOTE_EVENT_PROCESSOR_TOPIC)
                                .put(ENTITY_TABLE, this.table)
                                .put(EventBusConstants.CHANGE_NOTIFICATION_TYPE, EventBusConstants.ChangeNotificationType.CONFIG_CHANGE.name())
                                .put(REQUEST, request)
                                .put(GlobalConstants.ID, id)
                                .put(GlobalConstants.RESULT, item));
            }
        }
    }

    public Future<Void> updateStore()
    {
        initDone.set(false);

        return initStore();
    }

    public JsonArray getIds()
    {
        return new JsonArray(new ArrayList(items.keySet()));
    }

    public JsonArray getItemsByValues(String field, JsonArray values)
    {
        if (field.equalsIgnoreCase(GlobalConstants.ID))
        {
            return getItems(values);
        }

        else
        {
            return new JsonArray(items.values().parallelStream()
                    .filter(item -> values.contains(item.getValue(field)))
                    .map(JsonObject::copy)
                    .collect(Collectors.toList()));

        }
    }

    public JsonArray getItemsByValue(String field, String value)
    {
        return new JsonArray(items.values().parallelStream()
                .filter(item -> item.containsKey(field) && item.getString(field).equalsIgnoreCase(value))
                .map(JsonObject::copy)
                .collect(Collectors.toList()));
    }

    public JsonArray flatItemsByMultiValueField(String field, Object value, String flatField)
    {

        return new JsonArray(items.values().parallelStream()
                .filter(item -> item.getJsonArray(field) != null && item.getJsonArray(field).contains(value))
                .map(item -> item.getValue(flatField))
                .collect(Collectors.toList()));

    }

    public JsonArray flatItemsByValueField(String field, Object value, String flatField)
    {
        return new JsonArray(items.values().parallelStream()
                .filter(item -> item.containsKey(flatField) && item.getValue(field) != null && item.getValue(field).equals(value))
                .map(item -> item.getValue(flatField))
                .collect(Collectors.toList()));

    }

    public JsonArray flatItemsByMultiValueFields(String field1, Object value, String flatField, String field2)
    {

        return new JsonArray(items.values().parallelStream()
                .filter(item -> item.getJsonArray(field1) != null && item.getJsonArray(field2) != null && !item.getJsonArray(field2).isEmpty() && item.getJsonArray(field1).contains(value))
                .map(item -> item.getValue(flatField))
                .collect(Collectors.toList()));

    }

    public JsonArray getItemsByMultiValueField(String field, Object value)
    {

        return new JsonArray(items.values().stream()
                .filter(item -> item.getJsonArray(field) != null && item.getJsonArray(field).contains(value))
                .map(JsonObject::copy)
                .collect(Collectors.toList()));

    }

    public JsonArray getItemsByMapValueField(String key, String field, Object value)
    {

        return new JsonArray(items.values().stream()
                .filter(item -> item.getJsonObject(key) != null && item.getJsonObject(key).containsKey(field) && item.getJsonObject(key).getValue(field).equals(value))
                .map(JsonObject::copy)
                .collect(Collectors.toList()));

    }

    //fetch the item if field contains particular value and value has given id.
    public JsonArray getItemsByMapMultiValueField(String key, String field, Object value)
    {

        return new JsonArray(items.values().stream()
                .filter(item -> item.getJsonObject(key) != null && item.getJsonObject(key).containsKey(field) && item.getJsonObject(key).getJsonArray(field).contains(value))
                .map(JsonObject::copy)
                .collect(Collectors.toList()));

    }

    public JsonArray getItemsByMapMultiValueField(String key, String field, JsonArray values)
    {

        return new JsonArray(items.values().stream()
                .filter(item -> item.getJsonObject(key) != null && item.getJsonObject(key).containsKey(field) && values.contains(item.getJsonObject(key).getValue(field)))
                .map(JsonObject::copy)
                .collect(Collectors.toList()));

    }

    public JsonArray getItemsByMultiValueFieldAny(String field, JsonArray values)
    {

        return new JsonArray(items.values().stream()
                .filter(item -> item.getJsonArray(field) != null && !item.getJsonArray(field).isEmpty() && item.getJsonArray(field).stream().anyMatch(values::contains))
                .map(JsonObject::copy)
                .collect(Collectors.toList()));

    }

    public JsonArray getItemsByMultiValueFieldAll(String field, JsonArray values)
    {

        return new JsonArray(items.values().stream()
                .filter(item -> item.getJsonArray(field) != null && !item.getJsonArray(field).isEmpty() && item.getJsonArray(field).stream().allMatch(values::contains))
                .map(JsonObject::copy)
                .collect(Collectors.toList()));

    }

    public JsonArray getDistinctMapItems(String key, String field, Object value, String flatField)
    {
        return new JsonArray(this.items.values().stream()
                .filter(item -> item.getJsonObject(key) != null && item.getJsonObject(key).containsKey(field) && item.getJsonObject(key).getValue(field).equals(value) && item.containsKey(flatField))
                .map(item -> item.getLong(flatField))
                .distinct()
                .collect(Collectors.toList()));
    }

    public JsonArray getItemsByMapMultiValueField(String key, String field, Object value, String field2, JsonArray values)
    {
        return new JsonArray(this.items.values().stream()
                .filter(item -> item.getJsonObject(key) != null && item.getJsonObject(key).containsKey(field) &&
                        item.getJsonObject(key).getValue(field).equals(value) && values.contains(item.getValue(field2)))
                .map(item -> item.getLong(field2))
                .distinct().toList());
    }

    public JsonArray flatItems(String field, Object value, String flatField)
    {

        return new JsonArray(items.values().parallelStream()
                .filter(item -> item.containsKey(field) && item.getValue(field).equals(value))
                .map(item -> item.getValue(flatField))
                .distinct().collect(Collectors.toList()));

    }

    public JsonArray getItemsByValue(String field, int value)
    {

        return new JsonArray(items.values().stream()
                .filter(item -> item.getInteger(field) == value)
                .map(JsonObject::copy)
                .collect(Collectors.toList()));
    }

    public JsonArray getItemsByValue(String field, Long value)
    {

        return new JsonArray(items.values().stream()
                .filter(item -> item.containsKey(field) && item.getLong(field).equals(value))
                .map(JsonObject::copy)
                .collect(Collectors.toList()));

    }

    public JsonArray getItemsByValue(String field, boolean value)
    {
        return new JsonArray(items.values().stream()
                .filter(item -> item.getBoolean(field) == value)
                .map(JsonObject::copy)
                .collect(Collectors.toList()));
    }

    public JsonObject getItemByValue(String field, String value)
    {
        var result = items.values().stream()
                .filter(item -> item.getString(field) != null && item.getString(field).equalsIgnoreCase(value))
                .findFirst().orElse(null);

        return result != null ? result.copy() : null;
    }

    public JsonObject getItemByValue(String field, Long value)
    {
        if (field.equalsIgnoreCase(GlobalConstants.ID))
        {
            return getItem(value);
        }
        else
        {
            var result = items.values().stream()
                    .filter(item -> item.containsKey(field) && item.getLong(field).equals(value))
                    .findFirst().orElse(null);

            return result != null ? result.copy() : null;
        }
    }

    public JsonObject getItemByValue(String field, int value)
    {


        var result = items.values().stream()
                .filter(item -> item.containsKey(field) && item.getInteger(field) == value)
                .findFirst().orElse(null);

        return result != null ? result.copy() : null;
    }

    public JsonObject getItemByValue(String field, boolean value)
    {

        var result = items.values().stream()
                .filter(item -> item.getBoolean(field) == value)
                .findFirst().orElse(null);

        return result != null ? result.copy() : null;

    }

    public JsonArray flatItemsByValues(String field, JsonArray values, String flatField)
    {

        return new JsonArray(items.values().stream()
                .filter(item -> item.containsKey(flatField) && values.contains(item.getValue(field)))
                .map(item -> item.getValue(flatField))
                .collect(Collectors.toList()));

    }

    public JsonArray flatItemsByMultipleValue(String field, JsonArray values, String flatField, String flatValue)
    {

        return new JsonArray(items.values().stream()
                .filter(item -> values.contains(item.getValue(field)) && item.getValue(flatField).equals(flatValue))
                .map(item -> item.getLong(GlobalConstants.ID))
                .collect(Collectors.toList()));

    }

    public JsonArray flatItemsByValue(String field, Object value, String flatField, long flatValue)
    {

        return new JsonArray(items.values().stream()
                .filter(item -> item.getValue(field) != null && item.getValue(field).equals(value) && item.getValue(flatField) != null && CommonUtil.getLong(item.getValue(flatField)) == flatValue)
                .map(JsonObject::copy)
                .collect(Collectors.toList()));

    }

    public JsonArray flatItemsByValue(String field, String value, String flatField, String flatValue)
    {

        return new JsonArray(items.values().stream()
                .filter(item -> item.getString(field) != null && item.getString(field).equalsIgnoreCase(value)
                        && item.getString(flatField) != null && item.getString(flatField).equalsIgnoreCase(flatValue))
                .map(JsonObject::copy)
                .collect(Collectors.toList()));

    }

    public JsonObject flatItemByMultipleValues(String field, String value, String flatField, String flatValue, String flatField2, String flatValue2)
    {

        return items.values().stream()
                .filter(item -> item.getString(field) != null && item.getString(field).equalsIgnoreCase(value) && item.getString(flatField) != null && item.getString(flatField).equalsIgnoreCase(flatValue)
                        && item.getString(flatField2) != null && item.getString(flatField2).equalsIgnoreCase(flatValue2))
                .map(JsonObject::copy)
                .findFirst().orElse(null);

    }

    public JsonArray flatItemsByMultipleValues(String field, JsonArray values, String flatField, JsonArray flatValues)
    {
        return new JsonArray(items.values().stream()
                .filter(item -> values.contains(item.getValue(field)) && flatValues.contains(item.getValue(flatField)))
                .map(item -> item.getLong(GlobalConstants.ID))
                .collect(Collectors.toList()));
    }

    public JsonArray flatItemsByMultiValueFieldAny(String field, JsonArray values, String flatField)
    {

        return new JsonArray(items.values().stream()
                .filter(item -> item.getJsonArray(field) != null && !item.getJsonArray(field).isEmpty() && item.getJsonArray(field).stream().anyMatch(values::contains))
                .map(item -> item.getValue(flatField))
                .collect(Collectors.toList()));

    }

    public Map<Long, Integer> getUsedCount(String field, boolean multiValue)
    {
        var count = new HashMap<Long, Integer>();

        this.items.values().forEach(item ->
        {

            if (item.containsKey(field))
            {
                if (!multiValue)
                {
                    count.put(item.getLong(field), count.getOrDefault(item.getLong(field), GlobalConstants.DUMMY_NUMERIC_VALUE) + 1);
                }
                else
                {
                    item.getJsonArray(field).forEach(id -> count.put(CommonUtil.getLong(id), count.getOrDefault(CommonUtil.getLong(id), GlobalConstants.DUMMY_NUMERIC_VALUE) + 1));
                }
            }
        });

        return count;
    }

    public Future<JsonObject> getReferenceEntities(long id)
    {
        var promise = Promise.<JsonObject>promise();

        if (this.referenceEntities != null && !this.referenceEntities.isEmpty())
        {
            Bootstrap.vertx().<JsonObject>executeBlocking(future ->
            {
                var referenceEntities = new JsonObject();

                this.referenceEntities.forEach(referenceEntity ->
                {
                    var store = getConfigStore((ConfigStore) referenceEntity.get(REFERENCE_ENTITY_STORE));

                    var refProperty = CommonUtil.getString(referenceEntity.get(REFERENCE_ENTITY_PROPERTY));

                    var refEntity = (Entity) referenceEntity.get(REFERENCE_ENTITY);

                    JsonArray entities;

                    switch ((ReferenceEntityPropertyType) referenceEntity.get(REFERENCE_ENTITY_PROPERTY_TYPE))
                    {
                        case VALUE ->
                        {
                            entities = store.getItemsByValue(refProperty, id);

                            if (entities != null && !entities.isEmpty())
                            {
                                referenceEntities.put(refEntity.getName(), new JsonArray(entities.stream()
                                        .map(item -> CommonUtil.removeSensitiveFields(JsonObject.mapFrom(item), true))
                                        .collect(Collectors.toList())));
                            }
                        }

                        //Fetch the entities if any monitor is used in scheduler
                        case MULTIMAP_VALUE ->
                        {
                            entities = store.getItemsByMapMultiValueField(refProperty, CommonUtil.getString(referenceEntity.get(REFERENCE_ENTITY_NESTED_PROPERTY)), id);

                            if (entities != null && !entities.isEmpty())
                            {
                                referenceEntities.put(refEntity.getName(), new JsonArray(entities.stream()
                                        .map(item -> CommonUtil.removeSensitiveFields(JsonObject.mapFrom(item), true))
                                        .collect(Collectors.toList())));
                            }
                        }

                        case CONTEXT_VALUE ->
                        {
                            entities = store.flatItemsByValue(CommonUtil.getString(referenceEntity.get(REFERENCE_ENTITY_CONTEXT_PROP_KEY)), referenceEntity.get(REFERENCE_ENTITY_CONTEXT_PROP_VALUE)
                                    , CommonUtil.getString(referenceEntity.get(REFERENCE_ENTITY_CONTEXT_PROP_CONDITION)), id);

                            if (entities != null && !entities.isEmpty())
                            {
                                referenceEntities.put(refEntity.getName(), new JsonArray(entities.stream()
                                        .map(item -> CommonUtil.removeSensitiveFields(JsonObject.mapFrom(item), true))
                                        .collect(Collectors.toList())));
                            }
                        }

                        case MULTI_VALUE ->
                        {
                            entities = store.getItemsByMultiValueField(refProperty, id);

                            if (entities != null && !entities.isEmpty())
                            {
                                referenceEntities.put(refEntity.getName(), new JsonArray(entities.stream()
                                        .map(item -> CommonUtil.removeSensitiveFields(JsonObject.mapFrom(item), true))
                                        .collect(Collectors.toList())));
                            }
                        }
                    }


                });

                future.complete(referenceEntities);

            }, result ->
            {
                if (result.succeeded())
                {
                    promise.complete(result.result());
                }

                else
                {
                    logger.error(result.cause());

                    promise.fail(result.cause());
                }
            });

        }

        else
        {
            promise.complete(new JsonObject());
        }

        return promise.future();

    }


    /**
     * MOTADATA-1772 - API Server not performing -optimally - CBI Observation
     * Changed method to calculate used count.
     * Before : If we have 23000 SNMP catalog and 15000 devices then, it was calculating used count by iterating 15000 devices for each catalog i.e. total iteration in worst case would be 23000 * 15000
     * After : Now, we are calculating reference mapping first, by iterating 15000 devices only at one time and then iterating 23000 device.
     * Performance : Before it was taking 20-30 second. Now, it is taking 400-500 ms.
     */
    public Future<JsonObject> getReferenceCountsByItem()
    {
        var promise = Promise.<JsonObject>promise();

        if (this.referenceEntities != null && !this.referenceEntities.isEmpty())
        {
            Bootstrap.vertx().<JsonObject>executeBlocking(future ->
            {
                var referenceCounts = new JsonObject();

                for (var referenceEntity : this.referenceEntities)
                {
                    var store = getConfigStore((ConfigStore) referenceEntity.get(REFERENCE_ENTITY_STORE));

                    var refProperty = CommonUtil.getString(referenceEntity.get(REFERENCE_ENTITY_PROPERTY));

                    var type = referenceEntity.get(REFERENCE_ENTITY_PROPERTY_TYPE);

                    Map<Long, Integer> entities = null;

                    if (type.equals(ReferenceEntityPropertyType.VALUE))
                    {
                        entities = store.getUsedCount(refProperty, false);
                    }
                    else if (type.equals(ReferenceEntityPropertyType.MULTI_VALUE))
                    {
                        entities = store.getUsedCount(refProperty, true);
                    }

                    if (entities != null && !entities.isEmpty())
                    {
                        for (var id : items.keySet())
                        {
                            if (entities.containsKey(id))
                            {
                                var key = CommonUtil.getString(id);

                                referenceCounts.put(key, referenceCounts.containsKey(key) ? referenceCounts.getInteger(key) + entities.get(id) : entities.get(id));
                            }
                        }
                    }
                }

                future.complete(referenceCounts);

            }, result ->
            {
                if (result.succeeded())
                {
                    promise.complete(result.result());
                }

                else
                {
                    logger.error(result.cause());

                    promise.fail(result.cause());
                }
            });

        }

        else
        {
            promise.complete(new JsonObject());
        }

        return promise.future();

    }

    public enum ConfigStore
    {
        METRIC_PLUGIN,
        RUNBOOK_PLUGIN,
        TOPOLOGY_PLUGIN,
        MAC_SCANNER,
        AGENT,
        APPLICATION_MAPPER,
        AUTH_TOKEN,
        BUSINESS_HOUR,
        CREDENTIAL_PROFILE,
        OBJECT,
        ARCHIVED_OBJECT,
        CUSTOM_MONITORING_FIELD,
        DISCOVERY,
        GROUP,
        LDAP_SERVER,
        LOG_PARSER,
        MAIL_SERVER,
        METRIC,
        METRIC_EXPLORER,
        PASSWORD_POLICY,
        PROTOCOL_MAPPER,
        PROXY_SERVER,
        REMOTE_EVENT_PROCESSOR,
        SCHEDULER,
        SMS_GATEWAY,
        SNMP_DEVICE_CATALOG,
        SNMP_OID_GROUP,
        SNMP_TRAP_PROFILE,
        SNMP_TRAP_FORWARDER,
        SNMP_TRAP_LISTENER,
        SYSTEM_FILE,
        SYSTEM_PROCESS,
        SYSTEM_SERVICE,
        USER,
        MY_ACCOUNT,
        REBRANDING,
        USER_ROLE,
        MISC,
        DEPENDENCY_MAPPER,
        DASHBOARD,
        WIDGET,
        TEMPLATE,
        FLOW_SAMPLING_RATE,
        FLOW_IP_GROUP,
        EVENT_SOURCE,
        LOG_PARSER_PLUGIN,
        METRIC_POLICY,
        FLOW_SETTINGS,
        REPORT,
        EVENT_POLICY,
        DATA_RETENTION_POLICY,
        CONFIG_TEMPLATE,
        CONFIGURATION,
        BACKUP_PROFILE,
        STORAGE_PROFILE,
        ARTIFACT,
        INTEGRATION,
        PERSONAL_ACCESS_TOKEN,
        LOG_FORWARDER,
        FLOW_AS_MAPPER,
        FLOW_DOMAIN_MAPPER,
        FLOW_GEOLOCATION_MAPPER,
        FLOW_IP_MAPPER,
        LOG_COLLECTOR,
        SINGLE_SIGN_ON,
        TAG,
        INTEGRATION_PROFILE,
        COMPLIANCE_RULE,
        COMPLIANCE_BENCHMARK,
        COMPLIANCE_POLICY,
        COMPLIANCE_WEIGHTED_CALCULATION,
        TWO_FACTOR_AUTHENTICATION,
        TAG_RULE,
        NETROUTE,
        NETROUTE_POLICY,
        SLO_PROFILE
    }
}
