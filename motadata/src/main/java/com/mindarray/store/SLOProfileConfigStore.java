package com.mindarray.store;

import com.mindarray.GlobalConstants;
import com.mindarray.api.SLOProfile;
import com.mindarray.db.DBConstants;
import com.mindarray.util.Logger;
import io.vertx.core.Future;
import io.vertx.core.Promise;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

public class SLOProfileConfigStore extends AbstractConfigStore
{
    private static final Logger LOGGER = new Logger(SLOProfileConfigStore.class, GlobalConstants.MOTADATA_STORE, "SLO Profile Config Store");

    private static final SLOProfileConfigStore STORE = new SLOProfileConfigStore();

    private final Map<Long, JsonObject> activeSLOProfiles = new ConcurrentHashMap<>();

    private final AtomicInteger COUNTER = new AtomicInteger(0);

    private SLOProfileConfigStore()
    {
        super(DBConstants.TBL_SLO_PROFILE, LOGGER, false);
    }

    public static SLOProfileConfigStore getStore()
    {
        return STORE;
    }

    @Override
    public Future<Void> initStore()
    {
        var promise = Promise.<Void>promise();

        super.initStore().onComplete(result ->
        {
            if (result.succeeded())
            {
                try
                {
                    if (!items.isEmpty())
                    {
                        for (var item : items.values())
                        {
                            if (item.containsKey(SLOProfile.SLO_LAST_ACTIVE_CYCLE_START_DATE))
                            {
                                activeSLOProfiles.put(item.getLong(GlobalConstants.ID), item);
                            }
                        }
                    }

                    promise.complete();
                }
                catch (Exception exception)
                {
                    promise.fail(exception);

                    LOGGER.warn("failed to init SLO profile config store...");

                    LOGGER.error(exception);
                }
            }
            else
            {
                promise.fail(result.cause());
            }
        });

        return promise.future();
    }

    @Override
    public Future<Void> updateItem(long id)
    {
        return super.updateItem(id).onComplete(result ->
        {
            var item = items.get(id);

            if ((!item.containsKey(SLOProfile.SLO_ARCHIVED) || item.getString(SLOProfile.SLO_ARCHIVED).equalsIgnoreCase(GlobalConstants.NO)) && item.getLong(SLOProfile.SLO_LAST_ACTIVE_CYCLE_START_DATE) != null)
            {
                activeSLOProfiles.put(item.getLong(GlobalConstants.ID), item);
            }
        });
    }

    public JsonArray getActiveSLOProfiles()
    {
        return new JsonArray(this.activeSLOProfiles.values().parallelStream().collect(Collectors.toList()));
    }
}
