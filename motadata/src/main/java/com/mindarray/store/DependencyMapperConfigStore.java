/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

package com.mindarray.store;

import com.mindarray.GlobalConstants;
import com.mindarray.api.DependencyMapper;
import com.mindarray.db.DBConstants;
import com.mindarray.util.Logger;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

public class DependencyMapperConfigStore extends AbstractConfigStore
{
    private static final Logger LOGGER = new Logger(DependencyMapperConfigStore.class, GlobalConstants.MOTADATA_STORE, "Dependency Mapper Config Store");

    private static final DependencyMapperConfigStore STORE = new DependencyMapperConfigStore();

    private DependencyMapperConfigStore()
    {
        super(DBConstants.TBL_DEPENDENCY_MAPPER, LOGGER, false);
    }

    public static DependencyMapperConfigStore getStore()
    {
        return STORE;
    }

    public Map<String, List<JsonObject>> getItemsByObject(String target)
    {
        var items = new HashMap<String, List<JsonObject>>();

        items.put(DependencyMapper.PARENTS, new ArrayList<>(1));

        items.put(DependencyMapper.CHILDREN, new ArrayList<>(1));

        for (var item : this.items.values())
        {
            if (item.getString(DependencyMapper.DEPENDENCY_MAPPER_PARENT).equalsIgnoreCase(target))
            {
                items.get(DependencyMapper.PARENTS).add(item.copy());
            }
            else if (item.getString(DependencyMapper.DEPENDENCY_MAPPER_CHILD).equalsIgnoreCase(target))
            {
                items.get(DependencyMapper.CHILDREN).add(item.copy());
            }
        }

        return items;
    }

    public JsonArray getParents()
    {
        return new JsonArray(this.items.values().stream().filter(item -> ObjectConfigStore.getStore().getItemByIP(item.getString(DependencyMapper.DEPENDENCY_MAPPER_PARENT)) != null)
                .map(item -> ObjectConfigStore.getStore().getItemByIP(item.getString(DependencyMapper.DEPENDENCY_MAPPER_PARENT))).distinct().collect(Collectors.toList()));
    }
}
