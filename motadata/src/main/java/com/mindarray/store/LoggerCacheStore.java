/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

/* Change Logs:
 *   Date           Author              Notes
 *   28-Feb-2025    Smit Prajapati      MOTADATA-5431: Module level logging
 */

package com.mindarray.store;

import com.mindarray.util.MotadataConfigUtil;
import io.vertx.core.json.JsonArray;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

public class LoggerCacheStore extends AbstractCacheStore
{
    private static final LoggerCacheStore STORE = new LoggerCacheStore();

    private final Map<String, Boolean> items = new ConcurrentHashMap<>(); // key: object.id + # + instance.name -> value: instance.ip

    private LoggerCacheStore()
    {
    }

    public static LoggerCacheStore getStore()
    {
        return STORE;
    }

    /*
     *  when log level is changed to debug or trace we will
     *  reset the map and update modules according to request
     * */
    public void update(JsonArray modules)
    {
        try
        {
            items.replaceAll((key, value) -> false);

            for (var index = 0; index < modules.size(); index++)
            {
                items.put(modules.getString(index), true);
            }
        }
        catch (Exception ignore)
        {
        }
    }

    public boolean qualify(String module)
    {
        return MotadataConfigUtil.devMode() || items.getOrDefault(module, false);
    }

    public void add(String module)
    {
        items.put(module, false);
    }

    public Map<String, Boolean> getItems()
    {
        return items;
    }

}
