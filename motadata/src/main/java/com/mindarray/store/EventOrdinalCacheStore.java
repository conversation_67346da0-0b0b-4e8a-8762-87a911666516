/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

package com.mindarray.store;

import com.mindarray.Bootstrap;
import com.mindarray.GlobalConstants;
import com.mindarray.log.LogEngineConstants;
import com.mindarray.util.CodecUtil;
import com.mindarray.util.CommonUtil;
import com.mindarray.util.Logger;
import io.vertx.core.Future;
import io.vertx.core.Promise;
import io.vertx.core.buffer.Buffer;
import io.vertx.core.json.JsonObject;

import java.io.File;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicBoolean;

import static com.mindarray.GlobalConstants.*;

public class EventOrdinalCacheStore extends AbstractCacheStore
{
    private static final EventOrdinalCacheStore STORE = new EventOrdinalCacheStore();

    private static final Logger LOGGER = new Logger(EventOrdinalCacheStore.class, GlobalConstants.MOTADATA_STORE, "Event Ordinal Cache Store");
    private final Map<String, Integer> items = new ConcurrentHashMap<>();//it will have pluginid+eventcategory+columns as key and value will be ordinalid
    private final Map<Integer, String> itemsByOrdinal = new ConcurrentHashMap<>();//it will have pluginid+eventcategory+columns as key and value will be ordinalid

    private final AtomicBoolean dirty = new AtomicBoolean();

    private EventOrdinalCacheStore()
    {
    }

    public static EventOrdinalCacheStore getStore()
    {
        return STORE;
    }

    @Override
    public Future<Void> initStore()
    {
        var promise = Promise.<Void>promise();

        loadOrdinals();

        promise.complete();

        LOGGER.info(String.format("store %s initialized...", this.getClass().getSimpleName()));

        return promise.future();
    }

    public int getOrdinal(String key)
    {
        if (!items.containsKey(key))
        {
            items.put(key, items.size() + 1);

            dirty.set(true);
        }

        if (!itemsByOrdinal.containsKey(items.get(key)))
        {
            itemsByOrdinal.put(items.get(key), key);
        }

        return items.get(key);
    }

    public String getValue(int ordinal)
    {
        return itemsByOrdinal.get(ordinal);
    }

    private void loadOrdinals()
    {
        try
        {
            var file = new File(CURRENT_DIR + PATH_SEPARATOR + CONFIG_DIR + PATH_SEPARATOR + LogEngineConstants.EVENT_ORDINALS);

            if (file.exists())
            {
                var buffer = Bootstrap.vertx().fileSystem().readFileBlocking(file.getPath());

                if (buffer != null && buffer.getBytes().length > 0)
                {
                    var context = new JsonObject(Buffer.buffer(CodecUtil.toBytes(buffer.getBytes()))).getMap();

                    ((Map<String, Object>) context.get("event.ordinals")).forEach((key, value) ->
                    {

                        items.put(key, CommonUtil.getInteger(value));

                        itemsByOrdinal.put(CommonUtil.getInteger(value), key);
                    });

                    LOGGER.info("loaded event ordinals");
                }
            }

            else
            {
                Bootstrap.vertx().fileSystem().createFileBlocking(file.getPath());
            }
        }

        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }

    public boolean dirty()
    {
        return dirty.get();
    }

    public void setDirty(boolean value)
    {
        dirty.set(value);
    }

    public void dump(String path)
    {
        if (!items.isEmpty())
        {
            Bootstrap.vertx().<Void>executeBlocking(promise ->
            {
                try
                {
                    Bootstrap.vertx().fileSystem().writeFileBlocking(path,
                            Buffer.buffer(CodecUtil.compress(new JsonObject().put("event.ordinals", new HashMap<>(items)).encode().getBytes())));

                    dirty.set(true);
                }

                catch (Exception exception)
                {
                    LOGGER.error(exception);
                }

                promise.complete();
            });
        }
    }
}
