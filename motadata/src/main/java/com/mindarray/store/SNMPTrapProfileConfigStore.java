/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

package com.mindarray.store;

import com.mindarray.Bootstrap;
import com.mindarray.GlobalConstants;
import com.mindarray.api.SNMPTrapForwarder;
import com.mindarray.api.SNMPTrapProfile;
import com.mindarray.db.DBConstants;
import com.mindarray.util.Logger;
import io.vertx.core.Future;
import io.vertx.core.Promise;
import io.vertx.core.json.JsonObject;

import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

import static com.mindarray.api.APIConstants.*;

public class SNMPTrapProfileConfigStore extends AbstractConfigStore
{

    private static final Logger LOGGER = new Logger(SNMPTrapProfileConfigStore.class, GlobalConstants.MOTADATA_STORE, "SNMP Trap Profile Config Store");

    private static final SNMPTrapProfileConfigStore STORE = new SNMPTrapProfileConfigStore();
    private final Map<String, Long> filterProfiles = new ConcurrentHashMap<>();
    private final Map<String, Long> clearProfilesByOID = new ConcurrentHashMap<>();
    private final Map<Long, Integer> clearProfilesByTimer = new ConcurrentHashMap<>();
    private final Map<String, JsonObject> itemsByOID = new ConcurrentHashMap<>();

    private SNMPTrapProfileConfigStore()
    {
        super(DBConstants.TBL_SNMP_TRAP_PROFILE, LOGGER, false, List.of(
                Map.of(
                        REFERENCE_ENTITY, Entity.SNMP_TRAP_FORWARDER,
                        REFERENCE_ENTITY_PROPERTY, SNMPTrapForwarder.SNMP_TRAP_FORWARDER_PROFILES,
                        REFERENCE_ENTITY_STORE, ConfigStore.SNMP_TRAP_FORWARDER,
                        REFERENCE_ENTITY_PROPERTY_TYPE, ReferenceEntityPropertyType.MULTI_VALUE
                )
        ));
    }

    public static SNMPTrapProfileConfigStore getStore()
    {
        return STORE;
    }

    @Override
    public Future<Void> initStore()
    {

        var promise = Promise.<Void>promise();

        super.initStore().onComplete(result ->
        {

            if (result.succeeded())
            {
                init();

                promise.complete();
            }

            else
            {
                promise.fail(result.cause());
            }
        });

        return promise.future();
    }

    @Override
    public Future<Void> addItem(long id)
    {
        var promise = Promise.<Void>promise();

        super.addItem(id).onComplete(result ->
        {
            try
            {
                if (result.succeeded())
                {
                    var item = getItem(id);

                    if (item.getString(SNMPTrapProfile.SNMP_TRAP_PROFILE_DROP_STATUS).equalsIgnoreCase(GlobalConstants.YES))
                    {
                        filterProfiles.put(item.getString(SNMPTrapProfile.SNMP_TRAP_PROFILE_OID), item.getLong(GlobalConstants.ID));
                    }

                    itemsByOID.put(item.getString(SNMPTrapProfile.SNMP_TRAP_PROFILE_OID), item);

                    if (item.containsKey(SNMPTrapProfile.SNMP_TRAP_PROFILE_AUTO_CLEAR_STATUS) && item.getString(SNMPTrapProfile.SNMP_TRAP_PROFILE_AUTO_CLEAR_STATUS).equalsIgnoreCase(GlobalConstants.YES))
                    {
                        if (item.getString(SNMPTrapProfile.SNMP_TRAP_PROFILE_AUTO_CLEAR_OID) != null)
                        {
                            clearProfilesByOID.put(item.getString(SNMPTrapProfile.SNMP_TRAP_PROFILE_AUTO_CLEAR_OID), item.getLong(GlobalConstants.ID));
                        }

                        if (item.getInteger(SNMPTrapProfile.SNMP_TRAP_PROFILE_AUTO_CLEAR_TIMER) != null)
                        {
                            clearProfilesByTimer.put(item.getLong(GlobalConstants.ID), item.getInteger(SNMPTrapProfile.SNMP_TRAP_PROFILE_AUTO_CLEAR_TIMER));
                        }
                    }
                }

                promise.complete();
            }
            catch (Exception exception)
            {
                LOGGER.error(exception);

                promise.fail(exception);
            }
        });
        return promise.future();
    }

    private void init()
    {
        Bootstrap.vertx().<Void>executeBlocking(future ->
        {
            try
            {
                filterProfiles.clear();

                itemsByOID.clear();

                clearProfilesByOID.clear();

                clearProfilesByTimer.clear();

                items.values().parallelStream().filter(item -> item.getString(SNMPTrapProfile.SNMP_TRAP_PROFILE_DROP_STATUS) != null && item.getString(SNMPTrapProfile.SNMP_TRAP_PROFILE_DROP_STATUS).equalsIgnoreCase(GlobalConstants.YES))
                        .collect(Collectors.groupingBy(item -> item.getString(SNMPTrapProfile.SNMP_TRAP_PROFILE_OID), Collectors.mapping(item -> item.getLong(GlobalConstants.ID), Collectors.toList())))
                        .forEach((key, value) -> filterProfiles.put(key, value.getFirst()));

                items.values().parallelStream().filter(item -> item.getString(SNMPTrapProfile.SNMP_TRAP_PROFILE_OID) != null)
                        .collect(Collectors.groupingBy(item -> item.getString(SNMPTrapProfile.SNMP_TRAP_PROFILE_OID), Collectors.toList()))
                        .forEach((key, values) -> itemsByOID.put(key, values.getFirst()));

                items.values().parallelStream().filter(item -> item.containsKey(SNMPTrapProfile.SNMP_TRAP_PROFILE_AUTO_CLEAR_STATUS) &&
                                item.getString(SNMPTrapProfile.SNMP_TRAP_PROFILE_AUTO_CLEAR_STATUS).equalsIgnoreCase(GlobalConstants.YES)
                                && item.getString(SNMPTrapProfile.SNMP_TRAP_PROFILE_AUTO_CLEAR_OID) != null)
                        .collect(Collectors.groupingBy(item -> item.getString(SNMPTrapProfile.SNMP_TRAP_PROFILE_AUTO_CLEAR_OID), Collectors.mapping(item -> item.getLong(GlobalConstants.ID), Collectors.toList())))
                        .forEach((key, values) -> clearProfilesByOID.put(key, values.getFirst()));

                items.values().parallelStream().filter(item -> item.containsKey(SNMPTrapProfile.SNMP_TRAP_PROFILE_AUTO_CLEAR_STATUS) &&
                                item.getString(SNMPTrapProfile.SNMP_TRAP_PROFILE_AUTO_CLEAR_STATUS).equalsIgnoreCase(GlobalConstants.YES)
                                && item.getInteger(SNMPTrapProfile.SNMP_TRAP_PROFILE_AUTO_CLEAR_TIMER) != null)
                        .collect(Collectors.groupingBy(item -> item.getLong(GlobalConstants.ID), Collectors.mapping(item -> item.getInteger(SNMPTrapProfile.SNMP_TRAP_PROFILE_AUTO_CLEAR_TIMER), Collectors.toList())))
                        .forEach((key, values) -> clearProfilesByTimer.put(key, values.getFirst()));

                future.complete();
            }
            catch (Exception exception)
            {
                LOGGER.error(exception);

                future.fail(exception.getMessage());
            }
        }, false, result ->
        {
        });
    }

    @Override
    public Future<Void> updateItem(long id)
    {
        return super.updateItem(id).onComplete(result -> init());
    }

    @Override
    public void deleteItem(long id)
    {
        if (items.containsKey(id))
        {
            super.deleteItem(id);

            init();
        }

    }

    public Long getFilterProfileId(String oid)
    {
        return filterProfiles.getOrDefault(oid, 0L);
    }

    public JsonObject getTrapProfiles(String oid)
    {
        return itemsByOID.containsKey(oid) ? itemsByOID.get(oid).copy() : null;
    }
}
