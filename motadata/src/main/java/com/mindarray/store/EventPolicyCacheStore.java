/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

package com.mindarray.store;

import com.mindarray.Bootstrap;
import com.mindarray.GlobalConstants;
import com.mindarray.policy.PolicyEngineConstants;
import com.mindarray.util.CodecUtil;
import com.mindarray.util.CommonUtil;
import com.mindarray.util.Logger;
import io.vertx.core.Future;
import io.vertx.core.Promise;
import io.vertx.core.buffer.Buffer;
import io.vertx.core.json.JsonObject;

import java.io.File;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicBoolean;

public class EventPolicyCacheStore extends AbstractCacheStore
{
    private static final EventPolicyCacheStore STORE = new EventPolicyCacheStore();

    private static final Logger LOGGER = new Logger(EventPolicyCacheStore.class, GlobalConstants.MOTADATA_STORE, "Event Policy Cache Store");
    private final Map<Long, JsonObject> ticks = new ConcurrentHashMap<>();//containing policy id and value as time
    private final AtomicBoolean dirty = new AtomicBoolean();
    private boolean updated = false;

    private EventPolicyCacheStore()
    {
    }

    public static EventPolicyCacheStore getStore()
    {
        return STORE;
    }

    @Override
    public Future<Void> initStore()
    {
        var promise = Promise.<Void>promise();

        var file = new File(GlobalConstants.CURRENT_DIR + GlobalConstants.PATH_SEPARATOR + GlobalConstants.CONFIG_DIR + GlobalConstants.PATH_SEPARATOR + PolicyEngineConstants.EVENT_POLICY_TRIGGER_TICKS);

        if (file.exists())
        {
            var buffer = Bootstrap.vertx().fileSystem().readFileBlocking(file.getPath());

            if (buffer != null && buffer.getBytes().length > 0)
            {
                var context = new JsonObject(Buffer.buffer(CodecUtil.toBytes(buffer.getBytes()))).getMap();

                ((Map<String, Object>) context.get("trigger.ticks")).forEach((key, value) -> ticks.put(CommonUtil.getLong(key), JsonObject.mapFrom(value)));

                LOGGER.info(String.format("%s loaded from the backup file...", PolicyEngineConstants.EVENT_POLICY_TRIGGER_TICKS));
            }
        }
        else
        {
            Bootstrap.vertx().fileSystem().createFileBlocking(file.getPath());
        }

        promise.complete();

        LOGGER.info(String.format("store %s initialized...", this.getClass().getSimpleName()));

        return promise.future();
    }

    public void updateTriggerTicks(long policyId, long tick)
    {
        ticks.computeIfAbsent(policyId, value -> new JsonObject());

        var context = ticks.get(policyId);

        context.getMap().computeIfAbsent(PolicyEngineConstants.POLICY_FIRST_TRIGGER_TICK, value -> tick * 1000);

        context.put(PolicyEngineConstants.POLICY_LAST_TRIGGER_TICK, tick * 1000);

        context.remove(PolicyEngineConstants.POLICY_ACKNOWLEDGE);

        updated = true;
    }

    public void remove(long policyId)
    {
        ticks.remove(policyId);
    }

    public boolean dirty()
    {
        return dirty.get();
    }

    public void setDirty(boolean value)
    {
        dirty.set(value);
    }


    public Map<Long, JsonObject> getTriggerTicks()
    {
        return new HashMap<>(ticks);
    }

    public JsonObject getTriggerTicks(long policyId)
    {
        return ticks.getOrDefault(policyId, new JsonObject());
    }

    public void dump(String path)
    {
        if (updated)
        {
            updated = false;

            dirty.set(true);

            Bootstrap.vertx().fileSystem().writeFileBlocking(path,
                    Buffer.buffer(CodecUtil.compress(new JsonObject().put("trigger.ticks", new HashMap<>(ticks)).encode().getBytes(StandardCharsets.UTF_8))));
        }
    }
}
