/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

package com.mindarray.store;

import com.mindarray.Bootstrap;
import com.mindarray.GlobalConstants;
import com.mindarray.db.DBConstants;
import com.mindarray.nms.NMSConstants;
import com.mindarray.util.CipherUtil;
import com.mindarray.util.CommonUtil;
import com.mindarray.util.Logger;
import io.vertx.core.Future;
import io.vertx.core.Promise;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;

import java.io.File;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

public class SNMPTemplateOIDGroupCacheStore extends AbstractCacheStore
{
    private static final Logger LOGGER = new Logger(SNMPTemplateOIDGroupCacheStore.class, GlobalConstants.MOTADATA_STORE, "SNMP Template OID Group Cache Store");
    private static final SNMPTemplateOIDGroupCacheStore STORE = new SNMPTemplateOIDGroupCacheStore();
    private final Map<String, JsonArray> itemsByVendor = new ConcurrentHashMap<>();
    private final Map<String, JsonObject> items = new ConcurrentHashMap<>();

    private SNMPTemplateOIDGroupCacheStore()
    {
    }

    public static SNMPTemplateOIDGroupCacheStore getStore()
    {
        return STORE;
    }

    @Override
    public Future<Void> initStore()
    {
        var promise = Promise.<Void>promise();

        if (GlobalConstants.BootstrapType.COLLECTOR == Bootstrap.bootstrapType())
        {
            promise.fail("remote event processor can not init store... it must be master or default boot sequence...");
        }

        else
        {
            Bootstrap.vertx().<Void>executeBlocking(future ->
            {

                try
                {
                    var files = Bootstrap.vertx().fileSystem().readDirBlocking(GlobalConstants.CURRENT_DIR + GlobalConstants.PATH_SEPARATOR + GlobalConstants.DB_DIR + GlobalConstants.PATH_SEPARATOR + GlobalConstants.SNMP_TEMPLATE_DIR);

                    if (files != null && !files.isEmpty())
                    {
                        var cipher = new CipherUtil();

                        files.forEach(file ->
                        {
                            try
                            {
                                var buffer = Bootstrap.vertx().fileSystem().readFileBlocking(file);

                                var groups = new JsonArray(cipher.decrypt(buffer.toString()));

                                if (!groups.isEmpty())
                                {
                                    var groupIds = new JsonArray();

                                    itemsByVendor.put(new File(file).getName(), groupIds);

                                    for (var index = 0; index < groups.size(); index++)
                                    {
                                        var group = groups.getJsonObject(index);

                                        if (group != null && !group.isEmpty())
                                        {
                                            var id = group.getString(NMSConstants.SNMP_OID_GROUP_ID);

                                            if (CommonUtil.isNotNullOrEmpty(id))
                                            {
                                                groupIds.add(id);

                                                items.put(id, group.put(DBConstants.FIELD_TYPE, DBConstants.ENTITY_TYPE_SYSTEM));

                                                if (CommonUtil.debugEnabled())
                                                {
                                                    LOGGER.debug(String.format("item added %s into the store", CommonUtil.removeSensitiveFields(items.get(id), false).encodePrettily()));
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                            catch (Exception exception)
                            {
                                LOGGER.error(exception);
                            }

                        });

                        future.complete();

                        LOGGER.info(String.format("store %s initialized...", this.getClass().getSimpleName()));
                    }

                    else
                    {
                        future.fail("no snmp device templates found...");
                    }

                }


                catch (Exception exception)
                {
                    future.fail(exception.getMessage());

                    LOGGER.error(exception);
                }

            }, result ->
            {

                if (result.succeeded())
                {
                    promise.complete();
                }

                else
                {
                    promise.fail(result.cause());
                }
            });
        }

        return promise.future();
    }

    public void updateItem(String id, JsonObject item)
    {
        items.put(id, item);
    }

    //when custom metric group delete then remove from cache also
    public void deleteItem(String id)
    {
        items.remove(id);
    }

    public JsonObject getItem(String id)
    {
        return items.containsKey(id) ? items.get(id).copy() : null;
    }

    public String getGroupName(String id)
    {
        return items.containsKey(id) ? items.get(id).getString(NMSConstants.SNMP_OID_GROUP_NAME) : null;
    }

    public JsonArray getItemsByType(String vendor, NMSConstants.Type type)
    {
        JsonArray items = null;

        vendor = vendor.replaceAll(" ", "-").toLowerCase();

        if (itemsByVendor.containsKey(vendor))
        {
            items = new JsonArray(itemsByVendor.get(vendor)
                    .stream()
                    .filter(item -> this.items.get(item).getString(NMSConstants.SNMP_OID_GROUP_DEVICE_TYPE).equalsIgnoreCase(NMSConstants.Type.SNMP_DEVICE.getName())
                            || this.items.get(item).getString(NMSConstants.SNMP_OID_GROUP_DEVICE_TYPE).equalsIgnoreCase(type.getName()))
                    .map(item -> this.items.get(item).copy())
                    .collect(Collectors.toList()));

        }

        return items;
    }

}
