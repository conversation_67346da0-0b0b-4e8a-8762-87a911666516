/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

/*
 *	Change Logs:
 * 	Date			Author			     Notes
 * 	26-Feb-2025		Darshan Parmar		 MOTADATA-5215: SonarQube Suggestions Resolution
 *   18-Mar-2025     Smit Prajapati      Added Trace Logger
 *   25-Mar-2025     Smit Prajapati      MOTADATA-5435: Flow back-pressure mechanism.
 *   2-Apr-2025      Bharat              MOTADATA-5637: Domain Mapping in Flow
 *   18-Apr-2025     Bharat             MOTADATA-5954: Domain Mapping | Domain name is not being mapped when an existing IP is specified in the IP/IP Range settings.
 *   21-Apr-2025     Bharat             MOTADATA-5954: Domain Mapping | removed IP mapping as it's not in use as replaced by domain mapping
 *   2-Jun-2025		 Smit Prajapati		MOTADATA-:6417 stat calculator will only fetch data and calculate the stat
 */
package com.mindarray.flow;

import com.google.common.hash.BloomFilter;
import com.google.common.hash.Funnels;
import com.mindarray.Bootstrap;
import com.mindarray.GlobalConstants;
import com.mindarray.api.*;
import com.mindarray.datastore.DatastoreConstants;
import com.mindarray.eventbus.EventBusConstants;
import com.mindarray.eventbus.EventEngine;
import com.mindarray.nms.NMSConstants;
import com.mindarray.store.*;
import com.mindarray.util.CommonUtil;
import com.mindarray.util.DateTimeUtil;
import com.mindarray.util.GeoDBUtil;
import com.mindarray.util.Logger;
import com.mindarray.visualization.VisualizationConstants;
import io.vertx.core.AbstractVerticle;
import io.vertx.core.Promise;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import org.joda.time.DateTime;

import java.nio.charset.StandardCharsets;
import java.util.*;

import static com.mindarray.GlobalConstants.ID;
import static com.mindarray.api.APIConstants.*;
import static com.mindarray.db.DBConstants.*;
import static com.mindarray.eventbus.EventBusConstants.*;
import static com.mindarray.flow.FlowEngineConstants.*;
import static com.mindarray.nms.NMSConstants.METRIC_INSTANCES;

/**
 * Processes network flow data by enriching it with additional information and transforming it for analysis.
 * <p>
 * The FlowProcessor is a core component of the flow processing system that receives raw flow data
 * from the FlowListener and performs the following operations:
 * <ul>
 *   <li>Enriches flow data with geolocation information (country, city, coordinates)</li>
 *   <li>Adds Autonomous System (AS) information to source and destination IPs</li>
 *   <li>Decodes IP addresses and resolves them to hostnames when possible</li>
 *   <li>Translates protocol numbers to protocol names</li>
 *   <li>Decodes Type of Service (ToS) values</li>
 *   <li>Interprets TCP flags</li>
 *   <li>Maps interface indices to interface names</li>
 *   <li>Calculates traffic metrics (bytes per second, packets per second, etc.)</li>
 *   <li>Formats the enriched data for storage and analysis</li>
 * </ul>
 * <p>
 * The processor uses various configuration stores to retrieve mapping information and settings
 * that control how flow data is processed and enriched.
 * <p>
 * After processing, the enriched flow data is published to the event bus for consumption by
 * other components such as the FlowStatCalculator and storage systems.
 */
@SuppressWarnings(value = {"UnstableApiUsage"})
public class FlowProcessor extends AbstractVerticle
{
    /**
     * Array of Type of Service (ToS) flag descriptions used for decoding ToS values
     */
    public static final String[] TOS_FLAGS = {" (Routine)", " (Priority)", " (Immediate)", " (Flash)", " (Flash Override)", " (CRITIC/ECP)", " (Inter Network Control)", " (Network Control)"};

    /**
     * Array of TCP flag names used for decoding TCP flag values
     */
    public static final String[] TCP_FLAGS = {"FIN", "SYN", "RST", "PSH", "ACK", "URG", "ECE", "CWR", "NONCE", "RESERVED"};

    /**
     * Logger for this class
     */
    private static final Logger LOGGER = new Logger(FlowProcessor.class, GlobalConstants.MOTADATA_FLOW, "Flow Processor");
    private static final JsonObject FIELD_DEFAULT_VALUES = new JsonObject()
            .put(SOURCE_IP, GlobalConstants.UNKNOWN)
            .put(DESTINATION_IP, GlobalConstants.UNKNOWN)
            .put(EGRESS_VOLUME_BYTES, 0)
            .put(INGRESS_VOLUME_BYTES, 0)
            .put(INGRESS_PACKETS, 0)
            .put(EGRESS_PACKETS, 0)
            .put(VOLUME_BYTES, 0)
            .put(PACKETS_PER_SEC, 0)
            .put(VOLUME_BYTES_PER_SEC, 0)
            .put(APPLICATION, GlobalConstants.UNKNOWN.toLowerCase(Locale.ROOT))
            .put(SOURCE_INTERFACE_INDEX, 0)
            .put(DESTINATION_INTERFACE_INDEX, 0)
            .put(SOURCE_PORT, 0)
            .put(DESTINATION_PORT, 0)
            .put(FlowEngineConstants.TCP_FLAGS, GlobalConstants.UNKNOWN)
            .put(DESTINATION_IP_AS, GlobalConstants.UNKNOWN)
            .put(DESTINATION_AS, GlobalConstants.UNKNOWN)
            .put(SOURCE_AS, GlobalConstants.UNKNOWN)
            .put(VOLUME_BYTES_PER_PACKET, 0)
            .put(PROTOCOL, "(" + GlobalConstants.UNKNOWN + ")")
            .put("user", GlobalConstants.UNKNOWN.toLowerCase(Locale.ROOT));
    private final Set<String> sources = new HashSet<>();
    private final Map<Integer, String> protocols = new HashMap<>();
    private final Map<Integer, String> apps = new HashMap<>();
    private final Map<String, Map<String, String>> interfaceNamesBySource = new HashMap<>();
    private final StringBuilder builder = new StringBuilder(0);
    private final Map<String, long[]> stats = new HashMap<>();
    private EventEngine eventEngine;
    private BloomFilter<String> probedIPAddresses;
    private BloomFilter<String> probedSamplingKeys;
    private Set<String> mappers;

    /**
     * Initializes the flow processor and sets up event handlers.
     * <p>
     * This method performs the following initialization tasks:
     * <ul>
     *   <li>Creates data structures for storing flow processing information</li>
     *   <li>Initializes Bloom filters for efficient IP address and sampling key lookups</li>
     *   <li>Loads protocol mappings from the ProtocolMapperConfigStore</li>
     *   <li>Loads application mappings from the ApplicationMapperConfigStore</li>
     *   <li>Loads sampling rate configurations from the FlowSamplingRateConfigStore</li>
     *   <li>Loads interface information from the MetricConfigStore</li>
     *   <li>Sets up event bus handlers for configuration changes</li>
     *   <li>Initializes the event engine for processing flow events</li>
     * </ul>
     * <p>
     * The method also sets up handlers for various configuration change events to ensure
     * that the processor always uses the most up-to-date configuration information.
     *
     * @param promise the promise to complete when initialization is finished
     * @throws Exception if an error occurs during initialization
     */
    @Override
    public void start(Promise<Void> promise) throws Exception
    {
        mappers = new HashSet<>();

        sources.addAll(EventSourceConfigStore.getStore().flatItemsByValues(EventBusConstants.EVENT_TYPE, new JsonArray().add(EVENT_FLOW), EVENT_SOURCE).getList());

        probedIPAddresses = BloomFilter.create(Funnels.stringFunnel(StandardCharsets.UTF_8), 5000000, 0.005);   //wrapper to reduce GeoDBUtil Hits

        probedSamplingKeys = BloomFilter.create(Funnels.stringFunnel(StandardCharsets.UTF_8), 5000000, 0.005);   //wrapper to add interface sampling rate

        // init protocol Mapper
        var items = ProtocolMapperConfigStore.getStore().getItems();

        for (var index = 0; index < items.size(); index++)
        {
            var item = items.getJsonObject(index);

            protocols.put(item.getInteger(ProtocolMapper.PROTOCOL_MAPPER_NUMBER), item.getInteger(ProtocolMapper.PROTOCOL_MAPPER_NUMBER) + " (" + item.getString(ProtocolMapper.PROTOCOL_MAPPER_NAME) + ")");
        }

        items = ApplicationMapperConfigStore.getStore().getItems();

        for (var index = 0; index < items.size(); index++)
        {
            var item = items.getJsonObject(index);

            apps.put(item.getInteger(ApplicationMapper.APPLICATION_MAPPER_PORT), item.getString(ApplicationMapper.APPLICATION_MAPPER_NAME));
        }

        items = FlowSamplingRateConfigStore.getStore().getItems();

        for (var index = 0; index < items.size(); index++)
        {
            var item = items.getJsonObject(index);

            probedSamplingKeys.put(item.getString(EVENT_SOURCE) + GlobalConstants.SEPARATOR + CommonUtil.getInteger(item.getString(NMSConstants.INTERFACE_INDEX)));
        }

        items = MetricConfigStore.getStore().getItemsByValue(Metric.METRIC_PLUGIN, NMSConstants.MetricPlugin.SNMP_INTERFACE.getName());

        for (var index = 0; index < items.size(); index++)
        {
            setInterfaceName(items.getJsonObject(index));
        }

        if (Bootstrap.bootstrapType() == GlobalConstants.BootstrapType.FLOW_COLLECTOR ||
                Bootstrap.bootstrapType() == GlobalConstants.BootstrapType.EVENT_PROCESSOR ||
                Bootstrap.bootstrapType() == GlobalConstants.BootstrapType.EVENT_COLLECTOR)
        {
            vertx.eventBus().<JsonObject>localConsumer(EVENT_CONFIG_CHANGE, message ->
            {

                switch (message.body().getString(ENTITY_TABLE) + message.body().getString(REQUEST))
                {
                    case TBL_PROTOCOL_MAPPER + REQUEST_CREATE, TBL_PROTOCOL_MAPPER + REQUEST_UPDATE ->
                    {
                        try
                        {
                            var item = ProtocolMapperConfigStore.getStore().getItem(message.body().getLong(ID));

                            if (item != null)
                            {
                                protocols.put(item.getInteger(ProtocolMapper.PROTOCOL_MAPPER_NUMBER), item.getInteger(ProtocolMapper.PROTOCOL_MAPPER_NUMBER) + " (" + item.getString(ProtocolMapper.PROTOCOL_MAPPER_NAME) + ")");
                            }
                        }
                        catch (Exception exception)
                        {
                            LOGGER.error(exception);

                            promise.fail(exception.getMessage());
                        }
                    }

                    case TBL_APPLICATION_MAPPER + REQUEST_CREATE ->
                    {
                        var item = ApplicationMapperConfigStore.getStore().getItem(message.body().getLong(ID));

                        if (item != null)
                        {
                            apps.put(item.getInteger(ApplicationMapper.APPLICATION_MAPPER_PORT), item.getString(ApplicationMapper.APPLICATION_MAPPER_NAME));
                        }
                    }

                    case TBL_METRIC + REQUEST_CREATE, TBL_METRIC + REQUEST_UPDATE ->
                    {
                        var item = MetricConfigStore.getStore().getItem(message.body().getLong(GlobalConstants.ID));

                        if (NMSConstants.MetricPlugin.SNMP_INTERFACE.getName().equalsIgnoreCase(item.getString(Metric.METRIC_PLUGIN)))
                        {
                            setInterfaceName(item);
                        }
                    }

                    default ->
                    {
                        // do nothing
                    }
                }
            });
        }
        else
        {
            vertx.eventBus().<JsonObject>localConsumer(EventBusConstants.EVENT_CHANGE_NOTIFICATION, message ->
            {
                var event = message.body();

                switch (EventBusConstants.ChangeNotificationType.valueOf(event.getString(EventBusConstants.CHANGE_NOTIFICATION_TYPE)))
                {
                    case ADD_PROTOCOL, UPDATE_PROTOCOL ->
                            protocols.put(event.getInteger(ProtocolMapper.PROTOCOL_MAPPER_NUMBER), event.getInteger(ProtocolMapper.PROTOCOL_MAPPER_NUMBER) + " (" + event.getString(ProtocolMapper.PROTOCOL_MAPPER_NAME) + ")");

                    case DELETE_PROTOCOL -> protocols.remove(event.getInteger(ProtocolMapper.PROTOCOL_MAPPER_NUMBER));

                    case ADD_APPLICATION_MAPPER, UPDATE_APPLICATION_MAPPER ->
                            apps.put(event.getInteger(ApplicationMapper.APPLICATION_MAPPER_PORT), event.getString(ApplicationMapper.APPLICATION_MAPPER_NAME));

                    case DELETE_APPLICATION_MAPPER ->
                            apps.remove(event.getInteger(ApplicationMapper.APPLICATION_MAPPER_PORT));

                    case ADD_METRIC ->
                    {

                        var item = MetricConfigStore.getStore().getItem(event.getLong(GlobalConstants.ID));

                        if (NMSConstants.MetricPlugin.SNMP_INTERFACE.getName().equalsIgnoreCase(item.getString(Metric.METRIC_PLUGIN)))
                        {
                            setInterfaceName(item);
                        }
                    }

                    case DELETE_OBJECT ->
                    {
                        if (!event.containsKey(NMSConstants.METRIC_INSTANCES) || event.getValue(METRIC_INSTANCES) == null)
                        {
                            var item = ObjectConfigStore.getStore().getItem(event.getLong(GlobalConstants.ID));

                            if (NMSConstants.Category.NETWORK.getName().equalsIgnoreCase(item.getString(AIOpsObject.OBJECT_CATEGORY)))
                            {
                                interfaceNamesBySource.remove(item.getString(AIOpsObject.OBJECT_IP));
                            }
                        }
                    }
                }
            });
        }

        //broadcasting stats to stat calculator
        vertx.setPeriodic(60 * 1000, timer ->
        {

            vertx.eventBus().send(EVENT_FLOW_STAT, stats);

            stats.clear();
        });

        eventEngine = new EventEngine().setLogger(LOGGER)
                .setEventType(config().getString(EventBusConstants.EVENT_TYPE))
                .setPersistEventOffset(true).setEventHandler(this::process).start(vertx, promise);
    }

    /**
     * Processes a flow event by enriching it with additional information and forwarding it for storage and analysis.
     * <p>
     * This method is the core processing function of the FlowProcessor. It takes a raw flow event,
     * enriches it with additional information, and forwards it to various components for storage,
     * analysis, and visualization.
     * <p>
     * The processing steps include:
     * <ul>
     *   <li>Handling remote events from flow collectors</li>
     *   <li>Managing sampling rates for interfaces</li>
     *   <li>Enriching flow data with source and destination information</li>
     *   <li>Decoding IP addresses and resolving them to hostnames</li>
     *   <li>Adding geolocation information (country, city, coordinates)</li>
     *   <li>Translating protocol numbers to protocol names</li>
     *   <li>Mapping application ports to application names</li>
     *   <li>Decoding Type of Service (ToS) values</li>
     *   <li>Interpreting TCP flags</li>
     *   <li>Calculating traffic metrics</li>
     *   <li>Formatting the enriched data for storage and analysis</li>
     * </ul>
     * <p>
     * After processing, the enriched flow data is:
     * <ul>
     *   <li>Sent to the event policy system for policy evaluation</li>
     *   <li>Forwarded to the FlowStatCalculator for statistical analysis</li>
     *   <li>Written to the datastore for storage and visualization</li>
     * </ul>
     *
     * @param event the raw flow event to process
     */
    private void process(JsonObject event)
    {
        try
        {
            if (CommonUtil.isNotNullOrEmpty(CommonUtil.getString(event.remove(RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_UUID))))
            {
                var source = CommonUtil.getString(event.remove("peer_ip_src"));

                if (!sources.contains(source))
                {
                    sources.add(source);

                    // Register Event to Event Object Manager
                    vertx.eventBus().send(EVENT_CHANGE_LOCAL_NOTIFICATION,
                            new JsonObject().put(EventBusConstants.EVENT_SOURCE, source)
                                    .put(EventBusConstants.EVENT, EVENT_FLOW)
                                    .put(CHANGE_NOTIFICATION_TYPE, EventBusConstants.ChangeNotificationType.ADD_EVENT_SOURCE)
                                    .put(GlobalConstants.PLUGIN_ID, DatastoreConstants.PluginId.FLOW_EVENT.getName()));
                }

                var samplingRate = CommonUtil.getInteger(event.remove("sampling_rate"));

                var samplingKey = source + GlobalConstants.SEPARATOR + event.getInteger("iface_out", Integer.MIN_VALUE);

                if (!probedSamplingKeys.mightContain(samplingKey))   // check for the existence of interface sampling rate in FlowSamplingRateConfigStore in Application side
                {
                    probedSamplingKeys.put(samplingKey);

                    vertx.eventBus().send(EVENT_CHANGE_LOCAL_NOTIFICATION, new JsonObject().put(CHANGE_NOTIFICATION_TYPE, ChangeNotificationType.ADD_FLOW_SAMPLING_RATE).put(EventBusConstants.EVENT_SOURCE, source).put(NMSConstants.INTERFACE_INDEX, event.getInteger("iface_out", Integer.MIN_VALUE)).put(INTERFACE_SAMPLING_RATE, samplingRate));
                }

                samplingKey = source + GlobalConstants.SEPARATOR + event.getInteger("iface_in", Integer.MIN_VALUE);

                if (!probedSamplingKeys.mightContain(samplingKey))   // check for the existence of interface sampling rate in FlowSamplingRateConfigStore in Application side
                {
                    probedSamplingKeys.put(samplingKey);

                    vertx.eventBus().send(EVENT_CHANGE_LOCAL_NOTIFICATION, new JsonObject().put(CHANGE_NOTIFICATION_TYPE, ChangeNotificationType.ADD_FLOW_SAMPLING_RATE).put(EventBusConstants.EVENT_SOURCE, source).put(NMSConstants.INTERFACE_INDEX, event.getInteger("iface_in", Integer.MIN_VALUE)).put(INTERFACE_SAMPLING_RATE, samplingRate));
                }

                FlowIPMapperConfigStore.getStore().setUser(event.getString(SOURCE_IP), event);

                event.remove("iface_out");

                event.remove("iface_in");

                vertx.eventBus().send(EventBusConstants.EVENT_EVENT_POLICY_QUALIFY, event);

                // Get or create the stats array with minimal locking
                var values = stats.computeIfAbsent(source, k -> new long[2]);

                values[0]++; // count

                values[1] += event.getInteger(EVENT_VOLUME_BYTES, 0); // volume bytes

                // Write to Database
                DatastoreConstants.write(event
                        .put(DatastoreConstants.DATASTORE_TYPE, DatastoreConstants.DatastoreType.FLOW.ordinal()), VisualizationConstants.VisualizationDataSource.FLOW.getName(), mappers, builder);
            }
            else
            {
                event.mergeIn(FIELD_DEFAULT_VALUES, false);

                event.put(SOURCE_IP, CommonUtil.getString(event.remove("ip_src")));

                decodeIP(event.getString(SOURCE_IP), event, "source");

                event.put(DESTINATION_IP, CommonUtil.getString(event.remove("ip_dst")));

                decodeIP(event.getString(DESTINATION_IP), event, "destination");
                // calculated fields

                var packets = CommonUtil.getLong(event.remove("packets"));

                var volumeBytes = CommonUtil.getLong(event.remove("bytes"));

                var source = CommonUtil.getString(event.remove("peer_ip_src"));

                var samplingRate = CommonUtil.getInteger(event.getString("sampling_rate"));

                var samplingKey = source + GlobalConstants.SEPARATOR + event.getInteger("iface_out", Integer.MIN_VALUE);

                if (FlowSamplingRateConfigStore.getStore().getSamplingRate(samplingKey) != null)
                {
                    samplingRate = FlowSamplingRateConfigStore.getStore().getSamplingRate(samplingKey);
                }
                else
                {
                    if (!probedSamplingKeys.mightContain(samplingKey)) // check for the existence of interface sampling rate in FlowSamplingRateConfigStore
                    {
                        probedSamplingKeys.put(samplingKey);

                        vertx.eventBus().send(EVENT_CHANGE_LOCAL_NOTIFICATION, new JsonObject().put(CHANGE_NOTIFICATION_TYPE, ChangeNotificationType.ADD_FLOW_SAMPLING_RATE).put(EventBusConstants.EVENT_SOURCE, source).put(NMSConstants.INTERFACE_INDEX, event.getInteger("iface_out", Integer.MIN_VALUE)).put(INTERFACE_SAMPLING_RATE, samplingRate));
                    }
                }

                samplingKey = source + GlobalConstants.SEPARATOR + event.getInteger("iface_in", Integer.MIN_VALUE);

                if (FlowSamplingRateConfigStore.getStore().getSamplingRate(samplingKey) != null)
                {
                    samplingRate = FlowSamplingRateConfigStore.getStore().getSamplingRate(samplingKey);
                }
                else
                {
                    if (!probedSamplingKeys.mightContain(samplingKey))  // check for the existence of interface sampling rate in FlowSamplingRateConfigStore
                    {
                        probedSamplingKeys.put(samplingKey);

                        vertx.eventBus().send(EVENT_CHANGE_LOCAL_NOTIFICATION, new JsonObject().put(CHANGE_NOTIFICATION_TYPE, ChangeNotificationType.ADD_FLOW_SAMPLING_RATE).put(EventBusConstants.EVENT_SOURCE, source).put(NMSConstants.INTERFACE_INDEX, event.getInteger("iface_in", Integer.MIN_VALUE)).put(INTERFACE_SAMPLING_RATE, samplingRate));
                    }
                }

                if (samplingRate > 1)
                {
                    volumeBytes *= samplingRate;

                    packets *= samplingRate;
                }

                var tag = CommonUtil.getShort(event.remove(TAG));

                if (tag == EGRESS_FLOW)
                {
                    event.put(EGRESS_VOLUME_BYTES, volumeBytes);

                    event.put(EGRESS_PACKETS, packets);
                }

                else if (tag == INGRESS_FLOW)
                {
                    event.put(INGRESS_VOLUME_BYTES, volumeBytes);

                    event.put(INGRESS_PACKETS, packets);
                }

                else if ("sFlow".equalsIgnoreCase(event.getString("flow_type")) && event.getInteger("export_proto_version", -1) == 5)
                {
                    if (FlowEngineConstants.EGRESS.equalsIgnoreCase(FlowSettingsConfigStore.getStore().getItem().getString(FlowSettings.FLOW_SETTINGS_SFLOW_DIRECTION)))
                    {
                        event.put(EGRESS_VOLUME_BYTES, volumeBytes);

                        event.put(EGRESS_PACKETS, packets);
                    }

                    else
                    {
                        event.put(INGRESS_VOLUME_BYTES, volumeBytes);

                        event.put(INGRESS_PACKETS, packets);
                    }
                }

                event.put(PACKETS, packets);

                event.put(VOLUME_BYTES, volumeBytes);

                if (event.containsKey(FlowEngineConstants.DURATION))  // log as flow send duration direct
                {
                    var duration = event.getLong(FlowEngineConstants.DURATION);

                    event.put(PACKETS_PER_SEC, duration > 0 ? CommonUtil.getFloat(packets / duration) : 0);

                    event.put(VOLUME_BYTES_PER_SEC, duration > 0 ? CommonUtil.getFloat(volumeBytes / duration) : 0);

                    event.put(FlowEngineConstants.DURATION, duration);
                }

                else if (event.containsKey("timestamp_start") && event.containsKey("timestamp_end"))
                {
                    try
                    {
                        var startTime = DateTime.parse(CommonUtil.getString(event.remove("timestamp_start")), FLOW_DATE_TIME_FORMAT).getMillis();

                        var endTime = DateTime.parse(CommonUtil.getString(event.remove("timestamp_end")), FLOW_DATE_TIME_FORMAT).getMillis();

                        var duration = endTime > startTime ? (endTime - startTime) / 1000 : 0;

                        event.put(PACKETS_PER_SEC, duration > 0 ? CommonUtil.getFloat(packets / duration) : 0);

                        event.put(VOLUME_BYTES_PER_SEC, duration > 0 ? CommonUtil.getFloat(volumeBytes / duration) : 0);

                        event.put(FlowEngineConstants.DURATION, duration);
                    }

                    catch (Exception ignored)
                    {
                        //#24861
                    }
                }

                // put application protocol and application details
                if (event.getString("class") != null)
                {
                    event.put(APPLICATION, CommonUtil.getString(event.remove("class")));
                }

                // Port resolution logic
                var sourcePort = CommonUtil.getInteger(event.remove("port_src"));

                var destinationPort = CommonUtil.getInteger(event.remove("port_dst"));

                if (!event.getString(APPLICATION).contains(GlobalConstants.UNKNOWN) && !event.getString(APPLICATION).contains("unknown"))
                {
                    event.put(SOURCE_PORT, sourcePort > 1024 ? 0 : sourcePort)
                            .put(DESTINATION_PORT, destinationPort > 1024 ? 0 : destinationPort);
                }

                else
                {
                    //if port mapped with both source and destination port, will give priority to destination port
                    if (apps.containsKey(sourcePort))
                    {
                        event.put(SOURCE_PORT, sourcePort > 1024 ? 0 : sourcePort);

                        event.put(APPLICATION, apps.get(sourcePort));
                    }

                    if (apps.containsKey(destinationPort))
                    {
                        event.put(DESTINATION_PORT, destinationPort > 1024 ? 0 : destinationPort);

                        event.put(APPLICATION, apps.get(destinationPort));
                    }
                }

                event.put(SOURCE_INTERFACE_INDEX, event.getInteger("iface_out", Integer.MIN_VALUE))
                        .put(FLOWS, event.getLong(FLOWS, 1L))
                        .put(DESTINATION_INTERFACE_INDEX, "interface-index-" + CommonUtil.getString(event.getInteger("iface_in", Integer.MIN_VALUE)))
                        .put(ORIGINAL_SOURCE_PORT, sourcePort)
                        .put(ORIGINAL_DESTINATION_PORT, destinationPort)
                        .put(FlowEngineConstants.TCP_FLAGS, getTCPFlags(event.remove("tcp_flags")))
                        .put(DESTINATION_IP_AS, event.remove("peer_as_dst"))
                        .put(DESTINATION_AS, event.getValue(DESTINATION_AS, event.remove("as_dst")))
                        .put(SOURCE_AS, event.getValue(SOURCE_AS, event.remove("as_src")))
                        .put(TOS, decodeTOS(event.getInteger(TOS, -1)))
                        .put(VOLUME_BYTES_PER_PACKET, packets > 0 ? CommonUtil.getFloat(volumeBytes / packets) : 0)
                        .put(PROTOCOL, protocols.getOrDefault(CommonUtil.getInteger(event.getValue("ip_proto")), event.getValue("ip_proto") + " (" + GlobalConstants.UNKNOWN + ")"));

                // remove garbage fields
                event.remove("export_proto_version");

                event.remove("export_proto_seqno");

                event.remove("peer_ip_dst");

                event.remove("flow_type");

                event.remove("ip_proto");

                event.remove("timestamp_start");

                event.remove("timestamp_end");

                event.remove("as_dst");

                event.remove("as_src");

                event.remove("event_type");

                event.remove("stamp_inserted");

                event.remove("stamp_updated");


                // remove null values
                var iterator = event.iterator();

                while (iterator.hasNext())
                {
                    if (iterator.next().getValue() == null)
                    {
                        iterator.remove();
                    }
                }

                if (!event.containsKey(EventBusConstants.EVENT_TIMESTAMP))
                {
                    event.put(EventBusConstants.EVENT_TIMESTAMP, DateTimeUtil.currentSeconds());
                }

                if (!sources.contains(source))
                {
                    sources.add(source);

                    // Register Event to Event Object Manager
                    vertx.eventBus().send(EVENT_CHANGE_LOCAL_NOTIFICATION,
                            new JsonObject().put(EventBusConstants.EVENT_SOURCE, source)
                                    .put(EventBusConstants.EVENT, EVENT_FLOW)
                                    .put(CHANGE_NOTIFICATION_TYPE, EventBusConstants.ChangeNotificationType.ADD_EVENT_SOURCE)
                                    .put(GlobalConstants.PLUGIN_ID, DatastoreConstants.PluginId.FLOW_EVENT.getName()));
                }

                event.put(EventBusConstants.EVENT_SOURCE, source);

                if (interfaceNamesBySource.containsKey(source))
                {
                    event.put(SOURCE_INTERFACE_INDEX, interfaceNamesBySource.get(source).getOrDefault(event.getString(SOURCE_INTERFACE_INDEX), "interface-index-" + event.getString(SOURCE_INTERFACE_INDEX)));
                }
                else
                {
                    event.put(SOURCE_INTERFACE_INDEX, "interface-index-" + event.getString(SOURCE_INTERFACE_INDEX));
                }

                event.put(GlobalConstants.PLUGIN_ID, DatastoreConstants.PluginId.FLOW_EVENT.getName());

                if (CommonUtil.traceEnabled())
                {
                    LOGGER.trace(event.encode());
                }

                if (Bootstrap.bootstrapType() == GlobalConstants.BootstrapType.FLOW_COLLECTOR ||
                        Bootstrap.bootstrapType() == GlobalConstants.BootstrapType.EVENT_COLLECTOR ||
                        Bootstrap.bootstrapType() == GlobalConstants.BootstrapType.EVENT_PROCESSOR)
                {
                    vertx.eventBus().send(EVENT_REMOTE, event.put(EVENT_TYPE, EVENT_FLOW)
                            .put(RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_UUID, Bootstrap.getRegistrationId()));
                }
                else
                {
                    event.remove("sampling_rate");

                    event.remove("iface_out");

                    event.remove("iface_in");

                    vertx.eventBus().send(EventBusConstants.EVENT_EVENT_POLICY_QUALIFY, event);

                    // Get or create the stats array with minimal locking
                    var values = stats.computeIfAbsent(source, k -> new long[2]);

                    values[0]++; // count

                    values[1] += event.getInteger(EVENT_VOLUME_BYTES, 0); // volume bytes

                    // Write to Database
                    DatastoreConstants.write(event
                            .put(DatastoreConstants.DATASTORE_TYPE, DatastoreConstants.DatastoreType.FLOW.ordinal()), VisualizationConstants.VisualizationDataSource.FLOW.getName(), mappers, builder);
                }
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }

    /**
     * Decodes an IP address and enriches the flow data with geolocation and AS information.
     * <p>
     * This method takes an IP address and enriches the flow data with additional information:
     * <ul>
     *   <li>Geolocation information (country, city, latitude, longitude)</li>
     *   <li>Autonomous System (AS) information</li>
     *   <li>Domain information if available</li>
     * </ul>
     * <p>
     * The method uses a Bloom filter to avoid redundant lookups for IP addresses that have
     * already been processed, improving performance for high-volume flow processing.
     *
     * @param ipAddress the IP address to decode
     * @param flow      the flow data to enrich with decoded information
     * @param flowType  the type of flow (source or destination)
     */
    private void decodeIP(String ipAddress, JsonObject flow, String flowType)
    {
        try
        {
            // default values (no need to add in fieldDefaultValues)
            flow.put(flowType + ".country", GlobalConstants.UNKNOWN.toLowerCase(Locale.ROOT))
                    .put(flowType + ".city", GlobalConstants.UNKNOWN.toLowerCase(Locale.ROOT))
                    .put(flowType + ".isp", GlobalConstants.UNKNOWN.toLowerCase(Locale.ROOT))
                    .put(flowType + ".domain", GlobalConstants.UNKNOWN.toLowerCase(Locale.ROOT))
                    .put(flowType + ".asn", GlobalConstants.UNKNOWN.toLowerCase(Locale.ROOT))
                    .put(flowType + ".aso", GlobalConstants.UNKNOWN.toLowerCase(Locale.ROOT))
                    .put(flowType + ".threat", GlobalConstants.NO.toLowerCase(Locale.ROOT));

            // if inverseMapper contains IP (no record found in DB) -> Send default
            // if not found in DB -> put into inverseMapper and send default
            if (!probedIPAddresses.mightContain(ipAddress) && !GeoDBUtil.resolveIP(ipAddress, flowType, flow))
            {
                probedIPAddresses.put(ipAddress);
            }

            FlowGeolocationMapperConfigStore.getStore().setGeoLocation(ipAddress, flow, flowType);

            FlowASMapperConfigStore.getStore().setAS(ipAddress, flow, flowType);

            FlowIPMapperConfigStore.getStore().setUser(ipAddress, flow);

            FlowDomainMapperConfigStore.getStore().setDomain(ipAddress, flow, flowType);
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }

    /**
     * Decodes the Type of Service (ToS) value from an IP packet header.
     * <p>
     * This method interprets the Type of Service (ToS) field from an IP packet header
     * and converts it into a human-readable string representation. The ToS field contains
     * information about how the packet should be handled in terms of priority, delay,
     * throughput, and reliability.
     * <p>
     * The method analyzes the bits in the ToS value to determine which service flags are set
     * and appends the corresponding flag descriptions to the result.
     *
     * @param tos numeric representation of Service type
     * @return a string representation of the decoded ToS value
     */
    private String decodeTOS(int tos)
    {
        try
        {
            if (tos < 0)
            {
                return "(Unknown)";
            }
            if (tos < TOS_FLAGS.length)
            {
                return tos + TOS_FLAGS[tos];
            }
            else
            {
                return tos + " (Unknown)";
            }
        }
        catch (Exception exception)
        {
            return "(Unknown)";
        }
    }

    /**
     * Decodes TCP flags from a numeric value into a human-readable string representation.
     * <p>
     * This method interprets the TCP flags field from a TCP packet header and converts it
     * into a human-readable string representation. TCP flags indicate the purpose and content
     * of a TCP packet and control the state of a TCP connection.
     * <p>
     * The method analyzes the bits in the TCP flags value to determine which flags are set
     * (such as SYN, ACK, FIN, RST, etc.) and concatenates the corresponding flag names
     * to create a descriptive string.
     * <p>
     * Common TCP flag combinations include:
     * <ul>
     *   <li>SYN: Initiates a connection</li>
     *   <li>ACK: Acknowledges received data</li>
     *   <li>FIN: Closes a connection</li>
     *   <li>RST: Resets a connection</li>
     *   <li>PSH: Pushes data to the application immediately</li>
     *   <li>URG: Indicates urgent data</li>
     * </ul>
     *
     * @param value numeric representation of TCP flags
     * @return a string representation of the decoded TCP flags
     */
    private String getTCPFlags(Object value)
    {
        try
        {
            var tcpFlag = CommonUtil.getInteger(value);

            var flags = new StringBuilder();

            var binary = Integer.toBinaryString(tcpFlag);

            for (var index = binary.length() - 1; index >= 0; index--)
            {
                if (binary.charAt(index) == '1' && binary.length() - index - 1 <= TCP_FLAGS.length)
                {
                    flags.append(TCP_FLAGS[binary.length() - index - 1]).append("|");
                }
            }

            if (!flags.isEmpty())
            {
                flags.deleteCharAt(flags.length() - 1);

                return flags.toString();
            }
        }
        catch (Exception ignored)
        {
        }

        return GlobalConstants.UNKNOWN.toLowerCase(Locale.ROOT);
    }

    /**
     * Maps interface indices to interface names for a network device.
     * <p>
     * This method extracts interface information from a metric configuration item
     * and creates a mapping between interface indices and interface names for a specific
     * network device (identified by its IP address).
     * <p>
     * The mapping is stored in the interfaceNamesBySource map, which is used during
     * flow processing to translate numeric interface indices into human-readable
     * interface names, making the flow data more meaningful for analysis and visualization.
     * <p>
     * This method is called during initialization and when interface configuration changes
     * are detected to ensure that the interface name mappings are always up-to-date.
     *
     * @param item the metric configuration item containing interface information
     */
    private void setInterfaceName(JsonObject item)
    {
        try
        {
            var context = new HashMap<String, String>();

            var objects = item.getJsonObject(Metric.METRIC_CONTEXT).getJsonArray(NMSConstants.OBJECTS);

            if (objects != null && !objects.isEmpty())
            {
                for (var j = 0; j < objects.size(); j++)
                {
                    context.put(objects.getJsonObject(j).getString(NMSConstants.INTERFACE_INDEX), objects.getJsonObject(j).getString(NMSConstants.INTERFACE_NAME));
                }

                interfaceNamesBySource.put(ObjectConfigStore.getStore().getItem(item.getLong(Metric.METRIC_OBJECT), false).getString(AIOpsObject.OBJECT_IP), context);
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }

    /**
     * Stops the flow processor and releases resources.
     * <p>
     * This method is called when the verticle is undeployed. It stops the event engine
     * and releases any resources used by the flow processor.
     * <p>
     * The method ensures a clean shutdown of the flow processor by properly stopping
     * the event engine, which handles the graceful termination of event processing.
     *
     * @param promise the promise to complete when the flow processor has stopped
     */
    @Override
    public void stop(Promise<Void> promise)
    {
        eventEngine.stop(vertx, promise);
    }
}
