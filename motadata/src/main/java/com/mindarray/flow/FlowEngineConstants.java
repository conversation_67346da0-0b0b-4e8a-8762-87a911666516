/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

package com.mindarray.flow;

import io.vertx.core.json.JsonArray;
import org.joda.time.format.DateTimeFormat;
import org.joda.time.format.DateTimeFormatter;

import java.util.stream.Stream;

import static com.mindarray.eventbus.EventBusConstants.EVENT_SOURCE;

/**
 * Constants used throughout the flow processing system.
 * <p>
 * This class defines a comprehensive set of constants used for processing and analyzing network flow data.
 * It includes constants for flow direction (ingress/egress), source and destination information,
 * traffic metrics, and other flow-related parameters.
 * <p>
 * These constants are used as field names in JSON objects representing flow data, as keys for
 * configuration parameters, and as identifiers in various flow processing operations.
 * <p>
 * The constants are organized into several categories:
 * <ul>
 *   <li>Flow direction constants (INGRESS_FLOW, EGRESS_FLOW)</li>
 *   <li>Source information constants (SOURCE_IP, SOURCE_PORT, etc.)</li>
 *   <li>Destination information constants (DESTINATION_IP, DESTINATION_PORT, etc.)</li>
 *   <li>Traffic metrics constants (FLOW_VOLUME_BYTES, PACKETS, etc.)</li>
 *   <li>Protocol and application constants</li>
 *   <li>Formatting and indexing constants</li>
 * </ul>
 */
public final class FlowEngineConstants
{
    /**
     * Constant representing ingress (incoming) flow direction with value 1
     */
    public static final short INGRESS_FLOW = 1;

    /**
     * Constant representing egress (outgoing) flow direction with value 2
     */
    public static final short EGRESS_FLOW = 2;

    /**
     * String representation of egress (outgoing) flow direction
     */
    public static final String EGRESS = "egress";

    /**
     * String representation of ingress (incoming) flow direction
     */
    public static final String INGRESS = "ingress";

    /**
     * Field name for source IP address in flow data
     */
    public static final String SOURCE_IP = "source.ip";

    //Source Constants
    /**
     * Field name for source port in flow data
     */
    public static final String SOURCE_PORT = "source.port";

    /**
     * Field name for original source port before any NAT translation
     */
    public static final String ORIGINAL_SOURCE_PORT = "original.source.port";

    /**
     * Field name for source interface index in flow data
     */
    public static final String SOURCE_INTERFACE_INDEX = "source.if.index";

    /**
     * Field name for source Autonomous System number
     */
    public static final String SOURCE_AS = "source.as";

    /**
     * Field name for peer source information
     */
    public static final String PEER_SOURCE = "peer.source";

    /**
     * Field name for source country in geolocation data
     */
    public static final String SOURCE_COUNTRY = "source.country";

    /**
     * Field name for source city in geolocation data
     */
    public static final String SOURCE_CITY = "source.city";

    /**
     * Field name for source latitude in geolocation data
     */
    public static final String SOURCE_LATITUDE = "source.latitude";

    /**
     * Field name for source longitude in geolocation data
     */
    public static final String SOURCE_LONGITUDE = "source.longitude";

    /**
     * Field name for destination IP address in flow data
     */
    public static final String DESTINATION_IP = "destination.ip";

    //Destination Constants
    /**
     * Field name for destination port in flow data
     */
    public static final String DESTINATION_PORT = "destination.port";

    /**
     * Field name for original destination port before any NAT translation
     */
    public static final String ORIGINAL_DESTINATION_PORT = "original.destination.port";

    /**
     * Field name for destination interface index in flow data
     */
    public static final String DESTINATION_INTERFACE_INDEX = "destination.if.index";

    /**
     * Field name for destination Autonomous System number
     */
    public static final String DESTINATION_AS = "destination.as";

    /**
     * Field name for destination IP's Autonomous System information
     */
    public static final String DESTINATION_IP_AS = "destination.ip.as";

    /**
     * Field name for peer destination information
     */
    public static final String PEER_DESTINATION = "peer.destination";

    /**
     * Field name for destination country in geolocation data
     */
    public static final String DESTINATION_COUNTRY = "destination.country";

    /**
     * Field name for destination city in geolocation data
     */
    public static final String DESTINATION_CITY = "destination.city";

    /**
     * Field name for destination latitude in geolocation data
     */
    public static final String DESTINATION_LATITUDE = "destination.latitude";

    /**
     * Field name for destination longitude in geolocation data
     */
    public static final String DESTINATION_LONGITUDE = "destination.longitude";

    /**
     * Field name for tag information in flow data
     */
    public static final String TAG = "tag";

    /**
     * Field name for total volume of bytes in the flow
     */
    public static final String FLOW_VOLUME_BYTES = "flow.volume.bytes";

    /**
     * Field name for number of flows per second
     */
    public static final String FLOWS_PER_SEC = "flows.per.sec";

    /**
     * Field name for volume of bytes per second in the flow
     */
    public static final String FLOW_VOLUME_BYTES_PER_SEC = "flow.volume.bytes.per.sec";


    //Other Constants
    /**
     * Field name for Type of Service (ToS) in IP header
     */
    public static final String TOS = "tos";

    /**
     * Field name for number of flows
     */
    public static final String FLOWS = "flows";

    /**
     * Field name for number of packets in the flow
     */
    public static final String PACKETS = "packets";

    /**
     * Field name for protocol used in the flow (TCP, UDP, ICMP, etc.)
     */
    public static final String PROTOCOL = "protocol";

    /**
     * Field name for duration of the flow in seconds
     */
    public static final String DURATION = "duration";

    /**
     * Field name for application identified in the flow
     */
    public static final String APPLICATION = "application";

    /**
     * Field name for TCP flags in the flow
     */
    public static final String TCP_FLAGS = "tcp.flags";

    /**
     * Field name for TCP retransmissions count
     */
    public static final String TCP_RETRANSMISSIONS = "tcp.retransmissions";

    /**
     * Field name for total volume of bytes in the flow
     */
    public static final String VOLUME_BYTES = "volume.bytes";

    /**
     * Field name for volume of bytes in ingress (incoming) direction
     */
    public static final String INGRESS_VOLUME_BYTES = "ingress.volume.bytes";

    /**
     * Field name for volume of bytes in egress (outgoing) direction
     */
    public static final String EGRESS_VOLUME_BYTES = "egress.volume.bytes";

    /**
     * Field name for number of packets in ingress (incoming) direction
     */
    public static final String INGRESS_PACKETS = "ingress.packets";

    /**
     * Field name for number of packets in egress (outgoing) direction
     */
    public static final String EGRESS_PACKETS = "egress.packets";

    /**
     * Field name for volume of bytes per second
     */
    public static final String VOLUME_BYTES_PER_SEC = "volume.bytes.per.sec";

    /**
     * Field name for volume of bytes per packet (average packet size)
     */
    public static final String VOLUME_BYTES_PER_PACKET = "volume.bytes.per.packet";

    /**
     * Field name for number of packets per second
     */
    public static final String PACKETS_PER_SEC = "packets.per.sec";

    /**
     * Field name for country code in geolocation data
     */
    public static final String COUNTRY_CODE = "country.code";

    /**
     * Date-time formatter for flow timestamps in format "yyyy-MM-dd HH:mm:ss.SSSSSS"
     */
    public static final DateTimeFormatter FLOW_DATE_TIME_FORMAT = DateTimeFormat.forPattern("yyyy-MM-dd HH:mm:ss.SSSSSS");

    /**
     * Array of column names that should be indexed for efficient querying
     */
    public static final JsonArray FLOW_INDEXABLE_COLUMNS = new JsonArray(Stream.of(EVENT_SOURCE, DESTINATION_IP_AS, PROTOCOL, DESTINATION_AS, SOURCE_AS, "source.threat", "destination.threat", SOURCE_IP, DESTINATION_IP, SOURCE_INTERFACE_INDEX, DESTINATION_INTERFACE_INDEX, APPLICATION, TCP_FLAGS, SOURCE_PORT, DESTINATION_PORT, TOS, SOURCE_COUNTRY, DESTINATION_COUNTRY, SOURCE_CITY, DESTINATION_CITY, "source.aso", "destination.aso", "source.domain", "destination.domain", "source.isp", "destination.isp", "user").toList());

    /**
     * Field name for custom sampling rate configured for an interface
     */
    public static final String INTERFACE_CUSTOM_SAMPLING_RATE = "interface.custom.sampling.rate";

    /**
     * Field name for sampling rate of an interface
     */
    public static final String INTERFACE_SAMPLING_RATE = "interface.sampling.rate";

    /**
     * Private constructor to prevent instantiation of this constants class.
     * All members of this class are static constants and the class should not be instantiated.
     */
    private FlowEngineConstants()
    {
    }

}
