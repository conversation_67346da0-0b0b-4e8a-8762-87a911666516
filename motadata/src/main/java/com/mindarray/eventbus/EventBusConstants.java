/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

/* Change Logs:
 *   Date           Author              Notes
 *   6-Feb-2025     <PERSON><PERSON>      Added Constant for Credential Profile.
 *   28-Feb-2025    Bharat            MOTADATA-5233: Changed ui.action.widget.share to ui.action.share
 *   28-Feb-2025    Smit Prajapati    MOTADATA-4956: Added Constants for Rule Based Tagging
 *   5-Mar-2025     Bharat            MOTADATA-4740: Two factor authentication 2FA
 *   25-Mar-2025    Smit Prajapati    MOTADATA-5435: Flow back-pressure mechanism.
 *   6-Mar-2025     <PERSON><PERSON>thviraj        MOTADATA-5331: NetRoute ,NetRoute policy and visualization constant added
 *   9-Apr-2025     Bharat            MOTADATA-5141: Alert Drill-down from email and Teams Notification
 *   18-Apr-2025    Vismit            MOTADATA-5613: Added event config notification.
 *   22-Apr-2025    Umang Sharma      Added event for flap datastore dump.
 *   2-Jun-2025		Smit Prajapati	  MOTADATA-:6417 added const for stat calculators
 *   10-Jun-2025    Pruthvi		      Added support for delete colum mapper
 *   2-Jun-2025     Smit Prajapati    MOTADATA-6418: EventPolicyInspector/EventPolicyAggregator Support
 *   05-Jun-2025    Vismit            MOTADATA-5333: Added event for config change tracker.
 * */

package com.mindarray.eventbus;

import com.mindarray.Bootstrap;
import com.mindarray.BootstrapAgent;
import com.mindarray.GlobalConstants;
import com.mindarray.agent.AgentConstants;
import com.mindarray.api.AIOpsObject;
import com.mindarray.api.Agent;
import com.mindarray.api.RemoteEventProcessor;
import com.mindarray.db.DBConstants;
import com.mindarray.nms.NMSConstants;
import com.mindarray.store.RemoteEventProcessorConfigStore;
import com.mindarray.util.*;
import io.vertx.core.buffer.Buffer;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;

import java.util.*;
import java.util.stream.Collectors;

import static com.mindarray.GlobalConstants.*;
import static com.mindarray.InfoMessageConstants.EVENT_TRACKER_WORKER_THREAD_ASSIGNED;
import static com.mindarray.agent.AgentConstants.AGENT;
import static com.mindarray.api.RemoteEventProcessor.*;

/**
 * Central repository for event bus related constants, utilities, and event handling methods.
 * <p>
 * This class serves as the backbone of the event-driven architecture in the Motadata application,
 * providing:
 * <p>
 * 1. Constants for event types, topics, and actions used throughout the application
 * 2. Utility methods for event handling (publishing, tracking, completing events)
 * 3. Registration methods for different components (agents, remote processors)
 * 4. Enums for change notification types and event routing strategies
 * <p>
 * The event bus is a critical communication mechanism that enables loose coupling between
 * components, allowing them to interact without direct dependencies. Events are used for
 * various purposes including:
 * <p>
 * - Notifications (alerts, status changes)
 * - Commands (start/stop operations)
 * - Data transfer (metrics, logs, configuration)
 * - System coordination (heartbeats, registration)
 * <p>
 * This class is designed to be used as a static utility, and therefore has a private constructor
 * to prevent instantiation.
 */
public final class EventBusConstants
{
    public static final String EVENT_REPLY_CONTEXTS = "event.reply.contexts";

    // db realted events
    public static final String EVENT_DB_COMPACT = "event.db.compact";

    //event general const
    public static final String EVENT_SOURCE = "event.source";

    public static final String EVENT = "event";

    public static final String EVENT_ID = "event.id";

    public static final String EVENT_TYPE = "event.type";

    public static final String ENGINE_TYPE = "engine.type";
    public static final String EVENT_LATENCY = "latency.ms";

    public static final String EVENT_COMPRESSION_TYPE = "compression.type";

    public static final String EVENT_ROUTER_CONFIG = "event.router.config";

    public static final String EVENT_NAME = "event.name";

    public static final String EVENT_TOPIC = "event.topic";

    public static final String EVENT_CONTEXT = "event.context";

    public static final String EVENT_STATE = "event.state";
    public static final String EVENT_PROGRESS_LOG = "event.progress.log";

    public static final String EVENT_STATE_RUNNING = "running";

    public static final String EVENT_STATE_COMPLETED = "completed";

    public static final String EVENT_STATE_QUEUED = "queued";

    public static final String EVENT_TIMESTAMP = "event.timestamp";

    public static final String EVENT_SCHEDULER = "event.scheduler";
    public static final String EVENT_VOLUME_BYTES = "event.volume.bytes";
    public static final String EVENT_SERVER = "server.event";

    public static final String EVENT_UI = "ui.event";

    public static final String EVENT_USER = "user.event.";
    public static final String EVENT_ADDRESS = "address";
    public static final String EVENT_USER_REGEX = "user.event\\..+";
    public static final String EVENT_QUERY_BULK = "bulk.query";
    public static final String EVENT_SUBSCRIBERS = "event.subscribers";

    public static final String EVENT_DIR = "events";
    public static final String EVENT_QUERY = ".query";

    public static final String EVENT_COPY_REQUIRED = "copy.required";

    public static final String EVENT_ADD = "event.add";

    public static final String EVENT_SUCCEED = "event.succeed";


    public static final String ENGINE_CATEGORY = "engine.category";
    public static final String PENDING_EVENTS = "pending.events";
    public static final String FINISHED_EVENTS = "finished.events";
    public static final String QUEUED_EVENTS = "queued.events";

    public static final String EVENT_FLOW_CACHE_CLEANUP = "flow.cache.cleanup";
    public static final String EVENT_FLOW_QUOTA_LIMIT_RESET = "flow.quota.limit.reset";

    //event tracker related util method...
    public static final String EVENT_PROGRESS_UPDATE = "event.progress.update";

    public static final String EVENT_TRACKER = "event.tracker";

    public static final String EVENT_FAIL = "event.fail";

    // event type const....
    public static final String EVENT_ABORT = "event.abort";
    public static final String EVENT_DNS_LOOKUP = "dns.lookup";
    public static final String EVENT_DNS_REVERSE_LOOKUP = "dns.reverse.lookup";
    public static final String EVENT_EMAIL_NOTIFICATION = "email.notification";
    public static final String EVENT_OAUTH_CONTEXT_GENERATE = "oauth.context.generate";
    public static final String EVENT_OAUTH_TOKEN_GENERATE = "oauth.token.generate";
    public static final String EVENT_OAUTH_CONTEXT_UPDATE = "oauth.context.update";
    public static final String EVENT_OAUTH_CONTEXT_FETCH = "oauth.context.fetch";

    public static final String EVENT_NOTIFICATION = "notification";

    public static final String EVENT_PUBLICATION = "publication";

    public static final String EVENT_PUBLICATION_DATASTORE_WRITE = "publication.datastore.write";
    public static final String EVENT_PUBLICATION_DATASTORE_READ = "publication.datastore.read";
    public static final String EVENT_PUBLICATION_MOTADATA_MANAGER = "publication.motadata.manager";
    public static final String EVENT_PUBLICATION_MOTADATA_OBSERVER = "publication.motadata.observer";

    public static final String EVENT_REMOTE = "remote";

    public static final String EVENT_AGENT = "agent";
    public static final String EVENT_REMOTE_EVENT_PROCESSOR = "remote.event.processor";
    public static final String EVENT_CONFIG_UPGRADE = "config.upgrade";
    public static final String EVENT_CONFIG_RUNBOOK = "config.runbook";

    public static final String EVENT_RUNBOOK = "runbook";
    public static final String EVENT_DATABASE_BACKUP = "database.backup";
    public static final String EVENT_DATABASE_RESTORE = "database.restore";
    public static final String EVENT_MASTER_UPGRADE = "master.upgrade";
    public static final String EVENT_MANAGER_UPGRADE = "manager.upgrade";

    // motadata manager constants

    public static final String EVENT_MOTADATA_MANAGER = "motadata.manager";
    public static final String EVENT_APP = "app";
    public static final String EVENT_HA_OBSERVER = "ha.observer";

    public static final String EVENT_MANAGER_RESPONSE_PROCESSOR = "manager.response.processor";
    public static final String EVENT_MOTADATA_MANAGER_HEARTBEAT = "motadata.manager.heartbeat";

    public static final String EVENT_RUNBOOK_WORKLOG_QUERY = "runbook.worklog.query";

    public static final String EVENT_DATASTORE_ACKNOWLEDGEMENT = "datastore.acknowledgement";

    public static final String EVENT_PLUGIN_ENGINE_RESPONSE_PROCESSOR = "plugin.engine.response.processor";
    public static final String EVENT_RUNBOOK_STATE_CHANGE = "runbook.state.change";

    public static final String EVENT_LOG = "log";
    public static final String EVENT_LOG_PATTERN = "log.pattern";
    public static final String EVENT_LOG_PROCESS = "log.process";

    public static final String EVENT_FLOW = "flow";
    public static final String EVENT_FLOW_STAT = "flow.stat";
    public static final String EVENT_LOG_STAT = "log.stat";
    public static final String EVENT_TRAP = "trap";
    public static final String EVENT_TRAP_ACTION = "trap.action";
    public static final String EVENT_LOG_PARSING_PROBE = "log.parsing.probe";
    public static final String EVENT_METRIC_POLICY = "metric.policy";
    public static final String EVENT_AIOPS_METRIC_POLICY = "aiops.metric.policy";
    public static final String EVENT_AIOPS_METRIC_POLICY_MANAGER = "aiops.metric.policy.manager";
    public static final String EVENT_AIOPS_METRIC_POLICY_RESPONSE = "aiops.metric.policy.response";
    public static final String EVENT_EVENT_POLICY_AGGREGATE = "event.policy.aggregate";
    public static final String EVENT_EVENT_POLICY_INSPECT = "event.policy.inspect";
    public static final String EVENT_EVENT_POLICY_TEST = "event.policy.test";
    public static final String EVENT_METRIC_POLLER_TEST = "metric.poller.test";
    public static final String EVENT_METRIC_AGENT = "metric.agent";
    public static final String EVENT_PACKET_AGENT = "packet.agent";
    public static final String EVENT_HEALTH_METRIC = "health.metric";
    public static final String EVENT_SSH_CLIENT_MANAGER = "ssh.client.manager";
    public static final String EVENT_SSH_CLIENT_CLOSE = "ssh.client.close";

    public static final String EVENT_METRIC_POLL = "metric.poll";
    public static final String EVENT_METRIC_POLL_SCHEDULE = "metric.poll.schedule";

    public static final String EVENT_CHANGE_NOTIFICATION = "change.notification";

    public static final String EVENT_CHANGE_LOCAL_NOTIFICATION = "change.local.notification";

    public static final String EVENT_OBJECT_PROVISION = "object.provision";

    public static final String EVENT_OBJECT_UNPROVISION = "object.unprovision";
    public static final String EVENT_METRIC_PROVISION = "metric.provision";
    public static final String EVENT_METRIC_UNPROVISION = "metric.unprovision";
    public static final String EVENT_METRIC_INSTANCE_UNPROVISION = "metric.instance.unprovision";

    public static final String EVENT_METRIC_ENABLE = "metric.enable";
    public static final String EVENT_METRIC_DISABLE = "metric.disable";
    public static final String EVENT_METRIC_SUSPEND = "metric.suspend";
    public static final String EVENT_OBJECT_ENABLE = "object.enable";
    public static final String EVENT_OBJECT_DISABLE = "object.disable";
    public static final String EVENT_OBJECT_MAINTENANCE = "object.maintenance";
    public static final String EVENT_METRIC_REDISCOVER = "metric.rediscover";

    public static final String EVENT_METRIC_POLL_RESPONSE = "metric.poll.response";


    public static final String EVENT_POLLING_ERROR_QUERY = "polling.error.query";

    public static final String EVENT_POLLING_ERROR_NOTIFICATION_TEST = "polling.error.notification.test";

    public static final String EVENT_DISCOVERY = "discovery";
    public static final String EVENT_DISCOVERY_RUN = "discovery.run";
    public static final String EVENT_DISCOVERY_COMPLETE = "discovery.complete";
    public static final String EVENT_SCHEDULER_COMPLETE = "scheduler.complete";
    public static final String EVENT_DISCOVERY_ABORT = "discovery.abort";
    public static final String EVENT_DISCOVERY_RESPONSE = "discovery.response";

    public static final String EVENT_REDISCOVER = "rediscover";

    public static final String EVENT_DATASTORE_PING = "datastore.ping";

    public static final String EVENT_CONFIG_UPGRADE_MANAGER = "config.upgrade.manager";

    public static final String EVENT_PLUGIN_ENGINE = "plugin.engine";
    public static final String EVENT_LICENSE_UPDATE = "license.update";
    public static final String EVENT_PLUGIN_ENGINE_ABORT = "plugin.engine.abort";
    public static final String EVENT_REDISCOVER_STOP = "rediscover.stop";

    public static final String EVENT_ROUTER = "router";
    public static final String EVENT_TIME_OUT = "time.out";
    public static final String EVENT_CLEAR_CONTEXT = "clear";
    public static final String EVENT_REDISCOVER_RESPONSE = "rediscover.response";
    public static final String EVENT_PLUGIN_ENGINE_RESPONSE = "plugin.engine.response";
    public static final String EVENT_REDISCOVER_SCHEDULER_UPDATE = "rediscover.scheduler.update";
    public static final String EVENT_REPLY = ".reply";
    public static final String EVENT_REPLY_ADDRESS = "reply.address";

    public static final String EVENT_OBSERVER_HEARTBEAT = "observer.heartbeat";

    //config/cache sync constant for HA
    public static final String EVENT_HA_CONFIG_OBSERVER_SYNC = "ha.config.observer.sync";
    public static final String EVENT_HA_CACHE_OBSERVER_SYNC = "ha.cache.observer.sync";
    public static final String EVENT_HA_CONFIG_MANGER_SYNC = "ha.config.manager.sync";
    public static final String EVENT_HA_CACHE_MANAGER_SYNC = "ha.cache.manager.sync";

    // for datastore (with/without HA)
    public static final String EVENT_DATASTORE_WRITE = "datastore.write";

    // for datastore with HA
    public static final String EVENT_HA_DATASTORE_SECONDARY_SYNC = "ha.datastore.secondary.sync";                   //secondary data sync with primary..
    public static final String EVENT_HA_ACKNOWLEDGEMENT = "ha.acknowledgement";

    //Remote Event Processor Constant
    public static final String EVENT_METRIC_ENRICHER = "metric.enricher";
    public static final String EVENT_REGISTRATION = "registration";
    public static final String EVENT_DATASTORE_REGISTER = "datastore.register";
    public static final String EVENT_CONFIG_INIT = "config.init";
    public static final String EVENT_REMOTE_PROCESSOR_HEARTBEAT = "remote.event.processor.heartbeat";
    public static final String EVENT_REMOTE_PROCESSOR_START = "remote.event.processor.start";
    public static final String EVENT_REMOTE_PROCESSOR_STOP = "remote.event.processor.stop";

    public static final String EVENT_REMOTE_PROCESSOR_RESTART = "remote.event.processor.restart";
    public static final String EVENT_REMOTE_PROCESSOR_DELETE = "remote.event.processor.delete";
    public static final String EVENT_REMOTE_PROCESSOR_UPGRADE = "remote.event.processor.upgrade";
    public static final String EVENT_REMOTE_EVENT_PROCESSOR_ACTION = "remote.event.processor.action";

    public static final String EVENT_ACKNOWLEDGEMENT = "acknowledgement";

    public static final String EVENT_AGENT_RESPONSE_STREAM = "agent.response.stream";
    public static final String EVENT_AGENT_REGISTRATION = "agent.registration";

    public static final String EVENT_AGENT_START = "agent.start";
    public static final String EVENT_AGENT_ENABLE = "agent.enable";
    public static final String EVENT_AGENT_DISABLE = "agent.disable";
    public static final String EVENT_AGENT_UNKNOWN = "agent.unknown";

    public static final String EVENT_AGENT_CONFIGURATION_CHANGE = "agent.config.change";
    public static final String EVENT_AGENT_UPGRADE = "agent.upgrade";
    public static final String EVENT_AGENT_STOP = "agent.stop";

    public static final String EVENT_AGENT_RESTART = "agent.restart";

    public static final String EVENT_AGENT_DELETE = "agent.delete";
    public static final String EVENT_AGENT_CPU_WARNING = "cpu.warning.event";
    public static final String EVENT_AGENT_CPU_CRITICAL = "cpu.critical.event";
    public static final String EVENT_AGENT_MEMORY_WARNING = "memory.warning.event";
    public static final String EVENT_AGENT_MEMORY_CRITICAL = "memory.critical.event";
    public static final String EVENT_AGENT_HEARTBEAT = "agent.heartbeat";
    public static final String EVENT_AGENT_HEALTH = "agent.health";
    public static final String EVENT_AGENT_HEALTH_CLEAR = "agent.health.clear";
    public static final String EVENT_AGENT_DOWNLOAD_LOG = "agent.download.log";
    public static final String EVENT_AGENT_ACTION = "agent.action";
    public static final String EVENT_AGENT_UPLOAD_LOG = "agent.upload.log";
    public static final String EVENT_AGENT_HEALTH_STATS = "agent.health.stats";
    public static final String EVENT_UNKNOWN = "agent.unknown";
    public static final String EVENT_MEMORY_WARNING = "memory.warning.event";
    public static final String EVENT_MEMORY_CRITICAL = "memory.critical.event";
    public static final String EVENT_CPU_WARNING = "cpu.warning.event";
    public static final String EVENT_CPU_CRITICAL = "cpu.critical.event";
    public static final String EVENT_REPORT_EXPORT = "report.export";
    public static final String EVENT_REPORT = "report";
    public static final String EVENT_REPORT_RENDER = "report.render";
    public static final String EVENT_REPORT_RESPONSE_PROCESSOR = "report.response.processor";
    public static final String EVENT_AUTH_TOKEN_CREATE = "auth.token.create";
    public static final String EVENT_PAT_CREATE = "pat.create";
    public static final String EVENT_EVENT_POLICY_QUALIFY = "event.policy.qualify";
    public static final String EVENT_LOG_FORWARDER = "log.forwarder";

    // integration const
    public static final String EVENT_INTEGRATION_RESPONSE_GET = "integration.response.get";
    public static final String EVENT_INTEGRATION_TEST = "integration.test";
    public static final String EVENT_INTEGRATION_SYNC = "integration.sync";


    public static final String EVENT_INTEGRATION = "integration";
    public static final String EVENT_POLICY_ACTION_TRIGGER = "policy.action.trigger";

    //Compliance Policy

    public static final String EVENT_COMPLIANCE_POLICY = "compliance.policy";


    // Ha constants

    public static final String EVENT_SWITCH_OVER = "switch.over";

    // reporting const
    public static final String EVENT_STREAM = "tracker.stream";
    public static final String EVENT_STREAM_START = "tracker.stream.start";
    public static final String EVENT_STREAM_STOP = "tracker.stream.stop";
    public static final String EVENT_STREAM_CONTEXT = "tracker.stream.context";
    public static final String EVENT_AUDIT = "audit";
    public static final String EVENT_DIAGNOSTIC = "diagnostic";
    public static final String EVENT_DIAGNOSTIC_STATS = "diagnostic.stats";
    public static final String EVENT_AUDIT_EXPORT = "event.audit.export";
    public static final String EVENT_LOG_EXPORT = "event.log.export";

    public static final String EVENT_ENGINE_STATS = "engine.stats";
    public static final String EVENT_JVM_STATS = "jvm.stats";
    public static final String EVENT_HEALTH_MANAGER_NOTIFICATION = "health.manager.notification";
    public static final String EVENT_HEALTH_STATISTICS = "health.statistics";

    public static final String EVENT_ENGINE_STATS_RESPONSE = "engine.stats.response";
    public static final String EVENT_JVM_STATS_RESPONSE = "jvm.stats.response";
    public static final String EVENT_DATASTORE_STATS_RESPONSE = "datastore.stats.response";
    public static final String EVENT_DNS_CACHE_FLUSH = "dns.cache.flush";

    public static final String EVENT_USER_NOTIFICATION = "user.notification";
    public static final String EVENT_USER_NOTIFICATION_COUNT = "user.notification.count";
    public static final String EVENT_USER_NOTIFICATION_CLEAR = "user.notification.clear";
    public static final String EVENT_PROXY_SERVICE_REQUEST = "proxy.service.request";
    public static final String EVENT_CONFIG_CHANGE = "config.change";

    // live streamer const
    public static final String EVENT_STREAMING_BROADCAST = "streaming.broadcast";
    public static final String EVENT_STREAMING_START = "streaming.start";
    public static final String EVENT_STREAMING_STOP = "streaming.stop";
    public static final String EVENT_STREAMING_WORKER_TRACKER = "streaming.worker.tracker";
    public static final String EVENT_MAC_SCANNER_RESPONSE = "mac.scanner.response";

    public static final String EVENT_TOPOLOGY = "topology";
    public static final String EVENT_TOPOLOGY_RUN = "topology.run";
    public static final String EVENT_TOPOLOGY_COMPLETE = "topology.complete";
    public static final String EVENT_TOPOLOGY_STOP = "topology.stop";

    //dependency/topology const
    public static final String EVENT_TOPOLOGY_RESPONSE = "topology.response";

    public static final String EVENT_DEPENDENCY = "dependency";
    public static final String EVENT_DEPENDENCY_QUERY = "dependency.query";

    public static final String EVENT_AVAILABILITY_CORRELATION = "availability.correlation";

    public static final String EVENT_DATASTORE_QUERY_RESPONSE = "datastore.query.response";

    public static final String EVENT_COLUMN_MAPPER_UPDATE = "column.mapper.update";

    public static final String EVENT_METRIC_COLUMN_MAPPER_QUERY = "metric.column.mapper.query";
    public static final String EVENT_EVENT_COLUMN_MAPPER_QUERY = "event.column.mapper.query";
    public static final String EVENT_INDEXABLE_COLUMN_UPDATE = "indexable.column.update";
    public static final String EVENT_EVENT_CATEGORY_UPDATE = "event.category.update";
    public static final String EVENT_OBJECT_STATUS_UPDATE = "object.status.update";
    public static final String EVENT_OBJECT_STATUS_DURATION_QUERY = "object.status.duration.query";
    public static final String EVENT_DATASTORE_FLUSH = "datastore.flush";
    public static final String EVENT_DATASTORE_INIT = "datastore.init";

    public static final String EVENT_DATASTORE_RETENTION_TRIGGER = "event.datastore.retention.trigger";
    public static final String EVENT_INDEXABLE_COLUMN_QUERY = "indexable.column.query";
    public static final String EVENT_POLLER_AUTO_SCALE = "poller.auto.scale";
    public static final String EVENT_MOTADATA_STATS_QUERY = "motadata.stats.query";

    public static final String EVENT_VISUALIZATION = "visualization.render";
    public static final String EVENT_VISUALIZATION_METRIC = "visualization.metric.render";
    public static final String EVENT_VISUALIZATION_AVAILABILITY = "visualization.availability.render";
    public static final String EVENT_VISUALIZATION_EVENT = "visualization.event.render";
    public static final String EVENT_VISUALIZATION_EVENT_HISTORY = "visualization.event.history.render";
    public static final String EVENT_VISUALIZATION_NETROUTE = "visualization.netroute.render";
    public static final String EVENT_VISUALIZATION_CACHING = "visualization.caching";
    public static final String EVENT_VISUALIZATION_METRIC_RESPONSE_PROCESSOR = "visualization.metric.response.processor";
    public static final String EVENT_VISUALIZATION_EVENT_RESPONSE_PROCESSOR = "visualization.event.response.processor";
    public static final String EVENT_VISUALIZATION_EVENT_HISTORY_RESPONSE_PROCESSOR = "visualization.event.history.response.processor";
    public static final String EVENT_VISUALIZATION_CACHE_INVALIDATE = "visualization.cache.invalidate";
    public static final String EVENT_VISUALIZATION_SESSION_ACTIVE = "visualization.session.active";
    public static final String EVENT_VISUALIZATION_SESSION_INACTIVE = "visualization.session.inactive";
    public static final String EVENT_VISUALIZATION_COMPLIANCE = "visualization.compliance.render";

    public static final String EVENT_VISUALIZATION_QUERY_ABORT = "visualization.query.abort";
    public static final String EVENT_RESTART_ENGINE = "restart.engine";
    public static final String EVENT_VISUALIZATION_RESPONSE = "visualization.response";
    public static final String EVENT_VISUALIZATION_SUB_QUERY_CONTEXT = "visualization.sub.query.context";
    public static final String EVENT_VISUALIZATION_REALTIME_WIDGET_REFRESH = "visualization.realtime.widget.refresh";
    //policy constants
    public static final String EVENT_METRIC_POLICY_TRIGGER_DURATION_QUERY = "policy.metric.trigger.duration.query";
    public static final String EVENT_METRIC_POLICY_SEVERITY_DURATION_CALCULATE = "metric.policy.severity.duration.calculate";
    public static final String EVENT_METRIC_POLICY_CLEAR = "metric.policy.clear";
    public static final String EVENT_POLICY_ACTION = "policy.action";
    public static final String EVENT_EVENT_POLICY_SUPPRESS = "event.policy.suppress";
    public static final String EVENT_METRIC_POLICY_SUPPRESS = "metric.policy.suppress";
    public static final String EVENT_METRIC_POLICY_TRIGGER_TICK_QUERY = "metric.policy.trigger.tick.query";
    public static final String EVENT_METRIC_POLICY_ACKNOWLEDGE = "metric.policy.acknowledge";
    public static final String EVENT_METRIC_POLICY_COMMENT_UPDATE = "metric.policy.comment.update";
    public static final String EVENT_POLICY_NOTIFICATION = "policy.notification";
    public static final String EVENT_EVENT_POLICY_AGGREGATOR_QUERY = "event.policy.aggregator.query";
    public static final String EVENT_EVENT_POLICY_AGGREGATOR_RESPONSE = "event.policy.aggregator.response";

    public static final String EVENT_SEVERITY_CHANGE = "severity.change";
    public static final String EVENT_ACTIVE_NOTIFICATION_QUERY = "active.notification.query";
    public static final String EVENT_ACTIVE_NOTIFICATION_CLEAR = "active.notification.clear";

    // netroute policy const
    public static final String EVENT_NETROUTE_POLICY = "netroute.policy";
    public static final String EVENT_NETROUTE_POLICY_CLEAR = "netroute.policy.clear";
    public static final String EVENT_NETROUTE_POLICY_SUPPRESS = "netroute.policy.suppress";
    public static final String EVENT_NETROUTE_POLICY_TRIGGER_DURATION_QUERY = "netroute.policy.trigger.duration.query";
    public static final String EVENT_NETROUTE_POLICY_TRIGGER_TICK_QUERY = "netroute.policy.trigger.tick.query";
    public static final String EVENT_NETROUTE_POLICY_ACKNOWLEDGE = "netroute.policy.acknowledge";
    public static final String EVENT_NETROUTE_POLICY_COMMENT_UPDATE = "netroute.policy.comment.update";
    public static final String EVENT_NETROUTE_POLICY_SEVERITY_DURATION_CALCULATE = "netroute.policy.severity.duration.calculate";

    public static final String EVENT_OBJECT_STATUS_CHANGE = "object.status.change";

    public static final String EVENT_USER_PING = "user.ping";

    public static final String REMOTE_SESSION_UPDATE = "remote.session.update";


    public static final String EVENT_CONFIG_QUERY = "config.query";
    public static final String EVENT_SEVERITY_QUERY = "severity.query";
    public static final String EVENT_SEVERITY_COUNT_QUERY = "severity.count.query";
    public static final String EVENT_OBJECT_STATUS_QUERY = "object.status.query";

    public static final String EVENT_VISUALIZATION_QUERY = "visualization.query";

    public static final String EVENT_VISUALIZATION_QUERY_RESPONSE = "visualization.query.response";

    public static final String EVENT_TAG_RULE_RUN = "tag.rule.run";

    public static final String EVENT_TAG_SYNC = "tag.sync";

    /// ////////////////////// Configuration Events ///////////////////////////////////

    public static final String EVENT_CONFIG_CREATE = "config.create";

    public static final String EVENT_CONFIG_DELETE = "config.delete";

    public static final String EVENT_CONFIG_RESPONSE_PROCESSOR = "config.response.processor";

    public static final String EVENT_CONFIG_DISCOVERY = "config.discovery";

    public static final String EVENT_CONFIG_BACKUP = "config.backup";

    public static final String EVENT_CONFIG_RESTORE = "config.restore";

    public static final String EVENT_CONFIG_SYNC = "config.sync";

    public static final String EVENT_CONFIG_INFO = "config.info";

    public static final String EVENT_CONFIG_REQUEST_STATE_CHANGE = "config.request.state.change";

    public static final String EVENT_CONFIG_BACKUP_CLEANUP = "config.backup.cleanup";

    public static final String EVENT_CONFIG_MANAGE = "config.manage";

    public static final String EVENT_CONFIG_EXPORT = "config.export";

    public static final String EVENT_CONFIG_NOTIFICATION = "event.config.notification";

    public static final String EVENT_CONFIG_CHANGE_REQUEST_ENQUEUE = "event.config.change.request.enqueue";

    // NetRoute const

    public static final String EVENT_NETROUTE_STATUS_UPDATE = "netroute.status.update";
    public static final String EVENT_NETROUTE_POLL_RESPONSE = "netroute.poll.response";
    public static final String EVENT_NETROUTE_POLICY_RESPONSE = "netroute.policy.response";

    // compliance constant
    public static final String EVENT_COMPLIANCE_MANAGER = "compliance.manager";
    public static final String EVENT_RULE_ENGINE = "rule.engine";
    public static final String EVENT_COMPLIANCE_POLICY_INSPECTION_COMPLETE = "compliance.policy.inspection.complete";
    public static final String EVENT_COMPLIANCE_POLICY_STATE_CHANGE = "compliance.policy.state.change";
    public static final String EVENT_COMPLIANCE_POLICY_DATASTORE_DUMP = "compliance.policy.datastore.dump";
    //log constant
    public static final String EVENT_LOG_PARSER_PLUGIN_PARSE = "log.parser.plugin.parse";

    public static final String CHANGE_NOTIFICATION_TYPE = "change.notification.type";
    public static final String DEFAULT_ROUTER_INDEX = ".1";
    public static final String EVENT_VISUALIZATION_TEST = EVENT_VISUALIZATION + "." + ENV_TEST;
    public static final String EVENT_VISUALIZATION_CACHING_TEST = EVENT_VISUALIZATION_CACHING + "." + ENV_TEST;
    public static final String EVENT_AIOPS_METRIC_POLICY_MANAGER_TEST = EVENT_AIOPS_METRIC_POLICY_MANAGER + "." + ENV_TEST;

    //SLO constant
    public static final String EVENT_SLO_QUALIFY = "slo.qualify";
    public static final String EVENT_SLO_UPDATE = "slo.update";

    // event topic const
    public static final String EVENT_METRIC_ENRICHER_TEST = EVENT_METRIC_ENRICHER + "." + ENV_TEST;

    public static final String UI_EVENT_UUID = "ui.event.uuid";  //ui event related common consts..

    /// ////////////////////// UI ACTION TYPES ///////////////////////////////////
    public static final String UI_ACTION_CONFIGURATION_TEST = "ui.action.ldap.server.test";
    public static final String UI_ACTION_MAIL_SERVER_CONFIGURATION_TEST = "ui.action.mail.server.test";
    public static final String UI_ACTION_SMS_GATEWAY_CONFIGURATION_TEST = "ui.action.sms.gateway.test";
    public static final String UI_ACTION_PROXY_SERVER_TEST = "ui.action.proxy.server.test";
    public static final String UI_ACTION_TWO_FACTOR_AUTHENTICATION_TEST = "ui.action.two.factor.authentication.test";
    public static final String UI_ACTION_CREDENTIAL_PROFILE_TEST = "ui.action.credential.profile.test";
    public static final String UI_ACTION_DNS_SERVER_CONFIGURATION_TEST = "ui.action.dns.server.configuration.test";
    public static final String UI_ACTION_LDAP_SYNC = "ui.action.ldap.server.sync";
    public static final String UI_ACTION_OID_GROUP_TEST = "ui.action.oid.group.test";
    public static final String UI_ACTION_METRIC_PLUGIN_TEST = "ui.action.metric.plugin.test";
    public static final String UI_ACTION_TOPOLOGY_PLUGIN_TEST = "ui.action.topology.plugin.test";
    public static final String UI_ACTION_RUNBOOK_PLUGIN_TEST = "ui.action.runbook.plugin.test";
    public static final String UI_ACTION_LOG_FORWARDER_PROFILE_TEST = "ui.action.log.forwarder.profile.test";
    public static final String UI_ACTION_STREAMING_START = "ui.action.streaming.start";
    public static final String UI_ACTION_STREAMING_STOP = "ui.action.streaming.stop";

    public static final String UI_ACTION_SCHEDULER_RUN = "ui.action.scheduler.run";
    public static final String UI_ACTION_REDISCOVER_STOP = "ui.action.rediscover.stop";
    public static final String UI_ACTION_REDISCOVER = "ui.action.rediscover";
    public static final String UI_ACTION_POLL = "ui.action.poll";
    public static final String UI_ACTION_REDISCOVER_START = "ui.action.rediscover.start";
    public static final String UI_ACTION_RUNBOOK_PLUGIN_RUN = "ui.action.runbook.plugin.run";
    public static final String UI_ACTION_TASK_STREAM_START = "ui.action.task.stream.start";
    public static final String UI_ACTION_TASK_STREAM_STOP = "ui.action.task.stream.stop";
    public static final String UI_ACTION_TASK_STREAM_CONTEXT = "ui.action.task.stream.context";
    public static final String UI_ACTION_TOPOLOGY_HIERARCHY_FETCH = "ui.action.topology.hierarchy.fetch";
    public static final String UI_ACTION_TOPOLOGY_PARENT_FETCH = "ui.action.topology.parent.fetch";
    public static final String UI_ACTION_TOPOLOGY_START = "ui.action.topology.start";
    public static final String UI_ACTION_TOPOLOGY_STOP = "ui.action.topology.stop";
    public static final String UI_ACTION_BACKUP_START = "ui.action.backup.start";
    public static final String UI_ACTION_BACKUP_STOP = "ui.action.backup.stop";
    public static final String UI_ACTION_TRAP_ACKNOWLEDGE = "ui.action.trap.acknowledge";

    public static final String UI_ACTION_COMPLIANCE_POLICY_RUN = "ui.action.compliance.policy.run";

    public static final String UI_ACTION_OBJECT_POLLING_ERROR_QUERY = "ui.action.object.polling.error.query";
    public static final String UI_ACTION_ARCHIVE_ENABLE_GET = "ui.action.archive.enable.get";
    public static final String UI_ACTION_DIAGNOSTIC_RUN = "ui.action.diagnostic.run";

    public static final String UI_ACTION_VISUALIZATION_RENDER = "ui.action.visualization.render";
    public static final String UI_ACTION_VISUALIZATION_ABORT = "ui.action.visualization.abort";
    public static final String UI_ACTION_LOG_PARSER_SAMPLE_FILE_READ = "ui.action.log.parser.sample.file.read";
    public static final String UI_ACTION_LOG_PARSER_SAMPLE_LOG_PARSE = "ui.action.log.parser.sample.log.parse";
    public static final String UI_ACTION_LOG_PARSER_SAMPLE_FILE_UPLOAD = "ui.action.log.parser.sample.file.upload";
    public static final String UI_ACTION_LOG_PARSER_PATTERN_DETECT = "ui.action.log.parser.pattern.detect";
    public static final String UI_ACTION_LOG_PARSER_PATTERN_PARSE = "ui.action.log.parser.pattern.parse";
    public static final String UI_ACTION_POLICY_CLEAR = "ui.action.policy.clear";
    public static final String UI_ACTION_POLICY_SUPPRESS = "ui.action.policy.suppress";
    public static final String UI_ACTION_POLICY_ACKNOWLEDGE = "ui.action.policy.acknowledge";
    public static final String UI_ACTION_POLICY_COMMENT_UPDATE = "ui.action.policy.comment.update";
    public static final String UI_ACTION_POLICY_ACTIVE_FLAP_GET = "ui.action.policy.active.flap.get";
    public static final String UI_ACTION_POLICY_QUERY = "ui.action.policy.query";
    public static final String UI_ACTION_METRIC_POLL_TIMESTAMP_FETCH = "ui.action.metric.poll.timestamp.fetch";
    public static final String UI_ACTION_SNMP_OID_SEARCH = "ui.action.snmp.oid.search";
    public static final String UI_ACTION_REPORT_EXPORT = "ui.action.report.export";
    public static final String UI_ACTION_LOGON_SESSION_POPULATE = "ui.action.logon.session.populate";
    public static final String UI_ACTION_USER_SESSION_UPDATE = "ui.action.user.session.update";
    public static final String UI_ACTION_USER_NAVIGATE = "ui.action.user.navigate";
    public static final String UI_ACTION_SHARE = "ui.action.share";
    public static final String UI_ACTION_SESSION_ACTIVE = "ui.action.session.active";
    public static final String UI_ACTION_SESSION_INACTIVE = "ui.action.session.inactive";
    public static final String UI_ACTION_LOGGER_LEVEL_CHANGE = "ui.action.logger.level.change";
    public static final String UI_ACTION_LOGGER_LEVEL_GET = "ui.action.logger.level.get";
    public static final String UI_ACTION_STORAGE_PROFILE_TEST = "ui.action.storage.profile.test";
    public static final String UI_ACTION_INTEGRATION_TEST = "ui.action.integration.test";
    public static final String UI_ACTION_INTEGRATION_SYNC = "ui.action.integration.sync";
    public static final String UI_ACTION_DECLARE_INCIDENT = "ui.action.declare.incident";
    public static final String UI_ACTION_INTEGRATION_RESPONSE_GET = "ui.action.integration.response.get";
    public static final String UI_ACTION_CONFIG_TEMPLATE_EXPORT = "ui.action.config.template.export";
    public static final String UI_ACTION_ARTIFACT_COMPATIBILITY_CHECK = "ui.action.artifact.compatibility.check";
    public static final String UI_ACTION_DISCOVERY_RESULT_EXPORT = "ui.action.discovery.result.export";
    public static final String UI_ACTION_INSTANCE_SEVERITY_QUERY = "ui.action.instance.severity.query";
    public static final String UI_ACTION_SSH_CLIENT_MANAGER = "ui.action.ssh.client.manager";
    public static final String UI_ACTION_TAG_RULE_TEST = "ui.action.tag.rule.test";
    public static final String UI_ACTION_TAG_RULE_RUN = "ui.action.tag.rule.run";

    /// //////////////// UI Notification consts..//////////////////////////////////

    public static final String UI_NOTIFICATION_CSV_EXPORT_READY = "ui.notification.csv.export.ready";
    public static final String UI_NOTIFICATION_DISCOVERY_STATISTICS = "ui.notification.discovery.statistics";
    public static final String UI_NOTIFICATION_DISCOVERY_PROBES = "ui.notification.discovery.probes";
    public static final String UI_NOTIFICATION_OBJECT_DISCOVERY_PROGRESS = "ui.notification.object.discovery.progress";
    public static final String UI_NOTIFICATION_DISCOVERY_PROGRESS = "ui.notification.discovery.progress";
    public static final String UI_NOTIFICATION_APP_DISCOVERY_PROGRESS = "ui.notification.app.discovery.progress";
    public static final String UI_NOTIFICATION_DISCOVERY_STATE_CHANGE = "ui.notification.discovery.state.change";
    public static final String UI_NOTIFICATION_REDISCOVER_PROGRESS = "ui.notification.rediscover.progress";
    public static final String UI_NOTIFICATION_REDISCOVER_PROVISION_PROGRESS = "ui.notification.rediscover.provision.progress";
    public static final String UI_NOTIFICATION_STREAMING_BROADCAST = "ui.notification.streaming.broadcast";
    public static final String UI_NOTIFICATION_OBJECT_PROVISION_PROGRESS = "ui.notification.object.provision.progress";

    public static final String UI_NOTIFICATION_AGENT = "ui.notification.agent";
    public static final String UI_NOTIFICATION_REMOTE_PROCESSOR = "ui.notification.remote.processor";

    public static final String UI_NOTIFICATION_USER_NOTIFICATION_CLEAR = "ui.notification.user.notification.clear";

    public static final String UI_NOTIFICATION_ACTIVE_NOTIFICATION_CLEAR = "ui.notification.active.notification.clear";

    public static final String UI_NOTIFICATION_ACTIVE_NOTIFICATION_QUERY = "ui.notification.active.notification.query";

    public static final String UI_NOTIFICATION_AGENT_UPGRADE_PROGRESS = "ui.notification.agent.upgrade.progress";
    public static final String UI_NOTIFICATION_REMOTE_EVENT_PROCESSOR_UPGRADE_PROGRESS = "ui.notification.remote.event.processor.upgrade.progress";
    public static final String UI_NOTIFICATION_REDISCOVER_ERROR = "ui.notification.rediscover.error";
    public static final String UI_NOTIFICATION_TOPOLOGY_ERROR = "ui.notification.topology.error";
    public static final String UI_NOTIFICATION_SERVICE_HEALTH = "ui.notification.service.health";
    public static final String UI_NOTIFICATION_PROXY_SERVICE_HEALTH = "ui.notification.proxy.service.health";
    public static final String UI_NOTIFICATION_USER_NOTIFICATION = "ui.notification.user.notification";
    public static final String UI_NOTIFICATION_WIDGET_QUERY_ID = "ui.notification.widget.query.id";
    public static final String UI_NOTIFICATION_REPORT_PROGRESS = "ui.notification.report.progress";
    public static final String UI_NOTIFICATION_FLOW_SETTINGS = "ui.notification.flow.settings";

    public static final String UI_NOTIFICATION_USER_LOGOUT = "ui.notification.user.logout";
    public static final String UI_NOTIFICATION_DISK_UTILIZATION_EXCEED_NOTIFICATION = "ui.notification.disk.utilization.exceed";
    public static final String UI_NOTIFICATION_SOUND_NOTIFICATION = "ui.notification.sound.notification";
    public static final String UI_NOTIFICATION_DIAGNOSTIC_ZIP_AVAILABLE = "ui.notification.diagnostic.zip.available";
    public static final String UI_NOTIFICATION_PASSWORD_EXPIRE = "ui.notification.password.expire";
    public static final String UI_NOTIFICATION_LICENSE_EXPIRE = "ui.notification.license.expire";
    public static final String UI_NOTIFICATION_REFRESH_TOKEN_EXPIRE = "ui.notification.refresh.token.expire";
    public static final String UI_INTEGRATION_PROFILE_TEST = "ui.integration.profile.test";

    /// ////////////////////// end////////////////////
    public static final Set<String> PASSOVER_EVENTS = Set.of(EVENT_MOTADATA_MANAGER_HEARTBEAT, EVENT_REMOTE_PROCESSOR_HEARTBEAT, EVENT_AGENT_HEARTBEAT);

    static final String EVENT_TOPIC_DELIMITER = " ";
    public static final String FLOW_TOPIC = EVENT_FLOW + EVENT_TOPIC_DELIMITER;
    public static final String AGENT_TOPIC = EVENT_AGENT + EVENT_TOPIC_DELIMITER;
    public static final String MOTADATA_OBSERVER_TOPIC = EVENT_HA_OBSERVER + EVENT_TOPIC_DELIMITER;
    public static final String MOTADATA_MANAGER_TOPIC = EVENT_MOTADATA_MANAGER + EVENT_TOPIC_DELIMITER;
    public static final String REMOTE_EVENT_PROCESSOR_TOPIC = EVENT_REMOTE_EVENT_PROCESSOR + EVENT_TOPIC_DELIMITER;
    public static final String SHUTDOWN_TOPIC = "shutdown" + EVENT_TOPIC_DELIMITER;
    public static final String DATASTORE_QUERY_ACK_TOPIC = "datastore.query.ack" + EVENT_TOPIC_DELIMITER;
    public static final String DATASTORE_QUERY_TOPIC = "datastore.query" + EVENT_TOPIC_DELIMITER;
    public static final String DATASTORE_CONNECTION_ALIVE_TOPIC = "datastore.connection.alive" + EVENT_TOPIC_DELIMITER;
    public static final String DATASTORE_QUERY_RESPONSE_TOPIC = EventBusConstants.EVENT_DATASTORE_QUERY_RESPONSE + EVENT_TOPIC_DELIMITER;
    public static final String DATASTORE_OPERATION_TOPIC = "datastore.operation" + EVENT_TOPIC_DELIMITER;
    public static final String DATASTORE_BROKER_OPERATION_TOPIC = "datastore.broker.operation" + EVENT_TOPIC_DELIMITER;
    private static final Logger LOGGER = new Logger(EventBusConstants.class, MOTADATA_EVENT_BUS, "Event Bus Constants");

    /**
     * Replaces dots with hyphens in a configuration string.
     * <p>
     * This utility method is used to sanitize or format configuration keys
     * by replacing dots (.) with hyphens (-). This is often necessary when
     * configuration keys need to be used in contexts where dots have special
     * meaning, such as in hierarchical configurations or file paths.
     *
     * @param config The configuration string to process
     * @return A new string with all dots replaced by hyphens
     */
    public static String replace(String config)
    {
        return config.replace(".", "-");
    }

    /**
     * Marks the start of an event and assigns it to a worker thread.
     * <p>
     * This method sends a message to the appropriate event bus address based on the bootstrap type:
     * - For COLLECTOR or SECONDARY installation mode: Sends to EVENT_REMOTE
     * - For AGENT: Sends to EVENT_AGENT with the agent UUID
     * - For all other types: Sends to EVENT_PROGRESS_UPDATE
     * <p>
     * The message includes the event ID, state (set to "running"), event type (EVENT_PROGRESS_UPDATE),
     * and a log message indicating which thread is handling the event and when it started.
     * <p>
     * This method is typically called when an asynchronous operation begins, allowing the system
     * to track its progress and status.
     *
     * @param eventId The unique identifier for the event
     * @param thread  The name of the thread that will handle the event
     */
    public static void startEvent(long eventId, String thread)
    {
        if (Bootstrap.bootstrapType() == GlobalConstants.BootstrapType.COLLECTOR || Bootstrap.getInstallationMode().equalsIgnoreCase(InstallationMode.SECONDARY.name()))
        {
            Bootstrap.vertx().eventBus().send(EventBusConstants.EVENT_REMOTE, new JsonObject().put(EventBusConstants.EVENT_ID, eventId)
                    .put(EventBusConstants.EVENT_STATE, EventBusConstants.EVENT_STATE_RUNNING)
                    .put(EventBusConstants.EVENT_TYPE, EVENT_PROGRESS_UPDATE)
                    .put(EventBusConstants.EVENT_PROGRESS_LOG, String.format(EVENT_TRACKER_WORKER_THREAD_ASSIGNED, thread, DateTimeUtil.timestamp())));
        }
        else if (Bootstrap.bootstrapType() == BootstrapType.AGENT)
        {
            Bootstrap.vertx().eventBus().send(EVENT_AGENT, new JsonObject().put(EVENT_ID, eventId)
                    .put(EVENT_STATE, EVENT_STATE_RUNNING)
                    .put(Agent.AGENT_UUID, BootstrapAgent.getAgentUUID())
                    .put(EVENT_TYPE, EVENT_PROGRESS_UPDATE)
                    .put(EVENT_PROGRESS_LOG, String.format(EVENT_TRACKER_WORKER_THREAD_ASSIGNED, thread, DateTimeUtil.timestamp())));
        }
        else
        {
            Bootstrap.vertx().eventBus().send(EventBusConstants.EVENT_PROGRESS_UPDATE, new JsonObject().put(EventBusConstants.EVENT_ID, eventId)
                    .put(EventBusConstants.EVENT_STATE, EventBusConstants.EVENT_STATE_RUNNING)
                    .put(EventBusConstants.EVENT_PROGRESS_LOG, String.format(EVENT_TRACKER_WORKER_THREAD_ASSIGNED, thread, DateTimeUtil.timestamp())));
        }
    }

    /**
     * Updates an event with progress information.
     * <p>
     * This is a convenience method that calls the three-parameter version of updateEvent
     * with a null state parameter, which means the event state will not be updated.
     *
     * @param eventId  The unique identifier for the event to update
     * @param progress The progress message to add to the event log
     * @see #updateEvent(long, String, String)
     */
    public static void updateEvent(long eventId, String progress)
    {
        updateEvent(eventId, progress, null);
    }

    /**
     * Updates an event with progress information and optionally updates its state.
     * <p>
     * This method sends a message to the appropriate event bus address based on the bootstrap type:
     * - For COLLECTOR or SECONDARY installation mode: Sends to EVENT_REMOTE
     * - For AGENT: Sends to EVENT_AGENT with the agent UUID
     * - For all other types: Sends to EVENT_PROGRESS_UPDATE
     * <p>
     * The message always includes the event ID, event type (EVENT_PROGRESS_UPDATE),
     * and the progress log message. If the state parameter is not null, the message
     * also includes the event state (set to "running").
     * <p>
     * This method is typically called during an asynchronous operation to provide
     * updates on its progress.
     *
     * @param eventId  The unique identifier for the event to update
     * @param progress The progress message to add to the event log
     * @param state    If not null, the event state will be updated to this value
     */
    public static void updateEvent(long eventId, String progress, String state)
    {
        if (Bootstrap.bootstrapType() == GlobalConstants.BootstrapType.COLLECTOR || Bootstrap.getInstallationMode().equalsIgnoreCase(InstallationMode.SECONDARY.name()))
        {
            if (state != null)
            {
                Bootstrap.vertx().eventBus().send(EventBusConstants.EVENT_REMOTE, new JsonObject().put(EventBusConstants.EVENT_ID, eventId)
                        .put(EventBusConstants.EVENT_TYPE, EVENT_PROGRESS_UPDATE)
                        .put(EventBusConstants.EVENT_STATE, EVENT_STATE_RUNNING)
                        .put(EventBusConstants.EVENT_PROGRESS_LOG, progress));
            }
            else
            {
                Bootstrap.vertx().eventBus().send(EventBusConstants.EVENT_REMOTE, new JsonObject().put(EventBusConstants.EVENT_ID, eventId)
                        .put(EventBusConstants.EVENT_TYPE, EVENT_PROGRESS_UPDATE)
                        .put(EventBusConstants.EVENT_PROGRESS_LOG, progress));
            }
        }
        else if (Bootstrap.bootstrapType() == BootstrapType.AGENT)
        {
            Bootstrap.vertx().eventBus().send(EVENT_AGENT, new JsonObject().put(EVENT_ID, eventId)
                    .put(EVENT_TYPE, EVENT_PROGRESS_UPDATE)
                    .put(Agent.AGENT_UUID, BootstrapAgent.getAgentUUID())
                    .put(EVENT_PROGRESS_LOG, progress));
        }
        else
        {
            if (state != null)
            {
                Bootstrap.vertx().eventBus().send(EventBusConstants.EVENT_PROGRESS_UPDATE, new JsonObject().put(EventBusConstants.EVENT_ID, eventId)
                        .put(EventBusConstants.EVENT_STATE, EVENT_STATE_RUNNING)
                        .put(EventBusConstants.EVENT_PROGRESS_LOG, progress));
            }
            else
            {
                Bootstrap.vertx().eventBus().send(EventBusConstants.EVENT_PROGRESS_UPDATE, new JsonObject().put(EventBusConstants.EVENT_ID, eventId)
                        .put(EventBusConstants.EVENT_PROGRESS_LOG, progress));
            }
        }
    }

    /**
     * Sets up the registration ID for a remote event processor.
     * <p>
     * This method:
     * 1. Retrieves the registration ID from the configuration
     * 2. If a valid ID exists, creates a registration event with:
     * - The host name of the current machine
     * - The registration ID
     * - The primary IP address of the machine
     * 3. Writes this information to the registration file
     * 4. Sets the registration ID in the Bootstrap class for future reference
     * <p>
     * This registration process is essential for identifying and tracking remote
     * event processors in a distributed system. The registration ID serves as a
     * unique identifier for the processor, allowing the system to route events
     * correctly and maintain processor state.
     *
     * @throws Exception If an error occurs during the registration process
     */
    public static void setRemoteEventProcessorRegistrationId()
    {
        try
        {
            var registrationId = getRegistrationId();

            if (registrationId != null && !registrationId.isEmpty())
            {
                var ipAddresses = CommonUtil.getIPAddresses();

                if (CommonUtil.debugEnabled())
                {
                    LOGGER.debug(String.format("ip address is : %s", ipAddresses));
                }

                var event = new JsonObject().put(RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_HOST, CommonUtil.getHostName()).put(RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_UUID, registrationId)
                        .put(RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_IP, !ipAddresses.isEmpty() ? ipAddresses.getFirst() : CommonUtil.getHostName());

                Bootstrap.vertx().fileSystem().writeFileBlocking(GlobalConstants.CURRENT_DIR + GlobalConstants.PATH_SEPARATOR + GlobalConstants.CONFIG_DIR + GlobalConstants.PATH_SEPARATOR + GlobalConstants.REGISTRATION_FILE
                        , Buffer.buffer(CommonUtil.getString(event)));

                Bootstrap.setRegistrationId(registrationId);
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);

            throw exception;
        }
    }

    /**
     * Sets up the registration ID for a manager component.
     * <p>
     * This method:
     * 1. Attempts to retrieve an existing registration ID from the configuration
     * 2. If no valid ID exists, generates a new UUID (with hyphens removed and converted to uppercase)
     * 3. Updates the motadata.json configuration file with the new ID
     * 4. Sets the registration ID in the Bootstrap class for future reference
     * 5. Logs the assigned UUID for troubleshooting purposes
     * <p>
     * Unlike setRemoteEventProcessorRegistrationId(), this method will generate a new ID
     * if one doesn't exist, ensuring that every manager component always has a valid
     * identifier. This is critical for manager components that need to be uniquely
     * identifiable in a distributed system.
     *
     * @throws Exception If an error occurs during the registration process
     */
    public static void setManagerRegistrationId()
    {
        try
        {
            var registrationId = getRegistrationId();

            if (registrationId == null || registrationId.isEmpty())
            {
                registrationId = UUID.randomUUID().toString().replace("-", EMPTY_VALUE).trim().toUpperCase();

                var context = Bootstrap.vertx().fileSystem().readFileBlocking(GlobalConstants.CURRENT_DIR + PATH_SEPARATOR + GlobalConstants.CONFIG_DIR + PATH_SEPARATOR + "motadata.json").toJsonObject().put(MANAGER_UUID, registrationId);

                Bootstrap.vertx().fileSystem().writeFileBlocking(GlobalConstants.CURRENT_DIR + PATH_SEPARATOR + GlobalConstants.CONFIG_DIR + PATH_SEPARATOR + "motadata.json", Buffer.buffer(context.encodePrettily()));
            }

            Bootstrap.setRegistrationId(registrationId);

            LOGGER.info(String.format("uuid is set to %s ", Bootstrap.getRegistrationId()));
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);

            throw exception;
        }
    }

    /**
     * Publishes an event to a session and multiple subscribers.
     * <p>
     * This method:
     * 1. Publishes the event to the specified session ID
     * 2. If subscribers are provided, publishes the event to each subscriber
     * <p>
     * This is useful for broadcasting events to multiple recipients, such as
     * when notifying all users in a group about a system event or update.
     *
     * @param sessionId   The primary session ID to publish to
     * @param subscribers A JsonArray of additional session IDs to publish to
     * @param event       The type of event being published
     * @param context     The JsonObject containing the event data
     */
    public static void publish(String sessionId, JsonArray subscribers, String event, JsonObject context)
    {
        publish(sessionId, event, context);

        if (subscribers != null && !subscribers.isEmpty())
        {
            subscribers.forEach(subscriber -> publish(CommonUtil.getString(subscriber), event, context));
        }
    }

    /**
     * Publishes an event to all UI subscribers.
     * <p>
     * This method broadcasts an event to the EVENT_UI address, which is typically
     * subscribed to by UI components. The method handles data compression automatically:
     * <p>
     * - If the context explicitly specifies COMPRESSION_TYPE_ZERO, the data is sent uncompressed
     * - Otherwise, the context is compressed using CodecUtil.compress() to reduce network traffic
     * <p>
     * This method is commonly used for broadcasting UI notifications, updates to dashboards,
     * or other information that needs to be displayed to users.
     *
     * @param event   The type of event being published
     * @param context The JsonObject containing the event data
     */
    public static void publish(String event, JsonObject context)
    {
        try
        {
            if (context.containsKey(EventBusConstants.EVENT_COMPRESSION_TYPE) && CommonUtil.getByteValue(context.getValue(EventBusConstants.EVENT_COMPRESSION_TYPE)) == COMPRESSION_TYPE_ZERO)
            {
                Bootstrap.vertx().eventBus().publish(EVENT_UI, new JsonObject().put(EVENT_TYPE, event).put(EVENT_CONTEXT, context));
            }
            else
            {
                Bootstrap.vertx().eventBus().publish(EVENT_UI, new JsonObject().put(EVENT_TYPE, event).put(EventBusConstants.EVENT_COMPRESSION_TYPE, COMPRESSION_TYPE_ONE)
                        .put(EVENT_CONTEXT, CodecUtil.compress(context.encode())));
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }

    /**
     * Publishes an event to a specific user session.
     * <p>
     * This method sends an event to a specific user session identified by the sessionId.
     * The event is published to the EVENT_USER + sessionId address, which is typically
     * subscribed to by the user's client connection.
     * <p>
     * The method includes several features:
     * - In development mode, it logs the event details for debugging
     * - It handles data compression automatically:
     * - If the context explicitly specifies COMPRESSION_TYPE_ZERO, the data is sent uncompressed
     * - Otherwise, the context is compressed using CodecUtil.compress() to reduce network traffic
     * - If sessionId is null, no event is published (silent failure)
     * <p>
     * This method is commonly used for sending targeted notifications, updates, or
     * responses to specific users or client connections.
     *
     * @param sessionId The session ID to publish to, typically representing a user's connection
     * @param event     The type of event being published
     * @param context   The JsonObject containing the event data
     */
    public static void publish(String sessionId, String event, JsonObject context)
    {
        try
        {
            if (MotadataConfigUtil.devMode())
            {
                LOGGER.trace(new JsonObject().put(EVENT_TYPE, event).put(EVENT_CONTEXT, context));
            }

            if (sessionId != null)
            {
                if (context.containsKey(EventBusConstants.EVENT_COMPRESSION_TYPE) && CommonUtil.getByteValue(context.getValue(EventBusConstants.EVENT_COMPRESSION_TYPE)) == COMPRESSION_TYPE_ZERO)
                {
                    Bootstrap.vertx().eventBus().publish(EVENT_USER + sessionId, new JsonObject().put(EVENT_TYPE, event).put(EVENT_CONTEXT, context));
                }
                else
                {
                    Bootstrap.vertx().eventBus().publish(EVENT_USER + sessionId, new JsonObject().put(EventBusConstants.EVENT_COMPRESSION_TYPE, COMPRESSION_TYPE_ONE)
                            .put(EVENT_TYPE, event).put(EVENT_CONTEXT, CodecUtil.compress(context.encode())));
                }
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }

    /**
     * Publishes a user notification event to the event bus.
     * <p>
     * This method sends a notification event to the EVENT_USER_NOTIFICATION address,
     * but only if the event contains a non-null MESSAGE field. This validation ensures
     * that only meaningful notifications with actual content are sent.
     * <p>
     * Unlike the other publish methods that broadcast events to multiple recipients,
     * this method uses the send() operation which delivers the event to a single consumer.
     * This is appropriate for user notifications that are typically processed by a single
     * notification handler.
     *
     * @param event A JsonObject containing the notification data, which must include a MESSAGE field
     */
    public static void publishUserNotificationEvent(JsonObject event)
    {
        if (event.getString(MESSAGE) != null)
        {
            Bootstrap.vertx().eventBus().send(EventBusConstants.EVENT_USER_NOTIFICATION, event);
        }
    }

    /**
     * Completes an event by sending it to the appropriate event bus address based on its status.
     * <p>
     * This method:
     * 1. Extracts the event ID from the event
     * 2. Determines the appropriate event bus address based on the event's status:
     * - STATUS_SUCCEED: Sends to EVENT_SUCCEED
     * - STATUS_ABORT: Sends to EVENT_ABORT
     * - Any other status: Sends to EVENT_FAIL
     * 3. Calls the clear method to clean up the event context
     * <p>
     * This method is typically called when an asynchronous operation finishes,
     * either successfully, due to an abort request, or due to a failure.
     *
     * @param event A JsonObject containing the event data, including EVENT_ID and STATUS
     */
    public static void complete(JsonObject event)
    {
        try
        {
            var eventId = event.getLong(EventBusConstants.EVENT_ID);

            if (eventId != null)
            {
                if (event.getString(STATUS).equalsIgnoreCase(STATUS_SUCCEED))
                {
                    Bootstrap.vertx().eventBus().send(EventBusConstants.EVENT_SUCCEED, event);
                }
                else if (event.getString(STATUS).equalsIgnoreCase(STATUS_ABORT))
                {
                    Bootstrap.vertx().eventBus().send(EventBusConstants.EVENT_ABORT, event);
                }
                else
                {
                    Bootstrap.vertx().eventBus().send(EventBusConstants.EVENT_FAIL, event);
                }

                clear(event);
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }

    /**
     * Clears the context of an event from the event bus load balancer.
     * <p>
     * This method:
     * 1. Temporarily removes the event type from the event
     * 2. Publishes the event to EVENT_CLEAR_CONTEXT to notify the load balancer
     * to clear any stored context for this event
     * 3. Restores the event type to the event
     * <p>
     * This method is typically called after an event is completed to free up
     * resources and prevent memory leaks in the event bus system.
     *
     * @param event A JsonObject containing the event data to clear
     */
    public static void clear(JsonObject event)
    {
        var eventType = event.remove(EventBusConstants.EVENT_TYPE);

        Bootstrap.vertx().eventBus().publish(EventBusConstants.EVENT_CLEAR_CONTEXT, event); //clear the context of load balancer

        event.put(EventBusConstants.EVENT_TYPE, eventType);
    }

    private static String getRegistrationId()
    {
        var registrationId = EMPTY_VALUE;

        var motadata = GlobalConstants.CURRENT_DIR + PATH_SEPARATOR + GlobalConstants.CONFIG_DIR + PATH_SEPARATOR + "motadata.json";

        if (Bootstrap.vertx().fileSystem().existsBlocking(motadata))
        {
            var context = Bootstrap.vertx().fileSystem().readFileBlocking(motadata).toJsonObject();

            if (context != null && CommonUtil.isNotNullOrEmpty(context.getString(MANAGER_UUID)))
            {
                registrationId = context.getString(MANAGER_UUID);
            }
        }

        return registrationId;
    }

    /**
     * Registers an agent with the system and optionally sets up heartbeat messages.
     * <p>
     * This method performs the complete agent registration process:
     * 1. Collects system information (hostname, IP addresses, OS details)
     * 2. Retrieves or generates a unique agent UUID
     * - If no UUID exists, gets one from the registration ID or generates a new one
     * - Updates the agent configuration file with the UUID
     * 3. Creates a registration context with all agent information
     * 4. Sends a registration message to the EVENT_AGENT address
     * 5. If requested, sets up periodic heartbeat messages every AGENT_PING_INTERVAL_SECONDS
     * <p>
     * The registration process is essential for integrating agents into the monitoring system.
     * It allows the central system to discover, identify, and communicate with agents deployed
     * across the network. The optional heartbeat mechanism ensures that the system can detect
     * when agents become unavailable.
     *
     * @param sendHeartbeat If true, sets up periodic heartbeat messages after registration;
     *                      if false, only performs the initial registration
     */
    public static void registerAgent(boolean sendHeartbeat)
    {
        try
        {
            var duration = DateTimeUtil.currentSeconds();

            var hostName = CommonUtil.getHostName();

            var ipAddresses = CommonUtil.getIPAddresses();

            var configs = AgentConfigUtil.getChildAgentConfigs();

            var agentUUID = configs.getJsonObject(AGENT).getString(Agent.AGENT_UUID);

            if (CommonUtil.debugEnabled())
            {
                LOGGER.debug(String.format("ip addresses : %s", ipAddresses));
            }

            if (CommonUtil.isNullOrEmpty(agentUUID))  //TODO for SAAS generate uuid - customer id + index
            {
                agentUUID = getRegistrationId();

                configs.getJsonObject(AGENT).put(Agent.AGENT_UUID, agentUUID);

                CommonUtil.dumpConfigs(Agent.AGENT_FILE, configs);

                if (CommonUtil.debugEnabled())
                {
                    LOGGER.debug(String.format("agent registration id: %s....", agentUUID));
                }
            }

            BootstrapAgent.setAgentUUID(agentUUID);

            var ipAddress = !ipAddresses.isEmpty() ? ipAddresses.stream().filter(value -> value.equalsIgnoreCase(BootstrapAgent.getLocalAddress())).findFirst().orElseGet(ipAddresses::getFirst) : hostName;

            var context = new JsonObject().put(Agent.AGENT_VERSION, MotadataConfigUtil.getVersion())
                    .put(GlobalConstants.OS_VERSION, GlobalConstants.OS_NAME + " " + GlobalConstants.OS_VERSION).put(AIOpsObject.OBJECT_IP, ipAddress)
                    .put(NMSConstants.OBJECTS, ipAddresses)
                    .put(Agent.AGENT_OS_NAME, OS_NAME).put(Agent.AGENT_UUID, agentUUID)
                    .put(EventBusConstants.EVENT_TYPE, EventBusConstants.EVENT_AGENT_REGISTRATION).put(AIOpsObject.OBJECT_HOST, hostName)
                    .put(Agent.AGENT_CONFIGS, configs.encode()).put(GlobalConstants.DURATION, duration);

            if (CommonUtil.debugEnabled())
            {
                LOGGER.debug(String.format("sending event for provision %s ", context.encodePrettily()));
            }

            // send agent registration message to server with hostname,osname,uuid,config
            Bootstrap.vertx().eventBus().send(EventBusConstants.EVENT_AGENT, context);

            if (sendHeartbeat)
            {
                // send heartbeat to motadata server
                var event = new JsonObject().put(EventBusConstants.EVENT_TYPE, EventBusConstants.EVENT_AGENT_HEARTBEAT)
                        .put(AIOpsObject.OBJECT_IP, ipAddress)
                        .put(GlobalConstants.DURATION, duration)
                        .put(Agent.AGENT_UUID, BootstrapAgent.getAgentUUID());

                // send heartbeat on every 60 sec
                Bootstrap.vertx().setPeriodic(1000L * AgentConstants.AGENT_PING_INTERVAL_SECONDS, timer -> Bootstrap.vertx().eventBus().send(EventBusConstants.EVENT_AGENT, event));
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }

    /**
     * Registers a component with the system based on its bootstrap type and installation mode.
     * <p>
     * This method handles the registration process for different types of components:
     * <p>
     * 1. For APP bootstrap type:
     * - STANDALONE/PRIMARY mode: Registers with both EVENT_REGISTRATION and EVENT_HA_OBSERVER,
     * and sets up encrypted heartbeat messages
     * - SECONDARY/FAILOVER mode: Registers with both EVENT_REMOTE and EVENT_HA_OBSERVER,
     * and sets up unencrypted heartbeat messages
     * <p>
     * 2. For COLLECTOR, EVENT_COLLECTOR, EVENT_PROCESSOR, FLOW_COLLECTOR bootstrap types:
     * - Registers with EVENT_REMOTE and sets up heartbeat messages
     * <p>
     * The registration process includes:
     * - Reading registration information from the registration file
     * - Creating a registration event with component details (version, UUID, type, etc.)
     * - Sending the registration event to appropriate event bus addresses
     * - Setting up periodic heartbeat messages (every 10 seconds)
     * <p>
     * This method is a critical part of the distributed system architecture, enabling
     * components to announce their presence and maintain connectivity with the system.
     *
     * @param cipherUtil The CipherUtil instance used for encrypting heartbeat messages
     * @throws Exception If an error occurs during the registration process
     */
    public static void register(final CipherUtil cipherUtil) throws Exception
    {
        try
        {
            var registrationFile = GlobalConstants.CURRENT_DIR + PATH_SEPARATOR + GlobalConstants.CONFIG_DIR + PATH_SEPARATOR + GlobalConstants.REGISTRATION_FILE;

            switch (Bootstrap.bootstrapType())
            {
                case APP ->
                {
                    switch (GlobalConstants.InstallationMode.valueOf(Bootstrap.getInstallationMode()))
                    {
                        case STANDALONE, PRIMARY ->
                        {
                            var event = Bootstrap.vertx().fileSystem().readFileBlocking(registrationFile).toJsonObject()
                                    .put(RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_VERSION, MotadataConfigUtil.getVersion())
                                    .put(EventBusConstants.EVENT_TYPE, EventBusConstants.EVENT_REGISTRATION)
                                    .put(EventBusConstants.EVENT_TOPIC, REMOTE_EVENT_PROCESSOR_TOPIC)
                                    .put(DBConstants.FIELD_TYPE, DBConstants.ENTITY_TYPE_SYSTEM)
                                    .put(REMOTE_EVENT_PROCESSOR_UUID, Bootstrap.getRegistrationId())
                                    .put(REMOTE_EVENT_PROCESSOR_INSTALLATION_MODE, Bootstrap.getInstallationMode())
                                    .put(RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_TYPE, Bootstrap.bootstrapType());

                            event.put(REMOTE_EVENT_PROCESSOR_INSTALLATION_MODE, EventBusConstants.getInstallationMode(event));

                            Bootstrap.vertx().eventBus().send(EVENT_REGISTRATION, event);

                            Bootstrap.vertx().eventBus().send(EVENT_HA_OBSERVER, event);                      // send registration to observer

                            var context = CodecUtil.compress(new JsonObject().put(EventBusConstants.EVENT_TYPE, EventBusConstants.EVENT_REMOTE_PROCESSOR_HEARTBEAT).put(RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_TYPE, Bootstrap.bootstrapType())
                                    .put(DURATION, DateTimeUtil.currentSeconds()).put(RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_INSTALLATION_MODE, event.getString(REMOTE_EVENT_PROCESSOR_INSTALLATION_MODE))
                                    .put(RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_UUID, Bootstrap.getRegistrationId()).encode());

                            Bootstrap.vertx().setPeriodic(10000L, timer -> Bootstrap.vertx().eventBus().send(EventBusConstants.EVENT_REMOTE,
                                    Buffer.buffer().appendShortLE(CommonUtil.getShort(EventBusConstants.REMOTE_EVENT_PROCESSOR_TOPIC.length()))
                                            .appendString(EventBusConstants.REMOTE_EVENT_PROCESSOR_TOPIC)
                                            .appendBytes(cipherUtil.encrypt(context)).getBytes()));
                        }

                        case SECONDARY, FAILOVER ->
                        {
                            var event = Bootstrap.vertx().fileSystem().readFileBlocking(registrationFile).toJsonObject()
                                    .put(RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_VERSION, MotadataConfigUtil.getVersion())
                                    .put(EventBusConstants.EVENT_TYPE, EventBusConstants.EVENT_REGISTRATION)
                                    .put(EventBusConstants.EVENT_TOPIC, REMOTE_EVENT_PROCESSOR_TOPIC)
                                    .put(DBConstants.FIELD_TYPE, DBConstants.ENTITY_TYPE_USER)
                                    .put(RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_INSTALLATION_MODE, Bootstrap.getInstallationMode())
                                    .put(REMOTE_EVENT_PROCESSOR_UUID, Bootstrap.getRegistrationId())
                                    .put(REMOTE_EVENT_PROCESSOR_INSTALLATION_MODE, Bootstrap.getInstallationMode())
                                    .put(RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_TYPE, Bootstrap.bootstrapType());

                            event.put(REMOTE_EVENT_PROCESSOR_INSTALLATION_MODE, EventBusConstants.getInstallationMode(event));

                            Bootstrap.vertx().eventBus().send(EVENT_REMOTE, event);                              // send registration to primary

                            Bootstrap.vertx().eventBus().send(EVENT_HA_OBSERVER, event);                         // send registration to observer

                            var context = new JsonObject().put(EventBusConstants.EVENT_TYPE, EventBusConstants.EVENT_REMOTE_PROCESSOR_HEARTBEAT)
                                    .put(DURATION, DateTimeUtil.currentSeconds()).put(RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_TYPE, BootstrapType.APP.name())
                                    .put(RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_INSTALLATION_MODE, event.getString(REMOTE_EVENT_PROCESSOR_INSTALLATION_MODE))
                                    .put(RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_UUID, Bootstrap.getRegistrationId());

                            Bootstrap.vertx().setPeriodic(10000L, timer -> Bootstrap.vertx().eventBus().send(EVENT_REMOTE, context));
                        }
                    }
                }

                case COLLECTOR, EVENT_COLLECTOR, EVENT_PROCESSOR, FLOW_COLLECTOR ->
                {

                    var event = Bootstrap.vertx().fileSystem().readFileBlocking(registrationFile).toJsonObject()
                            .put(RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_VERSION, MotadataConfigUtil.getVersion())
                            .put(EventBusConstants.EVENT_TYPE, EventBusConstants.EVENT_REGISTRATION)
                            .put(EventBusConstants.EVENT_TOPIC, REMOTE_EVENT_PROCESSOR_TOPIC)
                            .put(DBConstants.FIELD_TYPE, DBConstants.ENTITY_TYPE_USER)
                            .put(RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_INSTALLATION_MODE, Bootstrap.getInstallationMode())
                            .put(RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_TYPE, Bootstrap.bootstrapType());

                    Bootstrap.vertx().eventBus().send(EVENT_REMOTE, event);

                    // send heartbeat on every 10 sec
                    var context = new JsonObject().put(EventBusConstants.EVENT_TYPE, EventBusConstants.EVENT_REMOTE_PROCESSOR_HEARTBEAT)
                            .put(DURATION, DateTimeUtil.currentSeconds()).put(RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_TYPE, Bootstrap.bootstrapType().name())
                            .put(RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_UUID, Bootstrap.getRegistrationId());

                    Bootstrap.vertx().setPeriodic(10000L, timer -> Bootstrap.vertx().eventBus().send(EventBusConstants.EVENT_REMOTE, context));
                }
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);

            throw exception;
        }
    }

    /**
     * Determines the installation mode for a component based on existing configuration.
     * <p>
     * This method is critical for maintaining system integrity during high-availability
     * operations, particularly during switch-over scenarios. It works by:
     * <p>
     * 1. Checking if a component with the same UUID and type already exists in the
     * RemoteEventProcessorConfigStore
     * 2. If a match is found, returning the installation mode from the existing component
     * to maintain consistency
     * 3. If no match is found, returning the installation mode from the event, or
     * STANDALONE as a default
     * <p>
     * This approach prevents duplicate entries in the configuration database during
     * switch-over operations, ensuring that components maintain their identity and
     * configuration across system state changes.
     *
     * @param event A JsonObject containing component registration information,
     *              must include REMOTE_EVENT_PROCESSOR_TYPE and REMOTE_EVENT_PROCESSOR_UUID
     * @return The appropriate installation mode string for the component
     */
    public static String getInstallationMode(JsonObject event)
    {
        // when same uuid and same type exist in store, we will return that installation mode from our store
        // reason : otherwise it will consider a new entry and add duplicate entry in config db. in case of switch over we need this.

        var items = RemoteEventProcessorConfigStore.getStore().flatItemsByValue(REMOTE_EVENT_PROCESSOR_TYPE, event.getString(REMOTE_EVENT_PROCESSOR_TYPE), REMOTE_EVENT_PROCESSOR_UUID, event.getString(REMOTE_EVENT_PROCESSOR_UUID));

        if (!items.isEmpty())
        {
            LOGGER.trace(String.format("existing item found : %s ", items.getJsonObject(0).encode()));

            return items.getJsonObject(0).getString(RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_INSTALLATION_MODE, InstallationMode.STANDALONE.name());
        }

        return event.getString(REMOTE_EVENT_PROCESSOR_INSTALLATION_MODE, InstallationMode.STANDALONE.name());
    }

    public enum ChangeNotificationType
    {
        CONFIG_INIT,

        CONFIG_CHANGE,

        RUN_DISCOVERY,

        COMPLETE_DISCOVERY,

        ABORT_DISCOVERY,

        DELETE_METRIC,

        ENABLE_METRIC,

        DISABLE_METRIC,

        SUSPEND_METRIC,

        ADD_REMOTE_POLLER,

        UPDATE_REMOTE_POLLER,

        DELETE_REMOTE_POLLER,

        DELETE_OBJECT,

        MAIL_SERVER,

        PROXY_SERVER,

        DISABLE_PROXY_SERVER,

        DNS_SERVER,

        ADD_METRIC,

        ADD_OBJECT,

        UPDATE_OBJECT,

        UPDATE_AGENT,

        STOP_REDISCOVER_JOB,

        SNMP_TRAP_FORWARDER,

        ADD_POLICY,

        DELETE_POLICY,

        UPDATE_POLICY,

        DISABLE_POLICY,

        ADD_LOG_FORWARDER,

        UPDATE_LOG_FORWARDER,

        DELETE_LOG_FORWARDER,

        ABORT_PLUGIN_ENGINE_REQUEST,

        RUN_TOPOLOGY,

        ADD_USER,

        UPDATE_USER,

        DELETE_USER,

        COMPLETE_TOPOLOGY,

        STOP_TOPOLOGY,

        ADD_APPLICATION_MAPPER,

        UPDATE_APPLICATION_MAPPER,

        DELETE_APPLICATION_MAPPER,

        ADD_DEPENDENCY_MAPPER,

        UPDATE_DEPENDENCY_MAPPER,

        DELETE_DEPENDENCY_MAPPER,

        ADD_METADATA_FIELDS,

        ADD_APPLICATION,

        DELETE_APPLICATION,

        UPDATE_METRIC_COLUMN,

        DELETE_METRIC_COLUMN,

        UPDATE_EVENT_COLUMN,

        ADD_WIDGET,

        UPDATE_WIDGET,

        DELETE_WIDGET,

        UPDATE_AGENT_TAGS,

        UPDATE_AGENT_GROUPS,

        ADD_LOG_PARSER_PLUGIN,

        ADD_LOG_COLLECTOR,

        UPDATE_LOG_COLLECTOR,

        DELETE_LOG_COLLECTOR,

        UPDATE_LOG_PARSER_PLUGIN,

        DELETE_LOG_PARSER_PLUGIN,

        ADD_LOG_PARSER,

        UPDATE_LOG_PARSER,

        DELETE_LOG_PARSER,

        ADD_EVENT_SOURCE,

        ADD_FLOW_SAMPLING_RATE,

        DELETE_EVENT_SOURCE,

        UPDATE_EVENT_SOURCE,

        ADD_PROTOCOL,

        UPDATE_PROTOCOL,

        DELETE_PROTOCOL,

        ASSIGN_LOG_PARSER,

        UNASSIGN_LOG_PARSER,

        SUPPRESS_POLICY,

        UNSUPPRESS_POLICY,

        START_LOG_TAIL,

        STOP_LOG_TAIL,

        START_EVENT_TRACKER_STREAMING,

        STOP_EVENT_TRACKER_STREAMING,

        START_EVENT_STAT_STREAMING,

        STOP_EVENT_STAT_STREAMING,

        DISABLE_POLICY_ACTION_TRIGGER,

        LOGGER_LEVEL_CHANGE,

        ADD_ARTIFACT,

        UPDATE_ARTIFACT,

        DELETE_ARTIFACT,

        UPDATE_INTEGRATION,

        UPDATE_CACHE,

        DELETE_AGENT,

        DELETE_LDAP_USER,

        DELETE_ACCESS_TOKEN,

        START_TRAP_TAIL,

        STOP_TRAP_TAIL,

        ADD_INTEGRATION_PROFILE,

        UPDATE_INTEGRATION_PROFILE,

        DELETE_INTEGRATION_PROFILE,

        UPDATE_COMPLIANCE_POLICY,

        ADD_COMPLIANCE_POLICY,

        DELETE_COMPLIANCE_POLICY,

        UPDATE_COMPLIANCE_RULE,

        ADD_COMPLIANCE_RULE,

        DELETE_COMPLIANCE_RULE,

        UPDATE_CREDENTIAL_PROFILE,

        ADD_CREDENTIAL_PROFILE,

        DELETE_CREDENTIAL_PROFILE,

        CLEANUP_FLOW_CACHE,

        ADD_NETROUTE,

        DELETE_NETROUTE,

        SLO_CYCLE_START,

        SLO_CYCLE_END,
    }

    /*
     *  Dynamic Routing: time(second) based routing
     *                   -- route to the other handler at every second
     *                   -- use-case: In datastore-writer we write data minute wise so when same minute data is received it will send to same datastore so
     *                                only one writer creates that file
     *  Round Robin Routing: event based routing
     *                   -- route to the other handler at every event
     *                   -- use-case: pmacct sends all aggregated events to AIOps at the same time.
     *                                if dynamic routing is applied all events will be handled by single vertical. hence using round-robin routing
     * */
    public enum EventRouter
    {
        DYNAMIC(0),
        ROUND_ROBIN(1);

        private static final Map<Integer, EventRouter> VALUES = Collections.unmodifiableMap(Arrays.stream(values())
                .collect(Collectors.toMap(EventRouter::getValue, e -> e)));
        private final int name;

        EventRouter(Integer name)
        {
            this.name = name;
        }

        public static EventRouter valueOfName(Integer name)
        {
            return VALUES.get(name);
        }

        public Integer getValue()
        {
            return name;
        }
    }
}
