/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

package com.mindarray.job;

import com.mindarray.GlobalConstants;
import com.mindarray.util.LDAPUtil;
import com.mindarray.util.Logger;
import org.quartz.DisallowConcurrentExecution;
import org.quartz.Job;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;

import static com.mindarray.GlobalConstants.*;

/**
 * The LDAPSyncJob class is responsible for synchronizing LDAP users with the Motadata platform.
 * <p>
 * This class implements the Quartz Job interface and is annotated with {@link DisallowConcurrentExecution}
 * to ensure that only one instance of this job runs at a time. The job synchronizes user information
 * from an LDAP server with the Motadata platform's user database.
 * <p>
 * The job is scheduled to run at configurable intervals, determined by the LDAP server configuration.
 * The CRON expression used for scheduling is parameterized with placeholders (@@, ##) that are replaced
 * with actual values when the job is scheduled.
 * <p>
 * The synchronization process ensures that user information in the Motadata platform remains
 * consistent with the LDAP directory, which is often the authoritative source for user data
 * in enterprise environments.
 */
@DisallowConcurrentExecution
public class LDAPSyncJob implements Job
{
    /**
     * CRON expression template for scheduling this job.
     * <p>
     * This expression contains placeholders that are replaced with actual values when the job is scheduled:
     * <ul>
     *   <li>@@ - Replaced with the hour interval for synchronization</li>
     *   <li>## - Replaced with the day interval for synchronization</li>
     * </ul>
     * <p>
     * For example, if @@ is replaced with "8" and ## with "1", the job will run every 8 hours starting from day 1.
     */
    static final String LDAP_SYNC_JOB_CRON_EXPRESSION = "0 0 0/@@ 1/## * ? *";

    /**
     * Logger for this class
     */
    private static final Logger LOGGER = new Logger(LDAPSyncJob.class, GlobalConstants.MOTADATA_JOB, "LDAP Sync Job");

    /**
     * Executes the LDAP synchronization job.
     * <p>
     * This method retrieves the LDAP server ID from the job context and triggers the
     * synchronization of LDAP users with the Motadata platform. The synchronization
     * is performed by the {@link LDAPUtil#syncLDAPUsers} method.
     * <p>
     * If the LDAP server ID is not found in the job context, the synchronization fails
     * and a warning is logged.
     *
     * @param jobExecutionContext the context in which the job is executed
     * @throws JobExecutionException if an error occurs during job execution
     */
    @Override
    public void execute(JobExecutionContext jobExecutionContext) throws JobExecutionException
    {
        try
        {
            LOGGER.debug("Starting scheduled LDAP server sync");

            // Get the job data map to retrieve the LDAP server ID
            var jobContext = jobExecutionContext.getJobDetail().getJobDataMap();

            // Check if the LDAP server ID is available in the job context
            if (jobContext.containsKey(ID))
            {
                // Trigger the LDAP user synchronization for the specified LDAP server
                LDAPUtil.syncLDAPUsers(null, DEFAULT_USER, SYSTEM_REMOTE_ADDRESS, jobContext.getLong(ID));

                LOGGER.debug("Completed scheduled LDAP server sync");
            }
            else
            {
                // Log a warning if the LDAP server ID is not found
                LOGGER.warn("LDAP server sync failed. Reason: LDAP server id not found!");
            }
        }
        catch (Exception exception)
        {
            // Log any exceptions that occur during synchronization
            LOGGER.error(exception);
        }
    }
}
