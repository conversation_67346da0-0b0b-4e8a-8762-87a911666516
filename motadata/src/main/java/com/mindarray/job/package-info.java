/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

/**
 * The job package provides scheduling and execution of various system and user-defined jobs in the Motadata platform.
 * <p>
 * This package contains the following main components:
 * <ul>
 *   <li>{@link com.mindarray.job.JobScheduler} - Central class for managing job scheduling, initialization, and lifecycle</li>
 *   <li>{@link com.mindarray.job.CustomJob} - Executes user-defined jobs based on their type and configuration</li>
 *   <li>System Jobs - A collection of predefined jobs that perform various system maintenance tasks:
 *     <ul>
 *       <li>{@link com.mindarray.job.DailyMiscJobs} - Performs daily maintenance tasks like log retention and event ID counter reset</li>
 *       <li>{@link com.mindarray.job.DNSCacheFlushJob} - Flushes the DNS cache periodically</li>
 *       <li>{@link com.mindarray.job.DatastoreRetentionJob} - Manages data retention in the datastore</li>
 *       <li>{@link com.mindarray.job.FlapCacheBackupJob} - Backs up the flap cache data</li>
 *       <li>{@link com.mindarray.job.FlowCacheCleanupJob} - Cleans up the flow cache</li>
 *       <li>{@link com.mindarray.job.GeoDBSyncJob} - Synchronizes the geographical database</li>
 *       <li>{@link com.mindarray.job.LDAPSyncJob} - Synchronizes LDAP server data</li>
 *       <li>{@link com.mindarray.job.LicenseJob} - Manages license-related tasks</li>
 *       <li>{@link com.mindarray.job.ReportCleanupJob} - Cleans up old reports</li>
 *       <li>{@link com.mindarray.job.SystemNotificationJob} - Handles system notifications</li>
 *     </ul>
 *   </li>
 * </ul>
 * <p>
 * The job package uses the Quartz scheduler library for job scheduling and execution. Jobs can be scheduled to run
 * at specific times using CRON expressions or triggered manually. The {@link com.mindarray.job.JobScheduler} class
 * provides methods for scheduling, removing, and managing jobs.
 * <p>
 * System jobs are automatically scheduled during system initialization, while custom jobs can be created and scheduled
 * by users through the API. Custom jobs are handled by the {@link com.mindarray.job.CustomJob} class, which executes
 * different types of jobs based on their configuration.
 * <p>
 * The job package is essential for maintaining system health, performing routine maintenance tasks, and allowing
 * users to automate various operations in the Motadata platform.
 *
 * @see com.mindarray.job.JobScheduler
 * @see com.mindarray.job.CustomJob
 * @see com.mindarray.job.DailyMiscJobs
 * @see com.mindarray.job.DNSCacheFlushJob
 * @see com.mindarray.job.DatastoreRetentionJob
 * @see com.mindarray.job.FlapCacheBackupJob
 * @see com.mindarray.job.FlowCacheCleanupJob
 * @see com.mindarray.job.GeoDBSyncJob
 * @see com.mindarray.job.LDAPSyncJob
 * @see com.mindarray.job.LicenseJob
 * @see com.mindarray.job.ReportCleanupJob
 * @see com.mindarray.job.SystemNotificationJob
 */
package com.mindarray.job;