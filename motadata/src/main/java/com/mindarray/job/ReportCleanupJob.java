/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

package com.mindarray.job;

import com.mindarray.GlobalConstants;
import com.mindarray.util.DateTimeUtil;
import com.mindarray.util.Logger;
import org.apache.commons.io.FileUtils;
import org.quartz.Job;
import org.quartz.JobExecutionContext;

import java.nio.file.Files;
import java.nio.file.Path;

import static com.mindarray.GlobalConstants.*;

/**
 * The ReportCleanupJob class is responsible for cleaning up old report files in the Motadata platform.
 * <p>
 * This class implements the Quartz Job interface and performs regular cleanup of report files
 * to prevent excessive disk usage. Reports are generated for various purposes in the platform,
 * and old reports need to be periodically cleaned up to avoid accumulation of unnecessary files.
 * <p>
 * The job performs the following tasks:
 * <ol>
 *   <li>Calculates a timestamp for 1 hour ago</li>
 *   <li>Lists all files in the uploads/reports directory</li>
 *   <li>Deletes any files that are older than 1 hour</li>
 * </ol>
 * <p>
 * The job is scheduled to run every hour to ensure regular cleanup of report files.
 */
public class ReportCleanupJob implements Job
{
    /**
     * CRON expression for scheduling this job to run at the top of every hour
     */
    public static final String REPORT_CLEANUP_JOB_CRON_EXPRESSION = "0 0 * ? * * *";   // At second :00 of minute :00 of every hour

    /**
     * Logger for this class
     */
    private static final Logger LOGGER = new Logger(ReportCleanupJob.class, GlobalConstants.MOTADATA_JOB, "Report Cleanup Job");

    /**
     * Executes the report cleanup job.
     * <p>
     * This method performs the cleanup operation by:
     * <ol>
     *   <li>Calculating a timestamp for 1 hour ago</li>
     *   <li>Listing all files in the uploads/reports directory</li>
     *   <li>Deleting any files that are older than 1 hour</li>
     * </ol>
     * <p>
     * The cleanup operation helps prevent excessive disk usage by removing old report files
     * that are no longer needed. Reports are typically only needed for a short time after
     * they are generated, so a 1-hour retention period is sufficient.
     *
     * @param jobExecutionContext the context in which the job is executed
     */
    @Override
    public void execute(JobExecutionContext jobExecutionContext)
    {
        try
        {
            LOGGER.debug("starting Report cleanup job");

            // Calculate timestamp for 1 hour ago (3600000 milliseconds = 1 hour)
            var millis = DateTimeUtil.currentMilliSeconds() - 3600000;

            // List all files in the uploads/reports directory and process each one
            Files.list(Path.of(CURRENT_DIR + PATH_SEPARATOR + UPLOADS + PATH_SEPARATOR + REPORTS)).forEach(path ->
            {
                var file = path.toFile();

                // Delete files that are older than 1 hour
                if (file.isFile() && file.lastModified() < millis)
                {
                    FileUtils.deleteQuietly(file);
                }
            });

            LOGGER.debug("completed report cleanup job");
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }
}
