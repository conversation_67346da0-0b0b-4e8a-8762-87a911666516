/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

package com.mindarray.job;

import com.mindarray.Bootstrap;
import com.mindarray.GlobalConstants;
import com.mindarray.api.DataRetentionPolicy;
import com.mindarray.eventbus.EventBusConstants;
import com.mindarray.store.DataRetentionPolicyConfigStore;
import com.mindarray.util.*;
import io.vertx.core.json.JsonObject;
import org.apache.commons.io.FileUtils;
import org.quartz.DisallowConcurrentExecution;
import org.quartz.Job;
import org.quartz.JobExecutionContext;

import java.io.File;
import java.util.Calendar;

/**
 * The DailyMiscJobs class is responsible for executing various daily maintenance tasks.
 * <p>
 * This class implements the Quartz Job interface and is annotated with @DisallowConcurrentExecution
 * to ensure that only one instance of this job runs at a time. It performs several maintenance
 * tasks that need to be executed daily:
 * <ul>
 *   <li>Resetting the event ID counter</li>
 *   <li>Running log retention to clean up old logs</li>
 *   <li>Cleaning up the flap cache based on retention policy</li>
 *   <li>Triggering configuration backup cleanup</li>
 * </ul>
 * <p>
 * The job is scheduled to run every day at midnight (12:00 AM).
 */
@DisallowConcurrentExecution
public class DailyMiscJobs implements Job
{
    /**
     * CRON expression for scheduling this job to run every day at midnight (12:00 AM)
     */
    static final String DAILY_MISC_JOBS_CRON_EXPRESSION = "0 0 0 * * ?"; // execute on every day 12:00 AM

    /**
     * Logger for this class
     */
    private static final Logger LOGGER = new Logger(DailyMiscJobs.class, GlobalConstants.MOTADATA_JOB, "System Job");

    /**
     * Executes the daily maintenance tasks.
     * <p>
     * This method is called by the Quartz scheduler when the job is triggered. It performs
     * the following tasks:
     * <ol>
     *   <li>Resets the event ID counter to prevent overflow</li>
     *   <li>Runs log retention to delete old log files based on retention policy</li>
     *   <li>Cleans up the flap cache based on retention policy</li>
     *   <li>Triggers configuration backup cleanup</li>
     * </ol>
     *
     * @param context the context in which the job is executed
     */
    @Override
    public void execute(JobExecutionContext context)
    {
        // Reset the event ID counter
        LOGGER.info("resetting event id counter....");
        CommonUtil.resetEventId();
        LOGGER.info("event id counter reset done successfully...");

        // Run log retention
        LOGGER.info(String.format("running log retention job for system boot sequence %s", Bootstrap.bootstrapType()));
        LogUtil.runLogRetention(Bootstrap.bootstrapType() == GlobalConstants.BootstrapType.AGENT ?
                AgentConfigUtil.getAgentLogRetentionDays() :
                MotadataConfigUtil.getLogRetentionDays());
        LOGGER.info("log retention job completed successfully...");

        // Clean up flap cache
        LOGGER.info("flap cache retention job started...");
        cleanupFlapCache(MotadataConfigUtil.getFlapCacheRetentionDays());
        LOGGER.info("flap cache retention job completed successfully...");

        // Trigger configuration backup cleanup
        Bootstrap.vertx().eventBus().send(EventBusConstants.EVENT_CONFIG_BACKUP_CLEANUP,
                DataRetentionPolicyConfigStore.getStore().getItem(GlobalConstants.DEFAULT_ID)
                        .getJsonObject(DataRetentionPolicy.DATA_RETENTION_POLICY_CONTEXT)
                        .getJsonObject("CONFIG"));

        // manage SLO profile cycles at every midnight
        Bootstrap.vertx().eventBus().send(EventBusConstants.EVENT_SLO_QUALIFY, new JsonObject());
    }

    /**
     * Cleans up flap cache files based on the specified retention period.
     * <p>
     * This method deletes flap cache files that are older than the specified retention period.
     * It calculates the cutoff date based on the retention days and deletes any files created
     * before that date. The creation time of each file is extracted from its filename.
     * <p>
     * Implementation note: This method was added as part of MOTADATA-970 to define code for
     * retaining files within a specific timeline.
     *
     * @param retentionDays the number of days to retain flap cache files (files older than this will be deleted)
     */
    public void cleanupFlapCache(int retentionDays)
    {
        try
        {
            // Only proceed if retention days is valid
            if (retentionDays > GlobalConstants.NOT_AVAILABLE)
            {
                // Get the flap cache directory
                var flapCacheDir = new File(GlobalConstants.CURRENT_DIR + GlobalConstants.PATH_SEPARATOR +
                        "config-db-backups" + GlobalConstants.PATH_SEPARATOR + "flap-caches");

                // Check if the directory exists
                if (flapCacheDir.exists() && flapCacheDir.isDirectory())
                {
                    var files = flapCacheDir.listFiles();

                    if (files != null)
                    {
                        // Calculate the cutoff date (current date minus retention days)
                        var calendar = Calendar.getInstance();
                        calendar.set(Calendar.DATE, calendar.get(Calendar.DATE) - retentionDays);

                        // Set time to beginning of day (00:00:00.000)
                        calendar.set(Calendar.HOUR_OF_DAY, 0);
                        calendar.set(Calendar.MINUTE, 0);
                        calendar.set(Calendar.SECOND, 0);
                        calendar.set(Calendar.MILLISECOND, 0);

                        var cutoffTime = calendar.getTime().getTime();

                        // Process each file in the directory
                        for (var file : files)
                        {
                            // Extract creation time from filename (assumed to be the last part before .zip extension)
                            var creationTime = file.getName().split(GlobalConstants.DASH_SEPARATOR)
                                    [file.getName().split(GlobalConstants.DASH_SEPARATOR).length - 1]
                                    .replace(".zip", "");

                            // Delete files older than the cutoff date
                            if (Long.parseLong(creationTime) < cutoffTime)
                            {
                                FileUtils.deleteQuietly(file);
                                LOGGER.info(String.format("flap cache file %s deleted...", file));
                            }
                        }
                    }
                }
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }

}
