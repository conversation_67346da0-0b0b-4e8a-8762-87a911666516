/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

/*
 *  Change Logs:
 *  Date            Author          Notes
 *  14-Apr-2025      Bharat       Initial Version
 *  24-Apr-2025     Bharat        MOTADATA-5822: Metric Explorer Enhancements
 */

package com.mindarray.patch;

import com.mindarray.Bootstrap;
import com.mindarray.GlobalConstants;
import com.mindarray.api.APIUtil;
import com.mindarray.api.FlowDomainMapper;
import com.mindarray.api.UserRole;
import com.mindarray.store.FlowDomainMapperConfigStore;
import com.mindarray.store.FlowIPGroupConfigStore;
import com.mindarray.store.UserRoleConfigStore;
import com.mindarray.util.CommonUtil;
import com.mindarray.util.Logger;
import io.vertx.core.Future;
import io.vertx.core.Promise;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;

import java.net.InetAddress;
import java.util.ArrayList;

import static com.mindarray.GlobalConstants.*;
import static com.mindarray.api.FlowIPGroup.FLOW_IP_GROUP;
import static com.mindarray.api.FlowIPGroup.FLOW_IP_GROUP_NAME;
import static com.mindarray.db.DBConstants.*;

/**
 * Implementation of the Patch interface for version 8.0.20.
 * <p>
 * This patch migrates IP/IP Range mappings into domain mappings. It processes
 * IP groups from the FlowIPGroupConfigStore, handles various IP formats including
 * individual IPs, IP ranges (e.g., ***********-***********0), partial ranges
 * (e.g., ***********-55), and wildcard notations (using 'xx').
 * <p>
 * The patch converts these IP groups into standardized domain mappings and saves
 * them to the FlowDomainMapperConfigStore with the category "IP/IP Range".
 */
public class Patch8020 implements Patch
{
    private static final Logger LOGGER = new Logger(Patch8020.class, MOTADATA_PATCH, "Patch 8.0.20");

    private static final String VERSION = "8.0.20";

    /**
     * Returns the version number of this patch.
     *
     * @return The version string "8.0.20"
     */
    @Override
    public String getVersion()
    {
        return VERSION;
    }

    /**
     * Executes the patch operations for version 8.0.20.
     * <p>
     * This method orchestrates the patch execution by:
     * 1. Creating a list of futures for asynchronous operations
     * 2. Adding the IP group patching operation to the futures list
     * 3. Joining all futures and handling the completion result
     *
     * @return A Future that completes when the patch is successfully applied or fails with an exception
     */
    @Override
    public Future<Void> doPatch()
    {
        var promise = Promise.<Void>promise();

        var futures = new ArrayList<Future<Void>>();

        LOGGER.info("executing patch 8.0.20");

        futures.add(patchIPGroups());

        futures.add(patchMetricExplorerPermissions());

        Future.join(futures).onComplete(result ->
        {
            if (result.succeeded())
            {
                promise.complete();

                LOGGER.info("successfully executed patch 8.0.20");
            }
            else
            {
                promise.fail(result.cause());

                LOGGER.error(result.cause());
            }
        });

        return promise.future();
    }


    private Future<Void> patchMetricExplorerPermissions()
    {
        var promise = Promise.<Void>promise();

        try
        {
            var items = UserRoleConfigStore.getStore().getItems();

            var futures = new ArrayList<Future<Void>>();

            for (var index = 0; index < items.size(); index++)
            {
                var item = items.getJsonObject(index);

                boolean valid = false;


                if (item.containsKey(UserRole.USER_ROLE_CONTEXT))
                {
                    var context = item.getJsonArray(UserRole.USER_ROLE_CONTEXT);

                    if (context.contains("metric-explorer:read"))
                    {
                        valid = true;

                        context.remove("metric-explorer:read");

                        if (!context.contains("metric-explorers:read"))
                        {
                            context.add("metric-explorers:read");
                        }

                        if (!context.contains("metric-explorers:read-write"))
                        {
                            context.add("metric-explorers:read-write");
                        }

                        if (!context.contains("metric-explorers:delete"))
                        {
                            context.add("metric-explorers:delete");
                        }
                    }
                }

                if (valid)
                {
                    var future = Promise.<Void>promise();

                    futures.add(future.future());

                    Bootstrap.configDBService().update(TBL_USER_ROLE, new JsonObject().put(FIELD_NAME, GlobalConstants.ID).put(VALUE, item.getLong(ID)), item,
                            DEFAULT_USER, MOTADATA_SYSTEM, result ->
                            {
                                if (result.succeeded())
                                {
                                    UserRoleConfigStore.getStore().updateItem(item.getLong(ID)).onComplete(asyncResult ->
                                    {
                                        if (asyncResult.succeeded())
                                        {
                                            LOGGER.info(String.format("Successfully updated user role %s", item.getString(UserRole.USER_ROLE_NAME)));

                                            future.complete();
                                        }
                                        else
                                        {
                                            LOGGER.warn(String.format("Unable to update user role config store item %s with reason %s", item.encode(), asyncResult.cause().getMessage()));

                                            future.fail(asyncResult.cause().getMessage());
                                        }
                                    });
                                }
                                else
                                {
                                    LOGGER.warn(String.format("Unable to update user role config store item %s with reason %s", item.encode(), result.cause().getMessage()));

                                    future.fail(result.cause().getMessage());
                                }
                            });
                }
            }

            Future.join(futures).onComplete(result ->
            {
                if (result.succeeded())
                {
                    promise.complete();
                }
                else
                {
                    promise.fail(result.cause());
                }
            });
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);

            promise.fail(exception);
        }

        return promise.future();
    }

    // Merging IP/IP Range mappings into domain mappings.
    // Multiple IPs may be present, but only IPv4 and IPv6 addresses are allowed.
    // IP ranges are expanded into individual IPs and added to the domain mapping.
    private Future<Void> patchIPGroups()
    {
        var promise = Promise.<Void>promise();

        try
        {
            var items = FlowIPGroupConfigStore.getStore().getItems();

            var futures = new ArrayList<Future<Void>>();

            for (var index = 0; index < items.size(); index++)
            {
                var item = items.getJsonObject(index);

                var groups = item.getJsonArray(FLOW_IP_GROUP);

                for (var subIndex = 0; subIndex < groups.size(); subIndex++)
                {
                    var ipGroup = groups.getString(subIndex);

                    if (ipGroup.contains("-"))
                    {
                        // Handle IP range notation (e.g., ***********-***********0 or ***********-55)
                        var tokens = ipGroup.split("-");

                        var start = tokens[0].trim();

                        if (tokens[1].trim().matches("\\d+"))
                        {
                            // Handle abbreviated range format (e.g., ***********-55)
                            // This reconstructs the full end IP by taking the prefix from start IP
                            // and appending the end octet from the range
                            futures.add(extractIPGroups(start, start.substring(0, start.lastIndexOf('.')) + "." + tokens[1].trim(), item.getString(FLOW_IP_GROUP_NAME)));
                        }
                        else
                        {
                            // Handle full range format (e.g., ***********-***********0)
                            futures.add(extractIPGroups(start, ipGroup.split("-")[1].trim(), item.getString(FLOW_IP_GROUP_NAME)));
                        }
                    }
                    else if (ipGroup.contains("xx"))
                    {
                        // Handle wildcard notation (e.g., 192.168.1.xx)
                        // The extractIPGroups method will replace "xx" with 0 for start and 255 for end
                        futures.add(extractIPGroups(ipGroup, ipGroup, item.getString(FLOW_IP_GROUP_NAME)));
                    }
                    else
                    {
                        // Handle individual IP addresses
                        futures.add(save(new JsonArray().add(ipGroup), item.getString(FLOW_IP_GROUP_NAME)));
                    }
                }

            }

            Future.join(futures).onComplete(result ->
            {
                if (result.succeeded())
                {
                    promise.complete();
                }
                else
                {
                    promise.fail(result.cause());
                }
            });
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);

            promise.fail(exception);
        }

        return promise.future();
    }

    /**
     * Extracts and processes IP addresses from a range or wildcard notation.
     * <p>
     * This method:
     * 1. Handles wildcard notation by replacing "xx" with appropriate values (0 for start, 255 for end)
     * 2. Validates the IP range using APIUtil.validateRange
     * 3. Calculates all IP addresses in the range using CommonUtil.calculateIPRange
     * 4. Saves the extracted IP addresses as a domain mapping with the specified name
     *
     * @param start The starting IP address of the range or a wildcard IP
     * @param end   The ending IP address of the range or a wildcard IP
     * @param name  The name to use for the domain mapping
     * @return A Future that completes when the IP addresses have been extracted and saved,
     * or fails with an exception if an error occurs
     */
    private Future<Void> extractIPGroups(String start, String end, String name)
    {
        var promise = Promise.<Void>promise();

        try
        {
            var futures = new ArrayList<Future<Void>>();

            // Handle wildcard notation in IP addresses
            if (start.contains("xx"))
            {
                // Replace "xx" with "0" for the start IP to get the lowest IP in the range
                // For example, 192.168.1.xx becomes ***********
                start = start.replaceAll("xx", "0");
            }
            if (end.contains("xx"))
            {
                // Replace "xx" with "255" for the end IP to get the highest IP in the range
                // For example, 192.168.1.xx becomes *************
                end = end.replaceAll("xx", "255");
            }

            // Validate that the start and end IPs form a valid range
            if (APIUtil.validateRange(start, end))
            {
                var domains = new JsonArray();

                // Calculate all IP addresses in the range and add them to the domains array
                // This converts the range into individual IP addresses for precise mapping
                CommonUtil.calculateIPRange(InetAddress.getByName(start), InetAddress.getByName(end)).forEach(domains::add);

                // Save the calculated IP addresses as a domain mapping
                futures.add(save(domains, name));
            }

            Future.join(futures).onComplete(result -> promise.complete());

        }
        catch (Exception exception)
        {
            LOGGER.error(exception);

            promise.fail(exception);
        }

        return promise.future();
    }

    /**
     * Saves a domain mapping to the database and updates the FlowDomainMapperConfigStore.
     * <p>
     * This method:
     * 1. Creates a context object with the domain mapping details
     * 2. Saves the mapping to the COLLECTION_FLOW_DOMAIN_MAPPER collection
     * 3. Adds the saved item to the FlowDomainMapperConfigStore
     * 4. Logs trace information if tracing is enabled
     *
     * @param items A JsonArray containing the IP addresses or domains to be mapped
     * @param name  The name of the domain mapping
     * @return A Future that completes when the domain mapping has been saved,
     * or fails with an exception if an error occurs
     */
    private Future<Void> save(JsonArray items, String name)
    {
        var promise = Promise.<Void>promise();

        try
        {
            // Create a context object with domain mapping details
            // - name: The name of the domain mapping
            // - category: Set to "IP/IP Range" for all mappings created by this patch
            // - group: The JsonArray of IP addresses or domains to be mapped
            var context = new JsonObject()
                    .put(FlowDomainMapper.FLOW_DOMAIN_MAPPER_NAME, name)
                    .put(FlowDomainMapper.FLOW_DOMAIN_MAPPER_CATEGORY, "IP/IP Range")
                    .put(FlowDomainMapper.FLOW_DOMAIN_MAPPER_GROUP, items);

            // Save the domain mapping to the database
            // - COLLECTION_FLOW_DOMAIN_MAPPER: The collection where domain mappings are stored
            // - context: The domain mapping details
            // - DEFAULT_USER: The user performing the operation (system constant)
            // - MOTADATA_SYSTEM: The system identifier (system constant)
            Bootstrap.configDBService().save(TBL_FLOW_DOMAIN_MAPPER, context,
                    DEFAULT_USER, MOTADATA_SYSTEM, result ->
                    {
                        if (result.succeeded())
                        {
                            // Add the saved item to the FlowDomainMapperConfigStore
                            // This ensures the in-memory store is synchronized with the database
                            FlowDomainMapperConfigStore.getStore().addItem(result.result()).onComplete(asyncResult ->
                            {
                                if (asyncResult.succeeded())
                                {
                                    // Log trace information if tracing is enabled
                                    if (CommonUtil.traceEnabled())
                                    {
                                        LOGGER.trace(String.format("updated domain mapping : %s ", result.result()));
                                    }

                                    // Complete the promise to signal successful completion
                                    promise.complete();
                                }
                                else
                                {
                                    // Fail the promise if adding to the store fails
                                    promise.fail(asyncResult.cause());
                                }
                            });
                        }
                        else
                        {
                            // Log and propagate database save errors
                            LOGGER.error(result.cause());
                            promise.fail(result.cause());
                        }
                    });

        }
        catch (Exception exception)
        {
            LOGGER.error(exception);

            promise.fail(exception);
        }

        return promise.future();
    }
}
