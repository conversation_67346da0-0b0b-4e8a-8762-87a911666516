/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

package com.mindarray.patch;

import com.mindarray.Bootstrap;
import com.mindarray.store.AgentConfigStore;
import com.mindarray.util.CommonUtil;
import com.mindarray.util.Logger;
import io.vertx.core.Future;
import io.vertx.core.Promise;
import io.vertx.core.json.JsonObject;

import java.util.ArrayList;

import static com.mindarray.GlobalConstants.*;
import static com.mindarray.api.Agent.AGENT_STATUS;
import static com.mindarray.db.DBConstants.FIELD_NAME;
import static com.mindarray.db.DBConstants.TBL_AGENT;

public class Patch802 implements Patch
{

    private static final Logger LOGGER = new Logger(Patch802.class, MOTADATA_PATCH, "Patch 8.0.2");

    @Override
    public String getVersion()
    {
        return "8.0.2";
    }

    @Override
    public Future<Void> doPatch()
    {
        var future = Promise.<Void>promise();

        LOGGER.info("changing agent.status to down for all agents!");

        try
        {
            var agents = AgentConfigStore.getStore().getItems();

            var futures = new ArrayList<Future<Void>>();

            for (var index = 0; index < agents.size(); index++)
            {
                var promise = Promise.<Void>promise();

                futures.add(promise.future());

                var agent = agents.getJsonObject(index);

                if (CommonUtil.traceEnabled())
                {
                    LOGGER.trace(String.format("changing status for agent %s", agent.encode()));
                }

                Bootstrap.configDBService().update(TBL_AGENT,
                        new JsonObject().put(FIELD_NAME, ID).put(VALUE, agent.getLong(ID)),
                        agent.put(AGENT_STATUS, STATUS_DOWN),
                        DEFAULT_USER, MOTADATA_SYSTEM, result ->
                        {
                            if (result.succeeded())
                            {
                                AgentConfigStore.getStore().updateItem(agent.getLong(ID)).onComplete(asyncResult ->
                                {
                                    if (asyncResult.succeeded())
                                    {
                                        promise.complete();

                                        LOGGER.info(String.format("updated agent : %s", agent.getLong(ID)));
                                    }
                                    else
                                    {
                                        promise.fail(asyncResult.cause());

                                        LOGGER.warn(String.format("failed to update agent : %s ", agent.getLong(ID)));
                                    }
                                });
                            }
                            else
                            {
                                promise.fail(result.cause());

                                LOGGER.warn(String.format("failed to update agent : %s ", agent.getLong(ID)));
                            }
                        });
            }

            Future.join(futures).onComplete(result ->
            {
                if (result.succeeded())
                {
                    future.complete();
                }
                else
                {
                    future.fail(result.cause());
                }
            });
        }
        catch (Exception exception)
        {
            future.fail(exception);

            LOGGER.error(exception);
        }

        return future.future();
    }
}
