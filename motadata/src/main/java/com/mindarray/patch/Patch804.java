/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

package com.mindarray.patch;

import com.mindarray.Bootstrap;
import com.mindarray.api.LDAPServer;
import com.mindarray.api.MotadataApp;
import com.mindarray.api.RemoteEventProcessor;
import com.mindarray.store.ArtifactConfigStore;
import com.mindarray.store.LDAPServerConfigStore;
import com.mindarray.store.RemoteEventProcessorConfigStore;
import com.mindarray.util.CommonUtil;
import com.mindarray.util.Logger;
import io.vertx.core.Future;
import io.vertx.core.Promise;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;

import java.util.ArrayList;

import static com.mindarray.GlobalConstants.*;
import static com.mindarray.api.RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_INSTALLATION_MODE;
import static com.mindarray.api.RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_TYPE;
import static com.mindarray.db.DBConstants.*;

public class Patch804 implements Patch
{
    private static final Logger LOGGER = new Logger(Patch804.class, MOTADATA_PATCH, "Patch 8.0.4");

    private static final String VERSION = "8.0.4";

    @Override
    public String getVersion()
    {
        return VERSION;
    }

    /**
     * -> For BOI:-
     *
     * @CASE_1 - remote.event.processor.type = "MASTER"
     * @Solution - remote.event.processor.type = "APP", remote.event.processor.installation.mode = "PRIMARY"
     * @CASE_2 - remote.event.processor.type = "SLAVE"
     * @Solution - remote.event.processor.type = "APP", remote.event.processor.installation.mode = "STANDALONE"
     * @CASE_3 - remote.event.processor.type = "PRIMARY", remote.event.type = "DATASTORE"
     * @Solution - remote.event.processor.type = "DATASTORE", remote.event.processor.installation.mode = "PRIMARY"
     * @CASE_4 - remote.event.processor.type = "SECONDARY", remote.event.type = "DATASTORE"
     * @Solution - remote.event.processor.type = "DATASTORE", remote.event.processor.installation.mode = "SECONDARY"
     * @CASE_5 - remote.event.processor.type = "AGENT" || remote.event.processor.type = "COLLECTOR"
     * @Solution - remote.event.processor.type = "AGENT" || "COLLECTOR", remote.event.processor.installation.mode = "STANDALONE"
     * @-> For Master Branch:-
     * @CASE_1 - remote.event.processor.type = "DEFAULT"
     * @Solution - remote.event.processor.type = "APP", remote.event.processor.installation.mode = "STANDALONE"
     * @CASE_2 - remote.event.processor.type = "AGENT" || "COLLECTOR" || "DATASTORE"
     * @Solution - remote.event.processor.type = "AGENT" || "COLLECTOR" || "DATASTORE" , remote.event.installation.mode = "STANDALONE"
     **/
    @Override
    public Future<Void> doPatch()
    {
        var future = Promise.<Void>promise();

        LOGGER.info("changing remote.event.processor.type and remote.event.processor.installation.mode for all remote.event.processor");

        try
        {
            var items = LDAPServerConfigStore.getStore().getItems();

            var futures = new ArrayList<Future<Void>>();

            for (var index = 0; index < items.size(); index++)
            {
                var promise = Promise.<Void>promise();

                futures.add(promise.future());

                var item = items.getJsonObject(index);

                if (CommonUtil.traceEnabled())
                {
                    LOGGER.trace(String.format("applying patch for LDAP Server %s", item.encode()));
                }

                Bootstrap.configDBService().update(TBL_LDAP_SERVER,
                        new JsonObject().put(FIELD_NAME, ID).put(VALUE, item.getLong(ID)),
                        item.put(LDAPServer.LDAP_SERVER_USER_GROUPS, item.getJsonArray("ldap.user.groups", new JsonArray())),
                        DEFAULT_USER, MOTADATA_SYSTEM, result ->
                        {
                            if (result.succeeded())
                            {
                                LDAPServerConfigStore.getStore().updateItem(item.getLong(ID)).onComplete(asyncResult ->
                                {
                                    if (asyncResult.succeeded())
                                    {
                                        promise.complete();

                                        LOGGER.info(String.format("updated LDAP Server : %s", item.getLong(ID)));
                                    }
                                    else
                                    {
                                        promise.fail(asyncResult.cause());

                                        LOGGER.warn(asyncResult.cause().getMessage());
                                    }
                                });
                            }
                            else
                            {
                                promise.fail(result.cause());

                                LOGGER.warn(String.format("failed to update LDAP Server : %s ", item.getLong(ID)));
                            }
                        });
            }

            items = RemoteEventProcessorConfigStore.getStore().getItems();

            for (var index = 0; index < items.size(); index++)
            {
                var item = items.getJsonObject(index);

                if (CommonUtil.traceEnabled())
                {
                    LOGGER.trace(String.format("applying patch for remote event processor %s", item.encode()));
                }

                if (item.containsKey(REMOTE_EVENT_PROCESSOR_INSTALLATION_MODE) && !item.getString(REMOTE_EVENT_PROCESSOR_INSTALLATION_MODE).isEmpty())
                {
                    LOGGER.info(String.format("Not applying patch on item as it contains remote.event.processor.installation.mode: %s", item.getString(REMOTE_EVENT_PROCESSOR_INSTALLATION_MODE)));
                }
                else
                {
                    var promise = Promise.<Void>promise();

                    futures.add(promise.future());

                    switch (item.getString(REMOTE_EVENT_PROCESSOR_TYPE))
                    {
                        case "DEFAULT":

                            updateRemoteEventProcessorConfigs(item.put(REMOTE_EVENT_PROCESSOR_TYPE, BootstrapType.APP.name())
                                    .put(REMOTE_EVENT_PROCESSOR_INSTALLATION_MODE, InstallationMode.STANDALONE.name()), promise);

                            break;
                        case "MASTER":

                            updateRemoteEventProcessorConfigs(item.put(REMOTE_EVENT_PROCESSOR_TYPE, BootstrapType.APP.name())
                                    .put(REMOTE_EVENT_PROCESSOR_INSTALLATION_MODE, InstallationMode.PRIMARY.name()), promise);

                            break;
                        case "SLAVE":

                            updateRemoteEventProcessorConfigs(item.put(REMOTE_EVENT_PROCESSOR_TYPE, BootstrapType.APP.name())
                                    .put(REMOTE_EVENT_PROCESSOR_INSTALLATION_MODE, InstallationMode.SECONDARY.name()), promise);

                            break;

                        case "REPLICA":

                            updateRemoteEventProcessorConfigs(item.put(REMOTE_EVENT_PROCESSOR_TYPE, BootstrapType.APP.name())
                                    .put(REMOTE_EVENT_PROCESSOR_INSTALLATION_MODE, InstallationMode.FAILOVER.name()), promise);

                            break;

                        case "PRIMARY":

                            if (item.getString("remote.event.type").equalsIgnoreCase("DATANODE"))
                            {
                                updateRemoteEventProcessorConfigs(item.put(REMOTE_EVENT_PROCESSOR_INSTALLATION_MODE, InstallationMode.PRIMARY.name())
                                        .put(REMOTE_EVENT_PROCESSOR_TYPE, BootstrapType.DATASTORE.name()), promise);
                            }
                            break;

                        case "SECONDARY":

                            if (item.getString("remote.event.type").equalsIgnoreCase("DATANODE"))
                            {
                                updateRemoteEventProcessorConfigs(item.put(REMOTE_EVENT_PROCESSOR_INSTALLATION_MODE, InstallationMode.SECONDARY.name())
                                        .put(REMOTE_EVENT_PROCESSOR_TYPE, BootstrapType.DATASTORE.name()), promise);
                            }

                            break;

                        case "AGENT", "COLLECTOR", "EVENT_COLLECTOR", "EVENT_PROCESSOR", "FLOW_COLLECTOR", "DATASTORE":

                            updateRemoteEventProcessorConfigs(item.put(REMOTE_EVENT_PROCESSOR_INSTALLATION_MODE, InstallationMode.STANDALONE.name()), promise);

                            break;
                        default:
                            LOGGER.info("Not applying patch for type: " + item.getString(REMOTE_EVENT_PROCESSOR_TYPE));
                    }
                }

            }

            items = ArtifactConfigStore.getStore().getItems();

            for (var index = 0; index < items.size(); index++)
            {
                var item = items.getJsonObject(index);

                if (CommonUtil.traceEnabled())
                {
                    LOGGER.trace(String.format("applying patch for artifact %s", item.encode()));
                }

                if (item.containsKey(MotadataApp.ARTIFACT_MODE) && !item.getString(MotadataApp.ARTIFACT_MODE).isEmpty())
                {
                    LOGGER.info(String.format("Not applying patch on item containing artifact.mode: %s", item.getString(MotadataApp.ARTIFACT_MODE)));
                }
                else
                {
                    var promise = Promise.<Void>promise();

                    futures.add(promise.future());

                    switch (item.getString(MotadataApp.ARTIFACT_TYPE).toUpperCase())
                    {
                        case "DEFAULT":

                            updateArtifactConfigs(item.put(MotadataApp.ARTIFACT_TYPE, BootstrapType.APP.name().toLowerCase())
                                    .put(MotadataApp.ARTIFACT_MODE, InstallationMode.STANDALONE.name().toLowerCase()), promise);
                            break;

                        case "MASTER":

                            updateArtifactConfigs(item.put(MotadataApp.ARTIFACT_TYPE, BootstrapType.APP.name().toLowerCase())
                                    .put(MotadataApp.ARTIFACT_MODE, InstallationMode.PRIMARY.name().toLowerCase()), promise);
                            break;

                        case "SLAVE":

                            updateArtifactConfigs(item.put(MotadataApp.ARTIFACT_TYPE, BootstrapType.APP.name().toLowerCase())
                                    .put(MotadataApp.ARTIFACT_MODE, InstallationMode.SECONDARY.name().toLowerCase()), promise);
                            break;

                        case "REPLICA":

                            updateArtifactConfigs(item.put(MotadataApp.ARTIFACT_TYPE, BootstrapType.APP.name().toLowerCase())
                                    .put(MotadataApp.ARTIFACT_MODE, InstallationMode.FAILOVER.name().toLowerCase()), promise);
                            break;

                        case "PRIMARY":

                            updateArtifactConfigs(item.put(MotadataApp.ARTIFACT_TYPE, BootstrapType.DATASTORE.name().toLowerCase())
                                    .put(MotadataApp.ARTIFACT_MODE, InstallationMode.PRIMARY.name().toLowerCase()), promise);
                            break;

                        case "SECONDARY":

                            updateArtifactConfigs(item.put(MotadataApp.ARTIFACT_TYPE, BootstrapType.DATASTORE.name().toLowerCase())
                                    .put(MotadataApp.ARTIFACT_MODE, InstallationMode.SECONDARY.name().toLowerCase()), promise);
                            break;

                        case "AGENT", "COLLECTOR", "EVENT_COLLECTOR", "EVENT_PROCESSOR", "FLOW_COLLECTOR", "DATASTORE":

                            updateArtifactConfigs(item.put(MotadataApp.ARTIFACT_MODE, InstallationMode.STANDALONE.name().toLowerCase()), promise);
                            break;

                        default:
                            LOGGER.info("Not applying patch for type: " + item.getString(MotadataApp.ARTIFACT_TYPE));
                    }
                }
            }

            Future.join(futures).onComplete(result ->
            {
                if (result.succeeded())
                {
                    future.complete();
                }
                else
                {
                    future.fail(result.cause());
                }
            });
        }
        catch (Exception exception)
        {
            future.fail(exception);

            LOGGER.error(exception);
        }

        return future.future();
    }

    private void updateRemoteEventProcessorConfigs(JsonObject item, Promise<Void> promise)
    {
        Bootstrap.configDBService().update(TBL_REMOTE_EVENT_PROCESSOR,
                new JsonObject().put(FIELD_NAME, ID).put(VALUE, item.getLong(ID)),
                item.put(RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_VERSION, VERSION),
                DEFAULT_USER, MOTADATA_SYSTEM, result ->
                {
                    if (result.succeeded())
                    {
                        RemoteEventProcessorConfigStore.getStore().updateItem(item.getLong(ID)).onComplete(asyncResult ->
                        {
                            if (asyncResult.succeeded())
                            {
                                promise.complete();

                                LOGGER.info(String.format("updated remote event processor : %s : item: %s", item.getLong(ID), RemoteEventProcessorConfigStore.getStore().getItem(item.getLong(ID))));
                            }
                            else
                            {
                                promise.fail(asyncResult.cause());

                                LOGGER.warn(asyncResult.cause().getMessage());
                            }
                        });
                    }
                    else
                    {
                        promise.fail(result.cause());

                        LOGGER.warn(String.format("failed to update remote event processor : %s ", item.getLong(ID)));
                    }
                });
    }

    private void updateArtifactConfigs(JsonObject item, Promise<Void> promise)
    {
        Bootstrap.configDBService().update(TBL_ARTIFACT,
                new JsonObject().put(FIELD_NAME, ID).put(VALUE, item.getLong(ID)),
                item.put(MotadataApp.ARTIFACT_VERSION, VERSION),
                DEFAULT_USER, MOTADATA_SYSTEM, result ->
                {
                    if (result.succeeded())
                    {
                        ArtifactConfigStore.getStore().updateItem(item.getLong(ID)).onComplete(asyncResult ->
                        {
                            if (asyncResult.succeeded())
                            {
                                promise.complete();

                                LOGGER.info(String.format("updated artifact : %s: item: %s", item.getLong(ID), ArtifactConfigStore.getStore().getItem(item.getLong(ID))));
                            }
                            else
                            {
                                promise.fail(asyncResult.cause());

                                LOGGER.warn(asyncResult.cause().getMessage());
                            }
                        });
                    }
                    else
                    {
                        promise.fail(result.cause());

                        LOGGER.warn(String.format("failed to update artifact : %s ", item.getLong(ID)));
                    }
                });
    }
}
