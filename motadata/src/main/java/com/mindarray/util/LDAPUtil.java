/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

/*
 *  Change Logs:
 *  Date            Author          Notes
 *  14-Feb-2025     Chandresh      MOTADATA-4822: Enhanced getLDAPConnection method to support HA with FailoverServerSet
 *  3-Mar-2025      Chandresh      MOTADATA-4822: ldap.server.host -> ldap.server.primary.host
 *  2-May-2025      Aagam          MOTADATA-6010: Added support of Open LDAP
 */


package com.mindarray.util;

import com.mindarray.*;
import com.mindarray.api.APIConstants;
import com.mindarray.api.LDAPServer;
import com.mindarray.db.DBConstants;
import com.mindarray.eventbus.EventBusConstants;
import com.mindarray.store.LDAPServerConfigStore;
import com.mindarray.store.PersonalAccessTokenConfigStore;
import com.mindarray.store.UserConfigStore;
import com.unboundid.ldap.sdk.*;
import com.unboundid.util.ssl.SSLUtil;
import com.unboundid.util.ssl.TrustAllTrustManager;
import io.vertx.core.Future;
import io.vertx.core.Promise;
import io.vertx.core.impl.ConcurrentHashSet;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;

import java.security.GeneralSecurityException;
import java.util.*;

import static com.mindarray.GlobalConstants.*;
import static com.mindarray.api.LDAPServer.LDAP_SERVER_SECONDARY_HOST;
import static com.mindarray.api.LDAPServer.LDAP_SERVER_TYPE;
import static com.mindarray.api.User.*;
import static com.mindarray.db.DBConstants.*;
import static com.mindarray.eventbus.EventBusConstants.UI_ACTION_LDAP_SYNC;

/**
 * Utility class for LDAP (Lightweight Directory Access Protocol) operations.
 * <p>
 * This class provides functionality for:
 * - Establishing connections to LDAP servers (Active Directory and OpenLDAP)
 * - Authenticating users against LDAP servers
 * - Synchronizing users from LDAP to the local system
 * - Retrieving user information from LDAP directories
 * - Managing LDAP connections and handling LDAP-specific errors
 * <p>
 * The class supports both standard LDAP and secure LDAP (LDAPS) connections,
 * as well as high availability configurations with primary and secondary LDAP servers.
 */
public final class LDAPUtil
{

    private static final String CONNECT_ERROR_MESSAGE = "Failed to connect, verify IP and Port";
    private static final Logger LOGGER = new Logger(LDAPUtil.class, GlobalConstants.MOTADATA_UTIL, "LDAP Util");
    private static final int LDAP_CONNECTION_TIMEOUT_MILLIS = 60 * 1000;
    private static final int CONNECT_ERROR_CODE = 91;
    private static final int INVALID_CREDENTIALS_CODE = 49;
    private static final Set<Long> ACTIVE_SYNC_JOBS = new ConcurrentHashSet<>();

    /**
     * Synchronizes users from an LDAP server to the local system.
     * <p>
     * This method retrieves user information from the specified LDAP server and:
     * - Creates new users in the local system that exist in LDAP but not locally
     * - Updates existing users in the local system with information from LDAP
     * - Removes local users that no longer exist in LDAP
     * <p>
     * The method uses a concurrency control mechanism to prevent multiple synchronization
     * jobs for the same LDAP server from running simultaneously.
     *
     * @param sessionId     The session ID for event bus communication, can be null if not needed
     * @param user          The username of the user initiating the sync operation (for audit purposes)
     * @param remoteAddress The IP address of the client initiating the sync operation (for audit purposes)
     * @param id            The ID of the LDAP server configuration to use for synchronization
     */
    public static void syncLDAPUsers(String sessionId, String user, String remoteAddress, long id)
    {
        if (!ACTIVE_SYNC_JOBS.contains(id))
        {
            ACTIVE_SYNC_JOBS.add(id);

            try
            {
                var ldapServer = LDAPServerConfigStore.getStore().getItem(id);

                if (ldapServer != null && !ldapServer.isEmpty())
                {
                    var primaryHost = ldapServer.getString(LDAPServer.LDAP_SERVER_PRIMARY_HOST);

                    var port = ldapServer.getInteger(LDAPServer.LDAP_SERVER_PORT);

                    var username = ldapServer.getString(LDAPServer.LDAP_SERVER_USERNAME);

                    var fqdn = ldapServer.getString(LDAPServer.LDAP_SERVER_FQDN);

                    var groups = !LDAPServer.LDAP_SERVER_TYPE_OPEN.equals(ldapServer.getString(LDAPServer.LDAP_SERVER_TYPE))
                            ? ldapServer.getJsonArray(LDAPServer.LDAP_SERVER_USER_GROUPS).getList() : null;

                    if (!ldapServer.isEmpty() && CommonUtil.isNotNullOrEmpty(primaryHost) && CommonUtil.isNotNullOrEmpty(username)
                            && CommonUtil.isNotNullOrEmpty(ldapServer.getString(LDAPServer.LDAP_SERVER_PASSWORD)) && CommonUtil.isNotNullOrEmpty(fqdn) && port != null)
                    {
                        Bootstrap.vertx().<JsonObject>executeBlocking(future ->
                        {
                            LDAPConnection ldapConnection = null;

                            try
                            {
                                var context = LDAPUtil.getLDAPConnection(primaryHost, ldapServer.getString(LDAP_SERVER_SECONDARY_HOST), port, username, ldapServer.getString(LDAPServer.LDAP_SERVER_PASSWORD), fqdn, ldapServer.getString(LDAPServer.LDAP_SERVER_PROTOCOL), ldapServer.getString(LDAPServer.LDAP_SERVER_TYPE));

                                ldapConnection = (LDAPConnection) context.get(RESULT);

                                if (ldapConnection != null)
                                {
                                    var result = LDAPUtil.getLDAPUsers(ldapConnection, fqdn, UI_ACTION_LDAP_SYNC, groups, ldapServer.getString(LDAP_SERVER_TYPE));

                                    if (result.getJsonArray(RESULT) != null)
                                    {
                                        updateLDAPUsers(result.getJsonArray(RESULT), user, future, remoteAddress, groups, ldapServer);
                                    }
                                    else
                                    {
                                        ACTIVE_SYNC_JOBS.remove(id);

                                        if (result.containsKey(MESSAGE))
                                        {
                                            future.complete(new JsonObject().put(STATUS, STATUS_FAIL)
                                                    .put(MESSAGE, result.getString(MESSAGE))
                                                    .put(ERROR, result.getString(ERROR))
                                                    .put(ERROR_CODE, result.getString(ERROR_CODE))
                                                    .put(ID, id));

                                            LOGGER.warn(result.getString(MESSAGE));
                                        }
                                    }
                                }
                                else
                                {
                                    ACTIVE_SYNC_JOBS.remove(id);

                                    future.complete(new JsonObject().put(STATUS, STATUS_FAIL)
                                            .put(MESSAGE, String.format(ErrorMessageConstants.LDAP_SYNC_FAILED, context.get(MESSAGE)))
                                            .put(ERROR_CODE, ErrorCodes.ERROR_CODE_CONNECTION_FAILED)
                                            .put(ID, id));
                                }
                            }
                            catch (Exception exception)
                            {
                                ACTIVE_SYNC_JOBS.remove(id);

                                future.complete(new JsonObject().put(STATUS, STATUS_FAIL)
                                        .put(MESSAGE, String.format(ErrorMessageConstants.LDAP_SYNC_FAILED, exception.getMessage()))
                                        .put(ERROR, CommonUtil.formatStackTrace(exception.getStackTrace()))
                                        .put(ERROR_CODE, ErrorCodes.ERROR_CODE_INTERNAL_ERROR)
                                        .put(ID, id));

                                LOGGER.error(exception);
                            }
                            finally
                            {
                                close(ldapConnection);
                            }

                        }, result ->
                        {
                            ACTIVE_SYNC_JOBS.remove(id);

                            if (sessionId != null)
                            {
                                EventBusConstants.publish(sessionId, EventBusConstants.UI_ACTION_LDAP_SYNC, result.result());
                            }

                            LOGGER.debug(result.result().encodePrettily());
                        });
                    }
                    else
                    {
                        ACTIVE_SYNC_JOBS.remove(id);

                        if (sessionId != null)
                        {
                            EventBusConstants.publish(sessionId, EventBusConstants.UI_ACTION_LDAP_SYNC, new JsonObject().put(STATUS, STATUS_FAIL)
                                    .put(MESSAGE, ErrorMessageConstants.LDAP_SYNC_FAILED_NOT_CONFIGURED)
                                    .put(ERROR_CODE, ErrorCodes.ERROR_CODE_BAD_REQUEST)
                                    .put(ID, id));
                        }

                        LOGGER.warn(ErrorMessageConstants.LDAP_SYNC_FAILED_NOT_CONFIGURED);
                    }
                }
                else
                {
                    ACTIVE_SYNC_JOBS.remove(id);

                    if (sessionId != null)
                    {
                        EventBusConstants.publish(sessionId, EventBusConstants.UI_ACTION_LDAP_SYNC, new JsonObject().put(STATUS, STATUS_FAIL)
                                .put(MESSAGE, ErrorMessageConstants.LDAP_SYNC_FAILED_NOT_CONFIGURED)
                                .put(ERROR_CODE, ErrorCodes.ERROR_CODE_BAD_REQUEST)
                                .put(ID, id));
                    }

                    LOGGER.warn(ErrorMessageConstants.LDAP_SYNC_FAILED_NOT_CONFIGURED);
                }
            }

            catch (Exception exception)
            {
                ACTIVE_SYNC_JOBS.remove(id);

                if (sessionId != null)
                {
                    EventBusConstants.publish(sessionId, EventBusConstants.UI_ACTION_LDAP_SYNC, new JsonObject().put(STATUS, STATUS_FAIL)
                            .put(MESSAGE, String.format(ErrorMessageConstants.LDAP_SYNC_FAILED, exception.getMessage()))
                            .put(ERROR, CommonUtil.formatStackTrace(exception.getStackTrace()))
                            .put(ERROR_CODE, ErrorCodes.ERROR_CODE_INTERNAL_ERROR)
                            .put(ID, id));
                }

                LOGGER.error(exception);
            }
        }
        else
        {
            if (sessionId != null)
            {
                EventBusConstants.publish(sessionId, EventBusConstants.UI_ACTION_LDAP_SYNC, new JsonObject().put(STATUS, STATUS_FAIL)
                        .put(MESSAGE, ErrorMessageConstants.LDAP_SYNC_FAILED_ALREADY_RUNNING)
                        .put(ERROR_CODE, ErrorCodes.ERROR_CODE_LDAP_SYNC_ALREADY_RUNNING)
                        .put(ID, id));
            }
        }
    }

    /**
     * Updates the local user database with information from LDAP.
     * <p>
     * This private method handles the core logic of the LDAP synchronization process:
     * 1. Identifies new users that exist in LDAP but not in the local system
     * 2. Updates existing users with the latest information from LDAP
     * 3. Removes users from the local system that no longer exist in LDAP
     * 4. Updates the LDAP server's last synchronization timestamp
     * <p>
     * For users that are removed, it also cleans up associated resources like personal access tokens.
     *
     * @param syncedUsers   The JsonArray of users retrieved from the LDAP server
     * @param auditUser     The username to use for audit logging
     * @param future        The Promise to complete with the result of the operation
     * @param remoteAddress The client IP address for audit logging
     * @param groups        The list of LDAP groups that were searched
     * @param ldapServer    The JsonObject containing the LDAP server configuration
     */
    private static void updateLDAPUsers(JsonArray syncedUsers, String auditUser, Promise<JsonObject> future, String remoteAddress, List<String> groups, JsonObject ldapServer)
    {
        var futures = new ArrayList<Future<Void>>();

        try
        {
            var previousUsers = new JsonObject();

            var id = ldapServer.getLong(ID, 0L);

            var items = UserConfigStore.getStore().flatItemsByValue(USER_TYPE, USER_TYPE_LDAP, USER_LDAP_SERVER, id);

            for (var index = 0; index < items.size(); index++)
            {
                var user = items.getJsonObject(index);

                previousUsers.put(user.getString(USER_NAME), user);
            }

            var newUsers = new JsonArray();

            var existingUsers = new JsonArray();

            for (var index = 0; index < syncedUsers.size(); index++)
            {
                var user = syncedUsers.getJsonObject(index);

                var userName = user.getString(USER_NAME);

                if (!userName.equalsIgnoreCase(DEFAULT_USER))//as its internal superuser admin so will not allow admin username to sync
                {
                    if (previousUsers.containsKey(userName))
                    {
                        existingUsers.add(previousUsers.getJsonObject(userName).mergeIn(user));

                        previousUsers.remove(userName);
                    }
                    else
                    {
                        newUsers.add(user.put(USER_TYPE, USER_TYPE_LDAP).put(FIELD_TYPE, ENTITY_TYPE_USER).put(USER_LDAP_SERVER, id).put(USER_PREFERENCES, new JsonObject()));
                    }
                }
            }

            var promise = Promise.<Void>promise();

            futures.add(promise.future());

            Bootstrap.configDBService().saveAll(DBConstants.TBL_USER, newUsers, auditUser, remoteAddress, result ->
            {
                promise.complete();

                if (result.failed())
                {
                    LOGGER.warn("failed to save synced user...");
                }

            });

            for (var index = 0; index < existingUsers.size(); index++)
            {
                var userName = existingUsers.getJsonObject(index).getString(USER_NAME);

                var userPromise = Promise.<Void>promise();

                futures.add(userPromise.future());

                Bootstrap.configDBService().update(DBConstants.TBL_USER,
                        new JsonObject().put(DBConstants.FIELD_NAME, ID).put(VALUE, existingUsers.getJsonObject(index).getLong(ID)),
                        existingUsers.getJsonObject(index), auditUser, remoteAddress, result ->
                        {
                            userPromise.complete();

                            if (result.failed())
                            {
                                LOGGER.warn(String.format("failed to update ldap user: %s", userName));
                            }
                        });
            }

            //Users still left in oldUsersContext were not among the new Users. So they will be deleted.

            for (var user : previousUsers)
            {
                var userPromise = Promise.<Void>promise();

                futures.add(userPromise.future());

                var userContext = JsonObject.mapFrom(user.getValue());

                var userId = userContext.getLong(ID);

                // if any user is deleted from the LDAP server itself then delete the personal access token associated to that user
                UserConfigStore.getStore().getReferenceEntities(userId).onComplete(result ->
                {
                    try
                    {
                        if (result.succeeded())
                        {
                            var ids = new JsonArray();

                            if (result.result().containsKey(APIConstants.Entity.PERSONAL_ACCESS_TOKEN.getName())
                                    && !result.result().getJsonArray(APIConstants.Entity.PERSONAL_ACCESS_TOKEN.getName()).isEmpty())
                            {
                                result.result().getJsonArray(APIConstants.Entity.PERSONAL_ACCESS_TOKEN.getName())
                                        .forEach(entity -> ids.add(JsonObject.mapFrom(entity).getLong(ID)));
                            }

                            if (!ids.isEmpty())
                            {
                                Bootstrap.configDBService().deleteAll(TBL_PERSONAL_ACCESS_TOKEN,
                                        new JsonObject().put(DBConstants.FIELD_NAME, ID).put(VALUE, ids), auditUser, remoteAddress, response ->
                                        {
                                            if (response.succeeded())
                                            {
                                                PersonalAccessTokenConfigStore.getStore().deleteItems(ids);

                                                Bootstrap.configDBService().delete(DBConstants.TBL_USER,
                                                        new JsonObject().put(DBConstants.FIELD_NAME, ID).put(VALUE, userId), auditUser, remoteAddress, asyncResult ->
                                                        {
                                                            userPromise.complete();

                                                            if (asyncResult.failed())
                                                            {
                                                                LOGGER.warn("failed to delete ldap user: " + userContext.getString(USER_NAME));
                                                            }
                                                        });
                                            }
                                            else
                                            {
                                                LOGGER.warn(response.cause());

                                                userPromise.complete();
                                            }
                                        });
                            }
                            else
                            {
                                Bootstrap.configDBService().delete(DBConstants.TBL_USER,
                                        new JsonObject().put(DBConstants.FIELD_NAME, ID).put(VALUE, userId), auditUser, remoteAddress, asyncResult ->
                                        {
                                            userPromise.complete();

                                            if (asyncResult.failed())
                                            {
                                                LOGGER.warn("failed to delete ldap user: " + userContext.getString(USER_NAME));
                                            }
                                        });
                            }
                        }
                        else
                        {
                            LOGGER.warn(result.cause());

                            userPromise.complete();
                        }
                    }
                    catch (Exception exception)
                    {
                        LOGGER.warn(exception);

                        userPromise.complete();
                    }
                });
            }

            if (syncedUsers.isEmpty())
            {
                Future.join(futures).onComplete(response ->
                        UserConfigStore.getStore().updateStore().onComplete(result -> future.complete(new JsonObject().put(STATUS, STATUS_FAIL)
                                .put(ERROR_CODE, ErrorCodes.ERROR_CODE_LDAP_MOTADATA_USERS_GROUP_NO_USER_FOUND).put(ID, id).put(MESSAGE, String.format(ErrorMessageConstants.LDAP_SYNC_MOTADATA_USERS_GROUP_NO_USER_FOUND, groups.toString())))));

                LOGGER.warn(String.format(ErrorMessageConstants.LDAP_SYNC_MOTADATA_USERS_GROUP_NO_USER_FOUND, groups.toString()));
            }
            else
            {
                Future.join(futures).onComplete(response ->
                        UserConfigStore.getStore().updateStore().onComplete(result ->
                        {
                            var timestamp = DateTimeUtil.currentSeconds();

                            Bootstrap.configDBService().update(TBL_LDAP_SERVER,
                                    new JsonObject().put(FIELD_NAME, ID).put(VALUE, id),
                                    ldapServer.put(TIME_STAMP, timestamp),
                                    DEFAULT_USER, SYSTEM_REMOTE_ADDRESS, asyncResult ->
                                    {
                                        future.complete(new JsonObject().put(STATUS, STATUS_SUCCEED)
                                                .put(ERROR_CODE, ErrorCodes.ERROR_CODE_SUCCESS).put(MESSAGE, String.format(InfoMessageConstants.LDAP_SERVER_SYNC_SUCCEEDED, ldapServer.getString(LDAPServer.LDAP_SERVER_PRIMARY_HOST, EMPTY_VALUE))).put(TIME_STAMP, timestamp).put(ID, id));

                                        if (asyncResult.succeeded())
                                        {
                                            LDAPServerConfigStore.getStore().updateItem(id);
                                        }
                                        else
                                        {
                                            LOGGER.warn(String.format("failed to update last sync timestamp for LDAP server: %s, Possible Reason: %s, ", ldapServer.getString(LDAPServer.LDAP_SERVER_PRIMARY_HOST), asyncResult.cause()));
                                        }
                                    });
                        }));
            }
        }
        catch (Exception exception)
        {
            future.complete(new JsonObject().put(STATUS, STATUS_FAIL)
                    .put(MESSAGE, String.format(ErrorMessageConstants.LDAP_SYNC_FAILED, exception.getMessage()))
                    .put(ERROR, CommonUtil.formatStackTrace(exception.getStackTrace()))
                    .put(ID, ldapServer.getLong(ID, 0L))
                    .put(ERROR_CODE, ErrorCodes.ERROR_CODE_INTERNAL_ERROR));

            LOGGER.error(exception);
        }
    }

    /**
     * Establishes a connection to an LDAP server with support for failover to a secondary server.
     * <p>
     * This method creates an LDAP connection with the following features:
     * - Support for both primary and secondary LDAP servers (high availability)
     * - Support for both standard LDAP and secure LDAP (LDAPS) connections
     * - Support for different LDAP server types (Active Directory and OpenLDAP)
     * - Configurable connection and response timeouts
     * - Automatic binding with the provided credentials
     *
     * @param primaryHost   The hostname or IP address of the primary LDAP server
     * @param secondaryHost The hostname or IP address of the secondary LDAP server (can be null or empty)
     * @param port          The port number for the LDAP server connection
     * @param userName      The username for authentication to the LDAP server
     * @param password      The password for authentication to the LDAP server
     * @param domain        The domain (FQDN) for the LDAP server
     * @param protocol      The protocol to use (LDAP or LDAPS)
     * @param serverType    The type of LDAP server (Active Directory or OpenLDAP)
     * @return A map containing the LDAP connection object and any error information
     * Keys: RESULT (LDAPConnection object or null), MESSAGE (error message), ERROR (detailed error)
     */
    public static Map<String, Object> getLDAPConnection(String primaryHost, String secondaryHost, int port, String userName, String password, String domain, String protocol, String serverType)
    {
        var result = new HashMap<String, Object>();

        LDAPConnection connection;

        try
        {
            var ldapConnectionOptions = new LDAPConnectionOptions();

            ldapConnectionOptions.setConnectTimeoutMillis(LDAP_CONNECTION_TIMEOUT_MILLIS);

            ldapConnectionOptions.setResponseTimeoutMillis(LDAP_CONNECTION_TIMEOUT_MILLIS);

            FailoverServerSet failoverHosts;

            String[] hosts;

            int[] ports;

            // Configure hosts and ports based on secondaryHost availability
            // If a secondary host is provided, set up failover between primary and secondary
            if (CommonUtil.isNotNullOrEmpty(secondaryHost))
            {
                hosts = new String[]{primaryHost, secondaryHost};
                ports = new int[]{port, port};
            }
            else
            {
                // If no secondary host is provided, use only the primary host
                hosts = new String[]{primaryHost};
                ports = new int[]{port};
            }

            // Create appropriate FailoverServerSet based on protocol
            if (protocol.equalsIgnoreCase(LDAPServer.LDAP_SERVER_PROTOCOL_SSL))
            {
                failoverHosts = new FailoverServerSet(
                        hosts,
                        ports,
                        new SSLUtil(null, new TrustAllTrustManager()).createSSLSocketFactory(),
                        ldapConnectionOptions
                );
            }
            else
            {
                failoverHosts = new FailoverServerSet(
                        hosts,
                        ports,
                        ldapConnectionOptions
                );
            }

            connection = failoverHosts.getConnection();

            // Determine the appropriate bind DN format based on the LDAP server type
            String bindDN;

            if (serverType.equals(LDAPServer.LDAP_SERVER_TYPE_OPEN))
            {
                // OpenLDAP uses a different bind DN format than Active Directory
                // Format: uid=username,cn=users,cn=accounts,dc=example,dc=com
                bindDN = "uid=" + userName + ",cn=users,cn=accounts," + getBaseDN(domain);
            }
            else
            {
                // Active Directory typically uses the userPrincipalName format
                // Format: <EMAIL>
                bindDN = userName + "@" + domain;
            }

            // Attempt to bind (authenticate) with the LDAP server using the credentials
            connection.bind(bindDN, password);

            if (!connection.isConnected())
            {
                LOGGER.warn(String.format("failed to obtain ldap connection %s:%s...", primaryHost, port));
            }

            result.put(RESULT, connection);
        }
        catch (LDAPException ldapException)
        {
            result.put(RESULT, null);

            result.put(MESSAGE, getErrorMessage(ldapException));

            result.put(ERROR, ldapException.getExceptionMessage());

            LOGGER.error(ldapException);
        }
        catch (GeneralSecurityException securityException)
        {
            result.put(RESULT, null);

            result.put(MESSAGE, ErrorMessageConstants.LDAP_SECURE_CONNECTION_FAILED);

            result.put(ERROR, "Security error");

            LOGGER.error(securityException);
        }

        return result;
    }

    /**
     * Retrieves user information from an LDAP server based on specified groups.
     * <p>
     * This method searches the LDAP directory for users that belong to the specified groups
     * and extracts relevant user information such as:
     * - Username
     * - First name
     * - Last name
     * - Email address
     * - Mobile number
     * <p>
     * The method handles both Active Directory and OpenLDAP server types with appropriate
     * search filters and attribute mappings for each type.
     *
     * @param ldapConnection An established connection to the LDAP server
     * @param fqdn           The fully qualified domain name (FQDN) of the LDAP server
     * @param eventType      The type of event that triggered this operation (for logging and error messages)
     * @param groups         The list of group names to search for users within
     * @param serverType     The type of LDAP server (Active Directory or OpenLDAP)
     * @return A JsonObject containing the result status and either a JsonArray of users or error information
     * Structure: {status, error_code, result: [users]} or {status, error_code, message, error}
     */
    public static JsonObject getLDAPUsers(LDAPConnection ldapConnection, String fqdn, String eventType, List<String> groups, String serverType)
    {
        var result = new JsonObject();

        var users = new JsonArray();

        String message;

        if (CommonUtil.isNotNullOrEmpty(fqdn))
        {
            if (ldapConnection != null && ldapConnection.isConnected())
            {
                try
                {
                    var baseDn = getBaseDN(fqdn);

                    var groupDNs = getGroupDNs(ldapConnection, groups, baseDn);

                    if (!groupDNs.isEmpty() || serverType.equals(LDAPServer.LDAP_SERVER_TYPE_OPEN))
                    {
                        // Create filters for each group to find users who are members of these groups
                        var filters = new ArrayList<Filter>();

                        for (var groupDN : groupDNs)
                        {
                            // Create a filter that matches users who are members of this group
                            filters.add(Filter.create("(memberOf=" + groupDN + ")"));
                        }

                        Filter searchFilter;

                        if (serverType.equals(LDAPServer.LDAP_SERVER_TYPE_OPEN))
                        {
                            // For OpenLDAP, use a simpler filter that just looks for person objects with a uid
                            // This is because OpenLDAP has a different structure than Active Directory
                            searchFilter = Filter.create("(&(objectClass=person)(uid=*))");
                        }
                        else
                        {
                            // For Active Directory, create a more complex filter that:
                            // 1. Matches objects that are either users, persons, or groups (AND)
                            // 2. Matches objects that are members of at least one of the specified groups (OR)
                            searchFilter = Filter.createANDFilter(
                                    // Match objects that are either users, persons, or groups
                                    Filter.createORFilter(
                                            Filter.create("(objectCategory=user)"),
                                            Filter.create("(objectCategory=person)"),
                                            Filter.create("objectCategory=group")
                                    ),
                                    // Match objects that are members of at least one of the specified groups
                                    Filter.createORFilter(filters)
                            );
                        }

                        var nestedGroups = new ArrayList<String>();

                        ldapConnection.search(new SearchRequest(new SearchResultListener()
                        {
                            public void searchEntryReturned(SearchResultEntry searchResultEntry)
                            {
                                // Check if the entry is a group (has grouptype attribute)
                                if (searchResultEntry.getAttributeValue("grouptype") != null)
                                {
                                    // If this is a nested group (not in our original groups list), add it to nestedGroups
                                    if (!groups.contains(searchResultEntry.getAttributeValue("name")))
                                    {
                                        nestedGroups.add(searchResultEntry.getAttributeValue("name"));
                                    }
                                }
                                else
                                {
                                    // This is a user entry, extract user attributes
                                    var user = new JsonObject();

                                    // Extract username - try sAMAccountName first (Active Directory)
                                    if (searchResultEntry.getAttributeValue("sAMAccountName") != null)
                                    {
                                        user.put(USER_NAME, searchResultEntry.getAttributeValue("sAMAccountName"));
                                    }

                                    // If sAMAccountName not found, try uid (OpenLDAP)
                                    if (searchResultEntry.getAttributeValue("uid") != null && !user.containsKey(USER_NAME))
                                    {
                                        user.put(USER_NAME, searchResultEntry.getAttributeValue("uid"));
                                    }

                                    // Extract first name
                                    if (searchResultEntry.getAttributeValue("givenName") != null)
                                    {
                                        user.put(USER_FIRST_NAME, searchResultEntry.getAttributeValue("givenName"));
                                    }

                                    // Extract last name
                                    if (searchResultEntry.getAttributeValue("sn") != null)
                                    {
                                        user.put(USER_LAST_NAME, searchResultEntry.getAttributeValue("sn"));
                                    }

                                    // Extract email address
                                    if (searchResultEntry.getAttributeValue("mail") != null)
                                    {
                                        user.put(USER_EMAIL, searchResultEntry.getAttributeValue("mail"));
                                    }

                                    // Extract mobile number - try different attributes in order of preference
                                    if (searchResultEntry.getAttributeValue("mobile") != null)
                                    {
                                        user.put(USER_MOBILE, getMobileNumber(searchResultEntry.getAttributeValue("mobile")));
                                    }
                                    else if (searchResultEntry.getAttributeValue("othermobile") != null)
                                    {
                                        user.put(USER_MOBILE, getMobileNumber(searchResultEntry.getAttributeValue("othermobile")));
                                    }
                                    else if (searchResultEntry.getAttributeValue("telephonenumber") != null)
                                    {
                                        user.put(USER_MOBILE, getMobileNumber(searchResultEntry.getAttributeValue("telephonenumber")));
                                    }

                                    // Set user status to active
                                    user.put(USER_STATUS, GlobalConstants.YES);

                                    // Add user to the list if we extracted any information
                                    if (!user.isEmpty())
                                    {
                                        users.add(user);
                                    }
                                }
                            }

                            public void searchReferenceReturned(SearchResultReference searchResultReference)
                            {
                                //Not Required
                            }

                        }, baseDn.toString(), SearchScope.SUB, searchFilter));

                        if (!nestedGroups.isEmpty())
                        {
                            users.addAll(getLDAPUsers(ldapConnection, fqdn, eventType, nestedGroups, serverType).getJsonArray(RESULT));
                        }

                        result.put(STATUS, STATUS_SUCCEED).put(ERROR_CODE, ErrorCodes.ERROR_CODE_SUCCESS).put(RESULT, users);
                    }

                    else
                    {
                        /*
                         * This case can occur if either 'motadata users' group is not found or if FQDN is invalid.
                         * FQDN can not be invalid as it gets validated while testing credentials for LDAP. So the
                         * only possibility is that the group is not present.
                         */

                        message = eventType.equalsIgnoreCase(EventBusConstants.UI_ACTION_LDAP_SYNC)
                                ? String.format(ErrorMessageConstants.LDAP_SYNC_MOTADATA_USERS_GROUP_NOT_FOUND, groups.toString())
                                : String.format(ErrorMessageConstants.LOGIN_FAILED_MOTADATA_USERS_GROUP_NOT_FOUND, groups.toString());

                        result.put(STATUS, STATUS_FAIL).put(GlobalConstants.ERROR_CODE, ErrorCodes.ERROR_CODE_LDAP_MOTADATA_USERS_GROUP_NOT_FOUND).put(MESSAGE, message);

                        LOGGER.warn(message);

                    }
                }

                catch (Exception exception)
                {
                    result.put(STATUS, STATUS_FAIL).put(ERROR_CODE, ErrorCodes.ERROR_CODE_INTERNAL_ERROR).put(MESSAGE, eventType.equalsIgnoreCase(EventBusConstants.UI_ACTION_LDAP_SYNC) ? String.format(ErrorMessageConstants.LDAP_SYNC_FAILED, exception.getMessage()) : String.format(ErrorMessageConstants.LOGIN_FAILED, exception.getMessage()))
                            .put(ERROR, CommonUtil.formatStackTrace(exception.getStackTrace()));

                    LOGGER.error(exception);

                }
            }

            else
            {
                message = eventType.equalsIgnoreCase(EventBusConstants.UI_ACTION_LDAP_SYNC) ? ErrorMessageConstants.LDAP_SYNC_MISSING_OR_INVALID_LDAP_CONNECTION : ErrorMessageConstants.LOGIN_FAILED_MISSING_OR_INVALID_LDAP_CONNECTION;

                result.put(STATUS, STATUS_FAIL).put(GlobalConstants.ERROR_CODE, ErrorCodes.ERROR_CODE_BAD_REQUEST).put(MESSAGE, message);

                LOGGER.warn(message);

            }
        }

        else
        {
            message = eventType.equalsIgnoreCase(EventBusConstants.UI_ACTION_LDAP_SYNC) ? ErrorMessageConstants.LDAP_SYNC_FQDN_IS_EMPTY_OR_NULL : ErrorMessageConstants.LOGIN_FAILED_FQDN_IS_EMPTY_OR_NULL;

            result.put(STATUS, STATUS_FAIL).put(GlobalConstants.ERROR_CODE, ErrorCodes.ERROR_CODE_BAD_REQUEST).put(MESSAGE, message);

            LOGGER.warn(message);

        }

        return result;

    }

    /**
     * Converts a domain name into an LDAP base DN (Distinguished Name) format.
     * <p>
     * This method transforms a standard domain name (e.g., "example.com") into
     * the LDAP base DN format (e.g., "DC=example,DC=com") required for LDAP operations.
     * Each component of the domain is converted to a DC (Domain Component) attribute.
     *
     * @param domain The domain name to convert
     * @return A StringBuilder containing the formatted base DN
     */
    private static StringBuilder getBaseDN(String domain)
    {
        var builder = new StringBuilder();

        try
        {
            for (var token : domain.split("\\."))
            {
                builder.append("DC=");

                builder.append(token);

                builder.append(",");
            }

            builder.deleteCharAt(builder.length() - 1);
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }

        return builder;
    }

    /**
     * Extracts and formats a mobile phone number from an LDAP telephone attribute.
     * <p>
     * This method processes a telephone number string from LDAP by:
     * 1. Removing all non-digit characters
     * 2. Converting the resulting string to a long integer
     * 3. Handling any exceptions that might occur during conversion
     *
     * @param telephoneNumber The telephone number string from LDAP
     * @return The extracted phone number as a long integer, or 0 if extraction fails
     */
    private static long getMobileNumber(String telephoneNumber)
    {
        var mobileNumber = 0L;

        try
        {
            if (CommonUtil.isNotNullOrEmpty(telephoneNumber))
            {
                mobileNumber = CommonUtil.getLong(telephoneNumber.replaceAll("\\D", EMPTY_VALUE));
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }

        return mobileNumber;
    }

    /**
     * Retrieves the Distinguished Names (DNs) of LDAP groups based on their common names.
     * <p>
     * This method searches the LDAP directory for groups with the specified common names
     * and returns their Distinguished Names, which are needed for further LDAP operations
     * such as finding users who are members of these groups.
     * <p>
     * The method constructs an LDAP search filter that:
     * 1. Looks for objects of class "group"
     * 2. Matches the common names (CN) against the provided list of group names
     *
     * @param connection The established LDAP connection
     * @param groups     The list of group common names to search for
     * @param baseDn     The base DN where to start the search
     * @return A list of Distinguished Names (DNs) for the found groups
     */
    private static List<String> getGroupDNs(LDAPConnection connection, List<String> groups, StringBuilder baseDn)
    {
        final var groupDNs = new ArrayList<String>();

        try
        {
            if (connection != null && !baseDn.isEmpty())
            {
                var filters = new ArrayList<Filter>();

                for (var group : groups)
                {
                    filters.add(Filter.create("(CN=" + group.trim() + ")"));
                }

                var searchFilter = Filter.createANDFilter(Filter.create("(objectClass=group)"), Filter.createORFilter(filters));

                connection.search(new SearchRequest(new SearchResultListener()
                {
                    public void searchEntryReturned(SearchResultEntry searchResultEntry)
                    {
                        groupDNs.add(searchResultEntry.getAttributeValue("distinguishedName"));
                    }

                    public void searchReferenceReturned(SearchResultReference searchResultReference)
                    {
                        //Not required
                    }

                }, baseDn.toString(), SearchScope.SUB, searchFilter));
            }
        }

        catch (Exception exception)
        {
            LOGGER.error(exception);
        }

        return groupDNs;
    }

    /**
     * Converts LDAP exception error codes into human-readable error messages.
     * <p>
     * This method interprets LDAP exception error codes and provides appropriate
     * error messages based on the type of error:
     * - Connection errors (code 91): Returns a message about connection failure
     * - Invalid credentials errors (code 49): Extracts and returns a specific error message
     * from the diagnostic message, if available
     * - Other errors: Returns a generic message with the error code
     *
     * @param ldapException The LDAP exception to process
     * @return A human-readable error message
     */
    private static String getErrorMessage(LDAPException ldapException)
    {
        // Extract the numeric error code from the LDAP exception
        var errorCode = ldapException.toLDAPResult().getResultCode().intValue();

        // Handle connection errors (code 91)
        if (errorCode == CONNECT_ERROR_CODE)
        {
            return CONNECT_ERROR_MESSAGE;
        }
        // Handle invalid credentials errors (code 49)
        else if (errorCode == INVALID_CREDENTIALS_CODE)
        {
            var errorMessage = EMPTY_VALUE;

            // Get the diagnostic message which may contain more specific error information
            var diagnosticMessage = ldapException.toLDAPResult().getDiagnosticMessage();

            // Parse the diagnostic message to extract the specific error code
            // Active Directory often includes detailed error codes in the format "data: <code>, <message>"
            if (CommonUtil.isNotNullOrEmpty(diagnosticMessage) && diagnosticMessage.contains("data"))
            {
                // Extract the part after "data:"
                diagnosticMessage = diagnosticMessage.split("data")[1];

                // Extract the error code (everything before the first comma)
                var code = diagnosticMessage.substring(0, diagnosticMessage.indexOf(','));

                // Look up a human-readable message for this error code
                if (CommonUtil.isNotNullOrEmpty(code))
                {
                    errorMessage = ErrorMessageConstants.getLDAPErrorMessage(code.trim());
                }
            }

            return errorMessage;
        }
        // Handle all other error codes with a generic message
        else
        {
            return String.format("Error code(%s)", errorCode);
        }
    }

    /**
     * Safely closes an LDAP connection.
     * <p>
     * This method ensures that an LDAP connection is properly closed to release resources
     * and prevent connection leaks. It handles null connections and exceptions that might
     * occur during the closing process.
     *
     * @param ldapConnection The LDAP connection to close, can be null
     */
    public static void close(LDAPConnection ldapConnection)
    {
        try
        {
            if (ldapConnection != null)
            {
                ldapConnection.close();
            }
        }

        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }
}
