/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

package com.mindarray.util;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.dataformat.cbor.CBORFactory;
import com.mindarray.GlobalConstants;
import io.vertx.core.json.JsonObject;
import org.xerial.snappy.Snappy;

import java.io.*;

public class CodecUtil
{
    private static final Logger LOGGER = new Logger(CodecUtil.class, GlobalConstants.MOTADATA_UTIL, "Codec Util");

    private static final ObjectMapper objectMapper = new ObjectMapper(new CBORFactory());

    private CodecUtil()
    {
    }

    public static byte[] serialize(Object object)
    {
        byte[] result = null;

        try
        {
            if (object != null)
            {
                result = objectMapper.writeValueAsBytes(object);
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }

        return result;
    }

    public static Object deserialize(byte[] bytes, Class clazz)
    {
        Object result = null;

        try
        {
            if (bytes != null)
            {
                result = objectMapper.readValue(bytes, clazz);
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }

        return result;
    }

    public static byte[] compress(String value)
    {
        byte[] bytes = null;

        try
        {
            bytes = Snappy.compress(value);
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);

            LOGGER.warn(String.format("getting exception for serializing event %s", value));
        }

        return bytes;
    }

    public static byte[] compress(byte[] bytes)
    {
        try
        {
            bytes = Snappy.compress(bytes);
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }

        return bytes;
    }

    public static JsonObject toJSONObject(byte[] bytes, int offset, int length)
    {
        try
        {
            return new JsonObject(Snappy.uncompressString(bytes, offset, length));
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);

            LOGGER.warn("getting exception for serializing event uncompress json");
        }

        return new JsonObject();
    }

    public static JsonObject toJSONObject(byte[] bytes)
    {
        try
        {
            return new JsonObject(Snappy.uncompressString(bytes, "UTF-8"));
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);

            LOGGER.warn("getting exception for serializing event uncompress json");
        }

        return new JsonObject();
    }

    public static String toString(byte[] bytes)
    {
        String message = null;

        try
        {
            message = Snappy.uncompressString(bytes);
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);

            LOGGER.warn("getting exception for serializing event uncompress string");
        }

        return message;
    }

    public static byte[] toBytes(byte[] bytes)
    {
        try
        {
            bytes = Snappy.uncompress(bytes);
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);

            LOGGER.warn("getting exception for serializing event uncompress bytes");
        }

        return bytes;
    }

    public static byte[] toBytes(byte[] bytes, String eventType)
    {
        try
        {
            bytes = Snappy.uncompress(bytes);
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);

            LOGGER.warn(String.format(" %s getting exception for serializing event uncompress bytes", eventType));
        }

        return bytes;
    }

    public static byte[] toBytes(byte[] bytes, int offset, int length)
    {
        byte[] bufferBytes = null;

        try
        {
            bufferBytes = new byte[Snappy.uncompressedLength(bytes, offset, length)];

            Snappy.uncompress(bytes, offset, length, bufferBytes, 0);
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);

            LOGGER.warn("getting exception for serializing event uncompress bytes");
        }

        return bufferBytes;
    }

    public static byte[] toBytes(Object input)
    {
        byte[] bytes = null;

        try (var outputStream = new ByteArrayOutputStream(); ObjectOutput objectOutput = new ObjectOutputStream(outputStream))
        {
            objectOutput.writeObject(input);

            bytes = outputStream.toByteArray();
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }

        return bytes;
    }

    public static Object toObject(byte[] bytes)
    {
        Object result = null;

        try (var objectInput = new ObjectInputStream(new ByteArrayInputStream(bytes)))
        {
            result = objectInput.readObject();
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }

        return result;
    }
}
