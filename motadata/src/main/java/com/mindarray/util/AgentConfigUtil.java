/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

/*
 *	Change Logs:
 *	Date			Author			    Notes
 *  28-Feb-2025		<PERSON><PERSON> Singh		MOTADATA-5642: Agent Cache File Backlog Clearance Mechanism
 */
package com.mindarray.util;

import com.mindarray.GlobalConstants;
import com.mindarray.agent.AgentConstants;
import io.vertx.core.json.JsonObject;

import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;

import static com.mindarray.GlobalConstants.*;

public class AgentConfigUtil
{
    private static final Logger LOGGER = new Logger(AgentConfigUtil.class, GlobalConstants.MOTADATA_UTIL, "Agent Config Util");

    private static final JsonObject agentConfigs = new JsonObject();
    private static final JsonObject childAgentConfigs = new JsonObject();
    private static final Map<String, String> agentHealthThresholds = new HashMap<>();

    public static void loadAgentConfigs(JsonObject configs)
    {
        try
        {
            childAgentConfigs.mergeIn(configs);

            agentConfigs.mergeIn(configs.getJsonObject(AgentConstants.AGENT));

            CommonUtil.setLogLevel(agentConfigs.containsKey(SYSTEM_LOG_LEVEL) ? agentConfigs.getInteger(SYSTEM_LOG_LEVEL) : LOG_LEVEL_INFO);

            var packetAgentThresholds = new Integer[]{getPacketAgentMemoryWarningThresholdMB(), getPacketAgentMemoryCriticalThresholdMB(), getPacketAgentCPUWarningThresholdPercent(), getPacketAgentCPUCriticalThresholdPercent()};

            var logAgentThresholds = new Integer[]{getLogAgentMemoryWarningThresholdMB(), getLogAgentMemoryCriticalThresholdMB(), getLogAgentCPUWarningThresholdPercent(), getLogAgentCPUCriticalThresholdPercent()};

            var metricAgentThresholds = new Integer[]{getMetricAgentMemoryWarningThresholdMB(), getMetricAgentMemoryCriticalThresholdMB(), getMetricAgentCPUWarningThresholdPercent(), getMetricAgentCPUCriticalThresholdPercent()};

            agentHealthThresholds.put(AgentConstants.Agent.PACKET.getName(), String.join(",", Arrays.stream(packetAgentThresholds).map(String::valueOf).toArray(String[]::new)));

            agentHealthThresholds.put(AgentConstants.Agent.LOG.getName(), String.join(",", Arrays.stream(logAgentThresholds).map(String::valueOf).toArray(String[]::new)));

            agentHealthThresholds.put(AgentConstants.Agent.WINDOWS_EVENT_LOG.getName(), agentHealthThresholds.get(AgentConstants.Agent.LOG.getName()));

            agentHealthThresholds.put(AgentConstants.Agent.METRIC.getName(), String.join(",", Arrays.stream(metricAgentThresholds).map(String::valueOf).toArray(String[]::new)));

        }
        catch (Exception exception)
        {
            LOGGER.warn("failed to init agent config, hence aborting motadata boot process....");

            LOGGER.error(exception);
        }
    }

    public static int getAgentLogRetentionDays()
    {
        return agentConfigs.containsKey("agent.log.retention.days") ? agentConfigs.getInteger("agent.log.retention.days") : 2;
    }

    public static long getAgentCacheFlushTimerSeconds()
    {
        return (agentConfigs.containsKey("cache.flush.timer.seconds") ? Math.max(10, Math.min(300, agentConfigs.getInteger("cache.flush.timer.seconds"))) : 30) * 1000L;
    }

    public static long getAgentCacheFileMaxSizeBytes()
    {
        return (agentConfigs.containsKey("cache.file.max.size.mb") ? agentConfigs.getLong("cache.file.max.size.mb") : 100) * 1048576L;
    }

    public static long getAgentHealthInspectionTimerSeconds()
    {
        return agentConfigs.containsKey("agent.health.inspection.timer.seconds") ? Math.max(60, Math.min(300, agentConfigs.getInteger("agent.health.inspection.timer.seconds"))) : 120;
    }

    public static int getAgentHealthWindowCount()
    {
        return agentConfigs.containsKey("agent.health.window.count") ? agentConfigs.getInteger("agent.health.window.count") : 5;
    }

    public static Map<String, String> getAgentHealthThresholds()
    {
        return agentHealthThresholds;
    }

    public static int getMetricAgentMemoryWarningThresholdMB()
    {
        return agentConfigs.containsKey("metric.agent.memory.warning.threshold.mb") ? agentConfigs.getInteger("metric.agent.memory.warning.threshold.mb") : 25;
    }

    public static int getMetricAgentMemoryCriticalThresholdMB()
    {
        return agentConfigs.containsKey("metric.agent.memory.critical.threshold.mb") ? agentConfigs.getInteger("metric.agent.memory.critical.threshold.mb") : 50;
    }

    public static int getLogAgentMemoryWarningThresholdMB()
    {
        return agentConfigs.containsKey("log.agent.memory.warning.threshold.mb") ? agentConfigs.getInteger("log.agent.memory.warning.threshold.mb") : 50;
    }

    public static int getLogAgentMemoryCriticalThresholdMB()
    {
        return agentConfigs.containsKey("log.agent.memory.critical.threshold.mb") ? agentConfigs.getInteger("log.agent.memory.critical.threshold.mb") : 100;
    }

    public static int getPacketAgentMemoryWarningThresholdMB()
    {
        return agentConfigs.containsKey("packet.agent.memory.warning.threshold.mb") ? agentConfigs.getInteger("packet.agent.memory.warning.threshold.mb") : 50;
    }

    public static int getPacketAgentMemoryCriticalThresholdMB()
    {
        return agentConfigs.containsKey("packet.agent.memory.critical.threshold.mb") ? agentConfigs.getInteger("packet.agent.memory.critical.threshold.mb") : 100;
    }

    public static int getMetricAgentCPUWarningThresholdPercent()
    {
        return agentConfigs.containsKey("metric.agent.cpu.warning.threshold.percent") ? agentConfigs.getInteger("metric.agent.cpu.warning.threshold.percent") : 10;
    }

    public static int getMetricAgentCPUCriticalThresholdPercent()
    {
        return agentConfigs.containsKey("metric.agent.cpu.critical.threshold.percent") ? agentConfigs.getInteger("metric.agent.cpu.critical.threshold.percent") : 25;
    }

    public static int getLogAgentCPUWarningThresholdPercent()
    {
        return agentConfigs.containsKey("log.agent.cpu.warning.threshold.percent") ? agentConfigs.getInteger("log.agent.cpu.warning.threshold.percent") : 50;
    }

    public static int getLogAgentCPUCriticalThresholdPercent()
    {
        return agentConfigs.containsKey("log.agent.cpu.critical.threshold.percent") ? agentConfigs.getInteger("log.agent.cpu.critical.threshold.percent") : 75;
    }

    public static int getPacketAgentCPUWarningThresholdPercent()
    {
        return agentConfigs.containsKey("packet.agent.cpu.warning.threshold.percent") ? agentConfigs.getInteger("packet.agent.cpu.warning.threshold.percent") : 25;
    }

    public static int getPacketAgentCPUCriticalThresholdPercent()
    {
        return agentConfigs.containsKey("packet.agent.cpu.critical.threshold.percent") ? agentConfigs.getInteger("packet.agent.cpu.critical.threshold.percent") : 50;
    }

    public static int getEventPublisherPort()
    {
        return agentConfigs.getInteger(EVENT_PUBLISHER_PORT, 9449);
    }

    public static int getEventSubscriberPort()
    {
        return agentConfigs.getInteger(EVENT_SUBSCRIBER_PORT, 9444);
    }

    public static String getRemoteEventPublisher()
    {
        return agentConfigs.getString(EVENT_PUBLISHER_HOST, "127.0.0.1");
    }

    public static String getRemoteEventSubscriber()
    {
        return agentConfigs.getString(EVENT_SUBSCRIBER_HOST, "127.0.0.1");
    }

    public static int getAgentMaxEventBacklogQueueSize()
    {
        return agentConfigs.containsKey("agent.max.event.backlog.queue.size") ? Math.max(5000, Math.min(agentConfigs.getInteger("agent.max.event.backlog.queue.size"), 200000)) : 100000;
    }

    public static int getAgentHTTPServerPort()
    {
        return agentConfigs.containsKey(HTTP_SERVER_PORT) ? agentConfigs.getInteger(HTTP_SERVER_PORT) : 443;
    }

    public static String getAgentEnableStatus(String status)
    {
        return CommonUtil.isNotNullOrEmpty(agentConfigs.getString(status)) ? agentConfigs.getString(status) : NO;
    }

    public static JsonObject getAgentConfigs()
    {
        return agentConfigs;
    }

    public static int getProcessDetectionAttempts()
    {
        return CommonUtil.getInteger(agentConfigs.getValue("agent.process.detection.attempts", 30));
    }

    public static JsonObject getChildAgentConfigs()
    {
        return childAgentConfigs;
    }

    public static int getShutdownHookTimeoutSeconds()
    {
        return agentConfigs.getInteger("shutdown.hook.timeout.seconds", 30);
    }

    public static int getAgentPingTimerSeconds()
    {
        return agentConfigs.containsKey("agent.ping.timer.seconds") ? agentConfigs.getInteger("agent.ping.timer.seconds") : 60;
    }

    /*
     * This method will be used to derive the number of file to be read at a time
     * e.x if total number of files in the cache directory is 1000 then we will read 1000/10 i.e. 100 files at a time and send it to the master/collector
     */
    public static int getAgentCacheFileChunks()
    {
        return agentConfigs.containsKey("cache.file.chunks") ? agentConfigs.getInteger("cache.file.chunks") : 10;
    }

    /*
     * This method will be used to decide whether to run the chunk based file reading or not
     * If cache directory size is less than 50 MB then we will send all the files at a time or else we will send chunks of files to master/collector
     */
    public static long getAgentCacheDirectoryMaxSizeBytes()
    {
        return (agentConfigs.containsKey("cache.dir.max.size.mb") ? agentConfigs.getInteger("cache.dir.max.size.mb") : 50) * 1048576;
    }
}
