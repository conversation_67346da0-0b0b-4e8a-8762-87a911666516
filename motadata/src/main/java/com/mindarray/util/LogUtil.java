/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

/*
	Change Logs:
	Date			Author			    Notes
	26-Feb-2025		Darshan Parmar		MOTADATA-5215: SonarQube Suggestions Resolution
*/
package com.mindarray.util;

import com.mindarray.Bootstrap;
import com.mindarray.GlobalConstants;
import com.mindarray.eventbus.EventBusConstants;
import io.vertx.core.buffer.Buffer;
import io.vertx.core.json.JsonObject;
import org.apache.commons.io.FileUtils;
import org.joda.time.DateTime;
import org.zeroturnaround.zip.ZipUtil;

import java.io.File;
import java.text.SimpleDateFormat;
import java.util.*;

import static com.mindarray.GlobalConstants.*;

public class LogUtil
{
    private static final Logger LOGGER = new Logger(LogUtil.class, GlobalConstants.MOTADATA_UTIL, "Log Util");

    private LogUtil()
    {
    }

    public static void runLogRetention(int retentionDays)
    {
        try
        {
            LOGGER.info("log retention job started.....");

            var qualifiedLogFiles = qualifyLogFiles(retentionDays, false);

            for (var logFiles : qualifiedLogFiles.entrySet())
            {
                for (var logFile : logFiles.getValue())
                {
                    LOGGER.info(String.format("start deleting log files %s ", logFile.toString()));

                    FileUtils.deleteQuietly(logFile);
                }

                if (logFiles.getKey().listFiles() == null || Objects.requireNonNull(logFiles.getKey().listFiles()).length == 0)
                {
                    LOGGER.info(String.format("deleting log folder %s ", logFiles.getKey()));

                    FileUtils.deleteDirectory(logFiles.getKey());
                }
            }

            LOGGER.info("log retention job completed....");
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }

    public static Map<File, List<File>> qualifyLogFiles(int retentionDay, boolean anyMatch)
    {
        var qualifiedFiles = new HashMap<File, List<File>>();

        try
        {
            if (retentionDay > -1)
            {
                var calendar = Calendar.getInstance();

                calendar.set(Calendar.DATE, calendar.get(Calendar.DATE) - retentionDay);

                calendar.set(Calendar.HOUR_OF_DAY, 0);

                calendar.set(Calendar.MINUTE, 0);

                calendar.set(Calendar.SECOND, 0);

                calendar.set(Calendar.MILLISECOND, 0);

                var qualifiedDates = new TreeSet<String>();

                var formatter = new SimpleDateFormat(GlobalConstants.LOG_RETENTION_DATE_FORMAT);

                var endTime = new DateTime().toDate().getTime();

                var curTime = new DateTime(calendar.getTime()).toDate().getTime();

                while (curTime <= endTime)
                {
                    qualifiedDates.add(formatter.format(new Date(curTime)));

                    curTime += 86400000; // 1 day in millis
                }

                var dates = qualifiedDates.toArray(new String[0]);

                if (dates.length > 0)
                {
                    var logFolder = new File(GlobalConstants.CURRENT_DIR + GlobalConstants.PATH_SEPARATOR + Logger.LOG_DIRECTORY);

                    if (logFolder.exists() && logFolder.listFiles() != null && Objects.requireNonNull(logFolder.listFiles()).length > 0)
                    {
                        qualify(dates, qualifiedFiles, logFolder, anyMatch);
                    }
                }
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }

        return qualifiedFiles;
    }

    private static void qualify(String[] qualifiedDates, Map<File, List<File>> qualifiedFiles, File logDir, boolean anyMatch)
    {
        try
        {
            if (logDir.listFiles() != null && Objects.requireNonNull(logDir.listFiles()).length > 0)
            {
                Arrays.asList(Objects.requireNonNull(logDir.listFiles())).iterator().forEachRemaining(logFile ->
                {
                    if (logFile.isDirectory())
                    {
                        qualify(qualifiedDates, qualifiedFiles, logFile, anyMatch);
                    }
                    else if ((anyMatch && Arrays.stream(qualifiedDates).anyMatch(logFile.getName().trim()::startsWith)) || (!anyMatch && Arrays.stream(qualifiedDates).noneMatch(logFile.getName().trim()::startsWith)))
                    {
                        if (!qualifiedFiles.containsKey(logDir))
                        {
                            qualifiedFiles.put(logDir, new ArrayList<>());
                        }

                        qualifiedFiles.get(logDir).add(logFile);
                    }
                });
            }

        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }

    public static void zipLogFiles(JsonObject event)
    {
        var path = GlobalConstants.CURRENT_DIR + GlobalConstants.PATH_SEPARATOR + "agent-logs";

        var file = new File(path);

        try
        {
            var qualifiedLogFiles = qualifyLogFiles(2, true);

            if (!qualifiedLogFiles.isEmpty())
            {
                file.mkdir();

                for (var logFiles : qualifiedLogFiles.values())
                {
                    for (var logFile : logFiles)
                    {
                        //24774
                        var tokens = logFile.getAbsolutePath().split(GlobalConstants.PATH_SEPARATOR.equalsIgnoreCase("\\") ? "\\\\" : GlobalConstants.PATH_SEPARATOR);

                        if (!tokens[tokens.length - 2].equalsIgnoreCase("logs"))
                        {
                            new File(path + GlobalConstants.PATH_SEPARATOR + tokens[tokens.length - 2]).mkdir();
                        }

                        var clonedLogFile = new File(logFile.getAbsolutePath().replace("logs", "agent-logs"));

                        if (clonedLogFile.createNewFile())
                        {
                            FileUtils.copyFile(logFile, clonedLogFile);
                        }
                    }
                }
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
        finally
        {
            try
            {
                if (new File(path).exists())
                {
                    ZipUtil.pack(file, new File(path + ".zip"), true);

                    Bootstrap.vertx().eventBus().send(EventBusConstants.EVENT_AGENT_UPLOAD_LOG, event);
                }

                FileUtils.deleteDirectory(file);

                LOGGER.info("log compression done....");
            }
            catch (Exception ignored)
            {
                //Ignored
            }
        }
    }

    public static void setLogLevel(int logLevel)
    {
        if (MotadataConfigUtil.getSystemBootstrapType().equalsIgnoreCase(BootstrapType.AGENT.name()))   // in case of agent... we need to overwrite updated log level in agent.json
        {
            if (Bootstrap.vertx().fileSystem().existsBlocking(CURRENT_DIR + PATH_SEPARATOR + CONFIG_DIR + PATH_SEPARATOR + "agent.json") && Bootstrap.vertx().fileSystem().readFileBlocking(CURRENT_DIR + PATH_SEPARATOR + CONFIG_DIR + PATH_SEPARATOR + "agent.json").length() > 0)
            {
                var configs = Bootstrap.vertx().fileSystem().readFileBlocking(CURRENT_DIR + PATH_SEPARATOR + CONFIG_DIR + PATH_SEPARATOR + "agent.json").toJsonObject();

                for (var key : configs.getMap().keySet())
                {
                    configs.getJsonObject(key).put(SYSTEM_LOG_LEVEL, logLevel);
                }

                Bootstrap.vertx().fileSystem().writeFileBlocking(CURRENT_DIR + PATH_SEPARATOR + CONFIG_DIR + PATH_SEPARATOR + "agent.json", Buffer.buffer(configs.encodePrettily()));

                CommonUtil.setLogLevel(logLevel);
            }
        }
        else
        {
            if (Bootstrap.vertx().fileSystem().existsBlocking(CURRENT_DIR + PATH_SEPARATOR + CONFIG_DIR + PATH_SEPARATOR + "motadata.json") && Bootstrap.vertx().fileSystem().readFileBlocking(CURRENT_DIR + PATH_SEPARATOR + CONFIG_DIR + PATH_SEPARATOR + "motadata.json").length() > 0)
            {
                var configs = Bootstrap.vertx().fileSystem().readFileBlocking(CURRENT_DIR + PATH_SEPARATOR + CONFIG_DIR + PATH_SEPARATOR + "motadata.json").toJsonObject();

                if (configs.containsKey(SYSTEM_LOG_LEVEL))
                {
                    configs.put(SYSTEM_LOG_LEVEL, logLevel);

                    Bootstrap.vertx().fileSystem().writeFileBlocking(CURRENT_DIR + PATH_SEPARATOR + CONFIG_DIR + PATH_SEPARATOR + "motadata.json", Buffer.buffer(configs.encodePrettily()));

                    CommonUtil.setLogLevel(logLevel);
                }
            }
        }
    }

    public static void resetLogLevel(int seconds)
    {
        if (CommonUtil.getLogLevel() < GlobalConstants.LOG_LEVEL_INFO)    // if not INFO
        {
            Bootstrap.vertx().setTimer(seconds * 1000L, timer ->
            {
                LOGGER.info(String.format("Changing log-level: %s to %s", GlobalConstants.LOG_LEVELS.get(CommonUtil.getLogLevel()), GlobalConstants.LOG_LEVELS.get(GlobalConstants.LOG_LEVEL_INFO)));

                setLogLevel(GlobalConstants.LOG_LEVEL_INFO);
            });
        }
    }
}
