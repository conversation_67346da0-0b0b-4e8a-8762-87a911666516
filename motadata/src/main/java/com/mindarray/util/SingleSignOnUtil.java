/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

/*
 *     Change Logs:
 *     Date            Author          Notes
 *     25-Mar-2025     Arpit           MOTADATA-5549: VAPT, unsafe-inline execution removed
 */
package com.mindarray.util;

import com.coveo.saml.SamlClient;
import com.google.common.base.Strings;
import com.mindarray.Bootstrap;
import com.mindarray.GlobalConstants;
import com.mindarray.api.SingleSignOn;
import com.mindarray.store.SingleSignOnConfigStore;
import io.vertx.core.buffer.Buffer;
import io.vertx.ext.web.RoutingContext;
import org.apache.commons.codec.binary.Base64;
import org.w3c.dom.Node;

import javax.xml.parsers.DocumentBuilderFactory;
import java.io.*;
import java.security.cert.CertificateFactory;
import java.security.cert.X509Certificate;
import java.util.Objects;
import java.util.zip.Inflater;
import java.util.zip.InflaterInputStream;

import static com.mindarray.GlobalConstants.COLON_SEPARATOR;
import static com.mindarray.GlobalConstants.RESULT;


public class SingleSignOnUtil
{

    // SAML Constants
    public static final String REDIRECT_TEMPLATE = "<html><head></head><body><form id='TheForm' action='%s' method='POST'>\n<input type='hidden' id='%s' name='%s' value='%s'/></form><script src='/SingleSignOn.js'></script></body></html>";

    public static final String SAML_REQUEST = "SAMLRequest";

    public static final String SAML_RESPONSE = "SAMLResponse";

    public static final String SAML_IDENTITY_PROVIDER_LOGOUT_URL = "saml.identity.provider.logout.url";

    public static final String NAME_ID = "NameID";

    public static final String SINGLE_LOGOUT_SERVICE = "SingleLogoutService";

    public static final String INVALID_RESPONSE = "Invalid SSO Configuration";


    private static final Logger LOGGER = new Logger(SingleSignOnUtil.class, GlobalConstants.MOTADATA_UTIL, "Single Sign On Util");

    private static SamlClient samlClient;

    private static String logoutURL;

    private SingleSignOnUtil()
    {

    }

    /**
     * This method will extract user for which logout request is received.
     * This is required due to issue : https://github.com/justinbleach/saml-client/issues/68
     *
     * @return userName
     */
    public static String getUserName(RoutingContext request)
    {
        String result = null;

        InputStream requestStream;

        try
        {
            requestStream =
                    new ByteArrayInputStream(Base64.decodeBase64(request.request().getParam(SAML_REQUEST)));

            if ("GET".equals(request.request().method().name()))
            {
                // If the request was a GET request, the value will have been deflated
                requestStream = new InflaterInputStream(requestStream, new Inflater(true));
            }

            result = Objects.requireNonNull(getNode(requestStream, NAME_ID)).getTextContent();

        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }

        return result;
    }

    public static String getLogoutURL()
    {
        return logoutURL;
    }

    private static Node getNode(InputStream inputStream, String nodeName)
    {
        Node node = null;

        try
        {
            var factory = DocumentBuilderFactory.newDefaultNSInstance();

            var builder = factory.newDocumentBuilder();

            var document = builder.parse(inputStream);

            // Extract the NameID from the LogoutRequest
            node = document.getElementsByTagNameNS("*", nodeName).item(0);
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }

        return node;
    }

    /**
     * This method is used to retrieve the logout url
     *
     * @param stream
     * @return
     */
    public static String getLogoutURL(InputStream stream)
    {
        String logoutURL = null;

        try
        {
            // Extract the IDPLogout location from the LogoutRequest
            logoutURL = Objects.requireNonNull(getNode(stream, SINGLE_LOGOUT_SERVICE)).getAttributes().getNamedItem("Location").getTextContent();

        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
        return logoutURL;
    }

    public static SamlClient getSAMLClient()
    {
        return samlClient;
    }

    public static void init()
    {
        Reader metadata = null;

        try
        {
            var configs = SingleSignOnConfigStore.getStore().getItem().getJsonObject(SingleSignOn.SAML_AUTHENTICATION_CONTEXT);

            if (SingleSignOnConfigStore.getStore().getItem() != null)
            {
                if (configs.getBoolean(SingleSignOn.SAML_FILE_BASED_AUTHENTICATION, false))
                {
                    metadata = new FileReader(configs.getString(SingleSignOn.SAML_CONFIGURATION_FILE));

                    samlClient = SamlClient.fromMetadata(configs.getString(SingleSignOn.SAML_SERVICE_PROVIDER_ENTITY_ID),
                            getServerURL() + "/api/v1/sso/callback",
                            metadata);

                    logoutURL = getLogoutURL(new FileInputStream(configs.getString(SingleSignOn.SAML_CONFIGURATION_FILE)));
                }
                else
                {
                    samlClient = new SamlClient(configs.getString(SingleSignOn.SAML_SERVICE_PROVIDER_ENTITY_ID),
                            getServerURL() + "/api/v1/sso/callback",
                            configs.getString(SingleSignOn.SAML_IDENTITY_PROVIDER_LOGIN_URL),
                            configs.getString(SingleSignOn.SAML_IDENTITY_PROVIDER_ENTITY_ID),
                            getCertificate());

                    logoutURL = SingleSignOnConfigStore.getStore().getItem().getJsonObject(SingleSignOn.SAML_AUTHENTICATION_CONTEXT)
                            .getString(SAML_IDENTITY_PROVIDER_LOGOUT_URL);
                }
            }
            else
            {
                LOGGER.warn("SSO Not configured");
            }

        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
        finally
        {
            try
            {
                if (metadata != null)
                {
                    metadata.close();
                }
            }
            catch (Exception ignored)
            {

            }
        }
    }


    private static X509Certificate getCertificate()
    {
        X509Certificate certificate = null;

        try
        {
            Buffer buffer;

            if (SingleSignOnConfigStore.getStore().getItem().getJsonObject(SingleSignOn.SAML_AUTHENTICATION_CONTEXT).containsKey(SingleSignOn.SAML_IDENTITY_PROVIDER_CERTIFICATE))
            {

                buffer = Bootstrap.vertx().fileSystem().readFileBlocking(GlobalConstants.CURRENT_DIR + GlobalConstants.PATH_SEPARATOR + GlobalConstants.UPLOADS +
                        GlobalConstants.PATH_SEPARATOR + SingleSignOnConfigStore.getStore().getItem().getJsonObject(SingleSignOn.SAML_AUTHENTICATION_CONTEXT).
                        getJsonArray(SingleSignOn.SAML_IDENTITY_PROVIDER_CERTIFICATE).getJsonObject(0).getString(RESULT));

            }
            else
            {
                buffer = Buffer.buffer(SingleSignOnConfigStore.getStore().getItem().getJsonObject(SingleSignOn.SAML_AUTHENTICATION_CONTEXT).getString(SingleSignOn.SAML_IDENTITY_PROVIDER_FINGERPRINT));
            }

            var factory = CertificateFactory.getInstance("X.509");

            certificate = (X509Certificate) factory.generateCertificate(new ByteArrayInputStream(buffer.getBytes()));

        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }

        return certificate;
    }

    public static String getServerURL()
    {
        var serverURL = new StringBuilder(0);

        try
        {
            serverURL.append(MotadataConfigUtil.httpsEnabled() ? "https" : "http").append("://").append(!Strings.isNullOrEmpty(MotadataConfigUtil.getVIPIPAddress()) ?
                    MotadataConfigUtil.getVIPIPAddress() : MotadataConfigUtil.getHost()).append((
                    MotadataConfigUtil.httpsEnabled() && MotadataConfigUtil.getHTTPServerPort(GlobalConstants.BootstrapType.APP.name()) == 443) ||
                    (!MotadataConfigUtil.httpsEnabled() && MotadataConfigUtil.getHTTPServerPort(GlobalConstants.BootstrapType.APP.name()) == 80) ? "" :
                    COLON_SEPARATOR + MotadataConfigUtil.getHTTPServerPort(GlobalConstants.BootstrapType.APP.name()));
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }

        return serverURL.toString();
    }
}
