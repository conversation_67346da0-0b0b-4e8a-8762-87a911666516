/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

package com.mindarray.util;

import com.mindarray.*;
import com.mindarray.api.ProxyServer;
import com.mindarray.eventbus.EventBusConstants;
import com.mindarray.store.ProxyServerConfigStore;
import io.vertx.circuitbreaker.CircuitBreaker;
import io.vertx.circuitbreaker.CircuitBreakerOptions;
import io.vertx.core.AbstractVerticle;
import io.vertx.core.Promise;
import io.vertx.core.buffer.Buffer;
import io.vertx.core.json.JsonObject;
import io.vertx.core.net.ProxyOptions;
import io.vertx.core.net.ProxyType;
import io.vertx.ext.auth.authentication.UsernamePasswordCredentials;

import java.util.Locale;

import static com.mindarray.GlobalConstants.*;
import static com.mindarray.eventbus.EventBusConstants.CHANGE_NOTIFICATION_TYPE;

public class ProxyServiceUtil extends AbstractVerticle
{
    private static final Logger LOGGER = new Logger(ProxyServiceUtil.class, GlobalConstants.MOTADATA_UTIL, "Proxy Service Util");
    private static final String PROXY_SERVICE = "proxy.service";
    private final CircuitBreaker circuitBreaker = CircuitBreaker.create("proxy-service-circuit-breaker", Bootstrap.vertx(),
            new CircuitBreakerOptions()
                    .setMaxFailures(5) // number of failure before opening the circuit
                    .setTimeout(30000) // consider a failure if the operation does not succeed in time
                    .setFallbackOnFailure(true)// do we call the fallback on failure
                    .setResetTimeout(60000) // time spent in open state before attempting to re-try
    ).openHandler(handler ->
    {

        EventBusConstants.publish(EventBusConstants.UI_NOTIFICATION_PROXY_SERVICE_HEALTH, new JsonObject().put(STATUS, STATUS_FAIL)
                .put(MESSAGE, ErrorMessageConstants.PROXY_SERVICE_FAILED).put(ERROR_CODE, ErrorCodes.ERROR_CODE_PROXY_SERVER_DOWN));

        LOGGER.warn(ErrorMessageConstants.PROXY_SERVICE_FAILED);

        Bootstrap.vertx().eventBus().send(EventBusConstants.EVENT_USER_NOTIFICATION, new JsonObject().put(STATUS, STATUS_FAIL).put(EventBusConstants.EVENT_TYPE, PROXY_SERVICE).put(MESSAGE, ErrorMessageConstants.PROXY_SERVICE_FAILED));

    }).closeHandler(handler ->
    {

        EventBusConstants.publish(EventBusConstants.UI_NOTIFICATION_PROXY_SERVICE_HEALTH, new JsonObject().put(STATUS, STATUS_SUCCEED)
                .put(MESSAGE, InfoMessageConstants.PROXY_SERVICE_RESTORED).put(ERROR_CODE, ErrorCodes.ERROR_CODE_SUCCESS));

        LOGGER.info(InfoMessageConstants.PROXY_SERVICE_RESTORED);

        Bootstrap.vertx().eventBus().send(EventBusConstants.EVENT_USER_NOTIFICATION, new JsonObject().put(STATUS, STATUS_SUCCEED).put(EventBusConstants.EVENT_TYPE, PROXY_SERVICE).put(MESSAGE, InfoMessageConstants.PROXY_SERVICE_RESTORED));

    }).fallback(handler -> handler.initCause(new Exception(ErrorMessageConstants.PROXY_SERVICE_FAILED)));
    private int timeout = 60 * 1000; // 60 sec

    @Override
    public void start(Promise<Void> promise) throws Exception
    {
        vertx.eventBus().<JsonObject>localConsumer(EventBusConstants.EVENT_CHANGE_NOTIFICATION, message ->
        {
            var event = message.body();

            if (EventBusConstants.ChangeNotificationType.valueOf(event.getString(CHANGE_NOTIFICATION_TYPE)) == EventBusConstants.ChangeNotificationType.PROXY_SERVER)
            {
                WebClientUtil.closeProxy();

                init();
            }
            else if (EventBusConstants.ChangeNotificationType.valueOf(event.getString(CHANGE_NOTIFICATION_TYPE)) == EventBusConstants.ChangeNotificationType.DISABLE_PROXY_SERVER)
            {
                WebClientUtil.closeProxy();
            }

        }).exceptionHandler(LOGGER::error);

        init();

        vertx.eventBus().<JsonObject>localConsumer(EventBusConstants.EVENT_PROXY_SERVICE_REQUEST, message ->
        {
            var event = message.body();

            try
            {
                circuitBreaker.<Buffer>execute(future ->
                        {
                            if (WebClientUtil.getProxyWebClient() != null)
                            {
                                var request = WebClientUtil.getProxyWebClient()
                                        .getAbs(event.getString(URI))
                                        .timeout(this.timeout);

                                if (event.containsKey(USERNAME) && event.containsKey(PASSWORD))
                                {
                                    request.authentication(new UsernamePasswordCredentials(event.getString(USERNAME), event.getString(PASSWORD)));
                                }

                                request.send(result ->
                                {
                                    if (result.succeeded())
                                    {
                                        if (result.result().body() == null || result.result().body().length() == 0)
                                        {
                                            LOGGER.warn("No data received from proxy server");
                                        }

                                        future.complete(result.result().body());
                                    }
                                    else
                                    {
                                        LOGGER.error(result.cause());

                                        future.complete(null); //hack: future.fail(result.cause()); doesn't work for Circuit Breaker
                                    }
                                });
                            }
                            else
                            {
                                future.fail(new RuntimeException("Proxy client is not initialized"));
                            }
                        })
                        .onComplete(result ->
                        {
                            if (result.result() != null)
                            {
                                message.reply(event.put(STATUS, STATUS_SUCCEED).put(RESULT, result.result()));
                            }
                            else
                            {
                                message.fail(NOT_AVAILABLE, "Proxy server error");
                            }
                        });
            }
            catch (Exception exception)
            {
                LOGGER.error(exception);

                message.fail(NOT_AVAILABLE, exception.getMessage());
            }
        }).exceptionHandler(LOGGER::error);

        promise.complete();
    }

    private void init()
    {
        var item = ProxyServerConfigStore.getStore().getItem();

        if (item != null)
        {
            var proxyOptions = new ProxyOptions().setHost(item.getString(ProxyServer.PROXY_SERVER_HOST)).setPort(item.getInteger(ProxyServer.PROXY_SERVER_PORT));

            if (item.getString(ProxyServer.PROXY_SERVER_USERNAME) != null && item.getString(ProxyServer.PROXY_SERVER_PASSWORD) != null)
            {
                proxyOptions.setUsername(item.getString(ProxyServer.PROXY_SERVER_USERNAME)).setPassword(item.getString(ProxyServer.PROXY_SERVER_PASSWORD));
            }

            if (item.getString(ProxyServer.PROXY_SERVER_TYPE) != null)
            {
                try
                {
                    proxyOptions.setType(ProxyType.valueOf(item.getString(ProxyServer.PROXY_SERVER_TYPE).toUpperCase(Locale.ROOT)));
                }
                catch (Exception exception)
                {
                    LOGGER.error(exception);
                }
            }

            if (item.getInteger(ProxyServer.PROXY_SERVER_TIME_OUT) != null)
            {
                timeout = item.getInteger(ProxyServer.PROXY_SERVER_TIME_OUT) * 1000;
            }

            WebClientUtil.init(proxyOptions);
        }
    }
}
