package com.mindarray.slo;

import com.mindarray.Bootstrap;
import com.mindarray.aiops.AIOpsConstants;
import com.mindarray.api.AIOpsObject;
import com.mindarray.api.APIConstants;
import com.mindarray.api.SLOProfile;
import com.mindarray.api.Tag;
import com.mindarray.datastore.DatastoreConstants;
import com.mindarray.db.DBConstants;
import com.mindarray.eventbus.EventBusConstants;
import com.mindarray.ha.HAConstants;
import com.mindarray.nms.NMSConstants;
import com.mindarray.policy.PolicyEngineConstants;
import com.mindarray.store.MetricConfigStore;
import com.mindarray.store.ObjectConfigStore;
import com.mindarray.store.SLOProfileConfigStore;
import com.mindarray.util.*;
import io.vertx.core.AbstractVerticle;
import io.vertx.core.Promise;
import io.vertx.core.buffer.Buffer;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;

import java.io.File;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;

import static com.mindarray.GlobalConstants.*;
import static com.mindarray.eventbus.EventBusConstants.*;

public class SLOManager extends AbstractVerticle
{
    private static final Logger LOGGER = new Logger(SLOManager.class, MOTADATA_SLO, "SLO Manager");

    private static final Map<Long, Map<String, JsonObject>> sloDetails = new HashMap<>();

    private boolean updated = false;

    private boolean dirty = false;

    @Override
    public void start(Promise<Void> promise)
    {
        try
        {
            load();

            var refreshTimer = new AtomicInteger(MotadataConfigUtil.getSLOCacheFlushTimerSeconds());

            // flush the cache
            vertx.setPeriodic(30 * 1000L, timer ->
            {
                refreshTimer.addAndGet(-30);

                if (updated)
                {
                    dump();

                    dirty = true;

                    updated = false;
                }
                else if (refreshTimer.get() <= 0)
                {
                    dump();

                    dirty = true;

                    refreshTimer.set(MotadataConfigUtil.getSLOCacheFlushTimerSeconds());
                }
            });

            vertx.eventBus().localConsumer(EventBusConstants.EVENT_SLO_QUALIFY, message -> qualify());

            vertx.eventBus().<JsonObject>localConsumer(EventBusConstants.EVENT_SLO_UPDATE, message -> process(message.body()));

            vertx.eventBus().<JsonObject>localConsumer(EVENT_CHANGE_NOTIFICATION, message ->
            {
                try
                {
                    if (message.body().getString(CHANGE_NOTIFICATION_TYPE, EMPTY_VALUE).equalsIgnoreCase(EventBusConstants.ChangeNotificationType.UPDATE_CACHE.name()))
                    {
                        if (dirty)
                        {
                            dirty = false;

                            HAConstants.notifyObserver(new JsonObject().put(HAConstants.CACHE_NAME, SLOConstants.SLO_CACHE_FILE).put(RESULT, vertx.fileSystem().readFileBlocking(CURRENT_DIR + PATH_SEPARATOR + CONFIG_DIR + PATH_SEPARATOR + SLOConstants.SLO_CACHE_FILE)));
                        }
                    }
                }
                catch (Exception exception)
                {
                    LOGGER.error(exception);
                }
            });

            // update data for entire SLO profile
            vertx.setPeriodic(TimeUnit.MINUTES.toMillis(1), timer->
            {
                for (var sloDetail : sloDetails.entrySet())
                {
                    var sloId = sloDetail.getKey();

                    var status = sloDetail.getValue().entrySet().stream()
                            .filter(entry -> !entry.getKey().isEmpty())
                            .map(entry -> getLastSLOStatus(entry.getValue()))
                            .allMatch(sloStatus -> sloStatus == null || sloStatus == SLOConstants.SLOStatus.OK);

                    updateSLO(sloDetails.get(sloId).get(EMPTY_VALUE), status ? SLOConstants.SLOStatus.OK : SLOConstants.SLOStatus.BREACHED, DateTimeUtil.currentSeconds());
                }
            });

            vertx.setPeriodic(TimeUnit.MINUTES.toMillis(1), timer->
            {
                for (var entry : sloDetails.entrySet())
                {
                    try
                    {
                        var timestamp = DateTimeUtil.currentSeconds();

                        var sloId = entry.getKey();

                        var item = SLOProfileConfigStore.getStore().getItem(sloId);

                        var context = item.getJsonObject(SLOProfile.SLO_CONTEXT);

                        // monitor wise details
                        var monitors = entry.getValue();

                        for (var details : monitors.entrySet())
                        {
                            // empty value key will be for overall slo profile
                            if (!details.getKey().equalsIgnoreCase(EMPTY_VALUE))
                            {
                                var tokens = details.getKey().split(INSTANCE_SEPARATOR);

                                var detail = details.getValue();

                                var data = new JsonObject()
                                        .put("slo.instance~achieved.percent", getSLOAchievedPercentage(detail))
                                        .put("slo.instance~error.budget.left.percent", getErrorBudgetLeft(detail))
                                        .put("slo.instance~violated.percent", getSLOViolatedPercentage(detail))
                                        .put("slo.instance~" + NMSConstants.INSTANCE_NAME, item.getString(SLOConstants.SLO_CYCLE_ID))
                                        .put("slo.instance~status", getSLOStatus(detail, context.getInteger(SLOProfile.SLO_TARGET), context.getInteger(SLOProfile.SLO_WARNING)))
                                        .put("slo.instance~burn.rate.percent", getSLOBurnRate(detail))
                                        .put("slo.instance~object.id", ObjectConfigStore.getStore().getObjectIdById(CommonUtil.getLong(tokens[0])));

                                if (tokens.length > 1)
                                {
                                    data.put("slo~instance", tokens[1]).put("slo~instance.type", context.getString(PolicyEngineConstants.INSTANCE_TYPE));
                                }

                                write(sloId, item.getString(SLOConstants.SLO_CYCLE_ID), timestamp, DatastoreConstants.PluginId.SLO_INSTANCE_METRIC, data);
                            }
                        }

                        // entire slo profile wise details
                        var detail = sloDetails.get(sloId).get(EMPTY_VALUE);

                        write(sloId, item.getString(SLOConstants.SLO_CYCLE_ID), timestamp, DatastoreConstants.PluginId.SLO_METRIC, new JsonObject()
                                .put("slo~achieved.percent", getSLOAchievedPercentage(detail))
                                .put("slo~error.budget.left.percent", getErrorBudgetLeft(detail))
                                .put("slo~violated.percent", getSLOViolatedPercentage(detail))
                                .put("slo~burn.rate.percent", getSLOBurnRate(detail))
                                .put("slo~" + NMSConstants.INSTANCE_NAME, item.getString(SLOConstants.SLO_CYCLE_ID))
                                .put("slo~status", getSLOStatus(detail, context.getInteger(SLOProfile.SLO_TARGET), context.getInteger(SLOProfile.SLO_WARNING))));
                    }
                    catch (Exception exception)
                    {
                        LOGGER.error(exception);
                    }
                }
            });

            promise.complete();
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);

            promise.fail(exception);
        }
    }

    private void qualify()
    {
        try
        {
            var currentTime = DateTimeUtil.currentSeconds();

            var items = SLOProfileConfigStore.getStore().getItems();

            for (var index = 0; index < items.size(); index++)
            {
                var item = items.getJsonObject(index);

                // do not consider archived slo profiles
                if (!item.containsKey(SLOProfile.SLO_ARCHIVED) || item.getString(SLOProfile.SLO_ARCHIVED).equalsIgnoreCase(NO))
                {
                    // means the SLO is already started, now we've to determine that weather it's time to restart the cycle or not!
                    if (item.containsKey(SLOProfile.SLO_LAST_ACTIVE_CYCLE_START_DATE))
                    {
                        var context = item.getJsonObject(SLOProfile.SLO_CONTEXT);

                        if (context.containsKey(SLOProfile.SLO_FREQUENCY))
                        {
                            var startDate =  item.getLong(SLOProfile.SLO_LAST_ACTIVE_CYCLE_START_DATE);

                            long endDate;

                            // TODO : we can modify here to get exact month and quarterly end date
                            switch (SLOConstants.SLOFrequency.valueOfName(context.getString(SLOProfile.SLO_FREQUENCY)))
                            {
                                case DAILY ->  endDate = startDate + TimeUnit.DAYS.toSeconds(1);
                                case WEEKLY -> endDate = startDate + TimeUnit.DAYS.toSeconds(7);
                                case MONTHLY -> endDate = startDate + TimeUnit.DAYS.toSeconds(30);
                                case QUARTERLY -> endDate = startDate + TimeUnit.DAYS.toSeconds(90);
                                default -> endDate = 0;
                            }

                            if (endDate != 0 && endDate <= currentTime)
                            {
                                startCycle(item, endDate);
                            }
                        }
                    }
                    // means the SLO is not started yet, we've to check that weather it's time to start now or not!
                    else
                    {
                        var startDate = item.getLong(SLOProfile.SLO_START_DATE);

                        if (startDate <= currentTime)
                        {
                            startCycle(item, startDate);
                        }
                    }
                }
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }

    // this will emit slo cycle start change notification and update the last active cycle start date in config store
    private void startCycle(JsonObject item, long startDate)
    {
        var sloId = item.getLong(ID);

        if (CommonUtil.debugEnabled())
        {
            LOGGER.debug(String.format("starting cycle for SLO id : %s", sloId));
        }

        item.put(SLOProfile.SLO_LAST_ACTIVE_CYCLE_START_DATE, startDate);

        // cycle id generated on each cycle start
        item.put(SLOConstants.SLO_CYCLE_ID, CommonUtil.newId());

        // resolve monitors
        var sloContext = item.getJsonObject(SLOProfile.SLO_CONTEXT);

        var objects = sloContext.containsKey(ENTITY_TYPE) && sloContext.getString(ENTITY_TYPE).equalsIgnoreCase(Tag.TAG)
                ? ObjectConfigStore.getStore().getObjectIdsByTags(sloContext.getJsonArray(ENTITIES))
                : sloContext.containsKey(ENTITY_TYPE) && sloContext.getString(ENTITY_TYPE).equalsIgnoreCase(APIConstants.Entity.GROUP.getName())
                ? ObjectConfigStore.getStore().getIdsByGroups(sloContext.getJsonArray(ENTITIES))
                : sloContext.getJsonArray(ENTITIES);


        item.put(SLOProfile.SLO_CONTEXT, sloContext.put(NMSConstants.OBJECTS, objects));

        // initialize the map
        sloDetails.computeIfAbsent(sloId, key -> new HashMap<>()).clear();

        // for entire slo profile aggregated of all monitors/instances
        sloDetails.get(sloId).put(EMPTY_VALUE, initSLOData(sloContext));

        var type = sloContext.getString(SLOProfile.SLO_OBJECT_TYPE, EMPTY_VALUE);

        for (var index = 0; index < objects.size(); index++)
        {
            var object = objects.getLong(index);

            var instances = MetricConfigStore.getStore().getMetricInstancesByInstanceType(object, type);

            var detail = sloDetails.get(sloId);

            // means SLO is for on instances
            if (instances != null && !instances.isEmpty())
            {
                for (var j = 0; j < instances.size(); j++)
                {
                    var instance = instances.getString(j);

                    detail.put(object + INSTANCE_SEPARATOR + instance, initSLOData(sloContext));
                }
            }
            // means SLO is for monitor
            else
            {
                detail.put(object.toString(), initSLOData(sloContext));
            }
        }

        // update the last cycle start date
        Bootstrap.configDBService().update(DBConstants.TBL_SLO_PROFILE, new JsonObject().put(DBConstants.FIELD_NAME, ID).put(VALUE, sloId),
                item,
                DEFAULT_USER, SYSTEM_REMOTE_ADDRESS, result ->
                {
                    if (result.succeeded())
                    {
                        SLOProfileConfigStore.getStore().updateItem(sloId).onComplete(asyncResult->
                        {
                            if (asyncResult.succeeded())
                            {
                                // start the SLO
                                vertx.eventBus().publish(EventBusConstants.EVENT_CHANGE_NOTIFICATION, new JsonObject()
                                        .put(EventBusConstants.CHANGE_NOTIFICATION_TYPE, EventBusConstants.ChangeNotificationType.SLO_CYCLE_START).put(ID, sloId));
                            }
                            else
                            {
                                LOGGER.warn("failed to start the SLO cycle");

                                LOGGER.error(asyncResult.cause());
                            }
                        });
                    }
                    else
                    {
                        LOGGER.error(result.cause());
                    }
                });
    }

    // main method to process based on inspected data
    private void process(JsonObject inspectedResult)
    {
        var severity = inspectedResult.getString(SEVERITY);

        var object = inspectedResult.getLong(AIOpsConstants.ENTITY_ID);

        var instance = inspectedResult.getString(INSTANCE);

        var timestamp = inspectedResult.getLong(EventBusConstants.EVENT_TIMESTAMP);

        var sloId = inspectedResult.getLong(ID);

        var key = object + (instance != null ? INSTANCE_SEPARATOR + instance : EMPTY_VALUE);

        // ignore the values if it's not added at the time of cycle start
        if (sloDetails.get(sloId).get(key) != null)
        {
            updateSLO(sloDetails.get(sloId).get(key), severity.equalsIgnoreCase(Severity.DOWN.name()) || severity.equalsIgnoreCase(Severity.CRITICAL.name()) ? SLOConstants.SLOStatus.BREACHED : SLOConstants.SLOStatus.OK, timestamp);

            updated = true;
        }
    }

    // dump to file
    private void dump()
    {
        try
        {
            Bootstrap.vertx().fileSystem().writeFileBlocking(CURRENT_DIR + PATH_SEPARATOR + CONFIG_DIR + PATH_SEPARATOR + SLOConstants.SLO_CACHE_FILE,
                    Buffer.buffer(CodecUtil.compress(new JsonObject().put("items", sloDetails).encode().getBytes())));
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }

    // load data from file
    private void load()
    {
        var file = new File(CURRENT_DIR + PATH_SEPARATOR + CONFIG_DIR + PATH_SEPARATOR + SLOConstants.SLO_CACHE_FILE);

        if (file.exists())
        {
            var buffer = Bootstrap.vertx().fileSystem().readFileBlocking(file.getPath());

            if (buffer != null && buffer.getBytes().length > 0)
            {
                var context = new JsonObject(Buffer.buffer(CodecUtil.toBytes(buffer.getBytes()))).getMap();

                for (var entry : ((Map<String, Object>) context.get("items")).entrySet())
                {
                    var key = entry.getKey();

                    var value = (Map<String, Object>) entry.getValue();

                    var map = new HashMap<String, JsonObject>();

                    for (var e: value.entrySet())
                    {
                        map.put(e.getKey(), JsonObject.mapFrom(e.getValue()));
                    }

                    sloDetails.put(CommonUtil.getLong(key), map);
                }

                LOGGER.info(String.format("%s loaded...", SLOConstants.SLO_CACHE_FILE));
            }
        }
        else
        {
            Bootstrap.vertx().fileSystem().createFileBlocking(file.getPath());
        }
    }

    // methods to dump data into db.
    private void write(long sloId, String cycleId, long timestamp, DatastoreConstants.PluginId pluginId, JsonObject event)
    {
        try
        {
            var buffer = Buffer.buffer("UTF-8");

            appendBytes(buffer, pluginId, timestamp);

            buffer.appendLongLE(sloId);

            buffer.appendIntLE(cycleId.length());

            buffer.appendString(cycleId);

            for (var entry : event.getMap().entrySet())
            {
                if (entry.getValue() != null)
                {
                    var column = entry.getKey();

                    var value = CommonUtil.getString(entry.getValue());

                    var category = DatastoreConstants.getDataCategory(false, column, value);

                    buffer.appendByte(category);

                    var bytes = column.getBytes(StandardCharsets.UTF_8);

                    buffer.appendIntLE(bytes.length);

                    buffer.appendBytes(bytes);

                    if (category == DatastoreConstants.DataCategory.FLOAT.getName())
                    {
                        ByteUtil.writeDouble(buffer, CommonUtil.getDouble(value));
                    }
                    else if (category == DatastoreConstants.DataCategory.NUMERIC.getName())//if it has decimal number than convert it into integer
                    {
                        buffer.appendLongLE(Math.round(CommonUtil.getDouble(value)));
                    }
                    else
                    {
                        bytes = value.getBytes(StandardCharsets.UTF_8);

                        buffer.appendIntLE(bytes.length);

                        buffer.appendBytes(bytes);
                    }
                }
            }

            vertx.eventBus().send(EventBusConstants.EVENT_DATASTORE_WRITE + "." + DatastoreConstants.DatastoreCategory.METRIC.getName(), buffer.getBytes());
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }

    private void appendBytes(Buffer buffer, DatastoreConstants.PluginId pluginId, long timestamp)
    {
        buffer.setLongLE(0, timestamp);

        var plugin = pluginId.getName() + DASH_SEPARATOR + "slo.metric";

        var bytes = plugin.getBytes(StandardCharsets.UTF_8);

        buffer.appendIntLE(bytes.length);

        buffer.appendBytes(bytes);

        buffer.appendByte(DatastoreConstants.DatastoreFormat.VERTICAL.getName().byteValue());

        buffer.appendByte(CommonUtil.getByteValue(DatastoreConstants.DatastoreType.SLO_METRIC.ordinal()));
    }

    // slo related utility methods
    private JsonObject initSLOData(JsonObject sloContext)
    {
        var data = new JsonObject();

        data.put(SLOConstants.TOTAL_DURATION,
                switch (SLOConstants.SLOFrequency.valueOfName(sloContext.getString(SLOProfile.SLO_FREQUENCY)))
                {
                    case DAILY -> TimeUnit.DAYS.toSeconds(1);
                    case WEEKLY -> TimeUnit.DAYS.toSeconds(7);
                    case MONTHLY -> TimeUnit.DAYS.toSeconds(30);
                    case QUARTERLY -> TimeUnit.DAYS.toSeconds(90);
                });

        data.put(SLOConstants.ERROR_MARGIN, data.getLong(SLOConstants.TOTAL_DURATION) - ((data.getLong(SLOConstants.TOTAL_DURATION) * sloContext.getInteger(SLOProfile.SLO_TARGET)) / 100));

        data.put(SLOConstants.DURATION_LEFT, data.getLong(SLOConstants.TOTAL_DURATION));

        data.put(SLOConstants.FLAPS, new JsonArray());

        data.put(SLOConstants.LAST_VIOLATED_PERCENT, 0.0f);

        return data;
    }

    private void updateSLO(JsonObject data, SLOConstants.SLOStatus currentStatus, long currentTimestamp)
    {
        SLOConstants.SLOStatus lastSLOStatus;
        var lastTimeStamp = data.getLong(SLOConstants.LAST_TIMESTAMP);
        var lastBreachedTimestamp = data.getLong(SLOConstants.LAST_BREACHED_TIMESTAMP);
        var durationLeft = data.getLong(SLOConstants.DURATION_LEFT);
        var totalDuration = data.getLong(SLOConstants.TOTAL_DURATION);
        var flaps = data.getJsonArray(SLOConstants.FLAPS);

        // means first poll received
        if (data.getInteger(SLOConstants.LAST_SLO_STATUS) == null)
        {
            lastSLOStatus = currentStatus;
            lastTimeStamp = currentTimestamp;

            if (currentStatus == SLOConstants.SLOStatus.BREACHED)
            {
                lastBreachedTimestamp = currentTimestamp;
            }
        }
        else
        {
            lastSLOStatus = SLOConstants.SLOStatus.values()[data.getInteger(SLOConstants.LAST_SLO_STATUS)];

            // in case of breached status will continuously minus the left duration
            if (currentStatus == SLOConstants.SLOStatus.BREACHED)
            {
                if (lastSLOStatus == SLOConstants.SLOStatus.BREACHED)
                {
                    durationLeft -= currentTimestamp - lastBreachedTimestamp;
                }

                lastBreachedTimestamp = currentTimestamp;
            }

            // if current status and last status is not matched then it's a flap
            if (lastSLOStatus != currentStatus)
            {
                // perform flap

                // total duration of last status
                var duration = currentTimestamp - lastTimeStamp;

                flaps.add(new JsonObject().put("slo.status", lastSLOStatus.getName()).put("slo.duration", duration).put("timestamp", lastTimeStamp));

                // means it went from breached to clear
                if (lastSLOStatus == SLOConstants.SLOStatus.BREACHED && currentStatus == SLOConstants.SLOStatus.OK)
                {
                    // we've to minus the breached duration from the left duration
                    durationLeft -= currentTimestamp - lastBreachedTimestamp;
                }

                lastSLOStatus = currentStatus;
                lastTimeStamp = currentTimestamp;
            }
        }

        data.put(SLOConstants.LAST_SLO_STATUS, lastSLOStatus.ordinal())
                .put(SLOConstants.LAST_TIMESTAMP, lastTimeStamp)
                .put(SLOConstants.LAST_BREACHED_TIMESTAMP, lastBreachedTimestamp)
                .put(SLOConstants.DURATION_LEFT, durationLeft)
                .put(SLOConstants.TOTAL_DURATION, totalDuration)
                .put(SLOConstants.FLAPS, flaps);
    }

    private float getSLOAchievedPercentage(JsonObject data)
    {
        return (data.getLong(SLOConstants.DURATION_LEFT) * 100) / (float) data.getLong(SLOConstants.TOTAL_DURATION);
    }

    private float getSLOBurnRate(JsonObject data)
    {
        var rate = getSLOViolatedPercentage(data) - data.getFloat(SLOConstants.LAST_VIOLATED_PERCENT);

        data.put(SLOConstants.LAST_VIOLATED_PERCENT, getSLOViolatedPercentage(data));

        return rate;
    }

    private float getSLOViolatedPercentage(JsonObject data)
    {
        return 100 - getSLOAchievedPercentage(data);
    }

    private int getSLOStatus(JsonObject data, int target, int warning)
    {
        var achieved = getSLOAchievedPercentage(data);

        if (achieved < target)
        {
            return SLOConstants.SLOStatus.BREACHED.ordinal();
        }
        else if (achieved < warning)
        {
            return SLOConstants.SLOStatus.WARNING.ordinal();
        }
        else
        {
            return SLOConstants.SLOStatus.OK.ordinal();
        }
    }

    private SLOConstants.SLOStatus getLastSLOStatus(JsonObject data)
    {
        if (data.getInteger(SLOConstants.LAST_SLO_STATUS) == null)
        {
            return null;
        }

        return SLOConstants.SLOStatus.values()[data.getInteger(SLOConstants.LAST_SLO_STATUS)];
    }

    private float getErrorBudgetLeft(JsonObject data)
    {
        return 100 - (((data.getLong(SLOConstants.TOTAL_DURATION) - data.getLong(SLOConstants.DURATION_LEFT)) * 100) / (float) data.getLong(SLOConstants.ERROR_MARGIN));
    }

    @Override
    public void stop(Promise<Void> promise) throws Exception
    {
        dump();

        promise.complete();
    }
}
