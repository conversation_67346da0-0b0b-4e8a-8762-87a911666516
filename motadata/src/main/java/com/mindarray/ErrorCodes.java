/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

/* Change Logs:
 *   Date           Author      Notes
 *   5-Mar-2025     Bharat      MOTADATA-4740: Two factor authentication 2FA
 */
package com.mindarray;

public final class ErrorCodes
{
    public static final String ERROR_CODE_SUCCESS = "MD000";
    public static final String ERROR_CODE_PING_FAILED = "MD001";
    public static final String ERROR_CODE_INVALID_PORT = "MD002";
    public static final String ERROR_CODE_INVALID_CREDENTIALS = "MD003";
    public static final String ERROR_CODE_TIMEOUT = "MD004";
    public static final String ERROR_CODE_INVALID_DATABASE = "MD005";
    public static final String ERROR_CODE_UNAUTHORIZED_DATABASE_ACCESS = "MD006";
    public static final String ERROR_CODE_UNAUTHORIZED_URL_ACCESS = "MD007";
    public static final String ERROR_CODE_API_INVALID_SECRET_KEY = "MD008";
    public static final String ERROR_CODE_API_INVALID_ACCESS_KEY = "MD009";
    public static final String ERROR_CODE_API_INVALID_TENANT_ID = "MD010";
    public static final String ERROR_CODE_API_INVALID_SUBSCRIPTION_ID = "MD011";
    public static final String ERROR_CODE_API_INVALID_CLIENT_ID = "MD012";
    public static final String ERROR_CODE_DATABASE_LOGIN = "MD013";
    public static final String ERROR_CODE_LOGIN_INVALID_REFRESH_TOKEN = "MD014";
    public static final String ERROR_CODE_LOGIN_LDAP_USER_NOT_CONFIGURED = "MD015";
    public static final String ERROR_CODE_LOGIN_LDAP_USER_NOT_ALLOWED = "MD016";
    public static final String ERROR_CODE_LOGIN_INVALID_LDAP_USER = "MD017";
    public static final String ERROR_CODE_LOGIN_USER_DISABLED = "MD018";
    public static final String ERROR_CODE_LOGIN_LDAP_AUTH_DISABLED = "MD019";
    public static final String ERROR_CODE_LOGIN_LDAP_SERVER_ERROR = "MD020";
    public static final String ERROR_CODE_LOGIN_INVALID_CREDENTIALS = "MD021";
    public static final String ERROR_CODE_BAD_REQUEST = "MD022";
    public static final String ERROR_CODE_LDAP_CONFIG_TEST = "MD023";
    public static final String ERROR_CODE_EMAIL_CONFIG_TEST = "MD024";
    public static final String ERROR_CODE_SMS_CONFIG_TEST = "MD025";
    public static final String ERROR_CODE_EMAIL_SERVICE_DOWN = "MD026";
    public static final String ERROR_CODE_SMS_SERVICE_DOWN = "MD027";
    public static final String ERROR_CODE_DNS_CONFIG_TEST = "MD028";
    public static final String ERROR_CODE_CREDENTIAL_TEST = "MD029";
    public static final String ERROR_CODE_SNMP_OID_GROUP_TEST = "MD030";
    public static final String ERROR_CODE_INTERNAL_ERROR = "MD031";
    public static final String ERROR_CODE_NO_INTERNET_CONNECTION = "MD032";
    public static final String ERROR_CODE_DISCOVERY_RESULT_SAVE = "MD033";
    public static final String ERROR_CODE_METRIC_PLUGIN_TEST = "MD034";
    public static final String ERROR_CODE_LDAP_SYNC_ALREADY_RUNNING = "MD035";
    public static final String ERROR_CODE_LDAP_MOTADATA_USERS_GROUP_NOT_FOUND = "MD036";
    public static final String ERROR_CODE_LDAP_MOTADATA_USERS_GROUP_NO_USER_FOUND = "MD037";
    public static final String ERROR_CODE_DISCOVERY_START = "MD038";
    public static final String ERROR_CODE_OBJECT_PROVISION_DUPLICATE = "MD039";
    public static final String ERROR_CODE_OBJECT_PROVISION_NO_PARENT = "MD040";
    public static final String ERROR_CODE_OBJECT_PROVISION_NO_CREDENTIAL_PROFILE = "MD041";
    public static final String ERROR_CODE_OBJECT_PROVISION_NO_SNMP_CATALOG = "MD042";
    public static final String ERROR_CODE_RESET_PASSWORD_NOT_AUTHORIZED = "MD043";
    public static final String ERROR_CODE_RESET_PASSWORD_SUPER_ADMIN = "MD044";
    public static final String ERROR_CODE_RESET_PASSWORD_LDAP_USER = "MD045";
    public static final String ERROR_CODE_PASSWORD_POLICY_NOT_SATISFIED = "MD046";
    public static final String ERROR_CODE_CONNECTION_FAILED = "MD047";
    public static final String ERROR_CODE_SITE_VPN_NOT_FOUND = "MD048";
    public static final String ERROR_CODE_VENDOR_NOT_SUPPORTED = "MD049";
    public static final String ERROR_CODE_DHCP_LEASE_FILE_NOT_FOUND = "MD050";
    public static final String ERROR_CODE_DHCP_CONFIG_FILE_NOT_FOUND = "MD051";
    public static final String ERROR_CODE_IBM_MQ_INVALID_CHANNEL = "MD052";
    public static final String ERROR_CODE_IBM_MQ_INVALID_QUEUE_MANAGER = "MD053";
    public static final String ERROR_CODE_AZURE_INVALID_RESOURCE_GROUP = "MD054";
    public static final String ERROR_CODE_AWS_INVALID_REGION_GROUP = "MD055";
    public static final String ERROR_CODE_NO_RESPONSE = "MD056";
    public static final String ERROR_CODE_DNS_RECORD_NOT_FOUND = "MD057";
    public static final String ERROR_CODE_CONNECTION_RESET = "MD058";
    public static final String ERROR_CODE_COMMAND_EXECUTION_FAILED = "MD059";
    public static final String ERROR_CODE_OID_NOT_FOUND = "MD060";
    public static final String ERROR_CODE_INVALID_OID_NAME = "MD061";
    public static final String ERROR_CODE_INVALID_OID_INDEX = "MD062";
    public static final String ERROR_CODE_INVALID_OID_VALUE = "MD063";
    public static final String ERROR_CODE_TERMINAL_SERVICE_NOT_RUNNING = "MD064";
    public static final String ERROR_CODE_DHCP_SERVICE_NOT_RUNNING = "MD065";
    public static final String ERROR_CODE_INVALID_SSL_CERTIFICATE = "MD066";
    public static final String ERROR_CODE_INVALID_DOMAIN = "MD067";
    public static final String ERROR_CODE_IIS_SERVICE_NOT_RUNNING = "MD068";
    public static final String ERROR_CODE_AD_SERVICE_NOT_RUNNING = "MD069";
    public static final String ERROR_CODE_FTP_SERVICE_NOT_RUNNING = "MD070";
    public static final String ERROR_CODE_WINDOWS_CLUSTER_SERVICE_NOT_RUNNING = "MD071";
    public static final String ERROR_CODE_CLOUD_INSTANCE_NOT_FOUND = "MD072";
    public static final String ERROR_CODE_TOPOLOGY_PLUGIN_TEST = "MD073";
    public static final String ERROR_CODE_ROUTING_PROTOCOL_NOT_FOUND = "MD074";
    public static final String ERROR_CODE_SYMANTEC_EMAIL_GATEWAY_NOT_FOUND = "MD075";
    public static final String ERROR_CODE_WIRELESS_CONTROLLER_NOT_FOUND = "MD076";
    public static final String ERROR_CODE_VRF_NOT_FOUND = "MD077";
    public static final String ERROR_CODE_VLAN_NOT_FOUND = "MD078";
    public static final String ERROR_CODE_MULTICAST_NOT_FOUND = "MD079";
    public static final String ERROR_CODE_STP_NOT_FOUND = "MD080";
    public static final String ERROR_CODE_REMOTE_VPN_NOT_FOUND = "MD081";
    public static final String ERROR_CODE_IPSLA_NOT_FOUND = "MD082";
    public static final String ERROR_CODE_BAD_RESPONSE = "MD083";
    public static final String ERROR_CODE_NO_ITEM_FOUND = "MD084";
    public static final String ERROR_CODE_DATABASE_UPDATE = "MD085";
    public static final String ERROR_CODE_DATABASE_SAVE = "MD086";
    public static final String ERROR_CODE_DATABASE_DELETE = "MD087";
    public static final String ERROR_CODE_INVALID_OID_GROUP = "MD088";
    public static final String ERROR_CODE_DATABASE_FETCH = "MD089";
    public static final String ERROR_CODE_LOGIN_TOKEN_COMPROMISED = "MD090";
    public static final String ERROR_CODE_AGENT_DOWN = "MD091";
    public static final String ERROR_CODE_EVENT_ROUTER_LIMIT = "MD092";
    public static final String ERROR_CODE_EVENT_ROUTER_NO_COLLECTOR = "MD093";
    public static final String ERROR_CODE_DUPLICATE = "MD094";
    public static final String ERROR_CODE_MANUAL_ABORTED = "MD095";
    public static final String ERROR_CODE_HYPERV_CLUSTER_SERVICE_NOT_RUNNING = "MD096";
    public static final String ERROR_CODE_DNS_SERVICE_NOT_RUNNING = "MD097";
    public static final String ERROR_CODE_INVALID_SS_BIN_PATH = "MD098";
    public static final String ERROR_CODE_INVALID_LINUX_OR_UNIX_HOST = "MD099";
    public static final String ERROR_CODE_NTP_SERVICE_NOT_RUNNING = "MD100";
    public static final String ERROR_CODE_SNMP_AGENT_SYSTEM_OID_NOT_FOUND = "MD101";
    public static final String ERROR_CODE_SNMP_CONTEXT_CONFIGURATION_ERROR = "MD102";
    public static final String ERROR_CODE_INVALID_PUBLIC_OR_PRIVATE_SSH_KEY = "MD103";
    public static final String ERROR_CODE_AGENT_BUSY = "MD104";
    public static final String ERROR_CODE_INVALID_PARSING_SCRIPT = "MD105";
    public static final String ERROR_CODE_RUNBOOK_PLUGIN_TEST = "MD106";
    public static final String ERROR_CODE_PROXY_SERVER_DOWN = "MD107";
    public static final String ERROR_CODE_INVALID_USER_NAME = "MD108";
    public static final String ERROR_CODE_INVALID_AUTH_PASSWORD = "MD109";
    public static final String ERROR_CODE_FAILED_TO_LIST_OBJECTS = "MD110";
    public static final String ERROR_CODE_TRACEROUTE = "MD111";
    public static final String ERROR_CODE_NOT_SUFFICIENT_DATA_POINTS = "MD112";
    public static final String ERROR_CODE_NO_ACTIVE_METRICS = "MD113";
    public static final String ERROR_CODE_AGENT_CONFIG_CHANGE = "MD114";
    public static final String ERROR_CODE_PROCESS_TIMEOUT = "MD115";
    public static final String ERROR_CODE_INVALID_FQDN = "MD116";
    public static final String ERROR_CODE_USER_DISABLED = "MD118";
    public static final String ERROR_CODE_USER_NOT_FOUND = "MD119";
    public static final String ERROR_CODE_USER_EMAIL_NOT_SET = "MD120";
    public static final String ERROR_CODE_PASSWORD_EXPIRED = "MD121";
    public static final String ERROR_CODE_LICENSE_LIMIT_EXCEEDED = "MD122";
    public static final String ERROR_CODE_REMOTE_EVENT_PROCESSOR_BUSY = "MD123";
    public static final String ERROR_CODE_CREDENTIAL_NOT_ASSIGNED = "MD126";
    public static final String ERROR_CODE_CONFIG_INIT_SESSION_FAILED = "MD129";
    public static final String ERROR_CODE_CONFIG_ENABLE_COMMAND_FAILED = "MD130";
    public static final String ERROR_CODE_CONFIG_ENABLE_USER_FAILED = "MD131";
    public static final String ERROR_CODE_CONFIG_ENABLE_PASSWORD_FAILED = "MD132";
    public static final String ERROR_CODE_CONFIG_ENABLE_MODE_FAILED = "MD133";
    public static final String ERROR_CODE_CONFIG_ENABLE_PASSWORD_NOT_PROVIDED = "MD134";
    public static final String ERROR_CODE_CONFIG_TEMPLATE_NOT_PROVIDED = "MD135";
    public static final String ERROR_CODE_CONFIG_OPERATION_NOT_PROVIDED = "MD136";
    public static final String ERROR_CODE_STORAGE_TEST = "MD137";

    public static final String ERROR_CODE_NOT_COMPATIBLE = "MD138";

    public static final String ERROR_CODE_UPGRADE_REQUIRED = "MD139";
    public static final String ERROR_CODE_CONFIG_OPERATION_INVALID_CONTENT = "MD140";
    public static final String ERROR_CODE_SSH_CONNECTION_FAILED = "MD141";
    public static final String ERROR_CODE_SSH_CONNECTION_LIMIT_EXCEEDED = "MD142";

    public static final String ERROR_CODE_TEAMS_SERVICE_DOWN = "MD143";
    public static final String ERROR_CODE_MFA_NOT_ENABLED = "MD144";
    public static final String ERROR_CODE_OTP_EXPIRED = "MD144";
    public static final String ERROR_CODE_TOTP_INVALID = "MD145";

    private ErrorCodes()
    {
    }
}
