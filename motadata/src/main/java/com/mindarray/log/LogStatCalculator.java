/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

/*  Change Logs:
 *  Date			Author			      Notes
 *  2-Jun-2025		Smit Prajapati		  MOTADATA-:6417 stat calculator will only fetch data and calculate the stat
 */

package com.mindarray.log;

import com.mindarray.Bootstrap;
import com.mindarray.GlobalConstants;
import com.mindarray.datastore.DatastoreConstants;
import com.mindarray.eventbus.EventBusConstants;
import com.mindarray.util.CommonUtil;
import com.mindarray.util.DateTimeUtil;
import com.mindarray.util.Logger;
import com.mindarray.util.MotadataConfigUtil;
import io.vertx.core.AbstractVerticle;
import io.vertx.core.Promise;
import io.vertx.core.json.JsonObject;

import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;

import static com.mindarray.GlobalConstants.BootstrapType;
import static com.mindarray.GlobalConstants.SEPARATOR_WITH_ESCAPE;
import static com.mindarray.eventbus.EventBusConstants.EVENT_SOURCE;
import static com.mindarray.eventbus.EventBusConstants.EVENT_TIMESTAMP;
import static com.mindarray.log.LogEngineConstants.*;

public class LogStatCalculator extends AbstractVerticle
{
    private static final Logger LOGGER = new Logger(LogStatCalculator.class, GlobalConstants.MOTADATA_LOG, "Log Stat Calculator");
    private static final int LOG_STAT_FLUSH_TIMER_SECONDS = MotadataConfigUtil.getLogStatFlushTimerSeconds();
    private final Map<String, long[]> stats = new HashMap<>();
    private final JsonObject event = new JsonObject();
    private final StringBuilder builder = new StringBuilder(0);
    private Set<String> mappers;


    @Override
    public void start(Promise<Void> promise) throws Exception
    {
        try
        {
            if (Bootstrap.bootstrapType() == BootstrapType.APP)
            {
                mappers = new HashSet<>();

                vertx.eventBus().<Map<String, long[]>>localConsumer(EventBusConstants.EVENT_LOG_STAT, message -> merge(message.body()));

                vertx.setPeriodic(LOG_STAT_FLUSH_TIMER_SECONDS * 1000L, timer ->
                {
                    calculate();

                    stats.clear();
                });

                promise.complete();

                LOGGER.debug(config().getString(EventBusConstants.EVENT_TYPE) + " started successfully!!!");
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);

            promise.fail(exception);
        }
    }

    /**
     * Merges incoming stats map into the local stats map.
     * For each key, the count and volume values are added to existing totals.
     */
    private void merge(Map<String, long[]> stats)
    {
        if (stats != null)
        {
            for (var entry : stats.entrySet())
            {
                var currentValues = entry.getValue();

                // Merge using compute for less branching and better clarity
                this.stats.compute(entry.getKey(), (k, values) ->
                {

                    if (values == null)
                    {
                        // Clone to prevent retaining references to input map's array
                        return new long[]{currentValues[0], currentValues[1]};
                    }
                    else
                    {
                        values[0] += currentValues[0]; // count

                        values[1] += currentValues[1]; // sum

                        return values;
                    }
                });
            }
        }
    }

    /**
     * Calculates the per-second statistics for each flow key,
     * and writes it to the datastore.
     */
    private void calculate()
    {
        if (!stats.isEmpty())
        {
            stats.keySet().forEach(key ->
            {
                var values = stats.get(key);

                var tokens = key.split(SEPARATOR_WITH_ESCAPE);

                if (values[0] > 0) //count
                {
                    DatastoreConstants.write(this.event.put(EVENT_CATEGORY, tokens[1]).put(EVENT_SOURCE_TYPE, tokens[2]).put(EVENT_SOURCE, tokens[0])
                            .put(LOG_VOLUME_BYTES, values[1])
                            .put(LOGS_PER_SEC, Math.round(CommonUtil.getDouble(values[0] / LOG_STAT_FLUSH_TIMER_SECONDS)))
                            .put(LOG_VOLUME_BYTES_PER_SEC, Math.round(CommonUtil.getDouble(values[1] / LOG_STAT_FLUSH_TIMER_SECONDS)))
                            .put(GlobalConstants.PLUGIN_ID, DatastoreConstants.PluginId.LOG_EVENT_STAT.getName())
                            .put(EVENT_TIMESTAMP, DateTimeUtil.currentSeconds())
                            .put(DatastoreConstants.DATASTORE_TYPE, DatastoreConstants.DatastoreType.LOG.ordinal()), "log.stat", mappers, builder);
                }

                values[0] = 0; //reset count

                values[1] = 0; // reset volume bytes

                this.event.clear();
            });
        }
    }
}