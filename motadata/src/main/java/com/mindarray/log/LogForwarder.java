/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

/*
 *	Change Logs:
 *	Date			Author			    Notes
 *  28-Feb-2025		Darshan Parmar		MOTADATA-5215: SonarQube Suggestions Resolution
 */
package com.mindarray.log;

import com.mindarray.Bootstrap;
import com.mindarray.GlobalConstants;
import com.mindarray.api.APIConstants;
import com.mindarray.datastore.DatastoreConstants;
import com.mindarray.eventbus.EventBusConstants;
import com.mindarray.eventbus.EventEngine;
import com.mindarray.policy.PolicyEngineConstants;
import com.mindarray.store.EventSourceConfigStore;
import com.mindarray.store.LogForwarderConfigStore;
import com.mindarray.util.CommonUtil;
import com.mindarray.util.Logger;
import com.mindarray.util.MotadataConfigUtil;
import com.mindarray.visualization.VisualizationConstants;
import io.vertx.core.AbstractVerticle;
import io.vertx.core.Promise;
import io.vertx.core.datagram.DatagramSocket;
import io.vertx.core.datagram.DatagramSocketOptions;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import io.vertx.core.net.NetClientOptions;
import io.vertx.core.net.NetSocket;

import java.util.*;

import static com.mindarray.GlobalConstants.*;
import static com.mindarray.api.LogForwarder.*;
import static com.mindarray.eventbus.EventBusConstants.*;

/**
 * A verticle responsible for forwarding logs to external systems.
 * <p>
 * The LogForwarder manages the forwarding of log events to external systems using
 * TCP or UDP connections. It supports filtering logs based on source, source type,
 * and group, as well as applying complex condition-based filters.
 * <p>
 * Key features:
 * <ul>
 *   <li>Supports both TCP and UDP forwarding</li>
 *   <li>Maintains connections to TCP destinations</li>
 *   <li>Filters logs based on source, source type, and group</li>
 *   <li>Applies condition-based filters for fine-grained control</li>
 *   <li>Formats logs as JSON or raw text based on configuration</li>
 * </ul>
 * <p>
 * The LogForwarder listens for log events on the event bus and forwards them to
 * the appropriate destinations based on the configured forwarders.
 */
public class LogForwarder extends AbstractVerticle
{
    /**
     * Newline separator for log messages
     */
    public static final String NEW_LINE_SEPARATOR = "\n";

    /**
     * Logger for this class
     */
    private static final Logger LOGGER = new Logger(LogForwarder.class, GlobalConstants.MOTADATA_LOG, "Log Forwarder");

    /**
     * Number of TCP connection retry attempts
     */
    private static final int TCP_RETRIES = MotadataConfigUtil.getLogForwarderTCPRetries();

    /**
     * Interval (in seconds) between TCP connection retry attempts
     */
    private static final int TCP_RETRY_SECONDS = MotadataConfigUtil.getLogForwarderTCPRetrySeconds();

    /**
     * Set for storing qualified forwarder IDs during filtering
     */
    private final Set<Long> qualifiedItems = new HashSet<>();

    /**
     * Map of log forwarders indexed by their IDs
     */
    private final Map<Long, JsonObject> logForwarders = new HashMap<>();

    /**
     * Map of forwarder IDs indexed by source
     */
    private final Map<String, List<Long>> forwardersBySource = new HashMap<>();

    /**
     * Map of forwarder IDs indexed by source type
     */
    private final Map<String, List<Long>> forwardersBySourceType = new HashMap<>();

    /**
     * Map of forwarder IDs indexed by group ID
     */
    private final Map<Long, List<Long>> forwardersByGroup = new HashMap<>();

    /**
     * Map of TCP connections indexed by forwarder ID
     */
    private final Map<Long, NetSocket> tcpForwarders = new HashMap<>();

    /**
     * UDP socket for sending UDP messages
     */
    private final DatagramSocket udpForwarder = Bootstrap.vertx().createDatagramSocket(new DatagramSocketOptions());

    /**
     * Event engine for handling log events
     */
    private EventEngine eventEngine;

    @Override
    public void start(Promise<Void> promise) throws Exception
    {

        try
        {
            var items = LogForwarderConfigStore.getStore().getItems();

            for (var index = 0; index < items.size(); index++)
            {
                logForwarders.put(items.getJsonObject(index).getLong(ID), items.getJsonObject(index));

                createForwarder(items.getJsonObject(index));
            }

            assign();
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }

        eventEngine = new EventEngine().setEventType(config().getString(EventBusConstants.EVENT_TYPE))
                .setPersistEventOffset(true).setLogger(LOGGER).setEventHandler(this::forward).start(vertx, promise);

        vertx.eventBus().<JsonObject>localConsumer(EVENT_CHANGE_NOTIFICATION, message ->
        {
            var event = message.body();

            var notificationType = EventBusConstants.ChangeNotificationType.valueOf(event.getString(CHANGE_NOTIFICATION_TYPE));

            switch (notificationType)
            {
                case ADD_LOG_FORWARDER ->
                {
                    var item = LogForwarderConfigStore.getStore().getItem(event.getLong(ID));

                    logForwarders.put(event.getLong(ID), item);

                    createForwarder(item);

                    assign();
                }

                case UPDATE_LOG_FORWARDER ->
                {
                    //as in case of enabling disabling if its tcp connection then will be removing its connection as we don't know how much time forwarder will be disabled on enable will establish connection
                    logForwarders.put(event.getLong(ID), LogForwarderConfigStore.getStore().getItem(event.getLong(ID)));

                    if (logForwarders.get(event.getLong(ID)).getString(LOG_FORWARDER_STATE).equalsIgnoreCase(NO) && tcpForwarders.containsKey(event.getLong(ID)))
                    {
                        tcpForwarders.remove(event.getLong(ID)).close();
                    }
                    else
                    {
                        createForwarder(logForwarders.get(event.getLong(ID)));
                    }
                }

                case DELETE_LOG_FORWARDER ->
                {
                    if (tcpForwarders.containsKey(event.getLong(ID)))
                    {
                        tcpForwarders.remove(event.getLong(ID)).close();
                    }

                    logForwarders.remove(event.getLong(ID));

                    assign();
                }

                default ->
                {
                    // do nothing
                }
            }

        }).exceptionHandler(LOGGER::error);

        promise.future().onComplete(result -> LOGGER.debug(config().getString(EVENT_TYPE) + " started successfully!!!"));
    }

    /**
     * Processes a log event and forwards it to appropriate destinations.
     * <p>
     * This method is the main entry point for log event processing in the LogForwarder.
     * It performs the following steps:
     * <ul>
     *   <li>Retrieves the event source configuration</li>
     *   <li>Identifies qualified forwarders based on source, source type, and groups</li>
     *   <li>For each qualified forwarder:
     *     <ul>
     *       <li>Checks if the forwarder is enabled</li>
     *       <li>Applies any configured filters</li>
     *       <li>If all conditions are met, forwards the log to the destination</li>
     *     </ul>
     *   </li>
     * </ul>
     * <p>
     * The method supports complex condition-based filtering, allowing for fine-grained
     * control over which logs are forwarded to which destinations.
     *
     * @param event The log event to be processed and potentially forwarded
     */
    private void forward(JsonObject event)
    {
        try
        {
            var item = EventSourceConfigStore.getStore().getItemByHost(event.getString(EVENT_SOURCE), false);

            if (item != null)
            {

                for (var entry : qualify(item.getJsonArray(LogEngineConstants.SOURCE_GROUPS), event.getString(EVENT_SOURCE), event.getString(LogEngineConstants.EVENT_SOURCE_TYPE)))
                {
                    var logForwarder = logForwarders.get(entry);

                    if (logForwarder.getString(LOG_FORWARDER_STATE).equalsIgnoreCase(YES))
                    {
                        var context = logForwarder.getJsonObject(LOG_FORWARDER_CONTEXT);

                        var groupConditionSatisfied = true;

                        if (context != null && !context.isEmpty())
                        {
                            var filter = context.getJsonObject(FILTERS).getJsonObject(DATA_FILTER, null);

                            if (filter != null && !filter.isEmpty())
                            {
                                var groupOperator = filter.getString(OPERATOR);

                                var conditionGroups = filter.getJsonArray(CONDITION_GROUPS);

                                for (var index = 0; index < conditionGroups.size(); index++)
                                {
                                    var conditionGroup = conditionGroups.getJsonObject(index);

                                    var operator = conditionGroup.getString(OPERATOR);

                                    var conditions = conditionGroup.getJsonArray(CONDITIONS);

                                    var satisfied = false;

                                    for (var j = 0; j < conditions.size(); j++)
                                    {
                                        var condition = conditions.getJsonObject(j);

                                        var operand = condition.getString(OPERAND).contains(CARET_SEPARATOR) ? condition.getString(OPERAND).split(CARET_SEPARATOR_WITH_ESCAPE)[0] : condition.getString(OPERAND);

                                        if (event.containsKey(operand))
                                        {
                                            satisfied = PolicyEngineConstants.evaluateCondition(conditionGroup.getString(FILTER).equalsIgnoreCase("include"), condition.getString(OPERATOR), condition.getValue(VALUE), event.getValue(operand));

                                            if ((satisfied && operator.equalsIgnoreCase(DatastoreConstants.ConditionGroup.OR.getName())) || (!satisfied && operator.equalsIgnoreCase(DatastoreConstants.ConditionGroup.AND.getName())))
                                            {
                                                break;
                                            }
                                        }
                                    }

                                    if (!satisfied && groupOperator.equalsIgnoreCase(DatastoreConstants.ConditionGroup.AND.getName()))
                                    {
                                        //if main condition is "AND" and any counter group condition is not true so breaking for loop as in "AND" condition all condition is needed to be true
                                        groupConditionSatisfied = false;

                                        break;
                                    }
                                    else if (satisfied && groupOperator.equalsIgnoreCase(DatastoreConstants.ConditionGroup.OR.getName()))
                                    {
                                        //if main condition is "OR" and any counter group condition is true so breaking for loop as in "OR" condition only one condition needed to be true
                                        groupConditionSatisfied = true;

                                        break;
                                    }
                                    else if (!satisfied && groupOperator.equalsIgnoreCase(DatastoreConstants.ConditionGroup.OR.getName()))
                                    {
                                        groupConditionSatisfied = false;
                                    }
                                }

                                if (groupConditionSatisfied)
                                {
                                    forward(logForwarder, event);
                                }
                            }
                            else
                            {
                                forward(logForwarder, event);
                            }
                        }
                        else
                        {
                            forward(logForwarder, event);
                        }
                    }

                }
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }

    private void assign()
    {
        forwardersBySource.clear();

        forwardersByGroup.clear();

        forwardersBySourceType.clear();

        try
        {
            logForwarders.forEach((key, value) ->
            {
                var forwarderContext = value.getJsonObject(LOG_FORWARDER_CONTEXT);

                JsonArray entities;

                if (forwarderContext.getJsonArray(ENTITIES) != null && !forwarderContext.getJsonArray(ENTITIES).isEmpty()) //if logForwarder contains filter of group or monitor so just assigning that filtered entities or groups
                {
                    entities = forwarderContext.getJsonArray(ENTITIES);

                    if (forwarderContext.getString(ENTITY_TYPE).equalsIgnoreCase(APIConstants.Entity.GROUP.getName()))
                    {
                        assignById(value.getLong(ID), entities, forwardersByGroup);
                    }
                    else if (forwarderContext.getString(ENTITY_TYPE).equalsIgnoreCase(VisualizationConstants.VisualizationGrouping.EVENT_SOURCE_TYPE.getName()))
                    {
                        assignBySource(value.getLong(ID), entities, forwardersBySourceType);
                    }
                    else
                    {
                        assignBySource(value.getLong(ID), entities, forwardersBySource);
                    }
                }
            });
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }

    }

    private void forward(JsonObject logForwarder, JsonObject event)
    {
        if (logForwarder.containsKey(LOG_FORWARDER_TYPE) && logForwarder.getString(LOG_FORWARDER_TYPE).equalsIgnoreCase(LOG_FORWARDER_TYPE_TCP) && tcpForwarders.containsKey(logForwarder.getLong(ID)) && tcpForwarders.getOrDefault(logForwarder.getLong(ID), null) != null)
        {
            tcpForwarders.get(logForwarder.getLong(ID)).write(enrich(event, logForwarder.getString(LOG_FORWARDER_LOG_TYPE)) + NEW_LINE_SEPARATOR)
                    .onComplete(result ->
                    {
                        if (CommonUtil.traceEnabled())
                        {
                            if (result.succeeded())
                            {
                                LOGGER.trace("Forwarding message to " + logForwarder.getString(LOG_FORWARDER_DESTINATION_IP) + ":" + logForwarder.getString(LOG_FORWARDER_DESTINATION_PORT) + " -> " + enrich(event, logForwarder.getString(LOG_FORWARDER_LOG_TYPE)));
                            }
                            else
                            {
                                LOGGER.trace("Failed to forward message to " + logForwarder.getString(LOG_FORWARDER_DESTINATION_IP) + ":" + logForwarder.getString(LOG_FORWARDER_DESTINATION_PORT) + " reasons : " + result.cause());
                            }
                        }
                    });
        }

        else if (logForwarder.containsKey(com.mindarray.api.LogForwarder.LOG_FORWARDER_TYPE) && logForwarder.getString(com.mindarray.api.LogForwarder.LOG_FORWARDER_TYPE).equalsIgnoreCase(com.mindarray.api.LogForwarder.LOG_FORWARDER_TYPE_UDP))
        {
            udpForwarder.send(enrich(event, logForwarder.getString(LOG_FORWARDER_LOG_TYPE)) + NEW_LINE_SEPARATOR, CommonUtil.getInteger(logForwarder.getString(LOG_FORWARDER_DESTINATION_PORT)), logForwarder.getString(LOG_FORWARDER_DESTINATION_IP))
                    .onComplete(result ->
                    {
                        if (CommonUtil.traceEnabled())
                        {
                            if (result.succeeded())
                            {
                                LOGGER.trace("Forwarding message to " + logForwarder.getString(LOG_FORWARDER_DESTINATION_IP) + ":" + logForwarder.getString(LOG_FORWARDER_DESTINATION_PORT) + " -> " + enrich(event, logForwarder.getString(LOG_FORWARDER_LOG_TYPE)));
                            }
                            else
                            {
                                LOGGER.trace("Failed to forward message to " + logForwarder.getString(LOG_FORWARDER_DESTINATION_IP) + ":" + logForwarder.getString(LOG_FORWARDER_DESTINATION_PORT) + " reasons : " + result.cause());
                            }
                        }
                    });
        }
    }

    /**
     * Identifies forwarders that qualify for a given log event.
     * <p>
     * This method determines which log forwarders should process a log event based on:
     * <ul>
     *   <li>The event source (IP address or hostname)</li>
     *   <li>The event source type (e.g., Windows, Linux, etc.)</li>
     *   <li>The groups that the event source belongs to</li>
     * </ul>
     * <p>
     * It collects forwarder IDs from three different maps:
     * <ul>
     *   <li>forwardersBySource - Forwarders assigned to specific sources</li>
     *   <li>forwardersBySourceType - Forwarders assigned to specific source types</li>
     *   <li>forwardersByGroup - Forwarders assigned to specific groups</li>
     * </ul>
     * <p>
     * The method returns a set of unique forwarder IDs that should process the log event.
     *
     * @param groups          The groups that the event source belongs to
     * @param source          The event source (IP address or hostname)
     * @param eventSourceType The event source type
     * @return A set of forwarder IDs that qualify for the log event
     */
    private Set<Long> qualify(JsonArray groups, String source, String eventSourceType)
    {
        try
        {
            qualifiedItems.clear();

            if (!forwardersBySource.getOrDefault(source, Collections.emptyList()).isEmpty())
            {
                qualifiedItems.addAll(forwardersBySource.get(source));
            }

            if (CommonUtil.isNotNullOrEmpty(eventSourceType) && !forwardersBySourceType.getOrDefault(eventSourceType, Collections.emptyList()).isEmpty())
            {
                qualifiedItems.addAll(forwardersBySourceType.get(eventSourceType));
            }

            for (var index = 0; index < groups.size(); index++)
            {
                var group = groups.getLong(index);

                if (!forwardersByGroup.getOrDefault(group, Collections.emptyList()).isEmpty())
                {
                    qualifiedItems.addAll(forwardersByGroup.get(group));
                }
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }

        return qualifiedItems;
    }

    private void assignBySource(long logForwarderID, JsonArray entities, Map<String, List<Long>> forwardersByEntity)
    {
        try
        {
            entities.forEach(entity ->
            {
                var object = CommonUtil.getString(entity);

                if (!forwardersByEntity.containsKey(object))
                {
                    forwardersByEntity.put(object, new ArrayList<>());
                }
                if (!forwardersByEntity.get(object).contains(logForwarderID))
                {
                    forwardersByEntity.get(object).add(logForwarderID);
                }
            });
        }

        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }

    private void assignById(long logForwarderID, JsonArray entities, Map<Long, List<Long>> forwardersByEntity)
    {
        try
        {
            entities.forEach(entity ->
            {
                var object = CommonUtil.getLong(entity);

                if (!forwardersByEntity.containsKey(object))
                {
                    forwardersByEntity.put(object, new ArrayList<>());
                }
                if (!forwardersByEntity.get(object).contains(logForwarderID))
                {
                    forwardersByEntity.get(object).add(logForwarderID);
                }
            });
        }

        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }

    /**
     * Creates a connection for a TCP log forwarder.
     * <p>
     * This method establishes a TCP connection for a log forwarder if:
     * <ul>
     *   <li>The forwarder is enabled (state is "YES")</li>
     *   <li>The forwarder type is TCP</li>
     *   <li>A connection doesn't already exist for this forwarder</li>
     * </ul>
     * <p>
     * The method creates a TCP client with configured retry parameters and
     * attempts to connect to the destination. If successful, it stores the
     * connection in the tcpForwarders map for later use.
     * <p>
     * UDP forwarders don't require persistent connections, so they're handled
     * differently when forwarding messages.
     *
     * @param logForwarder The log forwarder configuration
     */
    private void createForwarder(JsonObject logForwarder)
    {
        try
        {
            if (logForwarder.getString(LOG_FORWARDER_STATE).equalsIgnoreCase(YES) && logForwarder.getString(LOG_FORWARDER_TYPE).equalsIgnoreCase(LOG_FORWARDER_TYPE_TCP) && !tcpForwarders.containsKey(logForwarder.getLong(ID)))
            {
                Bootstrap.vertx().createNetClient(new NetClientOptions().setReconnectAttempts(TCP_RETRIES).setReconnectInterval(TCP_RETRY_SECONDS * 1000L))
                        .connect(CommonUtil.getInteger(logForwarder.getString(LOG_FORWARDER_DESTINATION_PORT)), logForwarder.getString(LOG_FORWARDER_DESTINATION_IP))
                        .onComplete(result ->
                        {

                            if (result.succeeded())
                            {
                                LOGGER.info("TCP Connection for " + logForwarder.getString(LOG_FORWARDER_DESTINATION_IP) + ":" + logForwarder.getString(LOG_FORWARDER_DESTINATION_PORT) + " established successfully...");

                                tcpForwarders.put(logForwarder.getLong(ID), result.result());
                            }
                            else
                            {
                                LOGGER.info("Failed to established TCP connection, for " + logForwarder.getString(LOG_FORWARDER_DESTINATION_IP) + ":" + logForwarder.getString(LOG_FORWARDER_DESTINATION_PORT) + " reason : " + result.cause().getMessage());
                            }
                        });
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }

    /**
     * Formats a log event for forwarding based on the forwarder type.
     * <p>
     * This method prepares a log event for forwarding by formatting it according
     * to the specified forwarder log type:
     * <ul>
     *   <li>For JSON type: Removes internal metadata fields and returns the event as a JSON string</li>
     *   <li>For other types: Returns the raw log message</li>
     * </ul>
     * <p>
     * The method ensures that internal metadata fields that aren't relevant to
     * external systems are removed before forwarding.
     *
     * @param event            The log event to format
     * @param logForwarderType The type of log format to use (JSON or other)
     * @return The formatted log message as a string
     */
    private String enrich(JsonObject event, String logForwarderType)
    {
        if (logForwarderType.equalsIgnoreCase(LOG_FORWARDER_LOG_TYPE_JSON))
        {
            event.remove(LogEngineConstants.RECEIVED_TIMESTAMP);

            event.remove(EventBusConstants.EVENT_VOLUME_BYTES);

            event.remove(PLUGIN_ID);

            event.remove(LogEngineConstants.EVENT_STAT_KEY);

            return event.encode();
        }
        else
        {
            return event.getString(EVENT);
        }
    }

    @Override
    public void stop(Promise<Void> promise) throws Exception
    {
        try
        {
            tcpForwarders.forEach((key, value) -> value.close());

            udpForwarder.close();
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);

            eventEngine.stop(vertx, promise);
        }

        eventEngine.stop(vertx, promise);
    }
}
