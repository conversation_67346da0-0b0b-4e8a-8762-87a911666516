/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

package com.mindarray.log;

import com.mindarray.GlobalConstants;
import com.mindarray.eventbus.EventBusConstants;
import com.mindarray.eventbus.EventEngine;
import com.mindarray.util.Logger;
import com.mindarray.util.MotadataConfigUtil;
import io.vertx.core.AbstractVerticle;
import io.vertx.core.Promise;

/**
 * A verticle responsible for forwarding log events to other components.
 * <p>
 * The LogProcessor acts as an event forwarder in the log processing system.
 * It receives log events and forwards them to other components via a configured port.
 * <p>
 * Key features:
 * <ul>
 *   <li>Sets up an EventEngine as an event forwarder</li>
 *   <li>Configures the event forwarder with a specific port</li>
 *   <li>Persists event offsets to ensure reliable delivery</li>
 * </ul>
 * <p>
 * Unlike other components in the log package, the LogProcessor doesn't perform
 * any processing on the log events themselves; it simply forwards them.
 */
public class LogProcessor extends AbstractVerticle
{
    /**
     * Logger for this class
     */
    private static final Logger LOGGER = new Logger(LogProcessor.class, GlobalConstants.MOTADATA_LOG, "Log Processor");

    /**
     * Event engine for forwarding log events
     */
    private EventEngine eventEngine;

    /**
     * Initializes and starts the LogProcessor verticle.
     * <p>
     * This method sets up an EventEngine instance configured as an event forwarder.
     * The event forwarder is configured with:
     * <ul>
     *   <li>Event offset persistence enabled for reliable delivery</li>
     *   <li>Event type from the verticle configuration</li>
     *   <li>Port number from the Motadata configuration</li>
     *   <li>Logger for tracking operations</li>
     * </ul>
     * <p>
     * The event forwarder receives log events and forwards them to other components
     * in the system that need to process or store log data.
     *
     * @param promise A Promise that will be completed when the verticle has been started
     * @throws Exception If an error occurs during startup
     */
    @Override
    public void start(Promise<Void> promise) throws Exception
    {
        // Create and configure the event engine as a forwarder
        eventEngine = new EventEngine().setPersistEventOffset(true)
                .setEventType(config().getString(EventBusConstants.EVENT_TYPE))
                .setEventForwarderPort(MotadataConfigUtil.getLogProcessorPort()).setEventForwarder(true)
                .setLogger(LOGGER).start(vertx, promise);
    }

    /**
     * Stops the LogProcessor verticle.
     * <p>
     * This method gracefully shuts down the event engine, ensuring that
     * any pending events are properly processed before shutdown.
     *
     * @param promise A Promise that will be completed when the verticle has been stopped
     * @throws Exception If an error occurs during shutdown
     */
    @Override
    public void stop(Promise<Void> promise) throws Exception
    {
        // Stop the event engine
        eventEngine.stop(vertx, promise);
    }
}
