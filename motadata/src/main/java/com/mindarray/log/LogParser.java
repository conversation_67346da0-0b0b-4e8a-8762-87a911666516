/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

/*
 *	Change Logs:
 *	Date			Author			    Notes
 *  03-Feb-2025		Darshan Parmar		MOTADATA-5215: SonarQube Suggestions Resolution
 *  02-Jun-2025		Smit Prajapati		MOTADATA-:6417 stat calculator will only fetch data and calculate the stat
 *  05-Jun-2025		Vismit Mandlik		MOTADATA-5333: Added metadata fields and event sending logic for config change syslog parser plugin
 */
package com.mindarray.log;

import com.google.common.hash.BloomFilter;
import com.google.common.hash.Funnels;
import com.mindarray.Bootstrap;
import com.mindarray.ErrorMessageConstants;
import com.mindarray.GlobalConstants;
import com.mindarray.api.AIOpsObject;
import com.mindarray.api.RemoteEventProcessor;
import com.mindarray.datastore.DatastoreConstants;
import com.mindarray.eventbus.EventEngine;
import com.mindarray.store.EventSourceConfigStore;
import com.mindarray.store.LogForwarderConfigStore;
import com.mindarray.store.LogParserConfigStore;
import com.mindarray.store.LogParserPluginConfigStore;
import com.mindarray.util.*;
import io.vertx.core.AbstractVerticle;
import io.vertx.core.Promise;
import io.vertx.core.buffer.Buffer;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;

import java.io.File;
import java.lang.reflect.Method;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.*;
import java.util.regex.Pattern;

import static com.mindarray.ErrorMessageConstants.*;
import static com.mindarray.GlobalConstants.*;
import static com.mindarray.api.APIConstants.*;
import static com.mindarray.api.LogParser.*;
import static com.mindarray.api.LogParserPlugin.LOG_PARSER_PLUGIN_NAME;
import static com.mindarray.datastore.DatastoreConstants.INDEXABLE_COLUMNS;
import static com.mindarray.db.DBConstants.*;
import static com.mindarray.eventbus.EventBusConstants.*;
import static com.mindarray.eventbus.EventBusConstants.ChangeNotificationType.ADD_LOG_PARSER_PLUGIN;
import static com.mindarray.log.LogEngineConstants.*;
import static com.mindarray.log.LogEngineConstants.LogParserType.*;

/**
 * A verticle responsible for parsing log data from various sources using different parsing strategies.
 * <p>
 * The LogParser is a central component of the log processing system that handles the parsing
 * of log data from various sources. It supports multiple parsing strategies:
 * <ul>
 *   <li>Regex-based parsing - Uses regular expressions to extract fields from log messages</li>
 *   <li>JSON parsing - Parses structured JSON log messages</li>
 *   <li>Delimiter-based parsing - Splits log messages using a specified delimiter</li>
 *   <li>Custom plugin-based parsing - Uses custom plugins for specialized log formats</li>
 * </ul>
 * <p>
 * Key features:
 * <ul>
 *   <li>Maintains maps of parsers, patterns, and other metadata</li>
 *   <li>Filters logs based on keywords and source</li>
 *   <li>Parses logs using different strategies</li>
 *   <li>Enriches log data with additional information</li>
 *   <li>Handles the loading and compilation of parser plugins</li>
 *   <li>Flushes parsed logs to storage</li>
 * </ul>
 * <p>
 * The LogParser integrates with the Vert.x event bus system for communication with other components.
 */
public class LogParser extends AbstractVerticle
{
    /**
     * Logger for this class
     */
    private static final Logger LOGGER = new Logger(LogParser.class, MOTADATA_LOG, "Log Parser");

    /**
     * Maximum size of log messages in bytes
     */
    private static final int MAX_LOG_SIZE_BYTES = MotadataConfigUtil.getMaxLogSizeBytes();

    /**
     * Set of metadata field names that are handled specially during parsing
     */
    private static final Set<String> METADATA_FIELDS = Set.of(TIME_STAMP, EVENT_TIMESTAMP, EVENT_SOURCE, EVENT_CATEGORY, MESSAGE, EVENT_PATTERN_ID, RECEIVED_TIMESTAMP, EVENT_VOLUME_BYTES, EVENT);

    /**
     * Pattern for replacing non-alphanumeric characters in field names
     */
    private static final Pattern FIELD_PATTERN = Pattern.compile("[^a-zA-Z0-9]");

    /**
     * Pattern for replacing hyphens in category names
     */
    private static final Pattern CATEGORY_PATTERN = Pattern.compile("-");

    /**
     * JsonObject for storing enriched event data during parsing
     */
    private final JsonObject enrichedEvent = new JsonObject();

    /**
     * Map of parsers indexed by their IDs
     */
    private final Map<Long, JsonObject> parsers = new HashMap<>();

    /**
     * Map of parser IDs indexed by source
     */
    private final Map<String, List<Long>> parsersBySource = new HashMap<>();

    /**
     * Map of timezones indexed by source
     */
    private final Map<String, String> timezonesBySource = new HashMap<>();

    /**
     * Map of condition keywords indexed by parser ID
     */
    private final Map<Long, List<String>> conditionKeywords = new HashMap<>();

    /**
     * Map of compiled regex patterns indexed by parser ID
     */
    private final Map<Long, Pattern> patterns = new HashMap<>();

    /**
     * Map of date/time formatters indexed by parser ID
     */
    private final Map<Long, String> dateTimeFormatters = new HashMap<>();

    /**
     * Map of parser plugins indexed by parser ID
     */
    private final Map<Long, Plugin> plugins = new HashMap<>();

    /**
     * Map of for storing stats
     */
    private final Map<String, long[]> stats = new HashMap<>();

    /** Sets for filtering logs */
    /**
     * Set of registered sources
     */
    private final Set<String> sources = new HashSet<>();

    /**
     * Set of registered categories
     */
    private final Set<String> categories = new HashSet<>();

    /**
     * StringBuilder for building strings efficiently
     */
    private final StringBuilder builder = new StringBuilder(0);

    /**
     * Map of enriched field names for caching
     */
    private final Map<String, String> enrichedFields = new HashMap<>();

    /**
     * JsonObject for storing indexable columns configuration
     */
    private JsonObject indexableColumns;

    /**
     * Event engine for handling log events
     */
    private EventEngine eventEngine;

    /**
     * Bloom filter for caching IP addresses that couldn't be resolved
     */
    private BloomFilter<String> garbageIPAddresses;

    /**
     * Set of column mappers
     */
    private Set<String> mappers;

    @Override
    public void start(Promise<Void> promise) throws Exception
    {
        try
        {
            garbageIPAddresses = BloomFilter.create(Funnels.stringFunnel(StandardCharsets.UTF_8), 5000000, 0.005);   //wrapper to reduce GeoDBUtil Hits

            var items = LogParserPluginConfigStore.getStore().getItems();

            for (var index = 0; index < items.size(); index++)
            {
                loadPlugin(items.getJsonObject(index));
            }

            items = LogParserConfigStore.getStore().getItems();

            for (var index = 0; index < items.size(); index++)
            {
                compile(items.getJsonObject(index));
            }

            items = EventSourceConfigStore.getStore().getItems();

            for (var index = 0; index < items.size(); index++)
            {
                var item = items.getJsonObject(index);

                if (item != null && item.containsKey(EVENT_TIMEZONE))
                {
                    timezonesBySource.put(item.getString(EVENT_SOURCE), item.getString(EVENT_TIMEZONE));
                }
            }

            var eventSources = EventSourceConfigStore.getStore().getItemsByMultiValueField(EVENT_TYPE, EVENT_LOG);

            for (var index = 0; index < eventSources.size(); index++)
            {
                var item = eventSources.getJsonObject(index);

                var source = item.getString(EVENT_SOURCE);

                if (item.containsKey(PLUGIN_ID))
                {
                    for (var pluginId : item.getJsonArray(PLUGIN_ID))
                    {
                        sources.add(source + DASH_SEPARATOR + pluginId);
                    }
                }
            }

            if (Bootstrap.bootstrapType() == BootstrapType.APP)
            {
                mappers = new HashSet<>();

                indexableColumns = new JsonObject();

                loadIndexableColumns();

                // while creating parser, when user runs the uploaded logs... [ref: UI_ACTION_LOG_PARSER_SAMPLE_LOG_PARSE]
                vertx.eventBus().<JsonObject>localConsumer(EVENT_LOG_PARSING_PROBE, message ->
                {
                    try
                    {
                        var event = message.body();

                        var fields = new LinkedHashMap<String, List<String>>();

                        var statuses = new ArrayList<>(event.getInteger(MAXIMUM_EVENTS));

                        var events = new JsonArray();

                        if (event.containsKey(SAMPLE_FILE_NAME))
                        {
                            var logFile = CURRENT_DIR + PATH_SEPARATOR + UPLOADS + PATH_SEPARATOR + event.getString(SAMPLE_FILE_NAME);

                            if (Bootstrap.vertx().fileSystem().existsBlocking(logFile))
                            {
                                try (var lines = Files.lines(Paths.get(logFile)))
                                {
                                    lines.limit(event.getInteger(MAXIMUM_EVENTS)).forEach(log ->
                                    {
                                        events.add(log.trim());

                                        statuses.add(parse(event, log.trim(), fields));
                                    });
                                }
                            }
                        }

                        if (!statuses.isEmpty())
                        {
                            message.reply(new JsonObject().put("events", events).put("parsing.statuses", statuses).put("parsed.fields", fields));
                        }
                        else
                        {
                            message.fail(NOT_AVAILABLE, LOG_FAILED_TO_PARSE);
                        }
                    }
                    catch (Exception exception)
                    {
                        LOGGER.error(exception);

                        message.fail(NOT_AVAILABLE, exception.getMessage());
                    }
                    finally
                    {
                        enrichedEvent.clear();
                    }
                });

                //as we have methods and classes loaded in cache so during log parsing create and update will be getting request
                vertx.eventBus().<JsonObject>localConsumer(EVENT_LOG_PARSER_PLUGIN_PARSE, message ->
                {
                    var event = message.body();

                    try
                    {
                        enrichedEvent.clear();  // precautionary!

                        if (parseLogByCustomPlugin(new JsonObject().put(EVENT, event.getString(EVENT).trim()), event, false))
                        {
                            enrichedEvent.remove(EVENT);

                            message.reply(enrichedEvent);
                        }
                        else
                        {
                            message.fail(NOT_AVAILABLE, LOG_PARSER_INVALID_PLUGIN_CLASS);
                        }
                    }
                    catch (Exception exception)
                    {
                        LOGGER.error(exception);

                        message.fail(NOT_AVAILABLE, LOG_PARSER_INVALID_PLUGIN_CLASS);
                    }
                    finally
                    {
                        enrichedEvent.clear();
                    }
                });

                vertx.eventBus().<JsonObject>localConsumer(EVENT_INDEXABLE_COLUMN_UPDATE, message ->
                {
                    var event = message.body();

                    try
                    {
                        if (!event.isEmpty())
                        {
                            for (var entry : event.getJsonObject(EVENT_CONTEXT).getMap().entrySet())
                            {
                                indexableColumns.getMap().computeIfAbsent(entry.getKey(), value -> new JsonArray());

                                for (var column : JsonObject.mapFrom(entry.getValue()).getMap().entrySet())
                                {
                                    if (!indexableColumns.getJsonArray(entry.getKey()).contains(column.getKey()))
                                    {
                                        indexableColumns.getJsonArray(entry.getKey()).add(column.getKey());
                                    }
                                }
                            }
                        }
                    }
                    catch (Exception exception)
                    {
                        message.fail(GlobalConstants.NOT_AVAILABLE, exception.getMessage());

                        LOGGER.error(exception);
                    }
                }).exceptionHandler(LOGGER::error);
            }

            vertx.eventBus().<JsonObject>localConsumer(EVENT_CHANGE_NOTIFICATION, message ->
            {
                var event = message.body();

                var id = event.getLong(ID);

                switch (ChangeNotificationType.valueOf(event.getString(CHANGE_NOTIFICATION_TYPE)))
                {
                    case ADD_LOG_PARSER_PLUGIN, UPDATE_LOG_PARSER_PLUGIN ->
                    {

                        var item = LogParserPluginConfigStore.getStore().getItem(id);

                        if (item != null)
                        {
                            loadPlugin(item);
                        }
                    }

                    case DELETE_LOG_PARSER_PLUGIN -> plugins.remove(id);

                    case ADD_LOG_PARSER -> compile(LogParserConfigStore.getStore().getItem(id));

                    case UPDATE_LOG_PARSER ->
                    {

                        var item = LogParserConfigStore.getStore().getItem(id);

                        parsers.put(id, item);

                        if (event.containsKey(ENTITIES)) // unassign source
                        {
                            for (var source : event.getJsonArray(ENTITIES))
                            {
                                var sourceIP = CommonUtil.getString(source);

                                if (parsersBySource.containsKey(sourceIP))
                                {
                                    parsersBySource.get(sourceIP).removeIf(id::equals);

                                    if (parsersBySource.get(sourceIP).isEmpty())
                                    {
                                        parsersBySource.remove(sourceIP);
                                    }
                                }
                            }
                        }
                        else    // assign source
                        {
                            if (item.containsKey(LOG_PARSER_ENTITIES) && !item.getJsonArray(LOG_PARSER_ENTITIES).isEmpty())
                            {
                                for (var source : item.getJsonArray(LOG_PARSER_ENTITIES))
                                {
                                    var sourceIP = CommonUtil.getString(source);

                                    parsersBySource.computeIfAbsent(sourceIP, value -> new ArrayList<>());

                                    if (parsersBySource.get(sourceIP).stream().noneMatch(id::equals))
                                    {
                                        parsersBySource.get(sourceIP).add(id);
                                    }
                                }
                            }
                        }
                    }

                    case DELETE_LOG_PARSER ->
                    {

                        parsers.remove(id);

                        patterns.remove(id);

                        dateTimeFormatters.remove(id);

                        conditionKeywords.remove(id);

                        parsersBySource.forEach((source, ids) -> ids.removeIf(id::equals));
                    }

                    case UPDATE_EVENT_SOURCE ->
                    {
                        if (event.containsKey(EVENT_TIMEZONE))
                        {
                            timezonesBySource.put(event.getString(AIOpsObject.OBJECT_IP), EventSourceConfigStore.getStore().getItemByValue(EVENT_SOURCE, event.getString(AIOpsObject.OBJECT_IP)).getString(EVENT_TIMEZONE));
                        }
                    }

                    default ->
                    {
                        // do nothing
                    }
                }
            }).exceptionHandler(LOGGER::error);

            //broadcasting stats to stat calculator
            vertx.setPeriodic(60 * 1000, timer ->
            {

                vertx.eventBus().send(EVENT_LOG_STAT, stats);

                stats.clear();
            });

            if (Bootstrap.bootstrapType() == BootstrapType.EVENT_COLLECTOR || Bootstrap.bootstrapType() == BootstrapType.EVENT_PROCESSOR)
            {
                vertx.eventBus().<JsonObject>localConsumer(EVENT_CONFIG_CHANGE, message ->
                {

                    switch (message.body().getString(ENTITY_TABLE) + message.body().getString(REQUEST))
                    {
                        case TBL_LOG_PARSER_PLUGIN + REQUEST_CREATE ->
                        {
                            try
                            {
                                var item = LogParserPluginConfigStore.getStore().getItem(message.body().getLong(ID));

                                if (item != null)
                                {
                                    loadPlugin(item);
                                }
                            }
                            catch (Exception exception)
                            {
                                LOGGER.error(exception);

                                promise.fail(exception.getMessage());
                            }
                        }

                        case TBL_LOG_PARSER + REQUEST_CREATE ->
                                compile(LogParserConfigStore.getStore().getItem(message.body().getLong(ID)));

                        case TBL_LOG_PARSER + REQUEST_UPDATE ->
                        {
                            var id = message.body().getLong(ID);

                            var item = LogParserConfigStore.getStore().getItem(id);

                            parsers.put(id, item);

                            for (var entry : parsersBySource.entrySet())
                            {
                                var source = entry.getKey();

                                var parsers = entry.getValue();

                                if (parsers.contains(id))
                                {
                                    if (!item.getJsonArray(LOG_PARSER_ENTITIES).contains(source))
                                    {
                                        parsers.remove(id);
                                    }
                                }
                                else
                                {
                                    if (item.getJsonArray(LOG_PARSER_ENTITIES).contains(source))
                                    {
                                        parsers.add(id);
                                    }
                                }

                            }
                        }

                        case TBL_EVENT_SOURCE + REQUEST_CREATE, TBL_EVENT_SOURCE + REQUEST_UPDATE ->
                        {
                            var result = message.body().getJsonObject(RESULT);

                            if (result.containsKey(EVENT_SOURCE) && result.containsKey(EVENT_TIMEZONE))
                            {
                                timezonesBySource.put(result.getString(EVENT_SOURCE), result.getString(EVENT_TIMEZONE));
                            }
                        }

                        default ->
                        {
                            // do nothing
                        }
                    }
                });
            }

            if (Bootstrap.bootstrapType() == BootstrapType.EVENT_PROCESSOR)
            {
                eventEngine = new EventEngine().setLogger(LOGGER).setEventType(config().getString(EVENT_TYPE)).setPersistEventOffset(true).setRemoteEventProcessor(true).setRemoteEventPublisher(CommonUtil.getRemoteEventSubscriber()).setRemoteEventPublisherPort(MotadataConfigUtil.getLogProcessorPort()).setEventHandler(this::filter).start(vertx, promise);

                if (CommonUtil.traceEnabled())
                {
                    LOGGER.trace(String.format("For Bootstrap type: %s", Bootstrap.bootstrapType()));
                }
            }
            else
            {
                eventEngine = new EventEngine().setLogger(LOGGER).setEventType(config().getString(EVENT_TYPE)).setPersistEventOffset(true).setEventHandler(this::filter).start(vertx, promise);

                if (CommonUtil.traceEnabled())
                {
                    LOGGER.trace(String.format("For Bootstrap type: %s", Bootstrap.bootstrapType()));
                }
            }


            promise.future().onComplete(result -> LOGGER.debug(config().getString(EVENT_TYPE) + " started successfully!!!"));
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);

            promise.fail(exception);
        }
    }

    /**
     * Filters and processes log events based on their source and content.
     * <p>
     * This method is the entry point for log event processing. It:
     * <ul>
     *   <li>Determines if the log should be processed locally or forwarded</li>
     *   <li>Identifies the appropriate parsers for the log based on its source</li>
     *   <li>Filters parsers based on condition keywords</li>
     *   <li>Initiates the parsing process for qualified parsers</li>
     * </ul>
     * <p>
     * If the log comes from a remote event processor, it's flushed directly.
     * Otherwise, the method identifies qualified parsers and initiates parsing.
     *
     * @param event The log event to be filtered and processed
     */
    private void filter(JsonObject event)
    {
        try
        {
            event.remove(EVENT_COPY_REQUIRED);

            //if logs are coming from Metric Collector we will parse that log in master
            if (event.containsKey(RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_UUID) && !event.containsKey("log.collector"))
            {
                flush(event);   // if log is coming from RPE -> flush directly  todo: to be tested
            }
            else
            {
                var source = event.getString(EVENT_SOURCE);

                event.put(EVENT_SOURCE, source);

                if (CommonUtil.isNotNullOrEmpty(source))
                {
                    var qualified = new JsonArray();

                    if (event.containsKey(ID))  //during file upload id will be there so parsing in that particular log parser
                    {
                        qualified.add(event.getLong(ID));
                    }
                    else if (parsersBySource.containsKey(source))
                    {
                        for (var id : parsersBySource.get(source))
                        {
                            var parser = parsers.get(id);

                            if (parser.getJsonArray(LOG_PARSER_CONDITION_KEYWORDS).isEmpty() || (parser.getString(LOG_PARSER_CONDITION).equals("all") ? conditionKeywords.get(parser.getLong(ID)).stream().allMatch(event.getString(EVENT).toLowerCase()::contains) : conditionKeywords.get(parser.getLong(ID)).stream().anyMatch(event.getString(EVENT).toLowerCase()::contains)))
                            {
                                qualified.add(id);
                            }
                        }
                    }

                    parse(event, qualified);
                }
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }

    /**
     * Parses a log event using the qualified parsers.
     * <p>
     * This method attempts to parse a log event using the parsers that have been
     * identified as qualified for this event. It:
     * <ul>
     *   <li>Tries each qualified parser in sequence until one successfully parses the log</li>
     *   <li>Enriches the parsed event with additional metadata</li>
     *   <li>Sets the event timestamp based on the parser's configuration</li>
     *   <li>Flushes the parsed event to storage</li>
     * </ul>
     * <p>
     * If none of the qualified parsers can parse the log, it's categorized as "Other"
     * and still flushed to storage with basic metadata.
     *
     * @param event            The log event to be parsed
     * @param qualifiedParsers Array of parser IDs that are qualified to parse this event
     *                         (empty array indicates the log should be categorized as "Other")
     */
    private void parse(JsonObject event, JsonArray qualifiedParsers)
    {
        try
        {
            var parsed = false;

            if (qualifiedParsers != null && !qualifiedParsers.isEmpty())
            {
                for (var index = 0; index < qualifiedParsers.size(); index++)
                {
                    var parser = parsers.get(qualifiedParsers.getLong(index));

                    enrichedEvent.clear().put(EVENT_SOURCE, event.getString(EVENT_SOURCE)).put(RECEIVED_TIMESTAMP, event.getLong(RECEIVED_TIMESTAMP)).put(MESSAGE, event.getString(EVENT)).put(EVENT, event.getString(EVENT));

                    if (parse(event, parser))
                    {
                        if (CommonUtil.isNullOrEmpty(enrichedEvent.getString(EVENT_CATEGORY)))
                        {
                            enrichedEvent.put(EVENT_CATEGORY, parser.getString(LOG_PARSER_NAME));
                        }

                        enrichedEvent.put(EVENT_SOURCE_TYPE, parser.getString(LOG_PARSER_SOURCE_TYPE)).put(PLUGIN_ID, parser.getInteger(PLUGIN_ID));

                        setEventTime(parser);

                        flush();

                        parsed = true;
                    }
                }
            }

            if (!parsed)       // other
            {
                enrichedEvent.clear().put(EVENT_SOURCE, event.getString(EVENT_SOURCE)).put(RECEIVED_TIMESTAMP, event.getLong(RECEIVED_TIMESTAMP)).put(MESSAGE, event.getString(EVENT)).put(EVENT_CATEGORY, LogEngineConstants.Type.OTHER.getName()).put(PLUGIN_ID, DatastoreConstants.PluginId.UNKNOWN_LOG_EVENT.getName()).put(EVENT_SOURCE_TYPE, LogEngineConstants.Type.OTHER.getName()).put(EVENT_TIMESTAMP, enrichedEvent.getLong(RECEIVED_TIMESTAMP)).put(EVENT_VOLUME_BYTES, event.getInteger(EVENT_VOLUME_BYTES)).put(EVENT, event.getString(EVENT));

                flush();
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
        finally
        {
            enrichedEvent.clear();
        }
    }

    /**
     * #internal, parse log as per category selected
     */
    private boolean parse(JsonObject event, JsonObject context)
    {
        return switch (valueOfName(context.getString(LOG_PARSER_TYPE)))
        {
            case REGEX -> parseLogByPattern(event, context, patterns.get(context.getLong(ID)), false);

            case JSON -> parseJSONLog(event, context, false);

            case DELIMITER -> parseLogByDelimiter(event, context, false);

            case CUSTOM ->
                    LicenseUtil.LICENSE_EDITION.get() == LicenseUtil.LicenseEdition.OBSERVABILITY ? parseLogByCustomPlugin(event, context, true) : parseLogByCustomPlugin(event, context, false);
        };
    }

    private void flush()
    {
        flush(enrichedEvent);

        enrichedEvent.clear();
    }

    /**
     * Flushes a parsed log event to storage and performs additional processing.
     * <p>
     * This method is responsible for the final processing and storage of parsed log events.
     * It performs several important tasks:
     * <ul>
     *   <li>Validates the event timestamp (drops events with timestamps before January 1, 2020)</li>
     *   <li>Trims message length to prevent oversized log entries</li>
     *   <li>Registers the source IP and category in the event source configuration store</li>
     *   <li>Writes the parsed log to the database</li>
     *   <li>Forwards the log to other components as needed (policy engine, statistics, forwarders)</li>
     * </ul>
     * <p>
     * The method behaves differently based on the bootstrap type:
     * <ul>
     *   <li>For EVENT_COLLECTOR or EVENT_PROCESSOR: Sends the event to the remote event processor</li>
     *   <li>For other types: Processes the event locally and writes it to storage</li>
     * </ul>
     *
     * @param event The parsed log event to be flushed to storage
     */
    private void flush(JsonObject event)
    {
        if (Bootstrap.bootstrapType() == BootstrapType.EVENT_COLLECTOR || Bootstrap.bootstrapType() == BootstrapType.EVENT_PROCESSOR)
        {
            if (!event.containsKey(EVENT_TIMESTAMP))    // it has to have event.timestamp
            {
                event.put(EVENT_TIMESTAMP, DateTimeUtil.currentSeconds());
            }

            event.put(EVENT_CATEGORY, CATEGORY_PATTERN.matcher(event.getString(EVENT_CATEGORY)).replaceAll(" ")); // remove "-" in event.category

            if (event.getLong(EVENT_TIMESTAMP) < 1577836800L) // Wednesday, January 1, 2020 5:30:00 AM GMT+05:30
            {
                LOGGER.warn(String.format("Log dropped due to invalid timestamp, %s", event.encode()));
                return;
            }

            var message = event.getString(MESSAGE);

            if (message.length() > MAX_LOG_SIZE_BYTES)
            {
                message = message.substring(0, MAX_LOG_SIZE_BYTES);

                event.put(MESSAGE, message);

                if (CommonUtil.traceEnabled())
                {

                    LOGGER.trace(String.format(LOG_MESSAGE_TOO_BIG_ERROR, message, MAX_LOG_SIZE_BYTES));
                }

            }

            vertx.eventBus().send(EVENT_REMOTE, event.put(EVENT_TYPE, EVENT_LOG).put(RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_UUID, Bootstrap.getRegistrationId()));
        }
        else
        {
            if (!CommonUtil.isNotNullOrEmpty(CommonUtil.getString(event.remove(RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_UUID)))) //logs are processed in master
            {
                event.put(EVENT_CATEGORY, event.getString(EVENT_CATEGORY).replaceAll(DASH_SEPARATOR, " "));

                var message = event.getString(MESSAGE);

                if (message.length() > MAX_LOG_SIZE_BYTES)
                {
                    message = message.substring(0, MAX_LOG_SIZE_BYTES);

                    event.put(MESSAGE, message);

                    if (CommonUtil.traceEnabled())
                    {

                        LOGGER.trace(String.format(LOG_MESSAGE_TOO_BIG_ERROR, message, MAX_LOG_SIZE_BYTES));
                    }

                }

                if (event.getLong(EVENT_TIMESTAMP) < 1577836800L) // Wednesday, January 1, 2020 5:30:00 AM GMT+05:30
                {
                    LOGGER.warn(String.format("Log dropped due to invalid timestamp, %s", event.encode()));
                    return;
                }
            }

            var source = event.getString(EVENT_SOURCE);

            if (!sources.contains(source + DASH_SEPARATOR + event.getInteger(PLUGIN_ID)) && !categories.contains(source + DASH_SEPARATOR + event.getString(EVENT_CATEGORY)))
            {
                if (CommonUtil.debugEnabled())
                {
                    LOGGER.debug(source + " registered with plugin id = " + event.getInteger(PLUGIN_ID) + " & category = " + event.getString(EVENT_CATEGORY));
                }

                sources.add(source + DASH_SEPARATOR + event.getInteger(PLUGIN_ID));

                categories.add(source + DASH_SEPARATOR + event.getString(EVENT_CATEGORY));

                vertx.eventBus().send(EVENT_CHANGE_LOCAL_NOTIFICATION, new JsonObject().put(EVENT_SOURCE, source).put(EVENT, EVENT_LOG).put(CHANGE_NOTIFICATION_TYPE, ChangeNotificationType.ADD_EVENT_SOURCE).put(EVENT_CATEGORY, event.getString(EVENT_CATEGORY)).put(PLUGIN_ID, event.getInteger(PLUGIN_ID)));

                vertx.eventBus().publish(EVENT_EVENT_CATEGORY_UPDATE, event.getString(EVENT_SOURCE_TYPE) + SEPARATOR + event.getInteger(PLUGIN_ID) + DASH_SEPARATOR + event.getString(EVENT_CATEGORY));
            }
            else if (!sources.contains(source + DASH_SEPARATOR + event.getInteger(PLUGIN_ID)))
            {
                if (CommonUtil.debugEnabled())
                {
                    LOGGER.debug(source + " registered with plugin id = " + event.getInteger(PLUGIN_ID));

                }

                sources.add(source + DASH_SEPARATOR + event.getInteger(PLUGIN_ID));

                vertx.eventBus().send(EVENT_CHANGE_LOCAL_NOTIFICATION, new JsonObject().put(EVENT_SOURCE, source).put(EVENT, EVENT_LOG).put(CHANGE_NOTIFICATION_TYPE, ChangeNotificationType.ADD_EVENT_SOURCE).put(PLUGIN_ID, event.getInteger(PLUGIN_ID)));
            }
            else if (!categories.contains(source + DASH_SEPARATOR + event.getString(EVENT_CATEGORY)))
            {
                if (CommonUtil.debugEnabled())
                {
                    LOGGER.debug(source + " registered with category = " + event.getString(EVENT_CATEGORY));
                }

                categories.add(source + DASH_SEPARATOR + event.getString(EVENT_CATEGORY));

                vertx.eventBus().send(EVENT_CHANGE_LOCAL_NOTIFICATION, new JsonObject().put(EVENT_SOURCE, source).put(EVENT, EVENT_LOG).put(CHANGE_NOTIFICATION_TYPE, ChangeNotificationType.ADD_EVENT_SOURCE).put(EVENT_CATEGORY, event.getString(EVENT_CATEGORY)));

                vertx.eventBus().publish(EVENT_EVENT_CATEGORY_UPDATE, event.getString(EVENT_SOURCE_TYPE) + SEPARATOR + event.getInteger(PLUGIN_ID) + DASH_SEPARATOR + event.getString(EVENT_CATEGORY));
            }

            vertx.eventBus().send(EVENT_EVENT_POLICY_QUALIFY, event);


            var values = stats.computeIfAbsent(event.getString(EVENT_SOURCE) + SEPARATOR + event.getString(EVENT_CATEGORY) + SEPARATOR + event.getString(EVENT_SOURCE_TYPE), k -> new long[2]);

            values[0]++; // count

            values[1] += event.getInteger(EVENT_VOLUME_BYTES, 0); // volume bytes

            if (CommonUtil.isNotNullOrEmpty(LogForwarderConfigStore.getStore().getItems()))
            {
                vertx.eventBus().send(EVENT_LOG_FORWARDER, event);
            }

            LogEngineConstants.write(event, mappers, builder, indexableColumns);
        }
    }

    /**
     * Sets the event timestamp based on the parser configuration.
     * <p>
     * This method determines and sets the appropriate timestamp for a log event based on:
     * <ul>
     *   <li>The parser type (custom, regex, JSON, delimiter)</li>
     *   <li>The date/time formatter type (formatter or numeric)</li>
     *   <li>The date/time unit (seconds or milliseconds)</li>
     * </ul>
     * <p>
     * The method handles several scenarios:
     * <ul>
     *   <li>For custom parsers: Uses the timestamp provided by the plugin</li>
     *   <li>For numeric timestamps: Converts from milliseconds to seconds if needed</li>
     *   <li>For formatted timestamps: Parses the timestamp using the configured formatter</li>
     *   <li>If no timestamp is found: Uses the received timestamp as a fallback</li>
     * </ul>
     *
     * @param context The parser configuration containing timestamp formatting information
     */
    private void setEventTime(JsonObject context)
    {
        try
        {
            enrichedEvent.put(EVENT_TIMESTAMP, enrichedEvent.getValue(RECEIVED_TIMESTAMP));  // default = received

            if (CUSTOM.equals(valueOfName(CommonUtil.getString(context.getString(LOG_PARSER_TYPE)))) && enrichedEvent.containsKey(TIME_STAMP))
            {
                enrichedEvent.put(EVENT_TIMESTAMP, enrichedEvent.remove(TIME_STAMP));   // it's always in seconds
            }

            else if (enrichedEvent.containsKey(TIME_STAMP))
            {
                if (LogDateTimeFormatterType.NUMERIC.getName().equals(context.getString(LOG_PARSER_DATE_TIME_FORMATTER_TYPE)))
                {
                    if (LogDateTimeUnit.SECONDS.getName().equals(context.getString(LOG_PARSER_DATE_TIME_FORMAT)))
                    {
                        enrichedEvent.put(EVENT_TIMESTAMP, enrichedEvent.remove(TIME_STAMP));
                    }
                    else if (LogDateTimeUnit.MILLIS.getName().equals(context.getString(LOG_PARSER_DATE_TIME_FORMAT)))
                    {
                        enrichedEvent.put(EVENT_TIMESTAMP, CommonUtil.getLong(enrichedEvent.remove(TIME_STAMP)) / 1000);    // ms -> sec
                    }
                }

                else if (LogDateTimeFormatterType.FORMATTER.getName().equals(context.getString(LOG_PARSER_DATE_TIME_FORMATTER_TYPE)) && dateTimeFormatters.get(context.getLong(ID)) != null)
                {
                    enrichedEvent.put(EVENT_TIMESTAMP, DateTimeUtil.timestampToSeconds(dateTimeFormatters.get(context.getLong(ID)), CommonUtil.getString(enrichedEvent.remove(TIME_STAMP)), timezonesBySource.getOrDefault(enrichedEvent.getString(EVENT_SOURCE), "")));
                }

                enrichedEvent.remove(TIME_STAMP);
            }
        }

        catch (Exception exception)
        {
            LOGGER.warn(exception);
        }
    }

    /**
     * Parses a log event using a regular expression pattern.
     * <p>
     * This method uses a regular expression pattern to extract fields from a log message.
     * It works by:
     * <ul>
     *   <li>Applying the pattern to the log message</li>
     *   <li>Extracting matched groups from the pattern</li>
     *   <li>Mapping the matched groups to fields defined in the parser configuration</li>
     *   <li>Enriching the event with the extracted field values</li>
     * </ul>
     * <p>
     * The method handles the case where the number of matched groups may not exactly
     * match the number of fields in the configuration.
     *
     * @param event     The log event to parse
     * @param context   The parser configuration
     * @param pattern   The compiled regular expression pattern
     * @param testProbe Whether this is a test probe
     * @return true if parsing was successful, false otherwise
     */
    private boolean parseLogByPattern(JsonObject event, JsonObject context, Pattern pattern, boolean testProbe)
    {
        var parsed = false;

        try
        {
            if (pattern != null)
            {
                var matcher = pattern.matcher(event.getString(EVENT));

                if (matcher.find())
                {
                    var fields = context.getJsonArray(LOG_PARSER_FIELDS);

                    if (fields != null)
                    {
                        var prefix = event.getString(EVENT_CATEGORY, context.getString(LOG_PARSER_NAME));

                        for (int index = matcher.groupCount() == fields.size() ? 1 : 0, fieldIndex = 0; index <= matcher.groupCount() && fieldIndex < fields.size(); index++, fieldIndex++)
                        {
                            var field = fields.getJsonObject(fieldIndex);

                            if (matcher.group(index) != null)
                            {
                                enrich(prefix, matcher.group(index).trim(), field.getString(LogParserField.NAME.getName()), field.getString(LogParserField.TYPE.getName()), event.getString(EVENT), testProbe);
                            }
                        }
                    }

                    parsed = true;
                }
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }

        return parsed;
    }

    /**
     * Parses a log event in JSON format.
     * <p>
     * This method parses a log message that is in JSON format. It works by:
     * <ul>
     *   <li>Converting the log message string to a JsonObject</li>
     *   <li>If log positions are specified in the parser configuration:
     *     <ul>
     *       <li>Extracting values from the specified positions in the JSON</li>
     *       <li>Mapping these values to fields defined in the parser configuration</li>
     *       <li>Enriching the event with the extracted field values</li>
     *     </ul>
     *   </li>
     *   <li>If no log positions are specified:
     *     <ul>
     *       <li>Merging the entire JSON object into the enriched event</li>
     *     </ul>
     *   </li>
     * </ul>
     *
     * @param event     The log event to parse
     * @param context   The parser configuration
     * @param testProbe Whether this is a test probe
     * @return true if parsing was successful, false otherwise
     */
    private boolean parseJSONLog(JsonObject event, JsonObject context, boolean testProbe)
    {
        var parsed = false;

        try
        {
            var log = new JsonObject(event.getString(EVENT));

            var positions = context.getJsonArray(LOG_PARSER_LOG_POSITIONS);

            if (positions != null && !positions.isEmpty())
            {
                var fields = context.getJsonArray(LOG_PARSER_FIELDS);

                var prefix = event.getString(EVENT_CATEGORY, context.getString(LOG_PARSER_NAME));

                for (var index = 0; index < positions.size(); index++)
                {
                    var field = fields.getJsonObject(index);

                    enrich(prefix, log.getValue(positions.getString(index)), field.getString(LogParserField.NAME.getName()), field.getString(LogParserField.TYPE.getName()), event.getString(EVENT), testProbe);
                }
            }

            else
            {
                enrichedEvent.mergeIn(log, false);
            }

            parsed = true;
        }

        catch (Exception exception)
        {
            LOGGER.error(exception);
        }

        return parsed;
    }

    /**
     * Parses a log event using a delimiter.
     * <p>
     * This method parses a log message by splitting it using a specified delimiter. It works by:
     * <ul>
     *   <li>Splitting the log message into tokens using the configured delimiter</li>
     *   <li>Identifying which token positions are of interest based on the parser configuration</li>
     *   <li>Mapping the tokens at those positions to fields defined in the parser configuration</li>
     *   <li>Enriching the event with the extracted field values</li>
     * </ul>
     * <p>
     * The method only considers the parsing successful if the log message contains at least
     * two tokens after splitting.
     *
     * @param event     The log event to parse
     * @param context   The parser configuration containing the delimiter and field mappings
     * @param testProbe Whether this is a test probe
     * @return true if parsing was successful, false otherwise
     */
    private boolean parseLogByDelimiter(JsonObject event, JsonObject context, boolean testProbe)
    {
        var parsed = false;

        try
        {
            var positions = context.getJsonArray(LOG_PARSER_LOG_POSITIONS).getList();

            var tokens = event.getString(EVENT).split(context.getString(LOG_PARSER_DELIMITER));

            var fields = context.getJsonArray(LOG_PARSER_FIELDS);

            if (tokens.length > 1 && fields != null)
            {
                parsed = true;

                var prefix = event.getString(EVENT_CATEGORY, context.getString(LOG_PARSER_NAME));

                for (var index = 0; index < tokens.length; index++)
                {
                    if (positions.contains(index))
                    {
                        var field = fields.getJsonObject(positions.indexOf(index));

                        enrich(prefix, tokens[index].trim(), field.getString(LogParserField.NAME.getName()), field.getString(LogParserField.TYPE.getName()), event.getString(EVENT), testProbe);
                    }
                }
            }
        }

        catch (Exception exception)
        {
            LOGGER.error(exception);
        }

        return parsed;
    }

    /**
     * Parses a log event using a custom plugin.
     * <p>
     * This method uses a custom plugin to parse a log message. It works by:
     * <ul>
     *   <li>Ensuring the event has a received timestamp</li>
     *   <li>Adding timezone information if available</li>
     *   <li>Invoking the plugin's parse method with the event</li>
     *   <li>If the plugin returns no error:
     *     <ul>
     *       <li>Processing flow information if requested and available</li>
     *       <li>Enriching the event with all fields returned by the plugin</li>
     *     </ul>
     *   </li>
     * </ul>
     * <p>
     * Custom plugins provide the most flexibility for parsing complex or proprietary log formats
     * that can't be handled by the standard parsing strategies.
     *
     * @param event       The log event to parse
     * @param context     The parser configuration
     * @param processFlow Whether to process flow information if available
     * @return true if parsing was successful, false otherwise
     */
    private boolean parseLogByCustomPlugin(JsonObject event, JsonObject context, boolean processFlow)
    {
        var parsed = false;

        try
        {
            if (!event.containsKey(RECEIVED_TIMESTAMP))    // custom plugins always expect received timestamp.
            {
                event.put(RECEIVED_TIMESTAMP, DateTimeUtil.currentSeconds());
            }

            if (timezonesBySource.containsKey(event.getString(EVENT_SOURCE)))
            {
                event.put(EVENT_TIMEZONE, timezonesBySource.get(event.getString(EVENT_SOURCE)));
            }

            var error = plugins.get(context.getLong(LOG_PARSER_PLUGIN)).method().invoke(plugins.get(context.getLong(LOG_PARSER_PLUGIN)).instance(), event);

            if (error == null)
            {
                classify(event, processFlow);

                var prefix = event.getString(EVENT_CATEGORY, context.getString(LOG_PARSER_NAME));

                for (var field : event)
                {
                    enrich(prefix, field.getValue(), field.getKey(), field.getKey(), event.getString(MESSAGE, event.getString(EVENT)), false);
                }

                parsed = true;
            }
            else if (CommonUtil.traceEnabled())
            {
                LOGGER.trace(String.format("%s - {%s, %s} [%s]", error, event.getString(EVENT_SOURCE), context.getString(LOG_PARSER_NAME), event.getString(EVENT)));
            }
        }

        catch (Exception exception)
        {
            LOGGER.error(exception);

            LOGGER.error(exception.getCause());
        }

        return parsed;
    }

    /**
     * Loads a log parser plugin from its configuration.
     * <p>
     * This method loads and initializes a custom log parser plugin based on its configuration.
     * It performs the following steps:
     * <ul>
     *   <li>Constructs the plugin class name from the plugin name</li>
     *   <li>Checks if the plugin class file exists</li>
     *   <li>If it exists, loads the class and creates an instance</li>
     *   <li>If it doesn't exist, requests the plugin to be added via the event bus</li>
     * </ul>
     * <p>
     * The method uses reflection to instantiate the plugin class and find its parse method.
     * It stores the plugin instance and method in the plugins map for later use.
     *
     * @param event The log parser plugin configuration
     */
    private void loadPlugin(JsonObject event)
    {
        try
        {
            var fileName = event.getString(LOG_PARSER_PLUGIN_NAME).replace("-", "").replace(" ", "");

            if (vertx.fileSystem().existsBlocking(CURRENT_DIR + PATH_SEPARATOR + PLUGIN_SCRIPT_DIR + PATH_SEPARATOR + PLUGIN_PARSER_DIR + PATH_SEPARATOR + fileName + ".class"))
            {
                var pluginClass = Class.forName(PLUGIN_PARSER_DIR + "." + event.getString(LOG_PARSER_PLUGIN_NAME).replace("-", "").replace(" ", ""), true, LOG_PLUGIN_CLASS_LOADER);

                plugins.put(event.getLong(ID), new Plugin(pluginClass.getConstructor().newInstance(), pluginClass.getDeclaredMethod(PARSE_METHOD, JsonObject.class)));
            }
            else
            {
                // if name contains in config db/store ,but not exist in plugin dir.
                // then send request to add parser plugin

                Bootstrap.vertx().eventBus().<JsonObject>request(EVENT_CHANGE_LOCAL_NOTIFICATION, event.put(FILENAME, fileName).put(EVENT_REPLY, YES).put(CHANGE_NOTIFICATION_TYPE, ADD_LOG_PARSER_PLUGIN), reply ->
                {
                    if (reply.succeeded())
                    {
                        try
                        {
                            var item = reply.result().body();

                            if (item.containsKey(STATUS) && STATUS_SUCCEED.equalsIgnoreCase(item.getString(STATUS)))
                            {
                                var pluginClass = Class.forName(PLUGIN_PARSER_DIR + "." + event.getString(LOG_PARSER_PLUGIN_NAME).replace("-", "").replace(" ", ""), true, LOG_PLUGIN_CLASS_LOADER);

                                plugins.put(event.getLong(ID), new Plugin(pluginClass.getConstructor().newInstance(), pluginClass.getDeclaredMethod(PARSE_METHOD, JsonObject.class)));

                                LOGGER.info(String.format("%s parser plugin added", event.getString(LOG_PARSER_PLUGIN_NAME)));
                            }
                            else if (item.containsKey(ERROR) && ErrorMessageConstants.LOG_PARSER_INVALID_PLUGIN_METHOD.equalsIgnoreCase(item.getString(ERROR)))
                            {
                                LOGGER.warn(ErrorMessageConstants.LOG_PARSER_INVALID_PLUGIN_METHOD);
                            }
                            else
                            {
                                LOGGER.warn(ErrorMessageConstants.TIMED_OUT);
                            }
                        }
                        catch (Exception exception)
                        {
                            LOGGER.error(exception);
                        }
                    }
                    else
                    {
                        LOGGER.warn(ErrorMessageConstants.TIMED_OUT);
                    }
                });
            }
        }

        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }

    /**
     * Enriches a log event with additional field data.
     * <p>
     * This method adds a field value to the enriched event, handling special cases:
     * <ul>
     *   <li>Timestamp fields - Stored separately and removed from the message</li>
     *   <li>Metadata fields - Added without a prefix</li>
     *   <li>IP address fields - Resolved to geographic information when possible</li>
     *   <li>Regular fields - Added with the appropriate prefix</li>
     * </ul>
     * <p>
     * The method also handles field name normalization, replacing non-alphanumeric
     * characters to ensure valid field names.
     *
     * @param prefix    The prefix to add to the field name (typically the parser name)
     * @param value     The value to add to the enriched event
     * @param field     The field name
     * @param type      The field type
     * @param message   The original log message
     * @param testProbe Whether this is a test probe (affects field name handling)
     */
    private void enrich(String prefix, Object value, String field, String type, String message, boolean testProbe)
    {
        if (value != null)
        {
            if (TIME_STAMP.equalsIgnoreCase(type))  // timestamp type field should have named timestamp
            {
                enrichedEvent.put(TIME_STAMP, value);

                enrichedEvent.put(MESSAGE, message.replace(CommonUtil.getString(value), EMPTY_VALUE).trim());   // removing timestamp from raw message
            }

            else if (METADATA_FIELDS.contains(field)) // if field is system field don't add prefix
            {
                enrichedEvent.put(field, value);
            }

            else if (!EVENT.equalsIgnoreCase(field))
            {
                var token = CommonUtil.getString(value);

                if (CommonUtil.isNotNullOrEmpty(token) && !token.equalsIgnoreCase("null") && !token.equals("-") && !token.equals("\\") && !token.equals("/")) // ignore garbage field
                {
                    if (field.endsWith(".ip") && !garbageIPAddresses.mightContain(CommonUtil.getString(value))) // if inverseMapper contains IP (no record found in DB) -> Send default
                    {
                        var result = GeoDBUtil.resolveIP(CommonUtil.getString(value), field.replace(".ip", EMPTY_VALUE).trim());

                        if (result == null)      // if not found in DB -> put into inverseMapper and send default
                        {
                            garbageIPAddresses.put(CommonUtil.getString(value));
                        }

                        else
                        {
                            result.getMap().keySet().forEach(key -> enrichedEvent.put(FIELD_PATTERN.matcher((prefix + "." + key).trim().toLowerCase(Locale.ROOT)).replaceAll("."), result.getValue(key)));
                        }
                    }

                    if (!testProbe)
                    {
                        var key = (prefix + "." + field).trim().toLowerCase(Locale.ROOT);

                        enrichedFields.computeIfAbsent(key, val -> FIELD_PATTERN.matcher(key).replaceAll("."));  // for other fields add prefix

                        enrichedEvent.put(enrichedFields.get(key), value);
                    }
                    else
                    {
                        /*
                         * MOTADATA-3039 : In case of event from user action for sample log file - log analytics in custom regex log parser creation,
                         * if we append parser name in field name and replace empty space with "." than it will differ from field name shown in UI.
                         * */
                        enrichedEvent.put(field, value);
                    }
                }
            }
        }
    }

    /**
     * Compiles and registers a log parser configuration.
     * <p>
     * This method prepares a log parser for use by:
     * <ul>
     *   <li>Storing the parser configuration in the parsers map</li>
     *   <li>Compiling regex patterns for regex-based parsers</li>
     *   <li>Registering date/time formatters for parsers that use them</li>
     *   <li>Extracting and storing condition keywords for filtering</li>
     *   <li>Associating the parser with its target sources</li>
     * </ul>
     * <p>
     * This method is called when a new parser is added or when an existing parser is updated.
     *
     * @param item The log parser configuration to compile and register
     */
    public void compile(JsonObject item)
    {
        try
        {
            parsers.put(item.getLong(ID), item);

            if (item.getString(LOG_PARSER_TYPE).equalsIgnoreCase(REGEX.getName()) && !patterns.containsKey(item.getLong(ID)))
            {
                patterns.put(item.getLong(ID), Pattern.compile(item.getString(REGEX.getName()), Pattern.CASE_INSENSITIVE));
            }

            if (LogDateTimeFormatterType.FORMATTER.getName().equals(item.getString(LOG_PARSER_DATE_TIME_FORMATTER_TYPE)) && item.getString(LOG_PARSER_DATE_TIME_FORMAT) != null && !dateTimeFormatters.containsKey(item.getLong(ID)))
            {
                dateTimeFormatters.put(item.getLong(ID), item.getString(LOG_PARSER_DATE_TIME_FORMAT));
            }

            if (item.containsKey(LOG_PARSER_CONDITION_KEYWORDS) && !item.getJsonArray(LOG_PARSER_CONDITION_KEYWORDS).isEmpty())
            {
                conditionKeywords.put(item.getLong(ID), item.getJsonArray(LOG_PARSER_CONDITION_KEYWORDS).stream().map(filter -> CommonUtil.getString(filter).toLowerCase()).toList());
            }

            if (item.containsKey(LOG_PARSER_ENTITIES) && !item.getJsonArray(LOG_PARSER_ENTITIES).isEmpty())
            {
                for (var source : item.getJsonArray(LOG_PARSER_ENTITIES))
                {
                    var sourceIP = CommonUtil.getString(source);

                    parsersBySource.computeIfAbsent(sourceIP, value -> new ArrayList<>());

                    if (parsersBySource.get(sourceIP).stream().noneMatch(item.getLong(ID)::equals))
                    {
                        parsersBySource.get(sourceIP).add(item.getLong(ID));
                    }
                }
            }

            if (item.containsKey(EVENT_SOURCE))
            {
                var sourceIP = item.getString(EVENT_SOURCE);

                parsersBySource.computeIfAbsent(sourceIP, value -> new ArrayList<>());

                if (parsersBySource.get(sourceIP).stream().noneMatch(item.getLong(ID)::equals))
                {
                    parsersBySource.get(sourceIP).add(item.getLong(ID));
                }
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }

    /**
     * used by user actions
     */
    private boolean parse(JsonObject context, String event, Map<String, List<String>> fields)
    {
        try
        {
            enrichedEvent.clear().put(MESSAGE, event);

            if (context.getJsonArray(LOG_PARSER_CONDITION_KEYWORDS).isEmpty() || context.getString(LOG_PARSER_CONDITION).equals("all") ? context.getJsonArray(LOG_PARSER_CONDITION_KEYWORDS).stream().map(item -> CommonUtil.getString(item).toLowerCase()).toList().stream().allMatch(event.toLowerCase()::contains) : context.getJsonArray(LOG_PARSER_CONDITION_KEYWORDS).stream().map(item -> CommonUtil.getString(item).toLowerCase()).toList().stream().anyMatch(event.toLowerCase()::contains))
            {
                switch (valueOfName(context.getString(LOG_PARSER_TYPE)))
                {
                    case CUSTOM ->
                    {

                        if (parseLogByCustomPlugin(new JsonObject().put(EVENT, event), context, false))
                        {
                            return parse(fields);
                        }
                    }

                    case JSON ->
                    {

                        if (parseJSONLog(new JsonObject().put(EVENT, event), context, true))
                        {
                            return parse(fields);
                        }
                    }

                    case DELIMITER ->
                    {

                        if (parseLogByDelimiter(new JsonObject().put(EVENT, event), context, true))
                        {
                            return parse(fields);
                        }
                    }

                    case REGEX ->
                    {

                        if (parseLogByPattern(new JsonObject().put(EVENT, event), context, Pattern.compile(context.getString(REGEX.getName()), Pattern.CASE_INSENSITIVE), true))
                        {
                            return parse(fields);
                        }
                    }
                }
            }
        }

        catch (Exception exception)
        {
            LOGGER.error(exception);
        }

        finally
        {
            enrichedEvent.clear();
        }

        return false;
    }

    /**
     * used by user actions
     */
    private boolean parse(Map<String, List<String>> fields)
    {
        enrichedEvent.remove(EVENT);

        try
        {
            enrichedEvent.getMap().keySet().forEach(key ->
            {
                List<String> values = new ArrayList<>();

                if (fields.containsKey(key))
                {
                    values = fields.get(key);
                }

                if (enrichedEvent.getValue(key) != null)
                {
                    values.add(CommonUtil.getString(enrichedEvent.getValue(key)));
                }

                else
                {
                    values.add("N/A");
                }

                fields.put(key, values);
            });

            return true;
        }

        catch (Exception exception)
        {
            LOGGER.error(exception);
        }

        return false;
    }

    private void loadIndexableColumns()
    {
        try
        {
            var file = new File(GlobalConstants.CURRENT_DIR + GlobalConstants.PATH_SEPARATOR + GlobalConstants.CONFIG_DIR + GlobalConstants.PATH_SEPARATOR + INDEXABLE_COLUMNS);

            if (file.exists())
            {
                var buffer = vertx.fileSystem().readFileBlocking(file.getPath());

                if (buffer != null && buffer.getBytes().length > 0)
                {
                    indexableColumns.mergeIn(new JsonObject(Buffer.buffer(CodecUtil.toBytes(buffer.getBytes()))));
                }
            }
            else
            {
                vertx.fileSystem().createFileBlocking(file.getPath());
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }

    /**
     * Classify flow and configuration backup events to the appropriate event bus channels.
     * <p>
     * If processFlow is true and the event contains flow data, this method sends the flow
     * to the event bus, updating the flow quota if necessary. It also logs the flow if tracing is enabled.
     * <p>
     * After processing flow data, the method removes the flow information from the event.
     * <p>
     * If the event is marked for configuration backup and configuration management license is enabled,
     * the method sends the event to the configuration change tracker.
     *
     * @param event       The event to process and send.
     * @param processFlow Whether to process and send flow data if present.
     */
    private void classify(JsonObject event, boolean processFlow)
    {
        if (processFlow && event.containsKey(EVENT_FLOW) && !LicenseUtil.DAILY_FLOW_QUOTA_REACHED.get())
        {
            var flow = event.getJsonObject(EVENT_FLOW).put("peer_ip_src", event.getString(EVENT_SOURCE));

            if (Bootstrap.bootstrapType() == BootstrapType.EVENT_COLLECTOR || Bootstrap.bootstrapType() == BootstrapType.EVENT_PROCESSOR || LicenseUtil.updateUsedFlowQuota(flow.toBuffer().length()))
            {
                vertx.eventBus().send(EVENT_FLOW, flow.put(EVENT_VOLUME_BYTES, flow.toBuffer().length()));       //log as flow

                if (CommonUtil.traceEnabled())
                {
                    LOGGER.trace(String.format("Log to Flow: %s", event.encode()));
                }
            }
        }

        event.remove(EVENT_FLOW);

        if (event.containsKey(EVENT_CONFIG_BACKUP) && event.getString(EVENT_CONFIG_BACKUP).equalsIgnoreCase(YES)
                && LicenseUtil.CONFIG_MANAGEMENT_ENABLED.get())
        {
            Bootstrap.vertx().eventBus().send(EVENT_CONFIG_CHANGE_REQUEST_ENQUEUE, event);
        }
    }

    @Override
    public void stop(Promise<Void> promise)
    {
        eventEngine.stop(vertx, promise);
    }

    record Plugin(Object instance, Method method)
    {

    }
}
