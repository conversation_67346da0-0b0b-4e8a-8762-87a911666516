/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

/**
 * The cache package provides services for retrieving cached data from various sources in the Motadata platform.
 * <p>
 * This package contains four main cache services:
 * <ul>
 *   <li>{@link com.mindarray.cache.CacheService} - Provides access to correlated metrics, correlation worklogs, and trap acknowledgments</li>
 *   <li>{@link com.mindarray.cache.AvailabilityCacheService} - Provides access to availability status information, heatmaps, and drill-down data</li>
 *   <li>{@link com.mindarray.cache.ConfigCacheService} - Provides access to device configuration summaries</li>
 *   <li>{@link com.mindarray.cache.PolicyCacheService} - Provides access to policy-related information including active policies and policy heatmaps</li>
 * </ul>
 * <p>
 * Each service is implemented as a Vert.x service proxy, allowing for asynchronous communication across the event bus.
 * The {@link com.mindarray.cache.CacheServiceProvider} verticle is responsible for starting and registering all cache services.
 * <p>
 * Cache services improve performance by retrieving pre-computed or previously stored data instead of performing
 * expensive operations each time the data is requested. The cached data is stored in files or in-memory structures
 * and is periodically refreshed to ensure data accuracy.
 *
 * @see com.mindarray.cache.CacheService
 * @see com.mindarray.cache.AvailabilityCacheService
 * @see com.mindarray.cache.ConfigCacheService
 * @see com.mindarray.cache.PolicyCacheService
 * @see com.mindarray.cache.CacheServiceProvider
 */
@ModuleGen(groupPackage = "com.mindarray.cache", name = "cache")
package com.mindarray.cache;

import io.vertx.codegen.annotations.ModuleGen;
