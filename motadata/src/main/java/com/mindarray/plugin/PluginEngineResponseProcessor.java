/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

/*   Change Logs:
 *   Date          Author              Notes
 *   2025-02-06    <PERSON><PERSON> Sharma      Added Compliance for PluginEngineResponseProcessor.
 *   28-Feb-2025   Darshan Parmar	 MOTADATA-5215: SonarQube Suggestions Resolution
 *   20-Feb-2025   Pruthviraj        Added NetRoute response handling
 */

package com.mindarray.plugin;

import com.mindarray.*;
import com.mindarray.api.*;
import com.mindarray.config.ConfigConstants;
import com.mindarray.db.DBConstants;
import com.mindarray.eventbus.EventBusConstants;
import com.mindarray.eventbus.EventEngine;
import com.mindarray.nms.NMSConstants;
import com.mindarray.runbook.Runbook;
import com.mindarray.store.MACScannerConfigStore;
import com.mindarray.store.SNMPTemplateOIDGroupCacheStore;
import com.mindarray.util.*;
import io.vertx.core.AbstractVerticle;
import io.vertx.core.Promise;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;

import java.util.Base64;
import java.util.Map;
import java.util.stream.Collectors;

import static com.mindarray.GlobalConstants.*;
import static com.mindarray.nms.NMSConstants.*;

/**
 * The PluginEngineResponseProcessor class is responsible for processing responses from the plugin engine.
 * It extends AbstractVerticle from the Vert.x framework to leverage its event-driven architecture.
 * <p>
 * This processor handles various types of plugin responses by:
 * 1. Receiving response events from the plugin engine
 * 2. Processing the responses based on their type (CONFIG, COMPLIANCE, RUNBOOK, etc.)
 * 3. Routing the processed responses to appropriate handlers
 * 4. Handling special cases like MAC scanner responses
 * 5. Enriching response data when necessary
 * 6. Saving data to the database when required
 * <p>
 * The class works closely with the PluginEngine and uses the constants defined in PluginEngineConstants.
 */
public class PluginEngineResponseProcessor extends AbstractVerticle
{
    /**
     * Logger instance for the PluginEngineResponseProcessor class
     */
    private static final Logger LOGGER = new Logger(PluginEngineResponseProcessor.class, MOTADATA_PLUGIN_ENGINE, "Plugin Engine Response Processor");

    /**
     * Event engine for handling blocking events
     */
    private EventEngine eventEngine;

    /**
     * Initializes the PluginEngineResponseProcessor verticle.
     * This method sets up event handlers for processing plugin engine responses,
     * MAC scanner responses, and other related events.
     *
     * @param promise Promise to be completed when initialization is done
     * @throws Exception If an error occurs during initialization
     */
    @Override
    public void start(Promise<Void> promise) throws Exception
    {
        var replyTopic = EventBusConstants.EVENT_PLUGIN_ENGINE_RESPONSE_PROCESSOR + EventBusConstants.EVENT_REPLY;

        eventEngine = new EventEngine().setEventType(EventBusConstants.EVENT_PLUGIN_ENGINE_RESPONSE_PROCESSOR)
                .setBlockingEvent(true).setLogger(LOGGER).setEventHandler(event ->
                {
                    try
                    {
                        var eventId = CommonUtil.newEventId();

                        EventBusConstants.updateEvent(event.getLong(EventBusConstants.EVENT_ID), String.format(InfoMessageConstants.EVENT_TRACKER_CHILD_EVENT_FORKED, eventId, DateTimeUtil.timestamp()));

                        vertx.<JsonObject>executeBlocking(future ->
                        {
                            try
                            {
                                var output = WorkerUtil.spawnWorker(event, event.getLong(EventBusConstants.EVENT_ID), eventId, event.getString(PluginEngineConstants.SCRIPT_LANGUAGE), false);

                                if (CommonUtil.isNotNullOrEmpty(output))
                                {
                                    event.mergeIn(new JsonObject(new String(Base64.getDecoder().decode(output.trim()))), true);

                                    ErrorMessageConstants.extractError(event);

                                    if (event.containsKey(STATUS) && event.getString(STATUS).equalsIgnoreCase(STATUS_SUCCEED))
                                    {
                                        event.put(ERROR_CODE, ErrorCodes.ERROR_CODE_SUCCESS);
                                    }

                                    else
                                    {
                                        ErrorMessageConstants.extractErrorCode(event, ErrorMessageConstants.PARSING_SCRIPT_FAILED, null, null, null);
                                    }
                                }

                                else
                                {
                                    event.put(STATUS, STATUS_FAIL).put(MESSAGE, ErrorMessageConstants.PARSING_SCRIPT_FAILED).put(ERROR_CODE, ErrorCodes.ERROR_CODE_INVALID_PARSING_SCRIPT);
                                }

                                future.complete(event);
                            }

                            catch (Exception exception)
                            {
                                LOGGER.error(exception);

                                if (exception.getMessage() != null && exception.getMessage().contains(ErrorMessageConstants.PROCESS_TIMED_OUT))
                                {
                                    event.put(STATUS, STATUS_FAIL).put(MESSAGE, ErrorMessageConstants.PARSING_SCRIPT_FAILED).put(ERROR_CODE, ErrorCodes.ERROR_CODE_PROCESS_TIMEOUT);
                                }

                                else
                                {
                                    event.put(STATUS, STATUS_FAIL).put(MESSAGE, ErrorMessageConstants.PARSING_SCRIPT_FAILED).put(ERROR_CODE, ErrorCodes.ERROR_CODE_INTERNAL_ERROR)
                                            .put(ERROR, CommonUtil.formatStackTrace(exception.getStackTrace()));
                                }

                                future.complete(event);
                            }
                        }, false, result ->
                        {
                            // complete blocking event
                            vertx.eventBus().send(replyTopic, result.result());

                            send(result.result());
                        });
                    }
                    catch (Exception exception)
                    {
                        LOGGER.error(exception);
                    }
                }).start(vertx, promise);

        // Set up a consumer to handle responses from the plugin engine
        vertx.eventBus().<JsonObject>localConsumer(EventBusConstants.EVENT_PLUGIN_ENGINE_RESPONSE, message ->
        {
            try
            {
                // Extract the event from the message
                var event = message.body();

                // Determine the type of request this response is for
                var request = PluginEngineConstants.PluginEngineRequest.valueOfName(event.getString(PluginEngineConstants.PLUGIN_ENGINE_REQUEST));

                // Update event tracking if this event is from the current processor
                if (event.getString(RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_UUID) != null && event.getString(RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_UUID).equalsIgnoreCase(Bootstrap.getRegistrationId()))
                {
                    EventBusConstants.updateEvent(event.getLong(EventBusConstants.EVENT_ID), String.format(InfoMessageConstants.EVENT_TRACKER_RESPONSE_RECEIVED, DateTimeUtil.timestamp()));
                }

                // Log the event details if trace is enabled
                if (CommonUtil.traceEnabled())
                {
                    LOGGER.trace(String.format("plugin engine response event: %s", CommonUtil.removeSensitiveFields(event, true).encodePrettily()));
                }

                // Route the response based on the request type
                if (request == PluginEngineConstants.PluginEngineRequest.CONFIG && event.containsKey(ConfigConstants.CONFIG_OPERATION))
                {
                    // Configuration operations are sent to the config response processor
                    vertx.eventBus().send(EventBusConstants.EVENT_CONFIG_RESPONSE_PROCESSOR, event.put(EventBusConstants.EVENT_COPY_REQUIRED, false));
                }
                else if (request == PluginEngineConstants.PluginEngineRequest.COMPLIANCE)
                {
                    // Compliance responses are sent to the address specified in the event
                    vertx.eventBus().send(event.getString(EventBusConstants.EVENT_ADDRESS), event);
                }
                else if (event.containsKey(EventBusConstants.EVENT) && event.getString(EventBusConstants.EVENT).equalsIgnoreCase(EventBusConstants.UI_ACTION_RUNBOOK_PLUGIN_TEST))
                {
                    // Runbook plugin test responses are just cleared (no further processing needed)
                    EventBusConstants.clear(event);
                }
                else if (request == PluginEngineConstants.PluginEngineRequest.NETROUTE)
                {
                    // Network route responses are sent to the netroute poll response handler
                    vertx.eventBus().send(EventBusConstants.EVENT_NETROUTE_POLL_RESPONSE, event);
                }
                else
                {
                    // Mark the event as complete in the event tracking system
                    EventBusConstants.complete(event);

                    // If this is a config-related runbook, send it to the config response processor
                    if (event.containsKey(RunbookPlugin.RUNBOOK_PLUGIN_CATEGORY) && event.getString(RunbookPlugin.RUNBOOK_PLUGIN_CATEGORY).equalsIgnoreCase(Runbook.RunbookCategory.CONFIG.getName()))
                    {
                        vertx.eventBus().send(EventBusConstants.EVENT_CONFIG_RESPONSE_PROCESSOR, event); // sending event to ConfigResponseProcessor for further process related to config.
                    }

                    // Process successful responses with result data
                    if (event.getString(STATUS).equalsIgnoreCase(STATUS_SUCCEED) && event.getJsonObject(RESULT) != null)
                    {
                        // Handle MAC scanner responses from runbook plugins
                        if (request == PluginEngineConstants.PluginEngineRequest.RUNBOOK && event.getString(RunbookPlugin.RUNBOOK_PLUGIN_TYPE) != null
                                && event.getString(RunbookPlugin.RUNBOOK_PLUGIN_TYPE).equalsIgnoreCase(Runbook.RunbookPluginType.MAC_SCANNER.getName()))
                        {
                            // Extract the result and remove it from the event (to avoid duplicate processing)
                            var result = event.getJsonObject(RESULT);
                            event.remove(RESULT);

                            // Get the list of discovered objects from the result
                            var objects = result.getJsonArray(OBJECTS);

                            if (objects != null && !objects.isEmpty())
                            {
                                // Prepare a list to store the enriched MAC address items
                                var items = new JsonArray();
                                var objectIP = event.getString(AIOpsObject.OBJECT_IP);

                                // Process each discovered object
                                for (var index = 0; index < objects.size(); index++)
                                {
                                    var object = objects.getJsonObject(index);

                                    // Handle different formats of MAC address data
                                    if (CommonUtil.isNotNullOrEmpty(object.getString(NMSConstants.SYSTEM_NETWORK_INTERFACE_MAC_ADDRESS)))
                                    {
                                        // Enrich system network interface MAC addresses
                                        enrich(items, object, SYSTEM_NETWORK_INTERFACE_MAC_ADDRESS, SYSTEM_NETWORK_INTERFACE_IP_ADDRESS, SYSTEM_NETWORK_INTERFACE, SYSTEM_NETWORK_INTERFACE_NAME, objectIP);
                                    }
                                    else if (CommonUtil.isNotNullOrEmpty(object.getString(INTERFACE_ADDRESS)))
                                    {
                                        // Enrich interface MAC addresses
                                        enrich(items, object, INTERFACE_ADDRESS, INTERFACE_IP, INTERFACE_INDEX, INTERFACE_NAME, objectIP);
                                    }
                                }

                                // Save the enriched MAC address items to the database
                                save(items);
                            }
                            else
                            {
                                LOGGER.info(String.format("mac scanning response is null or empty for object %s ", event.getString(AIOpsObject.OBJECT_NAME)));
                            }
                        }

                        // Handle network metric responses
                        else if (request == PluginEngineConstants.PluginEngineRequest.NETWORK_METRIC)
                        {
                            // Extract the result and remove it from the event
                            var result = event.getJsonObject(RESULT);
                            event.remove(RESULT);

                            // Process the objects array if it exists
                            if (result.getJsonArray(OBJECTS) != null)
                            {
                                var objects = result.getJsonArray(OBJECTS);
                                event.remove(OBJECTS);

                                // Process each object in the array
                                for (var index = 0; index < objects.size(); index++)
                                {
                                    // Create a new context by merging with the event
                                    var context = new JsonObject().mergeIn(event);
                                    var object = objects.getJsonObject(index);
                                    var valid = true;

                                    // Get the OID group from the event
                                    var oidGroup = event.getJsonObject(NMSConstants.SNMP_OID_GROUP);

                                    // Check for invalid OIDs in the object
                                    if (object.containsKey(NMSConstants.SNMP_OID_GROUP_INVALID_OIDS)) // invalid oids probing
                                    {
                                        // Get all OIDs from the group
                                        var probes = oidGroup.getJsonObject(NMSConstants.SNMP_OID_GROUP_OIDS).stream().map(Map.Entry::getKey).collect(Collectors.toList());

                                        // Remove invalid OIDs from the list
                                        object.getJsonArray(NMSConstants.SNMP_OID_GROUP_INVALID_OIDS).forEach(invalidOID -> probes.remove(CommonUtil.getString(invalidOID)));

                                        // If all OIDs are invalid, mark the object as invalid
                                        if (probes.isEmpty())
                                        {
                                            valid = false;
                                        }
                                    }

                                    // Only process valid objects
                                    if (valid)
                                    {
                                        // If the object has an OID group ID, set up metric properties
                                        if (object.getString(NMSConstants.SNMP_OID_GROUP_ID) != null)
                                        {
                                            // Set the metric name from the OID group
                                            context.put(Metric.METRIC_NAME, SNMPTemplateOIDGroupCacheStore.getStore()
                                                    .getGroupName(object.getString(NMSConstants.SNMP_OID_GROUP_ID)));

                                            // Set polling time if specified in the OID group
                                            if (oidGroup.containsKey(NMSConstants.SNMP_OID_GROUP_POLLING_TIME))
                                            {
                                                context.put(Metric.METRIC_POLLING_TIME, oidGroup.getInteger(NMSConstants.SNMP_OID_GROUP_POLLING_TIME));
                                            }

                                            // Set timeout if specified in the OID group
                                            if (oidGroup.containsKey(NMSConstants.SNMP_OID_GROUP_TIMEOUT))
                                            {
                                                object.put(TIMEOUT, oidGroup.getInteger(NMSConstants.SNMP_OID_GROUP_TIMEOUT));
                                            }

                                            // Set state if specified in the OID group
                                            if (oidGroup.containsKey(NMSConstants.SNMP_OID_GROUP_STATE))
                                            {
                                                context.put(Metric.METRIC_STATE, oidGroup.getString(NMSConstants.SNMP_OID_GROUP_STATE));
                                            }
                                        }

                                        // Set up the metric context with all required properties
                                        context.put(Metric.METRIC_PLUGIN, object.remove(Metric.METRIC_PLUGIN))
                                                .put(Metric.METRIC_CONTEXT, object)
                                                .put(Metric.METRIC_OBJECT, context.getLong(ID))
                                                .put(Metric.METRIC_CREDENTIAL_PROFILE, context.getLong(AIOpsObject.OBJECT_CREDENTIAL_PROFILE));

                                        // Send the metric to be provisioned
                                        vertx.eventBus().send(EventBusConstants.EVENT_METRIC_PROVISION, context);
                                    }
                                    else
                                    {
                                        // Log a warning for invalid metrics
                                        LOGGER.warn(String.format("failed to provision metric %s for object %s , reason : %s", SNMPTemplateOIDGroupCacheStore.getStore()
                                                .getGroupName(object.getString(NMSConstants.SNMP_OID_GROUP_ID)), event.getString(AIOpsObject.OBJECT_NAME), ErrorMessageConstants.SNMP_OID_GROUP_INVALID));
                                    }
                                }
                            }
                            else
                            {
                                // Log if no valid metrics were found
                                LOGGER.info(String.format("no valid network device metrics found for %s object...", event.getString(AIOpsObject.OBJECT_NAME)));
                            }
                        }
                    }
                }

                if (request == PluginEngineConstants.PluginEngineRequest.STREAMING) // send to clear streaming context
                {
                    vertx.eventBus().publish(EventBusConstants.EVENT_STREAMING_STOP, event);
                }
            }

            catch (Exception exception)
            {
                LOGGER.error(exception);
            }
        }).exceptionHandler(LOGGER::error);

        // Set up a consumer to handle MAC scanner responses
        vertx.eventBus().<JsonObject>localConsumer(EventBusConstants.EVENT_MAC_SCANNER_RESPONSE, message ->
        {
            // Extract the event from the message
            var event = message.body();

            try
            {
                // Get the result object from the event
                var result = event.getJsonObject(RESULT);

                if (result != null)
                {
                    // Get the array of discovered objects
                    var objects = result.getJsonArray(NMSConstants.OBJECTS);

                    if (objects != null && !objects.isEmpty())
                    {
                        // Prepare a list to store MAC scanner items
                        var items = new JsonArray();

                        // Process each discovered object
                        for (var index = 0; index < objects.size(); index++)
                        {
                            var object = objects.getJsonObject(index);

                            // Only process objects with valid MAC addresses that aren't already in the store
                            if (CommonUtil.isNotNullOrEmpty(object.getString(NMSConstants.INTERFACE_ADDRESS)) && MACScannerConfigStore.getStore().getItemByMACAddress(object.getString(NMSConstants.INTERFACE_ADDRESS)) == null)
                            {
                                // Create a new MAC scanner item with interface, address, and device IP
                                var item = new JsonObject()
                                        .put(MACScanner.MAC_SCANNER_INTERFACE, object.getString(NMSConstants.INTERFACE_NAME))
                                        .put(MACScanner.MAC_SCANNER_ADDRESS, object.getString(NMSConstants.INTERFACE_ADDRESS))
                                        .put(MACScanner.MAC_SCANNER_DEVICE_IP_ADDRESS, event.getString(AIOpsObject.OBJECT_IP));

                                // Add interface IP if available
                                if (CommonUtil.isNotNullOrEmpty(object.getString(INTERFACE_IP)))
                                {
                                    item.put(MACScanner.MAC_SCANNER_INTERFACE_IP_ADDRESS, object.getString(NMSConstants.INTERFACE_IP));
                                }

                                // Add the item to the list
                                items.add(item);

                                // If there's a local IP, create a duplicate item with that IP
                                if (object.containsKey(INTERFACE_LOCAL_IP))
                                {
                                    items.add(item.copy().put(MACScanner.MAC_SCANNER_INTERFACE_IP_ADDRESS, object.getString(INTERFACE_LOCAL_IP)));
                                }
                            }
                        }

                        // Save the MAC scanner items to the database
                        save(items);
                    }
                }
            }
            catch (Exception exception)
            {
                LOGGER.error(exception);
            }
        }).exceptionHandler(LOGGER::error);
    }

    /**
     * Enriches MAC address data and adds it to the items array.
     * This method extracts MAC address information from an object and creates
     * a new item with the MAC address, interface, and IP address information.
     *
     * @param items            The JSON array to add the enriched item to
     * @param object           The JSON object containing the MAC address data
     * @param macAddressKey    The key for the MAC address in the object
     * @param ipAddressKey     The key for the IP address in the object
     * @param interfaceKey     The key for the interface in the object
     * @param interfaceNameKey The key for the interface name in the object
     * @param objectIP         The IP address of the parent object
     */
    private void enrich(JsonArray items, JsonObject object, String macAddressKey, String ipAddressKey, String interfaceKey, String interfaceNameKey, String objectIP)
    {
        var macAddress = object.getString(macAddressKey);

        if (CommonUtil.isNotNullOrEmpty(macAddress) && MACScannerConfigStore.getStore().getItemByMACAddress(macAddress) == null)
        {
            if (CommonUtil.isNotNullOrEmpty(object.getString(ipAddressKey)))
            {
                items.add(new JsonObject().put(MACScanner.MAC_SCANNER_INTERFACE, CommonUtil.isNotNullOrEmpty(object.getString(interfaceNameKey)) ? object.getString(interfaceNameKey) : object.getString(interfaceKey))
                        .put(MACScanner.MAC_SCANNER_ADDRESS, macAddress)
                        .put(MACScanner.MAC_SCANNER_DEVICE_IP_ADDRESS, objectIP).put(MACScanner.MAC_SCANNER_INTERFACE_IP_ADDRESS, object.getString(ipAddressKey)));
            }

            else
            {
                items.add(new JsonObject().put(MACScanner.MAC_SCANNER_INTERFACE, CommonUtil.isNotNullOrEmpty(object.getString(interfaceNameKey)) ? object.getString(interfaceNameKey) : object.getString(interfaceKey))
                        .put(MACScanner.MAC_SCANNER_ADDRESS, macAddress)
                        .put(MACScanner.MAC_SCANNER_DEVICE_IP_ADDRESS, objectIP));
            }
        }
    }

    /**
     * Sends an event to the appropriate destination based on its type.
     * This method routes events to different event bus addresses based on the event type
     * (metric poll, plugin engine, topology) and the bootstrap type.
     *
     * @param event The event to send
     */
    private void send(JsonObject event)
    {
        switch (event.getString(EventBusConstants.EVENT_TYPE))
        {
            case EventBusConstants.EVENT_METRIC_POLL ->
            {
                if (CommonUtil.isNotNullOrEmpty(event.getString(STATUS)) && event.getString(STATUS).equalsIgnoreCase(STATUS_SUCCEED))
                {
                    NMSConstants.validateJSONObject(event);
                }

                if (MotadataConfigUtil.devMode())
                {
                    vertx.eventBus().publish(Bootstrap.bootstrapType() == GlobalConstants.BootstrapType.COLLECTOR || Bootstrap.getInstallationMode().equalsIgnoreCase(InstallationMode.SECONDARY.name()) ? EventBusConstants.EVENT_REMOTE : EventBusConstants.EVENT_METRIC_POLL_RESPONSE, event);
                }
                else
                {
                    vertx.eventBus().send(Bootstrap.bootstrapType() == GlobalConstants.BootstrapType.COLLECTOR || Bootstrap.getInstallationMode().equalsIgnoreCase(InstallationMode.SECONDARY.name()) ? EventBusConstants.EVENT_REMOTE : EventBusConstants.EVENT_METRIC_POLL_RESPONSE, event);
                }
            }

            case EventBusConstants.EVENT_PLUGIN_ENGINE ->
                    vertx.eventBus().send(Bootstrap.bootstrapType() == GlobalConstants.BootstrapType.COLLECTOR || Bootstrap.getInstallationMode().equalsIgnoreCase(InstallationMode.SECONDARY.name()) ? EventBusConstants.EVENT_REMOTE : EventBusConstants.EVENT_PLUGIN_ENGINE_RESPONSE, event);

            case EventBusConstants.EVENT_TOPOLOGY ->
            {
                if (CommonUtil.isNotNullOrEmpty(event.getString(STATUS)) && event.getString(STATUS).equalsIgnoreCase(STATUS_SUCCEED))
                {
                    NMSConstants.validateJSONArray(event);
                }

                vertx.eventBus().send(Bootstrap.bootstrapType() == GlobalConstants.BootstrapType.COLLECTOR || Bootstrap.getInstallationMode().equalsIgnoreCase(InstallationMode.SECONDARY.name()) ? EventBusConstants.EVENT_REMOTE : EventBusConstants.EVENT_TOPOLOGY_RESPONSE, event);
            }

            default ->
            {
                // do nothing
            }
        }
    }

    /**
     * Saves MAC scanner items to the database.
     * This method saves the items to the MAC scanner table in the database
     * and updates the MAC scanner config store with the saved items.
     *
     * @param items The JSON array of items to save
     */
    private void save(JsonArray items)
    {
        if (!items.isEmpty())
        {
            Bootstrap.configDBService().saveAll(DBConstants.TBL_MAC_SCANNER, items, DEFAULT_USER, SYSTEM_REMOTE_ADDRESS,
                    result ->
                    {
                        if (result.succeeded())
                        {
                            MACScannerConfigStore.getStore().updateItems(result.result());
                        }
                        else
                        {
                            LOGGER.error(result.cause());
                        }
                    });
        }
    }

    /**
     * Stops the PluginEngineResponseProcessor verticle.
     * This method stops the event engine and completes the promise.
     *
     * @param promise Promise to be completed when the verticle is stopped
     * @throws Exception If an error occurs during shutdown
     */
    @Override
    public void stop(Promise<Void> promise) throws Exception
    {
        eventEngine.stop(vertx, promise);
    }
}
