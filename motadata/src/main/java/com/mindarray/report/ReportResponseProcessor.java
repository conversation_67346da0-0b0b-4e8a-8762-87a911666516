/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

/*

 *
 *  Change Logs:
 *  Date			Author			Notes
 *  27-Feb-2025		Chopra Deven	MOTADATA-5143: Added support for forecast report of virtualization hosts and VMs/instance level report.
 *  28-Feb-2025		Darshan Parmar		MOTADATA-5215: SonarQube Suggestions Resolution
 */

package com.mindarray.report;

import com.mindarray.GlobalConstants;
import com.mindarray.api.Report;
import com.mindarray.eventbus.EventBusConstants;
import com.mindarray.store.ReportConfigStore;
import com.mindarray.util.CommonUtil;
import com.mindarray.util.DateTimeUtil;
import com.mindarray.util.Logger;
import com.mindarray.visualization.VisualizationConstants;
import io.vertx.core.AbstractVerticle;
import io.vertx.core.Promise;
import io.vertx.core.buffer.Buffer;
import io.vertx.core.eventbus.Message;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;

import java.util.*;
import java.util.stream.Collectors;

import static com.mindarray.GlobalConstants.*;
import static com.mindarray.api.APIConstants.SESSION_ID;
import static com.mindarray.api.Report.REPORT_TYPE;
import static com.mindarray.eventbus.EventBusConstants.EVENT_CONTEXT;
import static com.mindarray.eventbus.EventBusConstants.EVENT_TYPE;
import static com.mindarray.report.ReportConstants.*;
import static com.mindarray.visualization.VisualizationConstants.*;

public class ReportResponseProcessor extends AbstractVerticle
{
    private static final Logger LOGGER = new Logger(ReportResponseProcessor.class, GlobalConstants.MOTADATA_REPORTING, "Report Response Processor");

    @Override
    public void start(Promise<Void> promise) throws Exception
    {
        vertx.eventBus().localConsumer(config().getString(EVENT_TYPE), this::process);

        promise.complete();
    }

    /**
     * Added check mark as, we will be getting null item when report engine's process killed forcibly
     *
     * @param message Message
     */
    private void process(Message<JsonObject> message)
    {
        try
        {
            var event = message.body();

            var report = ReportConfigStore.getStore().getItem(event.getLong(ID));

            var type = report.getJsonObject(Report.REPORT_CONTEXT).getString(VISUALIZATION_TYPE, EMPTY_VALUE);

            if (event.containsKey(Report.REPORT_TYPE) && event.getString(REPORT_TYPE).equalsIgnoreCase(REPORT_TYPE_CAPACITY_PLANNING))
            {
                type = ReportVisualizationType.MULTIPLE_CAPACITY_PLANNING_REPORT.getName();
            }

            switch (ReportVisualizationType.valueOfName(type))
            {
                case MULTIPLE_TOP_N -> composeMultiTopNResponse(event, report);

                case MULTIPLE_AVAILABILITY -> composeMultiAvailabilityResponse(event, report);

                case MULTIPLE_CAPACITY_PLANNING_REPORT -> composeMultiCapacityPlaningReportResponse(event, report);

                case FORECAST -> composeForecastResponse(event, report);

                case UNHEALTHY_MONITORS -> composeUnhealthyMonitorListResponse(event, report);

                case MULTIPLE_WIDGETS ->
                {
                    // TODO: To Be Implemented
                }

                default ->
                {
                    //ignore
                }
            }
        }

        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }

    private void composeMultiTopNResponse(JsonObject context, JsonObject report)
    {
        try
        {
            // combine result
            List<JsonObject> rows = new ArrayList<>();

            var alias = report.getJsonObject(Report.REPORT_CONTEXT).getJsonObject(ALIAS);

            for (var item : context.getJsonArray(EVENT_CONTEXT))
            {
                var result = VisualizationConstants.unpack(Buffer.buffer(((JsonObject) item).getBinary(GlobalConstants.RESULT)), LOGGER, false, null, false, true);

                if (result.containsKey(RESULT))
                {
                    var values = result.getJsonArray(RESULT);

                    for (var index = 0; index < values.size(); index++)
                    {
                        rows.add(new JsonObject(values.getJsonObject(index).getMap().entrySet().stream().collect(Collectors.toMap(k -> alias.getString(k.getKey(), k.getKey()), Map.Entry::getValue))));
                    }
                }
            }

            var template = report.getJsonObject(Report.REPORT_CONTEXT).getJsonObject(TEMPLATE);

            // sort result and find top N
            var properties = template.getJsonObject(VisualizationConstants.VISUALIZATION_PROPERTIES).getJsonObject("grid").getJsonObject(SORTING);

            rows.sort(Comparator.comparing(m -> m.getLong(properties.getString(COLUMN)), Comparator.nullsLast(Comparator.reverseOrder())));

            if (rows.size() > properties.getInteger(LIMIT))
            {
                rows = rows.subList(0, properties.getInteger(LIMIT));
            }

            // Convert rows to column result set
            var result = new JsonObject();

            for (var row : rows)
            {
                row.getMap().forEach((key, value) ->
                        ((JsonArray) result.getMap().computeIfAbsent(key, val -> new JsonArray())).add(value));
            }

            // encode and send

            EventBusConstants.publish(context.getString(SESSION_ID), EventBusConstants.UI_ACTION_VISUALIZATION_RENDER, template.put(EventBusConstants.EVENT_COMPRESSION_TYPE, COMPRESSION_TYPE_ZERO)
                    .put(RESULT, VisualizationConstants.packGridResult(result, VisualizationConstants.VisualizationCategoryOrdinal.VISUALIZATION_GRID.ordinal(), context.getLong(QUERY_ID), -1).getBytes()));
        }

        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }

    private void composeMultiAvailabilityResponse(JsonObject context, JsonObject report)
    {
        try
        {
            var rows = new ArrayList<JsonObject>();

            var alias = report.getJsonObject(Report.REPORT_CONTEXT).getJsonObject(ALIAS);

            for (var item : context.getJsonArray(EVENT_CONTEXT))
            {
                var result = VisualizationConstants.unpack(Buffer.buffer(((JsonObject) item).getBinary(GlobalConstants.RESULT)), LOGGER, false, null, false, true);

                if (result.containsKey(RESULT))
                {
                    var columns = new JsonObject(VisualizationConstants.MULTI_AVAILABILITY_COLUMNS.replace("#counter#", ((JsonObject) item).getJsonArray(VISUALIZATION_RESULT_BY).getString(0)));

                    var values = result.getJsonArray(RESULT);

                    for (var index = 0; index < values.size(); index++)
                    {
                        var row = values.getJsonObject(index);

                        var iterator = columns.stream().iterator();

                        while (iterator.hasNext())
                        {
                            var entry = iterator.next();

                            var column = entry.getKey();

                            var dataType = CommonUtil.getInteger(entry.getValue().toString());

                            if (!row.containsKey(column))
                            {
                                if (dataType == DATA_TYPE_FLOAT32)
                                {
                                    row.put(column, Float.MIN_VALUE);
                                }
                                else if (dataType == DATA_TYPE_INT64)
                                {
                                    row.put(column, Long.MIN_VALUE);
                                }
                                else if (dataType == DATA_TYPE_INT32)
                                {
                                    row.put(column, Integer.MIN_VALUE);
                                }
                                else
                                {
                                    row.put(column, EMPTY_VALUE);
                                }
                            }
                        }

                        rows.add(new JsonObject(values.getJsonObject(index).getMap().entrySet().stream().collect(Collectors.toMap(k -> alias.getString(k.getKey(), k.getKey()), Map.Entry::getValue))));
                    }
                }
            }

            var template = report.getJsonObject(Report.REPORT_CONTEXT).getJsonObject(TEMPLATE);

            var result = new JsonObject();

            for (var row : rows)
            {
                row.getMap().forEach((key, value) ->
                        ((JsonArray) result.getMap().computeIfAbsent(key, val -> new JsonArray())).add(value));
            }


            EventBusConstants.publish(context.getString(SESSION_ID), EventBusConstants.UI_ACTION_VISUALIZATION_RENDER, template.put(EventBusConstants.EVENT_COMPRESSION_TYPE, COMPRESSION_TYPE_ZERO)
                    .put(RESULT, VisualizationConstants.packGridResult(result, VisualizationConstants.VisualizationCategoryOrdinal.VISUALIZATION_GRID.ordinal(), context.getLong(QUERY_ID), -1).getBytes()));
        }

        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }

    /**
     * Method used to calculate cpu, memory and disk percentage and based on this we will decide whether monitors are in idle, overutilized or underutilised
     *
     * @param context Context
     * @param report  Report
     */
    private void composeMultiCapacityPlaningReportResponse(JsonObject context, JsonObject report)
    {
        try
        {
            var rows = new ArrayList<JsonObject>();

            var result = new JsonObject();

            var template = report.getJsonObject(Report.REPORT_CONTEXT).getJsonObject(TEMPLATE);

            var threshold = report.getJsonObject(Report.REPORT_CONTEXT).getJsonObject(REPORT_CAPACITY_PLANNING_THRESHOLD_CRITERIA);

            var criteria = report.getJsonObject(Report.REPORT_CONTEXT).getJsonObject(REPORT_CAPACITY_PLANNING_CRITERIA);

            var columns = report.getJsonObject(Report.REPORT_CONTEXT).getJsonArray(REPORT_CAPACITY_PLANNING_COLUMN);

            var objects = new HashSet<Integer>();

            Map<String, Map<Integer, JsonObject>> stats = new HashMap<>(); // This variable is used to maintain stats for CPU, Memory and Disk

            for (var item : context.getJsonArray(EVENT_CONTEXT))
            {
                var response = VisualizationConstants.unpack(Buffer.buffer(((JsonObject) item).getBinary(GlobalConstants.RESULT)), LOGGER, false, null, false, true);

                if (response.containsKey(RESULT))
                {
                    var records = response.getJsonArray(RESULT);

                    for (var index = 0; index < records.size(); index++)
                    {
                        rows.add(records.getJsonObject(index));
                    }
                }
            }

            if (!rows.isEmpty())
            {
                stats.computeIfAbsent(REPORT_CPU_STATS, value -> new HashMap<>());

                stats.computeIfAbsent(REPORT_MEMORY_STATS, value -> new HashMap<>());

                stats.computeIfAbsent(REPORT_DISK_STATS, value -> new HashMap<>());

                for (var row : rows)
                {
                    row.getMap().forEach((key, value) ->
                    {

                        if ((!key.equalsIgnoreCase(TIME_STAMP) && (CommonUtil.getLong(value) != Long.MIN_VALUE || CommonUtil.getFloat(value) != -Float.MAX_VALUE))
                                && (key.contains("cpu") || key.contains("memory") || key.contains("disk")))
                        {
                            var tokens = key.split(CARET_SEPARATOR_WITH_ESCAPE);

                            if (tokens.length == 3)
                            {
                                JsonObject item = null;

                                if (key.contains("cpu"))
                                {
                                    item = stats.get(REPORT_CPU_STATS).computeIfAbsent(CommonUtil.getInteger(tokens[0]), val -> new JsonObject());
                                }
                                else if (key.contains("memory"))
                                {
                                    item = stats.get(REPORT_MEMORY_STATS).computeIfAbsent(CommonUtil.getInteger(tokens[0]), val -> new JsonObject());
                                }
                                else if (key.contains("disk"))
                                {
                                    item = stats.get(REPORT_DISK_STATS).computeIfAbsent(CommonUtil.getInteger(tokens[0]), val -> new JsonObject());
                                }

                                if (item != null)
                                {
                                    item.put(COUNT, (item.getInteger(COUNT, 0) + 1));

                                    if (evaluate(threshold.getDouble(tokens[1]), threshold.getString(tokens[1] + ".condition"), value))
                                    {
                                        item.put(THRESHOLD_COUNT, (item.getInteger(THRESHOLD_COUNT, 0) + 1));
                                    }

                                    objects.add(CommonUtil.getInteger(tokens[0]));
                                }
                            }
                            else
                            {
                                LOGGER.warn("Invalid key : " + key);
                            }
                        }
                    });
                }
            }

            if (!objects.isEmpty())
            {
                for (var object : objects)
                {
                    var cpu = calculate(stats.get(REPORT_CPU_STATS), object);

                    var memory = calculate(stats.get(REPORT_MEMORY_STATS), object);

                    var disk = calculate(stats.get(REPORT_DISK_STATS), object);

                    if (evaluate(criteria.getDouble(columns.getString(0)), criteria.getString(columns.getString(0) + ".condition"), cpu)
                            || evaluate(criteria.getDouble(columns.getString(1)), criteria.getString(columns.getString(1) + ".condition"), memory)
                            || evaluate(criteria.getDouble(columns.getString(2)), criteria.getString(columns.getString(2) + ".condition"), disk))
                    {
                        result.getMap().computeIfAbsent(VisualizationGrouping.MONITOR.getName(), value -> new JsonArray());

                        result.getMap().computeIfAbsent(REPORT_CPU_UTILIZATION, value -> new JsonArray());

                        result.getMap().computeIfAbsent(REPORT_MEMORY_UTILIZATION, value -> new JsonArray());

                        result.getMap().computeIfAbsent(REPORT_DISK_UTILIZATION, value -> new JsonArray());

                        result.getJsonArray(VisualizationGrouping.MONITOR.getName()).add(object);

                        result.getJsonArray(REPORT_CPU_UTILIZATION).add(String.format(REPORT_CAPACITY_PLANNING_MESSAGE, cpu, "%", "CPU Utilization", threshold.getString(columns.getString(0) + ".condition"), threshold.getString(columns.getString(0)), "%"));

                        result.getJsonArray(REPORT_MEMORY_UTILIZATION).add(String.format(REPORT_CAPACITY_PLANNING_MESSAGE, memory, "%", "Memory Utilization", threshold.getString(columns.getString(1) + ".condition"), threshold.getString(columns.getString(1)), "%"));

                        result.getJsonArray(REPORT_DISK_UTILIZATION).add(String.format(REPORT_CAPACITY_PLANNING_MESSAGE, disk, "%", "Disk Utilization", threshold.getString(columns.getString(2) + ".condition"), threshold.getString(columns.getString(2)), "%"));
                    }
                }
            }
            else
            {
                LOGGER.warn("Qualified monitors not found !!");
            }

            EventBusConstants.publish(context.getString(SESSION_ID), EventBusConstants.UI_ACTION_VISUALIZATION_RENDER, template.put(EventBusConstants.EVENT_COMPRESSION_TYPE, COMPRESSION_TYPE_ZERO)
                    .put(RESULT, VisualizationConstants.packGridResult(result, VisualizationConstants.VisualizationCategoryOrdinal.VISUALIZATION_GRID.ordinal(), context.getLong(QUERY_ID), -1).getBytes()));
        }

        catch (Exception exception)
        {
            LOGGER.error(exception);

            EventBusConstants.publish(context.getString(SESSION_ID), EventBusConstants.UI_ACTION_VISUALIZATION_RENDER, context.put(EventBusConstants.EVENT_COMPRESSION_TYPE, COMPRESSION_TYPE_ZERO).put(RESULT, packError(String.format("Report : %s, Got exception while generating report %s", report.getString(Report.REPORT_NAME), exception.getMessage()), context.getLong(QUERY_ID), -1)));
        }
    }

    /**
     * Method used to forecast cpu, memory and disk percentage and based on this we will decide when will monitor's cpu, memory and disk percentage will go more than 80, 90, 100
     *
     * @param context Context
     * @param report  Report
     */
    private void composeForecastResponse(JsonObject context, JsonObject report)
    {
        try
        {
            var result = new JsonObject();

            var reportContext = report.getJsonObject(Report.REPORT_CONTEXT);

            var template = reportContext.getJsonObject(TEMPLATE);

            var conditions = reportContext.getJsonArray(REPORT_FORECAST_THRESHOLD_CONDITIONS);

            var columns = reportContext.getJsonArray(REPORT_FORECAST_COLUMN);

            var stats = new HashMap<String, JsonObject>();

            var predictionIndex = 0;

            for (var j = 0; j < context.getJsonArray(EVENT_CONTEXT).size(); j++)
            {
                var rows = new ArrayList<JsonObject>();

                var eventContext = context.getJsonArray(EVENT_CONTEXT).getJsonObject(j);

                var response = VisualizationConstants.unpack(Buffer.buffer((eventContext).getBinary(GlobalConstants.RESULT)), LOGGER, false, eventContext.containsKey(QUERY_CONTEXT) ? eventContext.getJsonObject(QUERY_CONTEXT) : null, false, true);

                predictionIndex = response.containsKey(PREDICTION_INDEX) ? response.getInteger(PREDICTION_INDEX) : 0;

                if (response.containsKey(RESULT))
                {
                    var records = response.getJsonArray(RESULT);

                    for (var i = 0; i < records.size(); i++)
                    {
                        rows.add(records.getJsonObject(i));
                    }
                }

                if (!rows.isEmpty())
                {

                    /*
                     *  first we check for the error, if monitor have insufficient datapoint than we will iterate on row.
                     *  to check monitor specific error we need to extract monitor ID from the result (row)
                     */

                    var record = rows.getFirst();

                    record.getMap().forEach((key, value) ->
                    {
                        if (!key.equalsIgnoreCase(TIME_STAMP))
                        {
                            var tokens = key.split(CARET_SEPARATOR_WITH_ESCAPE);

                            if (tokens.length == 3)
                            {
                                if (response.containsKey(ERROR) && response.getString(ERROR).contains("group: " + tokens[0]) && response.containsKey(RESULT)) // check for the insufficient data error
                                {
                                    stats.put(tokens[0], null);
                                }
                                else
                                {
                                    stats.put(tokens[0], new JsonObject());
                                }
                            }
                        }
                    });

                    // predictionIndex represent forecasted data and (predictionIndex - 2) represent recently saved data in db which used to define "Already Reached!" state.
                    for (var index = predictionIndex - 2; index < rows.size(); index++)
                    {
                        var row = rows.get(index);

                        row.getMap().forEach((key, value) ->
                        {
                            if (!key.equalsIgnoreCase(TIME_STAMP) && (CommonUtil.getLong(value) != Long.MIN_VALUE || CommonUtil.getFloat(value) != -Float.MAX_VALUE) && !key.contains("upperbound") && !key.contains("lowerbound"))
                            {
                                var tokens = key.split(CARET_SEPARATOR_WITH_ESCAPE);

                                if (tokens.length == 3)
                                {
                                    var prediction = stats.get(tokens[0]);

                                    if (prediction != null)
                                    {
                                        for (var i = 0; i < conditions.size(); i++)
                                        {
                                            var condition = conditions.getJsonObject(i);

                                            if (prediction.size() < 3 && !prediction.containsKey(tokens[1] + DOT_SEPARATOR + condition.getInteger(THRESHOLD)) && evaluate(condition.getInteger(THRESHOLD), condition.getString(CONDITION), value))
                                            {
                                                if (row.getLong(TIMESTAMP) <= System.currentTimeMillis())
                                                {
                                                    prediction.put(tokens[1] + DOT_SEPARATOR + condition.getInteger(THRESHOLD), "Already Reached");
                                                }
                                                else
                                                {
                                                    prediction.put(tokens[1] + DOT_SEPARATOR + condition.getInteger(THRESHOLD), DateTimeUtil.millisToDuration(row.getLong(TIMESTAMP) - System.currentTimeMillis()));
                                                }
                                            }
                                        }
                                    }
                                }
                                else
                                {
                                    LOGGER.warn("Invalid key : " + key);
                                }

                            }
                        });

                    }

                }

                if (!stats.isEmpty())
                {
                    if (result.isEmpty())   // for the first time, we will create columns in result
                    {
                        if (reportContext.containsKey(INSTANCE)) // in case of instance report, need to create column for instance name
                        {
                            result.getMap().computeIfAbsent(INSTANCE, value -> new JsonArray());
                        }

                        result.getMap().computeIfAbsent(VisualizationGrouping.MONITOR.getName(), value -> new JsonArray());

                        for (var column : columns)
                        {
                            result.getMap().computeIfAbsent(column.toString(), value -> new JsonArray());
                        }
                    }

                    for (var entry : stats.entrySet())
                    {
                        if (entry.getKey().contains(GlobalConstants.GROUP_SEPARATOR)) // in case of instance report, need to split monitor id and instance name
                        {
                            result.getJsonArray(VisualizationGrouping.MONITOR.getName()).add(entry.getKey().split(GlobalConstants.GROUP_SEPARATOR)[0]);

                            result.getJsonArray(INSTANCE).add(entry.getKey().split(GlobalConstants.GROUP_SEPARATOR)[1]);
                        }
                        else
                        {
                            result.getJsonArray(VisualizationGrouping.MONITOR.getName()).add(entry.getKey());
                        }

                        for (var column : columns)
                        {
                            if (entry.getValue() == null)   // in case of insufficient data points
                            {
                                result.getJsonArray(column.toString()).add("Insufficient Data");
                            }
                            else if (entry.getValue().isEmpty() || (!entry.getValue().isEmpty() && !entry.getValue().containsKey(column.toString()))) // in case of no data found for the forecast criteria or in case of no data found any of column for the forecast criteria
                            {
                                result.getJsonArray(column.toString()).add("Stable Growth");
                            }
                            else if (!entry.getValue().isEmpty() && entry.getValue().containsKey(column.toString()))    // in case of we found duration than need to convert into month, day, hour
                            {
                                result.getJsonArray(column.toString()).add(entry.getValue().getString(column.toString()));
                            }
                        }
                    }
                }
                else
                {
                    LOGGER.warn("Qualified monitors not found !!");
                }

                stats.clear();
            }

            if (CommonUtil.traceEnabled())
            {
                LOGGER.trace("Publishing result of report ID : " + (context.getLong(ID) + " | Size of result : " + result.getJsonArray(VisualizationGrouping.MONITOR.getName()).size()));
            }

            EventBusConstants.publish(context.getString(SESSION_ID), EventBusConstants.UI_ACTION_VISUALIZATION_RENDER, template.put(EventBusConstants.EVENT_COMPRESSION_TYPE, COMPRESSION_TYPE_ZERO)
                    .put(RESULT, VisualizationConstants.packGridResult(result, VisualizationConstants.VisualizationCategoryOrdinal.VISUALIZATION_GRID.ordinal(), context.getLong(QUERY_ID), -1).getBytes()));

        }

        catch (Exception exception)
        {
            LOGGER.error(exception);

            EventBusConstants.publish(context.getString(SESSION_ID), EventBusConstants.UI_ACTION_VISUALIZATION_RENDER, context.put(EventBusConstants.EVENT_COMPRESSION_TYPE, COMPRESSION_TYPE_ZERO).put(RESULT, packError(String.format("Report : %s, Got exception while generating report %s", report.getString(Report.REPORT_NAME), exception.getMessage()), context.getLong(QUERY_ID), -1)));
        }
    }

    /**
     * @param context Context
     * @param report  Report
     */
    private void composeUnhealthyMonitorListResponse(JsonObject context, JsonObject report)
    {
        try
        {
            var result = new JsonObject();

            var template = report.getJsonObject(Report.REPORT_CONTEXT).getJsonObject(TEMPLATE);

            if (context.containsKey(EVENT_CONTEXT) && !context.getJsonArray(EVENT_CONTEXT).isEmpty())
            {
                var eventContext = context.getJsonArray(EVENT_CONTEXT);

                // need to covert row-wise result to column-wise result to packGridResult
                for (var i = 0; i < eventContext.size(); i++)
                {
                    eventContext.getJsonObject(i).forEach(entry -> ((JsonArray) result.getMap().computeIfAbsent(entry.getKey(), value -> new JsonArray())).add(entry.getValue()));
                }

            }

            EventBusConstants.publish(context.getString(SESSION_ID), EventBusConstants.UI_ACTION_VISUALIZATION_RENDER, template.put(EventBusConstants.EVENT_COMPRESSION_TYPE, COMPRESSION_TYPE_ZERO)
                    .put(RESULT, VisualizationConstants.packGridResult(result, VisualizationConstants.VisualizationCategoryOrdinal.VISUALIZATION_GRID.ordinal(), context.getLong(QUERY_ID), -1).getBytes()));

        }

        catch (Exception exception)
        {
            LOGGER.error(exception);

            EventBusConstants.publish(context.getString(SESSION_ID), EventBusConstants.UI_ACTION_VISUALIZATION_RENDER, context.put(EventBusConstants.EVENT_COMPRESSION_TYPE, COMPRESSION_TYPE_ZERO).put(RESULT, packError(String.format("Report : %s, Got exception while generating report %s", report.getString(Report.REPORT_NAME), exception.getMessage()), context.getLong(QUERY_ID), -1)));
        }
    }

    /**
     * Method used to evaluate the value for given condition and threshold
     *
     * @param threshold Threshold
     * @param condition Condition
     * @param value     Value
     * @return Flag
     */
    private boolean evaluate(Object threshold, String condition, Object value)
    {
        return switch (condition)
        {
            case ">" -> (CommonUtil.getDouble(value) > CommonUtil.getDouble(threshold));

            case ">=" -> (CommonUtil.getDouble(value) >= CommonUtil.getDouble(threshold));

            case "=", "==" -> (CommonUtil.getDouble(value) == CommonUtil.getDouble(threshold));

            case "<" -> (CommonUtil.getDouble(value) < CommonUtil.getDouble(threshold));

            case "<=" -> (CommonUtil.getDouble(value) <= CommonUtil.getDouble(threshold));

            default -> false;
        };
    }

    /**
     * @param stats  Stats
     * @param object Object
     * @return Percentage of time value breaches threshold
     */
    private int calculate(Map<Integer, JsonObject> stats, int object)
    {
        var value = 0;

        if (stats.containsKey(object) && stats.get(object).containsKey(THRESHOLD_COUNT) && stats.get(object).getInteger(COUNT) > 0)
        {
            value = Math.round((stats.get(object).getFloat(THRESHOLD_COUNT) * 100) / stats.get(object).getInteger(COUNT));
        }

        return value;
    }
}
