/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

/* Change Logs:
 *   Date          Author              Notes
 *   2025-02-28   <PERSON><PERSON>       Added Support for Log,trap and Flow in Event Stream.
 *   2025-03-03   Chopra Deven       MOTADATA-4973: Converted long object.id into the small object.id in RUNBOOK_WORKLOG request.
 *   2025-03-25   Chopra Deven       MOTADATA-5299: Added more timelines into VisualizationTimeline for forecast policy.
 *   2025-03-06   Pruthviraj         MOTADATA-5331 : netroute.metric and availability added in datastore types and datasource added for netroute
 *   2025-04-17   Chopra Deven       MOTADATA-5827 : Group and Tag available as Column in case of multiple instance types
 *   2025-03-25   Umang Sharma       Added Support for Status Flap Metric.
 * 9-May-2025		Sankalp			MOTADATA-5949: Added template for moxa switch
 *   2025-05-08   Umang Sharma       Added Support Raw in Metric Explorer by removing object.id
 *   2025-05-08   Darshan Parmar     MOTADATA-6084:  HPE Primera Template
 *   2025-05-09   Darshan Parmar     MOTADATA-6055 :  HPE 3PAR Template
 *   2025-05-09		Sankalp			 MOTADATA-5949: Added template for moxa switch
 *   2025-05-21   Darshan Parmar     MOTADATA-6184 :  Dell EMC Unity Template
 *   2025-05-26		Jenil			 MOTADATA-6220: Added template for windows snmp and linux snmp
 *   2025-23-05   Umang Sharma       MOTADATA-6344: Add query.start.time to Visualization Query for capturing current timestamp
 *   2025-05-23   Chopra Deven       MOTADATA-6187: merging "instance.ip" column for join.type = "instance.ip".
 *  June-5-2025     Umang Sharma            Added Support for widget/Alert for Netroute.
 *  June-24-2025    Darshan Parmar   MOTADATA-6502: Added EMAIL_GATEWAY tamplate.
 *  June-25-2025   Umang Sharma      MOTADATA-6585 Added support for instance level tag
 *  June-24-2025    Darshan Parmar   MOTADATA-6591: Added Tanzu Kubernetes tamplate.
 *  June-25-2025   Priyansh Sindhav   MOTADATA-6579 Added support for NSXT
 */

package com.mindarray.visualization;

/**
 * This class defines constants, enums, and utility methods used throughout the visualization system.
 * <p>
 * VisualizationConstants serves as a central repository for:
 * <ul>
 *   <li>String constants for visualization parameters and properties</li>
 *   <li>Enumeration types for visualization categories, data sources, and result types</li>
 *   <li>Utility methods for processing visualization data</li>
 *   <li>Functions for packing and unpacking visualization results</li>
 *   <li>Methods for filtering and qualifying entities for visualization</li>
 * </ul>
 * <p>
 * This class is used by all components in the visualization package to ensure
 * consistent naming, data processing, and result formatting across the system.
 * <p>
 * The class contains several inner enum types that define specific aspects of visualizations:
 * <ul>
 *   <li>{@link VisualizationJoinType} - Defines how data from different sources is joined</li>
 *   <li>{@link VisualizationGrouping} - Defines grouping options for visualization data</li>
 *   <li>{@link VisualizationDataSource} - Defines available data sources for visualizations</li>
 *   <li>{@link VisualizationCategory} - Defines categories of visualizations</li>
 *   <li>{@link VisualizationResultType} - Defines types of visualization results</li>
 *   <li>{@link VisualizationTimeline} - Defines time ranges for visualizations</li>
 * </ul>
 */

import com.mindarray.Bootstrap;
import com.mindarray.ErrorMessageConstants;
import com.mindarray.GlobalConstants;
import com.mindarray.api.*;
import com.mindarray.config.ConfigConstants;
import com.mindarray.datastore.DatastoreConstants;
import com.mindarray.db.DBConstants;
import com.mindarray.eventbus.EventBusConstants;
import com.mindarray.flow.FlowEngineConstants;
import com.mindarray.integration.IntegrationEngine;
import com.mindarray.log.LogEngineConstants;
import com.mindarray.nms.NMSConstants;
import com.mindarray.nms.SNMPTrapProcessor;
import com.mindarray.policy.PolicyEngineConstants;
import com.mindarray.report.ReportConstants;
import com.mindarray.store.*;
import com.mindarray.util.*;
import io.vertx.core.buffer.Buffer;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import org.apache.commons.lang3.StringUtils;

import java.io.File;
import java.math.BigInteger;
import java.nio.ByteBuffer;
import java.nio.ByteOrder;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

import static com.mindarray.GlobalConstants.*;
import static com.mindarray.api.AIOpsObject.OBJECT_IP;
import static com.mindarray.api.AIOpsObject.OBJECT_TARGET;
import static com.mindarray.api.RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_TYPE;
import static com.mindarray.api.RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_UUID;
import static com.mindarray.datastore.DatastoreConstants.DataType.*;
import static com.mindarray.datastore.DatastoreConstants.MAPPER_DATA_CATEGORIES;
import static com.mindarray.eventbus.EventBusConstants.*;
import static com.mindarray.nms.NMSConstants.*;
import static java.util.Map.entry;

public class VisualizationConstants
{
    public static final String VISUALIZATION_TYPE = "visualization.type";

    public static final String VISUALIZATION_NAME = "visualization.name";

    public static final String VISUALIZATION_GROUP_TYPE = "visualization.group.type";

    public static final String VISUALIZATION_CATEGORY = "visualization.category";

    public static final String VISUALIZATION_TIMELINE = "visualization.timeline";

    public static final String VISUALIZATION_TIMEZONE = "visualization.timezone";

    public static final String VISUALIZATION_DATA_SOURCES = "visualization.data.sources";

    public static final String VISUALIZATION_RESULT_BY = "visualization.result.by";

    public static final String QUERY_ID = "query.id";

    public static final String SUB_QUERY_ID = "sub.query.id";

    public static final String ENTITY_KEYS = "entity.keys";

    public static final String INSTANCE_TYPE = "instance.type";

    public static final String OBJECT_FILTER = "object.filter";

    public static final String ADMIN_ROLE = "admin.role";

    public static final String VISUALIZATION_STREAMING = "visualization.streaming";

    public static final String FILTER_KEYS = "filter.keys";

    public static final String DISCARD_DUMMY_ROWS = "discard.dummy.rows";

    public static final String VISUALIZATION_DATA_FILTER_JSON_TEMPLATE = "{\"data.filter\":{\"operator\":\"and\",\"filter\":\"include\",\"groups\":[{\"filter\":\"include\",\"operator\":\"and\",\"conditions\":[]}]}}";

    public static final String VISUALIZATION_RESULT_FILTER_JSON_TEMPLATE = "{\"result.filter\":{\"operator\":\"and\",\"filter\":\"include\",\"conditions\":[{\"value\":[],\"operand\":\"\",\"operator\":\"in\"}]}}";

    public static final String VISUALIZATION_DRILL_DOWN_FILTER_JSON_TEMPLATE = "{\"drill.down.filter\":{\"operator\":\"and\",\"filter\":\"include\",\"groups\":[{\"filter\":\"include\",\"operator\":\"and\",\"conditions\":[]}]}}";

    public static final String VISUALIZATION_CONDITION_GROUP_JSON_TEMPLATE = "{\"filter\":\"include\",\"operator\":\"and\",\"conditions\":[]}";

    public static final String VISUALIZATION_SYSTEM_PROCESS_DATASOURCE_JSON_TEMPLATE = "{\"visualization.data.sources\":[{\"visualization.result.by\":[\"monitor\"],\"type\":\"availability\",\"join.type\":\"all\",\"join.result\":\"availability\",\"filters\":{\"data.filter\":{},\"result.filter\":{}},\"data.points\":[{\"data.point\":\"system.process~uptime.percent\",\"aggregator\":\"avg\",\"entity.type\":\"Monitor\",\"entities\":[]},{\"data.point\":\"system.process~downtime.percent\",\"aggregator\":\"avg\",\"entity.type\":\"Monitor\",\"entities\":[]},{\"data.point\":\"system.process~suspendtime.percent\",\"aggregator\":\"avg\",\"entity.type\":\"Monitor\",\"entities\":[]},{\"data.point\":\"system.process~unknowntime.percent\",\"aggregator\":\"avg\",\"entity.type\":\"Monitor\",\"entities\":[]},{\"data.point\":\"system.process~maintenancetime.percent\",\"aggregator\":\"avg\",\"entity.type\":\"Monitor\",\"entities\":[]},{\"data.point\":\"system.process~disabletime.percent\",\"aggregator\":\"avg\",\"entity.type\":\"Monitor\",\"entities\":[]},{\"data.point\":\"system.process~unreachabletime.percent\",\"aggregator\":\"avg\",\"entity.type\":\"Monitor\",\"entities\":[]}]},{\"visualization.result.by\":[\"monitor\"],\"type\":\"availability\",\"filters\":{\"data.filter\":{},\"result.filter\":{}},\"data.points\":[{\"data.point\":\"system.process~uptime.seconds\",\"aggregator\":\"sum\",\"entity.type\":\"Monitor\",\"entities\":[]},{\"data.point\":\"system.process~downtime.seconds\",\"aggregator\":\"sum\",\"entity.type\":\"Monitor\",\"entities\":[]},{\"data.point\":\"system.process~suspendtime.seconds\",\"aggregator\":\"sum\",\"entity.type\":\"Monitor\",\"entities\":[]},{\"data.point\":\"system.process~unknowntime.seconds\",\"aggregator\":\"sum\",\"entity.type\":\"Monitor\",\"entities\":[]},{\"data.point\":\"system.process~maintenancetime.seconds\",\"aggregator\":\"sum\",\"entity.type\":\"Monitor\",\"entities\":[]},{\"data.point\":\"system.process~disabletime.seconds\",\"aggregator\":\"sum\",\"entity.type\":\"Monitor\",\"entities\":[]},{\"data.point\":\"system.process~unreachabletime.seconds\",\"aggregator\":\"sum\",\"entity.type\":\"Monitor\",\"entities\":[]}]}]}";

    public static final String VISUALIZATION_SYSTEM_PROCESS_AVAILABILITY_STATISTICS_DATASOURCE_JSON_TEMPLATE = "{\"visualization.data.sources\":[{\"visualization.result.by\":[\"monitor\"],\"type\":\"availability\",\"filters\":{\"data.filter\":{},\"result.filter\":{}},\"data.points\":[{\"data.point\":\"system.process~uptime.percent\",\"aggregator\":\"avg\",\"entity.type\":\"Monitor\",\"entities\":[]},{\"data.point\":\"system.process~downtime.percent\",\"aggregator\":\"avg\",\"entity.type\":\"Monitor\",\"entities\":[]},{\"data.point\":\"system.process~suspendtime.percent\",\"aggregator\":\"avg\",\"entity.type\":\"Monitor\",\"entities\":[]},{\"data.point\":\"system.process~unknowntime.percent\",\"aggregator\":\"avg\",\"entity.type\":\"Monitor\",\"entities\":[]},{\"data.point\":\"system.process~maintenancetime.percent\",\"aggregator\":\"avg\",\"entity.type\":\"Monitor\",\"entities\":[]},{\"data.point\":\"system.process~disabletime.percent\",\"aggregator\":\"avg\",\"entity.type\":\"Monitor\",\"entities\":[]},{\"data.point\":\"system.process~unreachabletime.percent\",\"aggregator\":\"avg\",\"entity.type\":\"Monitor\",\"entities\":[]}]}]}";

    public static final String VISUALIZATION_SYSTEM_SERVICE_DATASOURCE_JSON_TEMPLATE = "{\"visualization.data.sources\":[{\"visualization.result.by\":[\"monitor\"],\"type\":\"availability\",\"join.type\":\"all\",\"join.result\":\"availability\",\"filters\":{\"data.filter\":{},\"result.filter\":{}},\"data.points\":[{\"data.point\":\"system.service~uptime.percent\",\"aggregator\":\"avg\",\"entity.type\":\"Monitor\",\"entities\":[]},{\"data.point\":\"system.service~downtime.percent\",\"aggregator\":\"avg\",\"entity.type\":\"Monitor\",\"entities\":[]},{\"data.point\":\"system.service~suspendtime.percent\",\"aggregator\":\"avg\",\"entity.type\":\"Monitor\",\"entities\":[]},{\"data.point\":\"system.service~unknowntime.percent\",\"aggregator\":\"avg\",\"entity.type\":\"Monitor\",\"entities\":[]},{\"data.point\":\"system.service~maintenancetime.percent\",\"aggregator\":\"avg\",\"entity.type\":\"Monitor\",\"entities\":[]},{\"data.point\":\"system.service~disabletime.percent\",\"aggregator\":\"avg\",\"entity.type\":\"Monitor\",\"entities\":[]},{\"data.point\":\"system.service~unreachabletime.percent\",\"aggregator\":\"avg\",\"entity.type\":\"Monitor\",\"entities\":[]}]},{\"visualization.result.by\":[\"monitor\"],\"type\":\"availability\",\"filters\":{\"data.filter\":{},\"result.filter\":{}},\"data.points\":[{\"data.point\":\"system.service~uptime.seconds\",\"aggregator\":\"sum\",\"entity.type\":\"Monitor\",\"entities\":[]},{\"data.point\":\"system.service~downtime.seconds\",\"aggregator\":\"sum\",\"entity.type\":\"Monitor\",\"entities\":[]},{\"data.point\":\"system.service~suspendtime.seconds\",\"aggregator\":\"sum\",\"entity.type\":\"Monitor\",\"entities\":[]},{\"data.point\":\"system.service~unknowntime.seconds\",\"aggregator\":\"sum\",\"entity.type\":\"Monitor\",\"entities\":[]},{\"data.point\":\"system.service~maintenancetime.seconds\",\"aggregator\":\"sum\",\"entity.type\":\"Monitor\",\"entities\":[]},{\"data.point\":\"system.service~disabletime.seconds\",\"aggregator\":\"sum\",\"entity.type\":\"Monitor\",\"entities\":[]},{\"data.point\":\"system.service~unreachabletime.seconds\",\"aggregator\":\"sum\",\"entity.type\":\"Monitor\",\"entities\":[]}]}]}";

    public static final String VISUALIZATION_SYSTEM_SERVICE_AVAILABILITY_STATISTICS_DATASOURCE_JSON_TEMPLATE = "{\"visualization.data.sources\":[{\"visualization.result.by\":[\"monitor\"],\"type\":\"availability\",\"filters\":{\"data.filter\":{},\"result.filter\":{}},\"data.points\":[{\"data.point\":\"system.service~uptime.percent\",\"aggregator\":\"avg\",\"entity.type\":\"Monitor\",\"entities\":[]},{\"data.point\":\"system.service~downtime.percent\",\"aggregator\":\"avg\",\"entity.type\":\"Monitor\",\"entities\":[]},{\"data.point\":\"system.service~suspendtime.percent\",\"aggregator\":\"avg\",\"entity.type\":\"Monitor\",\"entities\":[]},{\"data.point\":\"system.service~unknowntime.percent\",\"aggregator\":\"avg\",\"entity.type\":\"Monitor\",\"entities\":[]},{\"data.point\":\"system.service~maintenancetime.percent\",\"aggregator\":\"avg\",\"entity.type\":\"Monitor\",\"entities\":[]},{\"data.point\":\"system.service~disabletime.percent\",\"aggregator\":\"avg\",\"entity.type\":\"Monitor\",\"entities\":[]},{\"data.point\":\"system.service~unreachabletime.percent\",\"aggregator\":\"avg\",\"entity.type\":\"Monitor\",\"entities\":[]}]}]}";

    public static final String QUERY_PROGRESS = "query.progress";

    public static final String JOIN_TYPE = "join.type";

    public static final Set<String> PASSOVER_INSTANCES = Set.of("application", "ipsla", "cisco.vedge.tunnel", "cisco.vedge.tloc", "cisco.vedge.interface");

    public static final String JOIN_BY_COLUMNS = "join.columns";

    public static final String JOIN_RESULT = "join.result";

    public static final String JOIN_ALIAS = "join.alias";

    public static final String SORT_BY = "sorting.column";

    public static final String PUBLISH_SUB_QUERY_PROGRESS = "publish.sub.query.progress";

    public static final int DATA_TYPE_INT32 = 48;

    public static final int DATA_TYPE_INT64 = 112;

    public static final int DATA_TYPE_FLOAT32 = 160;

    public static final int DATA_TYPE_STRING = 176;

    public static final String VISUALIZATION_TOP_N_CONTEXT = "{\"id\":-1,\"visualization.timeline\":{\"relative.timeline\":\"today\"},\"visualization.category\":\"TopN\",\"visualization.type\":\"Grid\",\"visualization.data.sources\":[{\"type\":\"flow\",\"category\":\"flow\",\"filters\":{\"data.filter\":{},\"result.filter\":{}},\"data.points\":[]}],\"visualization.properties\":{\"grid\":{\"searchable\":\"yes\",\"column.selection\":\"no\",\"header\":\"yes\",\"style\":{\"header.font.size\":\"small\"},\"sorting\":{\"limit\":300,\"order\":\"desc\",\"column\":\"@@@\"}}},\"visualization.result.by\":[],\"granularity\":{\"value\":5,\"unit\":\"m\"}}";

    public static final String VISUALIZATION_CHART_GRID_CONTEXT = "{\"visualization.timeline\":{\"relative.timeline\":\"today\",\"visualization.time.range.inclusive\":\"no\"},\"visualization.granularity\":\"5 m\",\"visualization.properties\":{\"grid\":{\"searchable\":\"yes\",\"column.selection\":\"no\",\"header\":\"yes\",\"style\":{\"header.font.size\":\"small\"}}},\"granularity\":{\"unit\":\"m\",\"value\":5},\"user.name\":\"admin\",\"id\":-1,\"visualization.category\":\"Chart\",\"visualization.data.sources\":[{\"type\":\"metric\",\"filters\":{\"data.filter\":{},\"result.filter\":{}},\"data.points\":[]}],\"visualization.type\":\"Grid\"}";

    public static final String VISUALIZATION_GAUGE_CONTEXT = "{\"id\":-1,\"visualization.timeline\":{\"relative.timeline\":\"today\"},\"visualization.category\":\"Gauge\",\"visualization.type\":\"MetroTile\",\"visualization.data.sources\":[{\"type\":\"flow\",\"filters\":{\"data.filter\":{},\"result.filter\":{}},\"data.points\":[]}],\"visualization.properties\":{\"gauge\":{\"style\":{\"chart.legend\":\"no\",\"chart.label\":\"no\",\"type\":\"number\",\"font.size\":\"small\"}}},\"granularity\":{\"value\":5,\"unit\":\"m\"}}";

    public static final String VISUALIZATION_CHART_CONTEXT = "{\"visualization.timeline\":{\"relative.timeline\":\"today\",\"visualization.time.range.inclusive\":\"no\"},\"visualization.granularity\":\"5 m\",\"visualization.properties\":{\"chart\":{\"vertical.legend\":\"no\",\"highchart.settings\":{},\"chart.legend\":\"yes\",\"sorting\":{\"limit\":10,\"order\":\"desc\"},\"chart.label\":\"no\",\"rotation.angle\":0}},\"granularity\":{\"unit\":\"m\",\"value\":5},\"ui.event.uuid\":\"5cefd2d9-69c2-43ab-8d85-12b2ef29cfa5\",\"user.name\":\"admin\",\"id\":-1,\"visualization.category\":\"Chart\",\"visualization.data.sources\":[],\"visualization.type\":\"Area\",\"session-id\":\"3bb9c217-5c87-4652-81e6-5797be89f0a1\"}";
    public static final String VISUALIZATION_PROPERTIES = "visualization.properties";
    public static final String SORTING = "sorting";
    public static final String QUERY_CONTEXT = "query.context";
    public static final int MAX_BAR_CHART_LIMIT = 150;
    public static final String PAGINATION_QUERY = "pagination.query";
    public static final String VISUALIZATION_GRANULARITY = "visualization.granularity";
    public static final String RESULT_FILTER = "result.filter";
    public static final String DRILL_DOWN_FILTER = "drill.down.filter";
    public static final String QUERY_PRIORITY = "query.priority";
    public static final Map<String, String> REPLACEABLE_COLUMNS = Map.ofEntries(Map.entry(SNMPTrapProcessor.SNMP_TRAP_OID, "trap.name"), Map.entry(PolicyEngineConstants.POLICY_ID, "policy.name"), Map.entry(NetRoute.NETROUTE_ID, NetRoute.NETROUTE_NAME));
    public static final String TYPE = "type";
    public static final String CATEGORY = "category";
    public static final String PLUGINS = "plugins";
    public static final String PLUGIN = "plugin";
    public static final String PLUGIN_ENTITIES = "plugin.entities";
    public static final String DATA_POINTS = "data.points";
    public static final String DATA_POINT = "data.point";
    public static final String DATA_POINT_ALIAS = "data.point.alias";
    public static final String DATA_TYPE = "data.type";
    public static final String DATA_POINT_LENGTH = "data.point.length";
    public static final String VISUALIZATION_ROWS = "rows";
    public static final int MAX_AGGREGATION_FUNCTIONS = 16;
    public static final int MAX_EVENT_AGGREGATION_FUNCTIONS = 10;
    public static final Map<String, JsonObject> VISUALIZATION_APP_DATA_SOURCES = Map.ofEntries(
            Map.entry(NMSConstants.SYSTEM_PROCESS, new JsonObject(VISUALIZATION_SYSTEM_PROCESS_DATASOURCE_JSON_TEMPLATE)),
            Map.entry(NMSConstants.SYSTEM_SERVICE, new JsonObject(VISUALIZATION_SYSTEM_SERVICE_DATASOURCE_JSON_TEMPLATE)),
            Map.entry(NMSConstants.SYSTEM_PROCESS_AVAILABILITY_STATISTICS, new JsonObject(VISUALIZATION_SYSTEM_PROCESS_AVAILABILITY_STATISTICS_DATASOURCE_JSON_TEMPLATE)),
            Map.entry(NMSConstants.SYSTEM_SERVICE_AVAILABILITY_STATISTICS, new JsonObject(VISUALIZATION_SYSTEM_SERVICE_AVAILABILITY_STATISTICS_DATASOURCE_JSON_TEMPLATE)));
    public static final String RELATIVE_TIMELINE = "relative.timeline";
    public static final String FROM_DATE = "from.date";
    public static final String TO_DATE = "to.date";
    public static final String FROM_TIME = "from.time";
    public static final String TO_TIME = "to.time";
    public static final String FROM_DATETIME = "from.datetime";
    public static final String DUMMY_FIELDS = "dummy.fields";
    public static final String VISUALIZATION_INVENTORY_TYPES = "visualization.inventory.types";
    public static final String VISUALIZATION_EXTRA_COLUMNS = "visualization.extra.columns";
    public static final String VISUALIZATION_DECODE_RESPONSE = "decode.response";
    public static final String VISUALIZATION_RESULT_TYPE = "visualization.result.type";
    public static final Set<String> PASSOVER_FILTER_INSTANCES = Set.of(NETWORK_SERVICE, INTERFACE, SYSTEM_PROCESS, ESXI_VM, SYSTEM_SERVICE, CITRIX_XEN_VM,
            HYPERV_VM, CISCO_WIRELESS_ACCESS_POINT, ARUBA_WIRELESS_ACCESS_POINT, RUCKUS_WIRELESS_ACCESS_POINT, NUTANIX_VM);

    //common constant
    public static final String TO_DATETIME = "to.datetime";
    public static final String AGGREGATOR = "aggregator";
    public static final String TEMPLATE_ID = "template.id";
    public static final String RESERVED_METRICS = "~duration";
    public static final String VISUALIZATION_TIME_RANGE_INCLUSIVE = "visualization.time.range.inclusive";
    public static final String GROUP_APP = "application";
    public static final String GROUP_VM = "vm";
    public static final String GROUP_ACCESS_POINT = "access point";
    public static final String GROUP_PROCESS = "process";
    public static final String GROUP_BY = "group.by";
    public static final String MULTI_AVAILABILITY_COLUMNS = "{\"#counter#~uptime.seconds^sum\":112,\"#counter#~uptime.percent^avg\":160,\"#counter#~downtime.seconds^sum\":112,\"#counter#~downtime.percent^avg\":160}";
    public static final String MAX_RECORDS = "max.records";
    public static final String CONTAINER_TYPE = "container.type";
    public static final long DEFAULT_NETWORK_TEMPLATE = 10000000000155L;
    public static final String TIMESTAMP = "Timestamp";
    public static final Map<String, String> VISUALIZATION_COMPLIANCE_TABLE_MAPPINGS = Map.ofEntries(
            Map.entry(VisualizationDataSource.COMPLIANCE_TRAIL.getName(), DBConstants.TABLE_COMPLIANCE_TRAIL),
            Map.entry(VisualizationDataSource.COMPLIANCE_STATS_ENTITY.getName(), DBConstants.TABLE_COMPLIANCE_STATS_ENTITY),
            Map.entry(VisualizationDataSource.COMPLIANCE_STATS_POLICY.getName(), DBConstants.TABLE_COMPLIANCE_STATS_POLICY));
    // container types
    public static final String CONTAINER_TYPE_REPORT = "report";
    static final String VISUALIZATION_TYPE_APPLICATION_AVAILABILITY = "Application Availability";
    static final String VISUALIZATION_TYPE_APPLICATION_STATUS = "Application Status";
    static final String VISUALIZATION_TYPE_APPLICATION_AVAILABILITY_TIME_SERIES = "Application Availability Time Series";
    static final String VISUALIZATION_TYPE_AVAILABILITY_TIME_SERIES = "Availability Time Series";
    static final String VISUALIZATION_TYPE_TODAY_AVAILABILITY = "Today's Availability";
    static final boolean VISUALIZATION_CACHING_ENABLED = MotadataConfigUtil.cachingEnabled();
    static final String VISUALIZATION_SESSIONS = "sessions";
    static final String INDEXABLE_COLUMNS = "indexable.columns";
    static final String VISUALIZATION_TAGS = "visualization.tags";
    //Query date and time constant
    static final String VISUALIZATION_TYPE_CHILD_VISUALIZATION = "child.visualization";
    static final String VISUALIZATION_CHILD_CATEGORIES = "visualization.child.categories";
    static final String VISUALIZATION_DRILL_DOWN = "drill.down";
    static final String QUERY_ABORT_REQUIRED = "query.abort.required";
    static final int INTERVAL_SECONDS = MotadataConfigUtil.getDBQueryAbortTimerSeconds();
    static final String ACTIVE_POLICY_DATA_POINTS = "{\"data.points\":[{\"aggregator\":\"last\",\"data.point\":\"severity\"},{\"aggregator\":\"last\",\"data.point\":\"policy.name\"},{\"aggregator\":\"last\",\"data.point\":\"instance\"},{\"aggregator\":\"last\",\"data.point\":\"policy.type\"},{\"aggregator\":\"last\",\"data.point\":\"policy.id\"},{\"aggregator\":\"last\",\"data.point\":\"metric\"},{\"aggregator\":\"last\",\"data.point\":\"value\"},{\"aggregator\":\"last\",\"data.point\":\"object.id\"},{\"aggregator\":\"last\",\"data.point\":\"duration\"},{\"aggregator\":\"last\",\"data.point\":\"previous.flap.timestamp\"},{\"aggregator\":\"last\",\"data.point\":\"policy.note\"},{\"aggregator\":\"last\",\"data.point\":\"acknowledge\"},{\"aggregator\":\"last\",\"data.point\":\"policy.acknowledge.by\"},{\"aggregator\":\"last\",\"data.point\":\"policy.acknowledge.time\"},{\"aggregator\":\"last\",\"data.point\":\"event.timestamp\"},{\"aggregator\":\"last\",\"data.point\":\"policy.tags\"}]}";
    static final String CUMULATIVE_STATUS_FLAP_DATA_POINTS = "{\"data.points\":[{\"aggregator\":\"\",\"data.point\":\"duration\"},{\"aggregator\":\"\",\"data.point\":\"status.flap.history\"},{\"aggregator\":\"\",\"data.point\":\"object.id\"}]}";
    static final String HOURLY_STATUS_FLAP_DATA_POINTS = "{\"data.points\":[{\"aggregator\":\"\",\"data.point\":\"duration\"},{\"aggregator\":\"\",\"data.point\":\"status.flap.history\"}]}";
    static final String INSTANCE_STATUS_FLAP_DATA_POINTS = "{\"data.points\":[{\"aggregator\":\"\",\"data.point\":\"duration\"},{\"aggregator\":\"\",\"data.point\":\"status.flap.history\"},{\"aggregator\":\"\",\"data.point\":\"object.id\"},{\"aggregator\":\"\",\"data.point\":\"instance\"}]}";
    static final String AVAILABILITY_DATA_POINTS = "{\"data.points\":[{\"aggregator\":\"avg\",\"data.point\":\"#counter#uptime.percent\"},{\"aggregator\":\"avg\",\"data.point\":\"#counter#downtime.percent\"},{\"aggregator\":\"avg\",\"data.point\":\"#counter#maintenancetime.percent\"},{\"aggregator\":\"avg\",\"data.point\":\"#counter#unreachabletime.percent\"},{\"aggregator\":\"avg\",\"data.point\":\"#counter#disabletime.percent\"},{\"aggregator\":\"avg\",\"data.point\":\"#counter#suspendtime.percent\"},{\"aggregator\":\"avg\",\"data.point\":\"#counter#unknowntime.percent\"},{\"aggregator\":\"sum\",\"data.point\":\"#counter#uptime.seconds\"},{\"aggregator\":\"sum\",\"data.point\":\"#counter#downtime.seconds\"},{\"aggregator\":\"sum\",\"data.point\":\"#counter#maintenancetime.seconds\"},{\"aggregator\":\"sum\",\"data.point\":\"#counter#unreachabletime.seconds\"},{\"aggregator\":\"sum\",\"data.point\":\"#counter#disabletime.seconds\"},{\"aggregator\":\"sum\",\"data.point\":\"#counter#suspendtime.seconds\"},{\"aggregator\":\"sum\",\"data.point\":\"#counter#unknowntime.seconds\"}]}";
    static final JsonArray VM_DATA_POINTS = new JsonArray().add(NMSConstants.CITRIX_XEN_VM + INSTANCE_SEPARATOR + STATUS).add(NMSConstants.ESXI_VM + INSTANCE_SEPARATOR + STATUS).add(NMSConstants.HYPERV_VM + INSTANCE_SEPARATOR + STATUS).add(NUTANIX_VM + INSTANCE_SEPARATOR + STATUS);
    static final JsonArray ACCESS_POINT_DATA_POINTS = new JsonArray().add(NMSConstants.CISCO_WIRELESS_ACCESS_POINT + INSTANCE_SEPARATOR + STATUS).add(NMSConstants.RUCKUS_WIRELESS_ACCESS_POINT + INSTANCE_SEPARATOR + STATUS).add(NMSConstants.ARUBA_WIRELESS_ACCESS_POINT + INSTANCE_SEPARATOR + STATUS);
    static final JsonArray APPLICATION_DATA_POINTS = new JsonArray().add(NMSConstants.SYSTEM_PROCESS + INSTANCE_SEPARATOR + STATUS).add(NMSConstants.SYSTEM_SERVICE + INSTANCE_SEPARATOR + STATUS);
    static final String END_TIME = "end.time";
    static final String START_TIME = "start.time";
    static final String COMPOSITE_QUERY_SIZE = "composite.query.size";
    static final String QUERY_CREATION_TIME = "query.creation.time"; //This is only to send start.time to DB
    private static final Map<String, Integer> DATASTORE_TYPES = Map.ofEntries(Map.entry(VisualizationDataSource.PERFORMANCE_METRIC.getName(), DatastoreConstants.DatastoreType.PERFORMANCE_METRIC.ordinal()),
            Map.entry(VisualizationDataSource.OBJECT_AVAILABILITY.getName(), DatastoreConstants.DatastoreType.OBJECT_STATUS_METRIC.ordinal()),
            Map.entry(VisualizationDataSource.LOG.getName(), DatastoreConstants.DatastoreType.LOG.ordinal()),
            Map.entry(VisualizationDataSource.FLOW.getName(), DatastoreConstants.DatastoreType.FLOW.ordinal()),
            Map.entry(VisualizationDataSource.TRAP.getName(), DatastoreConstants.DatastoreType.TRAP.ordinal()),
            Map.entry(VisualizationDataSource.USER_NOTIFICATION.getName(), DatastoreConstants.DatastoreType.NOTIFICATION.ordinal()),
            Map.entry(VisualizationDataSource.AUDIT.getName(), DatastoreConstants.DatastoreType.AUDIT.ordinal()),
            Map.entry(VisualizationDataSource.RUNBOOK_WORKLOG.getName(), DatastoreConstants.DatastoreType.RUNBOOK_WORKLOG.ordinal()),
            Map.entry(VisualizationDataSource.POLICY_RESULT.getName(), DatastoreConstants.DatastoreType.POLICY_RESULT.ordinal()),
            Map.entry(VisualizationDataSource.TRAP_ACKNOWLEDGEMENT.getName(), DatastoreConstants.DatastoreType.TRAP_ACKNOWLEDGEMENT.ordinal()),
            Map.entry(VisualizationDataSource.POLICY_FLAP.getName(), DatastoreConstants.DatastoreType.METRIC_POLICY_FLAP_HISTORY.ordinal()),
            Map.entry(VisualizationDataSource.TRAP_FLAP.getName(), DatastoreConstants.DatastoreType.TRAP_FLAP_HISTORY.ordinal()),
            Map.entry(VisualizationDataSource.HEALTH_METRIC.getName(), DatastoreConstants.DatastoreType.HEALTH_METRIC.ordinal()),
            Map.entry(VisualizationDataSource.CORRELATED_METRIC.getName(), DatastoreConstants.DatastoreType.CORRELATED_METRIC.ordinal()),
            Map.entry(VisualizationDataSource.CUMULATIVE_OBJECT_STATUS_FLAP.getName(), DatastoreConstants.DatastoreType.OBJECT_STATUS_FLAP_HISTORY.ordinal()),
            Map.entry(VisualizationDataSource.HOURLY_OBJECT_STATUS_FLAP.getName(), DatastoreConstants.DatastoreType.OBJECT_STATUS_FLAP_METRIC.ordinal()),
            Map.entry(VisualizationDataSource.COMPLIANCE.getName(), DatastoreConstants.DatastoreType.COMPLIANCE.ordinal()),
            Map.entry(VisualizationDataSource.STATIC_METRIC.getName(), DatastoreConstants.DatastoreType.STATIC_METRIC.ordinal()),
            Map.entry(VisualizationDataSource.NETROUTE_METRIC.getName(), DatastoreConstants.DatastoreType.NETROUTE_METRIC.ordinal()),
            Map.entry(VisualizationDataSource.NETROUTE_AVAILABILITY.getName(), DatastoreConstants.DatastoreType.NETROUTE_STATUS_METRIC.ordinal()));
    private static final Map<String, JsonObject> VISUALIZATION_INVENTORY_WIDGETS = Map.ofEntries(Map.entry("Service Check Inventory Summary", new JsonObject().put(CATEGORY, NMSConstants.Category.SERVICE_CHECK.getName()).put("queryCustomMonitoringFields", true).put("queryActiveAlerts", true).put("instance", EMPTY_VALUE)),
            Map.entry("Server Inventory Summary", new JsonObject().put(CATEGORY, Category.SERVER.getName()).put("queryCustomMonitoringFields", true).put("queryActiveAlerts", true).put("instance", EMPTY_VALUE)),
            Map.entry("System Service Inventory Summary", new JsonObject().put(CATEGORY, Category.SERVER.getName()).put("queryCustomMonitoringFields", false).put("queryActiveAlerts", false).put("instance", SYSTEM_SERVICE)),
            Map.entry("System Process Inventory Summary", new JsonObject().put(CATEGORY, Category.SERVER.getName()).put("queryCustomMonitoringFields", false).put("queryActiveAlerts", false).put("instance", SYSTEM_PROCESS)),
            Map.entry("Network Inventory Summary", new JsonObject().put(CATEGORY, Category.NETWORK.getName()).put("queryCustomMonitoringFields", true).put("queryActiveAlerts", true).put("instance", EMPTY_VALUE)),
            Map.entry("Virtualization Inventory Summary", new JsonObject().put(CATEGORY, Category.VIRTUALIZATION.getName()).put("queryCustomMonitoringFields", true).put("queryActiveAlerts", true).put("instance", EMPTY_VALUE)),
            Map.entry("Other Inventory Summary", new JsonObject().put(CATEGORY, Category.OTHER.getName()).put("queryCustomMonitoringFields", true).put("queryActiveAlerts", true).put("instance", EMPTY_VALUE)),
            Map.entry("WAN Link Inventory Summary", new JsonObject().put(CATEGORY, Category.NETWORK.getName()).put("queryCustomMonitoringFields", false).put("queryActiveAlerts", false).put("instance", IPSLA)),
            Map.entry("HCI Inventory Summary", new JsonObject().put(CATEGORY, Category.HCI.getName()).put("queryCustomMonitoringFields", true).put("queryActiveAlerts", true).put("instance", EMPTY_VALUE)),
            Map.entry("SDN Inventory Summary", new JsonObject().put(CATEGORY, Category.SDN.getName()).put("queryCustomMonitoringFields", true).put("queryActiveAlerts", true).put("instance", EMPTY_VALUE)),
            Map.entry("Container Inventory Summary", new JsonObject().put(CATEGORY, Category.SERVER.getName()).put("queryCustomMonitoringFields", false).put("queryActiveAlerts", false).put("instance", "docker.container")),
            Map.entry("Container Orchestration Inventory Summary", new JsonObject().put(CATEGORY, Category.CONTAINER_ORCHESTRATION.getName()).put("queryCustomMonitoringFields", true).put("queryActiveAlerts", true).put("instance", EMPTY_VALUE)),
            Map.entry("Interface Inventory Summary", new JsonObject().put(CATEGORY, Category.NETWORK.getName()).put("queryCustomMonitoringFields", false).put("queryActiveAlerts", false).put("instance", INTERFACE))
    );

    public static JsonObject unpack(Buffer buffer, Logger logger, boolean alias, JsonObject query, boolean removeDummyRow, boolean join)
    {
        var result = new JsonObject();

        try
        {
            //as of now not decoded sparkline as requirement not there
            if (buffer.length() > 0)
            {
                var instance = EMPTY_VALUE;

                var valid = true;

                var position = 0;

                result.put(QUERY_ID, buffer.getLongLE(position));
                position = position + 8; // 8 byte parent query id

                result.put(SUB_QUERY_ID, buffer.getLongLE(position));
                position = position + 8; // 8 byte current query id

                result.put(QUERY_PROGRESS, CommonUtil.getShort(buffer.getUnsignedByte(position)));
                position = position + 1; // 1 byte for query progress up to 100%

                result.put("rows", buffer.getLongLE(position));
                position = position + 8; // 1 byte for query progress up to 100%

                result.put("response.time.ms", buffer.getLongLE(position));
                position = position + 8; // 1 byte for query progress up to 100%

                var status = CommonUtil.getShort(buffer.getUnsignedByte(position));

                position = position + 1;

                if (status == 0 || status == 2)//if query failed status recieved from DB is either 0 and in case of abort from DB is status 2
                {
                    position = 38 + buffer.getIntLE(34);

                    if (buffer.length() < position + 1)
                    {
                        valid = false;
                    }

                    result.put(ERROR, buffer.getString(38, 38 + buffer.getIntLE(34)));
                }

                if (valid)
                {
                    result.put(GlobalConstants.STATUS, status); // 1 byte query status success or failed (0/1 value)

                    result.put(CATEGORY, CommonUtil.getShort(buffer.getUnsignedByte(position)));
                    position = position + 1; // 1 byte visualization category (Gauge =0 ,TopNChart =1 ,TopNGrid =2 ,Histogram =3, Grid = 4)

                    result.put(DATA_POINTS, CommonUtil.getShort(buffer.getUnsignedByte(position)));
                    position = position + 1; // 1 byte number of columns

                    if (buffer.length() > position)
                    {
                        result.put(VISUALIZATION_ROWS, buffer.getIntLE(position));
                        position = position + 4; // 4 byte for number of rows counts

                        if (query != null && query.containsKey(VISUALIZATION_CATEGORY) && query.getString(VISUALIZATION_CATEGORY).equalsIgnoreCase(VisualizationCategory.FORECAST.getName()) && !query.getString(VISUALIZATION_TYPE, VisualizationCategory.HISTOGRAM.getName()).equalsIgnoreCase(VisualizationConstants.VisualizationCategory.GRID.getName()))
                        {
                            result.put("prediction.index", buffer.getShortLE(position));
                            position = position + 2; // 2 byte for predicted row index
                        }

                        var rows = new ArrayList<JsonObject>();

                        for (var i = 0; i < result.getInteger(VISUALIZATION_ROWS); i++)
                        {

                            rows.add(new JsonObject());
                        }

                        var dummyRows = new HashSet<Integer>();

                        for (var i = 0; i < result.getInteger(DATA_POINTS); i++)
                        {
                            var context = new JsonObject();

                            result.put("total.bytes", buffer.getUnsignedIntLE(position));
                            position = position + 4; // 4 byte for number of rows counts

                            context.put(AGGREGATOR, buffer.getUnsignedByte(position));
                            position = position + 1; // 1 byte column aggregation type

                            context.put(DATA_TYPE, buffer.getUnsignedByte(position));
                            position = position + 1; // 1 byte column datatype

                            context.put(DATA_POINT_LENGTH, buffer.getShortLE(position));
                            position = position + 2; // 2 byte column length

                            context.put(DATA_POINT, CommonUtil.getString(buffer.getString(position, position + context.getInteger(DATA_POINT_LENGTH))));
                            position = position + context.getInteger(DATA_POINT_LENGTH); //  moving bytes' length of column name size

                            context.put("alias.name.length", buffer.getShortLE(position));
                            position = position + 2; // 2 byte alias name length

                            context.put(DATA_POINT_ALIAS, CommonUtil.getString(buffer.getString(position, position + context.getInteger("alias.name.length"))));
                            position = position + context.getInteger("alias.name.length"); //  moving bytes' length of column name size

                            var dataType = CommonUtil.getInteger(context.getValue(DATA_TYPE));

                            if (instance.isEmpty() && context.getString(DATA_POINT).contains(GlobalConstants.INSTANCE_SEPARATOR))
                            {
                                instance = context.getString(DATA_POINT).split(GlobalConstants.INSTANCE_SEPARATOR)[0];
                            }

                            if (CommonUtil.getInteger(context.getValue(AGGREGATOR)) == DatastoreConstants.AggregationType.SPARKLINE.ordinal())//sparkline
                            {
                                var sparklinePoints = buffer.getShortLE(position);

                                position = position + 2;  //2 byte (size of the sparkline points)

                                for (var rowIndex = 0; rowIndex < result.getInteger(VISUALIZATION_ROWS); rowIndex++)
                                {
                                    var values = new JsonObject();

                                    var timestamps = new ArrayList<>();

                                    if (dataType == DATA_TYPE_INT32)
                                    {
                                        for (var idx = 0; idx < sparklinePoints; idx++)
                                        {
                                            timestamps.add(buffer.getLongLE(position)); //Adding Interval/Timestamp

                                            position = position + 8; //  moving 8 bytes' each value
                                        }

                                        for (var idx = 0; idx < sparklinePoints; idx++)
                                        {
                                            values.put(CommonUtil.getString(timestamps.get(idx)), buffer.getIntLE(position)); //Adding Value

                                            position = position + 4; //  moving 4 bytes' each value
                                        }

                                    }
                                    else if (dataType == DATA_TYPE_INT64)
                                    {
                                        for (var idx = 0; idx < sparklinePoints; idx++)
                                        {
                                            timestamps.add(buffer.getLongLE(position)); //Adding Interval/Timestamp

                                            position = position + 8; //  moving 8 bytes' each value
                                        }

                                        for (var idx = 0; idx < sparklinePoints; idx++)
                                        {
                                            values.put(CommonUtil.getString(timestamps.get(idx)), buffer.getLongLE(position));//Adding Value

                                            position = position + 8; //  moving 8 bytes' each value
                                        }
                                    }
                                    else if (dataType == DATA_TYPE_FLOAT32)
                                    {
                                        for (var idx = 0; idx < sparklinePoints; idx++)
                                        {
                                            timestamps.add(buffer.getLongLE(position));

                                            position = position + 8; // moving 8 byte value
                                        }

                                        for (var idx = 0; idx < sparklinePoints; idx++)
                                        {
                                            values.put(CommonUtil.getString(timestamps.get(idx)), ByteBuffer.wrap(buffer.getBytes(position, position + 8)).order(ByteOrder.LITTLE_ENDIAN).getDouble()); //wrap float into Little Endian and move 8 bytes

                                            position = position + 8; //  moving 8 bytes' each value
                                        }
                                    }

                                    rows.get(rowIndex).put(context.getString(alias ? DATA_POINT_ALIAS : DATA_POINT), values);
                                }
                            }
                            else
                            {
                                if (dataType == DATA_TYPE_INT32)
                                {
                                    for (var rowIndex = 0; rowIndex < result.getInteger(VISUALIZATION_ROWS); rowIndex++)
                                    {
                                        var value = buffer.getIntLE(position);

                                        if (query != null && query.containsKey(VISUALIZATION_DECODE_RESPONSE) && context.getString(alias ? DATA_POINT_ALIAS : DATA_POINT).equalsIgnoreCase("monitor"))
                                        {
                                            rows.get(rowIndex).put("monitor", ObjectConfigStore.getStore().getObjectName(ObjectConfigStore.getStore().getIdByObjectId(CommonUtil.getInteger(value))));
                                        }
                                        else
                                        {
                                            rows.get(rowIndex).put(context.getString(alias ? DATA_POINT_ALIAS : DATA_POINT), value);
                                        }

                                        position = position + 4; //  moving 4 bytes' each value
                                    }
                                }
                                else if (dataType == DATA_TYPE_INT64)
                                {
                                    for (var rowIndex = 0; rowIndex < result.getInteger(VISUALIZATION_ROWS); rowIndex++)
                                    {
                                        var value = buffer.getLongLE(position);

                                        if (removeDummyRow)
                                        {
                                            if (value != Long.MIN_VALUE)
                                            {
                                                if (query != null && query.containsKey(VISUALIZATION_DECODE_RESPONSE) && context.getString(alias ? DATA_POINT_ALIAS : DATA_POINT).equalsIgnoreCase("monitor"))
                                                {
                                                    rows.get(rowIndex).put("monitor", ObjectConfigStore.getStore().getObjectName(ObjectConfigStore.getStore().getIdByObjectId(CommonUtil.getInteger(value))));
                                                }
                                                else
                                                {
                                                    rows.get(rowIndex).put(context.getString(alias ? DATA_POINT_ALIAS : DATA_POINT), value);
                                                }
                                            }

                                            else
                                            {
                                                dummyRows.add(rowIndex);
                                            }
                                        }

                                        else
                                        {
                                            if (query != null && query.containsKey(VISUALIZATION_DECODE_RESPONSE) && context.getString(alias ? DATA_POINT_ALIAS : DATA_POINT).equalsIgnoreCase("monitor"))
                                            {
                                                rows.get(rowIndex).put("monitor", ObjectConfigStore.getStore().getObjectName(ObjectConfigStore.getStore().getIdByObjectId(CommonUtil.getInteger(value))));
                                            }
                                            else
                                            {
                                                rows.get(rowIndex).put(context.getString(alias ? DATA_POINT_ALIAS : DATA_POINT), value);
                                            }
                                        }


                                        position = position + 8; //  moving 8 bytes' each value
                                    }
                                }
                                else if (dataType == DATA_TYPE_FLOAT32)
                                {
                                    for (var rowIndex = 0; rowIndex < result.getInteger(VISUALIZATION_ROWS); rowIndex++)
                                    {
                                        var value = ByteBuffer.wrap(buffer.getBytes(position, position + 8)).order(ByteOrder.LITTLE_ENDIAN).getDouble();

                                        if (removeDummyRow)
                                        {
                                            if (value != -Float.MAX_VALUE)
                                            {
                                                rows.get(rowIndex).put(context.getString(alias ? DATA_POINT_ALIAS : DATA_POINT), value);
                                            }

                                            else
                                            {
                                                dummyRows.add(rowIndex);
                                            }

                                        }

                                        else
                                        {
                                            rows.get(rowIndex).put(context.getString(alias ? DATA_POINT_ALIAS : DATA_POINT), value);
                                        }

                                        position = position + 8; //  moving 8 bytes' each value
                                    }
                                }

                                else if (dataType == DATA_TYPE_STRING)
                                {
                                    for (var rowIndex = 0; rowIndex < result.getInteger(VISUALIZATION_ROWS); rowIndex++)
                                    {
                                        var length = CommonUtil.getInteger(buffer.getUnsignedIntLE(position));

                                        position = position + 4;

                                        var value = buffer.getString(position, position + length);

                                        if (query != null && query.containsKey(VISUALIZATION_DECODE_RESPONSE) && context.getString(alias ? DATA_POINT_ALIAS : DATA_POINT).equalsIgnoreCase("monitor"))
                                        {
                                            if (value != null)
                                            {
                                                rows.get(rowIndex).put("monitor", ObjectConfigStore.getStore().getObjectName(ObjectConfigStore.getStore().getIdByObjectId(CommonUtil.getInteger(value))));
                                            }
                                        }
                                        else
                                        {
                                            rows.get(rowIndex).put(context.getString(alias ? DATA_POINT_ALIAS : DATA_POINT), value);
                                        }

                                        position = position + length; //  moving length of bytes' each value
                                    }
                                }
                            }
                        }

                        if (removeDummyRow && !dummyRows.isEmpty())
                        {
                            var indices = new ArrayList<>(dummyRows);

                            for (var i = indices.size() - 1; i >= 0; i--)
                            {
                                var index = indices.get(i);

                                if (index >= 0 && index < rows.size())
                                {
                                    rows.remove(index.intValue());
                                }
                            }
                        }

                        if (join && query != null && query.containsKey(JOIN_TYPE) && query.getString(JOIN_TYPE).equalsIgnoreCase(VisualizationConstants.VisualizationJoinType.JOIN_TYPE_ANY.getName()))
                        {
                            var records = new HashMap<String, List<JsonObject>>();

                            var columns = query.getJsonArray(JOIN_BY_COLUMNS);

                            var builder = new StringBuilder();

                            for (var row : rows)
                            {
                                valid = true;

                                for (var column : columns)
                                {
                                    if (row.containsKey(CommonUtil.getString(column)))
                                    {
                                        builder.append(row.getValue(CommonUtil.getString(column))).append(GlobalConstants.COLUMN_SEPARATOR);
                                    }

                                    else
                                    {
                                        valid = false;

                                        break;
                                    }
                                }

                                if (valid)
                                {
                                    records.computeIfAbsent(CommonUtil.getString(builder.deleteCharAt(builder.length() - 1)), value -> new ArrayList<>()).add(row);
                                }

                                builder.setLength(0);
                            }

                            result.put(GlobalConstants.RESULT, records);
                        }

                        else
                        {

                            result.put(GlobalConstants.RESULT, rows);
                        }

                        result.put(GlobalConstants.INSTANCE, instance);
                    }

                    else
                    {
                        if (CommonUtil.debugEnabled())
                        {
                            logger.debug("invalid query result:" + query);
                        }
                    }
                }
            }
        }
        catch (Exception exception)
        {
            logger.error(exception);

            if (query != null)
            {
                logger.warn("exception query context:" + query);
            }
        }

        return result;
    }

    // this method is separate from existing unpack method
    // which will be used to manipulate result to show in API in readable format.
    // if we have any further requirement/improvement/manipulation for API, we can do it here.
    public static JsonObject unpack(Buffer buffer, Logger logger, JsonObject query, boolean removeDummyRow)
    {
        var result = new JsonObject();

        var category = query != null ? query.getString(VisualizationConstants.VISUALIZATION_CATEGORY) : EMPTY_VALUE;

        try
        {
            if (buffer.length() > 0)
            {
                var instance = EMPTY_VALUE;

                var valid = true;

                var position = 0;

                result.put(QUERY_ID, buffer.getLongLE(position));
                position = position + 8; // 8 byte parent query id

                result.put(SUB_QUERY_ID, buffer.getLongLE(position));
                position = position + 8; // 8 byte current query id

                result.put(QUERY_PROGRESS, CommonUtil.getShort(buffer.getUnsignedByte(position)));
                position = position + 1; // 1 byte for query progress up to 100%

                result.put("rows", buffer.getLongLE(position));
                position = position + 8; // 1 byte for query progress up to 100%

                result.put("response.time.ms", buffer.getLongLE(position));
                position = position + 8; // 1 byte for query progress up to 100%

                var status = CommonUtil.getShort(buffer.getUnsignedByte(position));

                position = position + 1;

                if (status == 0)//if query failed
                {
                    position = 38 + buffer.getIntLE(34);

                    if (buffer.length() < position + 1)
                    {
                        valid = false;
                    }

                    result.put(ERROR, buffer.getString(38, 38 + buffer.getIntLE(34)));
                }

                if (valid)
                {
                    result.put(GlobalConstants.STATUS, status); // 1 byte query status success or failed (0/1 value)

                    result.put(CATEGORY, CommonUtil.getShort(buffer.getUnsignedByte(position)));

                    position = position + 1; // 1 byte visualization category (Gauge =0 ,TopNChart =1 ,TopNGrid =2 ,Histogram =3, Grid = 4)

                    result.put(DATA_POINTS, CommonUtil.getShort(buffer.getUnsignedByte(position)));

                    position = position + 1; // 1 byte number of columns

                    if (buffer.length() > position)
                    {
                        result.put(VISUALIZATION_ROWS, buffer.getIntLE(position));

                        position = position + 4; // 4 byte for number of rows counts

                        if (query != null && query.containsKey(VISUALIZATION_CATEGORY) && query.getString(VISUALIZATION_CATEGORY).equalsIgnoreCase(VisualizationCategory.FORECAST.getName()) && !query.getString(VISUALIZATION_TYPE, VisualizationCategory.HISTOGRAM.getName()).equalsIgnoreCase(VisualizationConstants.VisualizationCategory.GRID.getName()))
                        {
                            result.put("prediction.index", buffer.getShortLE(position));

                            position = position + 2; // 2 byte for predicted row index
                        }

                        var rows = new ArrayList<JsonObject>();

                        for (var i = 0; i < result.getInteger(VISUALIZATION_ROWS); i++)
                        {

                            rows.add(new JsonObject());
                        }

                        var dummyRows = new HashSet<Integer>();

                        for (var i = 0; i < result.getInteger(DATA_POINTS); i++)
                        {
                            try
                            {

                                var context = new JsonObject();

                                result.put("total.bytes", buffer.getUnsignedIntLE(position));

                                position = position + 4; // 4 byte for number of rows counts

                                context.put(AGGREGATOR, buffer.getUnsignedByte(position));
                                position = position + 1; // 1 byte column aggregation type

                                context.put(DATA_TYPE, buffer.getUnsignedByte(position));

                                position = position + 1; // 1 byte column datatype

                                context.put(DATA_POINT_LENGTH, buffer.getShortLE(position));

                                position = position + 2; // 2 byte column length

                                context.put(DATA_POINT, CommonUtil.getString(buffer.getString(position, position + context.getInteger(DATA_POINT_LENGTH))));

                                position = position + context.getInteger(DATA_POINT_LENGTH); //  moving bytes' length of column name size

                                context.put("alias.name.length", buffer.getShortLE(position));

                                position = position + 2; // 2 byte column length

                                context.put(DATA_POINT_ALIAS, CommonUtil.getString(buffer.getString(position, position + context.getInteger("alias.name.length"))));

                                position = position + context.getInteger("alias.name.length"); //  moving bytes' length of column name size

                                var dataType = CommonUtil.getInteger(context.getValue(DATA_TYPE));

                                if (instance.isEmpty() && context.getString(DATA_POINT).contains(GlobalConstants.INSTANCE_SEPARATOR))
                                {
                                    instance = context.getString(DATA_POINT).split(GlobalConstants.INSTANCE_SEPARATOR)[0];
                                }

                                if (dataType == DATA_TYPE_INT32)
                                {
                                    for (var rowIndex = 0; rowIndex < result.getInteger(VISUALIZATION_ROWS); rowIndex++)
                                    {
                                        var value = buffer.getIntLE(position);

                                        if (query != null && query.containsKey(VISUALIZATION_DECODE_RESPONSE) && (context.getString(DATA_POINT).equalsIgnoreCase("monitor") || context.getString(DATA_POINT).equalsIgnoreCase(AIOpsObject.OBJECT_ID)))
                                        {
                                            var object = ObjectConfigStore.getStore().getItemByObjectId(CommonUtil.getInteger(value));

                                            rows.get(rowIndex).put("monitor", object.getString(AIOpsObject.OBJECT_NAME)).put(OBJECT_IP, object.getString(OBJECT_IP, object.getString(OBJECT_TARGET))).put(ID, object.getLong(ID)).put(AIOpsObject.OBJECT_ID, CommonUtil.getInteger(value));
                                        }
                                        else
                                        {
                                            rows.get(rowIndex).put(context.getString(DATA_POINT), value);
                                        }

                                        position = position + 4; //  moving 4 bytes' each value
                                    }
                                }
                                else if (dataType == DATA_TYPE_INT64)
                                {
                                    for (var rowIndex = 0; rowIndex < result.getInteger(VISUALIZATION_ROWS); rowIndex++)
                                    {
                                        var value = buffer.getLongLE(position);

                                        if (removeDummyRow)
                                        {
                                            if (value != Long.MIN_VALUE)
                                            {
                                                if (query != null && query.containsKey(VISUALIZATION_DECODE_RESPONSE) && (context.getString(DATA_POINT).equalsIgnoreCase("monitor") || context.getString(DATA_POINT).equalsIgnoreCase(AIOpsObject.OBJECT_ID)))
                                                {
                                                    var object = ObjectConfigStore.getStore().getItemByObjectId(CommonUtil.getInteger(value));

                                                    rows.get(rowIndex).put("monitor", object.getString(AIOpsObject.OBJECT_NAME)).put(OBJECT_IP, object.getString(OBJECT_IP, object.getString(OBJECT_TARGET))).put(ID, object.getLong(ID)).put(AIOpsObject.OBJECT_ID, CommonUtil.getInteger(value));
                                                }
                                                else
                                                {
                                                    rows.get(rowIndex).put(context.getString(DATA_POINT), value);
                                                }
                                            }

                                            else
                                            {
                                                dummyRows.add(rowIndex);
                                            }
                                        }
                                        else
                                        {
                                            if (query != null && query.containsKey(VISUALIZATION_DECODE_RESPONSE) && (context.getString(DATA_POINT).equalsIgnoreCase("monitor") || context.getString(DATA_POINT).equalsIgnoreCase(AIOpsObject.OBJECT_ID)))
                                            {
                                                var object = ObjectConfigStore.getStore().getItemByObjectId(CommonUtil.getInteger(value));

                                                rows.get(rowIndex).put("monitor", object.getString(AIOpsObject.OBJECT_NAME)).put(OBJECT_IP, object.getString(OBJECT_IP, object.getString(OBJECT_TARGET))).put(ID, object.getLong(ID)).put(AIOpsObject.OBJECT_ID, CommonUtil.getInteger(value));
                                            }
                                            else
                                            {
                                                rows.get(rowIndex).put(context.getString(DATA_POINT), value);
                                            }
                                        }

                                        position = position + 8; //  moving 8 bytes' each value
                                    }
                                }
                                else if (dataType == DATA_TYPE_FLOAT32)
                                {
                                    for (var rowIndex = 0; rowIndex < result.getInteger(VISUALIZATION_ROWS); rowIndex++)
                                    {
                                        var value = ByteBuffer.wrap(buffer.getBytes(position, position + 8)).order(ByteOrder.LITTLE_ENDIAN).getDouble();

                                        if (removeDummyRow)
                                        {
                                            if (value != -Float.MAX_VALUE)
                                            {
                                                rows.get(rowIndex).put(context.getString(DATA_POINT), value);
                                            }
                                            else
                                            {
                                                dummyRows.add(rowIndex);
                                            }
                                        }
                                        else
                                        {
                                            rows.get(rowIndex).put(context.getString(DATA_POINT), value);
                                        }

                                        position = position + 8; //  moving 8 bytes' each value
                                    }
                                }
                                else if (dataType == DATA_TYPE_STRING)
                                {
                                    for (var rowIndex = 0; rowIndex < result.getInteger(VISUALIZATION_ROWS); rowIndex++)
                                    {
                                        var length = CommonUtil.getInteger(buffer.getUnsignedIntLE(position));

                                        position = position + 4;

                                        var value = buffer.getString(position, position + length);

                                        if (query != null && query.containsKey(VISUALIZATION_DECODE_RESPONSE) && (context.getString(DATA_POINT).equalsIgnoreCase("monitor") || context.getString(DATA_POINT).equalsIgnoreCase(AIOpsObject.OBJECT_ID)))
                                        {
                                            if (value != null)
                                            {
                                                var object = ObjectConfigStore.getStore().getItemByObjectId(CommonUtil.getInteger(value));

                                                rows.get(rowIndex).put("monitor", object.getString(AIOpsObject.OBJECT_NAME)).put(OBJECT_IP, object.getString(OBJECT_IP, object.getString(OBJECT_TARGET))).put(ID, object.getLong(ID)).put(AIOpsObject.OBJECT_ID, CommonUtil.getInteger(value));
                                            }
                                        }
                                        else
                                        {
                                            rows.get(rowIndex).put(context.getString(DATA_POINT), value);
                                        }

                                        position = position + length; //  moving length of bytes' each value
                                    }
                                }
                            }
                            catch (Exception exception)
                            {
                                logger.error(exception);
                            }
                        }

                        if (category.equalsIgnoreCase(VisualizationCategory.HISTOGRAM.getName()))
                        {
                            result.put(GlobalConstants.RESULT, setHistogramResult(rows, query, logger));
                        }
                        else
                        {
                            if (removeDummyRow && !dummyRows.isEmpty())
                            {
                                var indices = new ArrayList<>(dummyRows);

                                for (var i = indices.size() - 1; i >= 0; i--)
                                {
                                    var index = indices.get(i);

                                    if (index >= 0 && index < rows.size())
                                    {
                                        rows.remove(index.intValue());
                                    }
                                }
                            }

                            result.put(GlobalConstants.RESULT, rows);

                            result.put(GlobalConstants.INSTANCE, instance);
                        }
                    }

                    else
                    {
                        if (CommonUtil.debugEnabled())
                        {
                            logger.debug("invalid query result:" + query);
                        }
                    }
                }
            }
        }
        catch (Exception exception)
        {
            logger.error(exception);

            if (query != null)
            {
                logger.warn("exception query context:" + query);
            }
        }

        return result;
    }

    // this method is to manipulate result of histogram coming from datastore to show in API
    private static JsonArray setHistogramResult(List<JsonObject> rows, JsonObject query, Logger logger)
    {
        var records = new JsonArray();

        try
        {
            var iterator = rows.iterator();

            while (iterator.hasNext())
            {
                try
                {
                    var row = iterator.next();

                    var fields = row.fieldNames();

                    // need to remove single row with timestamp , in chart we have many dummy rows
                    if (fields.size() > 1)
                    {
                        for (var field : fields)
                        {
                            // field : 1^ping.sent.packets^avg
                            if (field.contains(CARET_SEPARATOR))
                            {
                                var record = new JsonObject();

                                var tokens = field.split(CARET_SEPARATOR_WITH_ESCAPE);

                                if (tokens.length > 2)
                                {
                                    var instanceName = EMPTY_VALUE;

                                    var token = tokens[0];

                                    // instance : 1###instance.name^metric.name^aggregator
                                    if (token.contains(GROUP_SEPARATOR))
                                    {
                                        instanceName = token.split(GROUP_SEPARATOR)[1];

                                        token = token.split(GROUP_SEPARATOR)[0];
                                    }

                                    if (query.containsKey(VisualizationConstants.VISUALIZATION_RESULT_BY))
                                    {
                                        var groupingColumns = query.getJsonArray(VisualizationConstants.VISUALIZATION_RESULT_BY);

                                        if (groupingColumns.contains("severity"))
                                        {
                                            record.put(SEVERITY, token);
                                        }
                                        else if (groupingColumns.contains("policy.id"))
                                        {
                                            record.put("policy", token);
                                        }
                                        else
                                        {
                                            if (groupingColumns.contains("group"))
                                            {
                                                record.put("group", token);
                                            }
                                            else if (groupingColumns.contains("tag"))
                                            {
                                                record.put("tag", token);
                                            }
                                            else if (groupingColumns.contains("monitor") || groupingColumns.contains("object.id") || !instanceName.equalsIgnoreCase(EMPTY_VALUE))          // last instance condition for result by instance
                                            {
                                                var object = ObjectConfigStore.getStore().getItemByObjectId(CommonUtil.getInteger(token));

                                                record.put("monitor", object.getString(AIOpsObject.OBJECT_NAME)).put(OBJECT_IP, object.getString(OBJECT_IP, object.getString(OBJECT_TARGET))).put(ID, object.getLong(ID)).put(AIOpsObject.OBJECT_ID, CommonUtil.getInteger(token));
                                            }

                                            record.put(INSTANCE_NAME, instanceName);

                                            record.put(METRIC, tokens[1]);

                                            record.put(AGGREGATOR, tokens[2]);
                                        }
                                    }

                                    record.put(VALUE, row.getValue(field));

                                    record.put(TIMESTAMP, row.getValue(TIMESTAMP));
                                }
                                // for multiple datapoint, we don't have group by. so we can directly set values
                                // ex : ping.received.packets^avg , ping.sent.packets^avg
                                else if (tokens.length > 1)
                                {
                                    record.put(METRIC, tokens[0]);

                                    record.put(AGGREGATOR, tokens[1]);

                                    record.put(VALUE, row.getValue(field));

                                    record.put(TIMESTAMP, row.getValue(TIMESTAMP));
                                }

                                records.add(record);
                            }
                        }
                    }
                    else
                    {
                        iterator.remove();
                    }
                }
                catch (Exception exception)
                {
                    logger.error(exception);
                }
            }
        }
        catch (Exception exception)
        {
            logger.error(exception);
        }

        return records;
    }

    public static Buffer packGridResult(JsonObject result, int visualizationType, long queryId, long subQueryId)
    {
        var buffer = Buffer.buffer();

        packHeaders(buffer, queryId, subQueryId, CommonUtil.getShort(result.containsKey(QUERY_PROGRESS) ? result.remove(QUERY_PROGRESS) : 100), CommonUtil.getShort(1));

        buffer.setUnsignedByte(34, CommonUtil.getShort(visualizationType));

        buffer.setUnsignedByte(35, CommonUtil.getShort(result.size()));

        result.getMap().forEach((key, value) ->
        {
            var records = (JsonArray) value;

            if (!records.isEmpty())
            {
                var dataType = new AtomicReference<>(STRING);

                var sparkline = key.contains("sparkline");

                var category = DatastoreConstants.getDataCategory(true, key.split(CARET_SEPARATOR_WITH_ESCAPE)[0], CommonUtil.getString(records.getValue(0)));

                if (sparkline)
                {
                    //For Sparkline, set data as per values...
                    var row = records.getJsonObject(0);

                    for (var entry : row.getMap().entrySet())
                    {
                        var column = row.getValue(CommonUtil.getString(entry.getKey()));

                        if (column instanceof Float || column instanceof Double)
                        {
                            dataType.set(FLOAT32);

                            break;
                        }
                        // in case of result received from custom script (GoLang), value converted from Long to BigInteger
                        else if (column instanceof Long || column instanceof BigInteger)
                        {
                            dataType.set(INT64);

                            break;
                        }
                        else if (column instanceof Integer)
                        {
                            dataType.set(INT32);

                            break;
                        }
                    }
                }
                else
                {
                    if (category == DatastoreConstants.DataCategory.FLOAT.getName() || records.getValue(0) instanceof Double || records.getValue(0) instanceof Float)
                    {
                        dataType.set(FLOAT32);
                    }

                    // in case of result received from custom script (GoLang), value converted from Long to BigInteger
                    else if (records.getValue(0) instanceof Long || records.getValue(0) instanceof BigInteger)
                    {
                        dataType.set(INT64);
                    }

                    else if (records.getValue(0) instanceof Integer)
                    {
                        dataType.set(INT32);

                        // In the custom script report when reading response from script (python, golang, node js), getting mix array of integer and Long like [12,4,6,3,5,-9223372036854775808]. So, It is
                        // throwing ClassCastException so, have to assign category by checking each value
                        for (var index = 0; index < records.size(); index++)
                        {
                            if (records.getValue(index) instanceof Long || CommonUtil.getLong(records.getValue(index)) == Long.MIN_VALUE)
                            {
                                dataType.set(INT64);

                                break;
                            }
                        }
                    }

                }

                buffer.setIntLE(36, records.size());

                buffer.setIntLE(buffer.length(), 0);//extra bytes

                if (sparkline)
                {
                    //Adding Aggregation Ordinal
                    buffer.setUnsignedByte(buffer.length(), CommonUtil.getShort(DatastoreConstants.AggregationType.SPARKLINE.ordinal()));
                }
                else
                {
                    buffer.setUnsignedByte(buffer.length(), STRING.equals(dataType.get()) ? CommonUtil.getShort(DatastoreConstants.AggregationType.COUNT.ordinal()) : CommonUtil.getShort(DatastoreConstants.AggregationType.LAST.ordinal()));
                }

                buffer.setUnsignedByte(buffer.length(), dataType.get().getSize());

                buffer.setShortLE(buffer.length(), CommonUtil.getShort(key.length()));

                buffer.setString(buffer.length(), key);

                buffer.setShortLE(buffer.length(), CommonUtil.getShort(key.length()));

                buffer.setString(buffer.length(), key);

                if (sparkline)
                {
                    //Adding length of the whole rows to buffer...
                    buffer.setShortLE(buffer.length(), CommonUtil.getShort(records.getJsonObject(0).size()));
                }

                for (var i = 0; i < records.size(); i++)
                {
                    switch (dataType.get())
                    {
                        case INT32 ->
                        {
                            if (sparkline)
                            {
                                for (var row : records.getJsonObject(i).getMap().entrySet())
                                {
                                    buffer.setLongLE(buffer.length(), CommonUtil.getLong(row.getKey()));
                                }

                                for (var row : records.getJsonObject(i).getMap().entrySet())
                                {
                                    buffer.setIntLE(buffer.length(), CommonUtil.getInteger(row.getValue()));
                                }
                            }
                            else
                            {
                                buffer.setIntLE(buffer.length(), CommonUtil.getInteger(records.getValue(i)));
                            }
                        }

                        case INT64 ->
                        {
                            if (sparkline)
                            {
                                for (var row : records.getJsonObject(i).getMap().entrySet())
                                {
                                    buffer.setLongLE(buffer.length(), CommonUtil.getLong(row.getKey()));
                                }

                                for (var row : records.getJsonObject(i).getMap().entrySet())
                                {
                                    buffer.setLongLE(buffer.length(), CommonUtil.getLong(row.getValue()));
                                }
                            }
                            else
                            {
                                buffer.setLongLE(buffer.length(), CommonUtil.getLong(records.getValue(i)));
                            }
                        }


                        case FLOAT32 ->
                        {
                            if (sparkline)
                            {

                                for (var row : records.getJsonObject(i).getMap().entrySet())
                                {
                                    buffer.setLongLE(buffer.length(), CommonUtil.getLong(row.getKey()));
                                }

                                for (var row : records.getJsonObject(i).getMap().entrySet())
                                {
                                    var bytes = ByteBuffer.allocate(8).putDouble(CommonUtil.getFloat(row.getValue())).array();

                                    for (var index = bytes.length - 1; index >= 0; index--)
                                    {
                                        buffer.setByte(buffer.length(), bytes[index]);
                                    }
                                }

                            }
                            else
                            {
                                var bytes = ByteBuffer.allocate(8).putDouble(CommonUtil.getFloat(records.getValue(i))).array();

                                for (var index = bytes.length - 1; index >= 0; index--)
                                {
                                    buffer.setByte(buffer.length(), bytes[index]);
                                }
                            }
                        }

                        case STRING ->
                        {
                            buffer.setIntLE(buffer.length(), CommonUtil.getInteger(String.valueOf(records.getValue(i)).getBytes(StandardCharsets.UTF_8).length));

                            buffer.setString(buffer.length(), String.valueOf(records.getValue(i)));
                        }
                    }
                }
            }
        });

        return buffer;
    }

    public static Buffer packChartResult(JsonObject result, int visualizationType, long queryId, long subQueryId)
    {
        var buffer = Buffer.buffer();

        packHeaders(buffer, queryId, subQueryId, CommonUtil.getShort(result.containsKey(QUERY_PROGRESS) ? result.remove(QUERY_PROGRESS) : 100), CommonUtil.getShort(1));

        buffer.setUnsignedByte(34, CommonUtil.getShort(visualizationType));

        buffer.setUnsignedByte(35, CommonUtil.getShort(result.size()));

        result.getMap().forEach((key, value) ->
        {
            var values = (JsonArray) value;

            var dataType = new AtomicReference<>(STRING);

            var category = DatastoreConstants.getDataCategory(true, key.split(CARET_SEPARATOR_WITH_ESCAPE)[0], CommonUtil.getString(values.getValue(0)));

            if (category == DatastoreConstants.DataCategory.FLOAT.getName())
            {
                dataType.set(FLOAT32);
            }
            else
            {
                if (values.getValue(0) instanceof Double || values.getValue(0) instanceof Float)
                {
                    dataType.set(FLOAT32);
                }

                // in case of result received from custom script (GoLang), value converted from Long to BigInteger
                else if (values.getValue(0) instanceof Long || values.getValue(0) instanceof BigInteger)
                {
                    dataType.set(INT64);
                }

                else if (values.getValue(0) instanceof Integer)
                {
                    dataType.set(INT32);

                    // In the custom script report when reading response from script (python, golang, node js), getting mix array of integer and Long like [12,4,6,3,5,-9223372036854775808]. So, It is
                    // throwing ClassCastException so, have to assign category by checking each value
                    for (var index = 0; index < values.size(); index++)
                    {
                        if (values.getValue(index) instanceof Long || CommonUtil.getLong(values.getValue(index)) == Long.MIN_VALUE)
                        {
                            dataType.set(INT64);

                            break;
                        }
                    }
                }
            }

            buffer.setIntLE(36, values.size());

            buffer.setIntLE(buffer.length(), 0);//extra bytes

            buffer.setUnsignedByte(buffer.length(), STRING.equals(dataType.get()) ? CommonUtil.getShort(DatastoreConstants.AggregationType.COUNT.ordinal()) : CommonUtil.getShort(DatastoreConstants.AggregationType.LAST.ordinal()));

            buffer.setUnsignedByte(buffer.length(), dataType.get().getSize());

            buffer.setShortLE(buffer.length(), CommonUtil.getShort(key.length()));

            buffer.setString(buffer.length(), key);

            buffer.setShortLE(buffer.length(), CommonUtil.getShort(key.length()));

            buffer.setString(buffer.length(), key);

            values.forEach(entry ->
            {
                switch (dataType.get())
                {
                    case INT32 -> buffer.setIntLE(buffer.length(), CommonUtil.getInteger(entry));

                    case INT64 -> buffer.setLongLE(buffer.length(), CommonUtil.getLong(entry));

                    case FLOAT32 ->
                    {

                        var bytes = ByteBuffer.allocate(8).putDouble(CommonUtil.getFloat(entry)).array();

                        for (var index = bytes.length - 1; index >= 0; index--)
                        {
                            buffer.setByte(buffer.length(), bytes[index]);
                        }
                    }

                    case STRING ->
                    {

                        buffer.setIntLE(buffer.length(), CommonUtil.getInteger(CommonUtil.getString(entry).getBytes(StandardCharsets.UTF_8).length));

                        buffer.setString(buffer.length(), CommonUtil.getString(entry));
                    }
                }
            });
        });

        return buffer;
    }

    static void send(String category, String type, JsonObject result, String message, long queryId, long subQueryId, Logger logger, String event)
    {
        try
        {
            if (result != null && !result.isEmpty())
            {
                switch (VisualizationConstants.VisualizationCategory.valueOfName(category))
                {
                    case GAUGE ->
                            Bootstrap.vertx().eventBus().publish(event, CodecUtil.compress(packGaugeResult(result, queryId, subQueryId).getBytes()));

                    case TOP_N ->
                    {
                        if (type.equalsIgnoreCase(VisualizationConstants.VisualizationCategory.GRID.getName()))
                        {
                            Bootstrap.vertx().eventBus().publish(event, CodecUtil.compress(packGridResult(result, VisualizationCategoryOrdinal.VISUALIZATION_TOPN_GRID.ordinal(), queryId, subQueryId).getBytes()));
                        }

                        else
                        {
                            Bootstrap.vertx().eventBus().publish(event, CodecUtil.compress(packChartResult(result, VisualizationCategoryOrdinal.VISUALIZATION_TOPN_CHART.ordinal(), queryId, subQueryId).getBytes()));
                        }
                    }
                    case HISTOGRAM ->
                            Bootstrap.vertx().eventBus().publish(event, CodecUtil.compress(packChartResult(result, VisualizationCategoryOrdinal.VISUALIZATION_HISTOGRAM.ordinal(), queryId, subQueryId).getBytes()));


                    default ->
                            Bootstrap.vertx().eventBus().publish(event, CodecUtil.compress(packGridResult(result, VisualizationCategoryOrdinal.VISUALIZATION_GRID.ordinal(), queryId, subQueryId).getBytes()));
                }
            }

            else
            {
                Bootstrap.vertx().eventBus().publish(event, CodecUtil.compress(packError(message, queryId, subQueryId).getBytes()));
            }
        }
        catch (Exception exception)
        {
            logger.error(exception);
        }
    }

    static void send(int category, Object value, Logger logger)
    {
        var buffer = Buffer.buffer();

        if (CommonUtil.traceEnabled())
        {
            logger.trace("Event aggregations columns updated sending to DB:" + JsonObject.mapFrom(value).encodePrettily());
        }

        buffer.appendByte(DatastoreConstants.OperationType.WIDGET_CREATE.getName()).appendBytes(new JsonObject().put(DatastoreConstants.DATASTORE_FORMAT, category)
                .mergeIn(JsonObject.mapFrom(value)).encode().getBytes());

        send(buffer);
    }

    // send this notification(s) to all DB , when create/delete widget happens
    static void send(Buffer buffer)
    {
        for (var id : RemoteEventProcessorConfigStore.getStore().getIds())
        {
            var item = RemoteEventProcessorConfigStore.getStore().getItem(CommonUtil.getLong(id));

            if (item != null && item.getString(REMOTE_EVENT_PROCESSOR_TYPE).equalsIgnoreCase(BootstrapType.DATASTORE.name()))
            {
                Bootstrap.vertx().eventBus().send(EVENT_PUBLICATION_DATASTORE_WRITE, new JsonObject()
                        .put(EventBusConstants.EVENT_COPY_REQUIRED, false).put(EventBusConstants.EVENT_TOPIC, DATASTORE_CONNECTION_ALIVE_TOPIC)
                        .put(REMOTE_EVENT_PROCESSOR_UUID, item.getString(REMOTE_EVENT_PROCESSOR_UUID))
                        .put(REMOTE_EVENT_PROCESSOR_TOPIC, DATASTORE_OPERATION_TOPIC)
                        .put(EVENT_CONTEXT, buffer.getBytes()));
            }
        }
    }

    // ------------------------------------- Decode Byte Buffer ----------------------------------------

    static Buffer packGaugeResult(JsonObject result, long queryId, long subQueryId)
    {
        var buffer = Buffer.buffer();

        packHeaders(buffer, queryId, subQueryId, CommonUtil.getShort(100), CommonUtil.getShort(1));

        buffer.setUnsignedByte(34, CommonUtil.getShort(VisualizationCategoryOrdinal.VISUALIZATION_GRID.ordinal()));

        buffer.setUnsignedByte(35, CommonUtil.getShort(result.size()));

        buffer.setIntLE(36, 1);

        result.getMap().forEach((key, value) ->
        {
            buffer.setIntLE(buffer.length(), 0);//extra bytes

            buffer.setUnsignedByte(buffer.length(), CommonUtil.getShort(DatastoreConstants.AggregationType.COUNT.ordinal()));

            buffer.setUnsignedByte(buffer.length(), CommonUtil.getShort(INT32.getName()));

            buffer.setShortLE(buffer.length(), CommonUtil.getShort(key.length()));

            buffer.setString(buffer.length(), key);

            buffer.setShortLE(buffer.length(), CommonUtil.getShort(key.length()));

            buffer.setString(buffer.length(), key);

            buffer.setIntLE(buffer.length(), CommonUtil.getInteger(value));
        });

        return buffer;
    }

    // ------------------------------------- Result Packing ----------------------------------------

    public static Buffer packError(String errorMessage, long queryId, long subQueryId)
    {
        var buffer = Buffer.buffer();

        packHeaders(buffer, queryId, subQueryId, CommonUtil.getShort(100), CommonUtil.getShort(0));

        buffer.setIntLE(34, errorMessage.length());//length of error

        buffer.setString(buffer.length(), errorMessage);//errors

        return buffer;
    }

    static void packHeaders(Buffer buffer, long queryId, long subQueryId, short progress, short status)
    {
        buffer.setLongLE(0, queryId);//queryId

        buffer.setLongLE(8, subQueryId);//subqueryId

        buffer.setUnsignedByte(16, progress);//progress of query

        buffer.setLongLE(17, 0);//records of query

        buffer.setLongLE(25, 0);//time of query

        buffer.setUnsignedByte(33, status);//status 0 means fail 1 success
    }

    public static Map<String, List<String>> filterGroups(String visualizationType, List<Object> filteredEntities)
    {
        var groups = new HashMap<String, List<String>>();

        if (!filteredEntities.isEmpty())
        {
            var builder = new StringBuilder();

            filteredEntities.forEach(group ->
            {
                var items = new JsonArray();

                if (visualizationType.equalsIgnoreCase(VisualizationConstants.VisualizationDataSource.LOG.getName()) || visualizationType.equalsIgnoreCase(VisualizationConstants.VisualizationDataSource.FLOW.getName()))
                {
                    items = EventSourceConfigStore.getStore().flatItemsByMultiValueFieldAny(LogEngineConstants.SOURCE_GROUPS, new JsonArray().add(CommonUtil.getLong(group)), EVENT_SOURCE);
                }

                else
                {
                    items = ObjectConfigStore.getStore().getObjectIdsByGroups(new JsonArray().add(CommonUtil.getLong(group)));
                }

                if (!items.isEmpty())
                {
                    builder.setLength(0);

                    CommonUtil.normalizeGroupHierarchy(GroupConfigStore.getStore().getItem(CommonUtil.getLong(group)), builder, null);

                    groups.put(builder.toString(), items.getList());
                }
            });
        }

        return groups;
    }

    public static void loadCategories(Map<String, Set<String>> eventCategories, Logger logger)
    {
        try
        {
            var file = new File(GlobalConstants.CURRENT_DIR + GlobalConstants.PATH_SEPARATOR + GlobalConstants.CONFIG_DIR + GlobalConstants.PATH_SEPARATOR + DatastoreConstants.EVENT_CATEGORIES);

            if (file.exists())
            {
                var buffer = Bootstrap.vertx().fileSystem().readFileBlocking(file.getPath());

                if (buffer != null && buffer.getBytes().length > 0)
                {
                    var categories = new JsonArray(Buffer.buffer(CodecUtil.toBytes(buffer.getBytes())));

                    for (var index = 0; index < categories.size(); index++)
                    {
                        var tokens = categories.getString(index).split(SEPARATOR_WITH_ESCAPE);

                        eventCategories.computeIfAbsent(tokens[0], value -> new HashSet<>()).add(tokens[1]);
                    }

                    logger.info("loaded event categories");
                }
            }
            else
            {
                Bootstrap.vertx().fileSystem().createFileBlocking(file.getPath());
            }
        }
        catch (Exception exception)
        {
            logger.error(exception);
        }
    }

    public static String prepareEventDataSource(JsonObject eventColumns, JsonObject context, JsonObject visualizationDataSource, Map<String, Object> entities, JsonObject user, JsonArray qualifiedSources, Set<Object> filteredEntities, Logger logger, Map<String, Set<String>> eventCategories)
    {
        var error = EMPTY_VALUE;

        try
        {

            var aggregations = 0;

            var plugins = new HashSet<>();

            var columns = new HashSet<String>();

            var visualizationDataPoints = new JsonArray();

            var dataPoints = visualizationDataSource.getJsonArray(VisualizationConstants.DATA_POINTS);

            var filterRequired = true;

            var eventSourceFilter = false;

            var eventHistory = false;

            var eventCategory = visualizationDataSource.getString(VisualizationConstants.TYPE).toLowerCase();

            var dataPointRequired = true;

            if (!visualizationDataSource.containsKey(CATEGORY))
            {
                visualizationDataSource.put(CATEGORY, EMPTY_VALUE);
            }

            if (eventCategory.equalsIgnoreCase(VisualizationDataSource.EVENT_HISTORY.getName()) && visualizationDataSource.getString(CATEGORY).equalsIgnoreCase(VisualizationDataSource.LOG.getName()))
            {
                JsonObject filters;

                var key = DATA_FILTER;
                //priority will be given to data filter..
                if (visualizationDataSource.containsKey(FILTERS) && visualizationDataSource.getJsonObject(FILTERS).getJsonObject(DATA_FILTER).isEmpty() && !visualizationDataSource.getJsonObject(FILTERS).getJsonObject(DRILL_DOWN_FILTER).isEmpty())
                {
                    filters = visualizationDataSource.getJsonObject(FILTERS);

                    key = DRILL_DOWN_FILTER;
                }
                else
                {
                    filters = visualizationDataSource.containsKey(FILTERS) && !visualizationDataSource.getJsonObject(FILTERS).getJsonObject(DATA_FILTER).isEmpty() ? visualizationDataSource.getJsonObject(FILTERS) : new JsonObject(VisualizationConstants.VISUALIZATION_DATA_FILTER_JSON_TEMPLATE);
                }


                var conditionGroups = filters.getJsonObject(key).getJsonArray(CONDITION_GROUPS);

                for (var i = 0; i < conditionGroups.size(); i++)
                {
                    var conditions = conditionGroups.getJsonObject(i).getJsonArray(CONDITIONS);

                    for (var j = 0; j < conditions.size(); j++)
                    {
                        var condition = conditions.getJsonObject(j);

                        if (!condition.getString(OPERAND).equalsIgnoreCase(EVENT_SOURCE) && !condition.getString(OPERAND).equalsIgnoreCase(MESSAGE) && !condition.getString(OPERAND).equalsIgnoreCase(LogEngineConstants.EVENT_CATEGORY) && !condition.getString(OPERAND).equalsIgnoreCase(LogEngineConstants.EVENT_SOURCE_TYPE) && !condition.getString(OPERAND).equalsIgnoreCase(LogEngineConstants.EVENT_PATTERN_ID))
                        {
                            dataPointRequired = false;

                            var operand = condition.getString(OPERAND);

                            var ids = eventColumns.getJsonObject(operand).getJsonArray(DatastoreConstants.MAPPER_PLUGIN_IDS);

                            eventCategory = eventColumns.getJsonObject(operand).getString(DatastoreConstants.MAPPER_EVENT_CATEGORY, EMPTY_VALUE);

                            removeDefaultPluginIds(ids);

                            var dataPoint = dataPoints.getJsonObject(0);

                            var pluginIds = new JsonArray();

                            pluginIds.add(ids.getValue(0) + DASH_SEPARATOR + eventCategory.replace(" ", ".").toLowerCase());

                            pluginIds.forEach(plugins::add);

                            if (dataPoint.containsKey(ENTITY_TYPE) && (dataPoint.getString(ENTITY_TYPE).equalsIgnoreCase(VisualizationConstants.VisualizationGrouping.EVENT_SOURCE.getName()) || dataPoint.getString(ENTITY_TYPE).equalsIgnoreCase(VisualizationConstants.VisualizationGrouping.EVENT_CATEGORY.getName()) || dataPoint.getString(ENTITY_TYPE).equalsIgnoreCase(VisualizationConstants.VisualizationGrouping.EVENT_SOURCE_TYPE.getName()) || dataPoint.getString(ENTITY_TYPE).equalsIgnoreCase(VisualizationConstants.VisualizationGrouping.GROUP.getName()) || dataPoint.getString(ENTITY_TYPE).equalsIgnoreCase(VisualizationConstants.VisualizationGrouping.MONITOR.getName())) && !dataPoint.getJsonArray(ENTITIES).isEmpty())
                            {
                                eventSourceFilter = true;
                            }

                            aggregations += prepareEventDataSource(true, visualizationDataPoints, pluginIds, plugins, dataPoint, visualizationDataSource, user, entities, qualifiedSources, filteredEntities, logger);

                            break;
                        }
                    }
                }
            }

            if (dataPointRequired)
            {
                for (var i = 0; i < dataPoints.size(); i++)
                {
                    var pluginIds = new JsonArray();

                    var dataPoint = dataPoints.getJsonObject(i);

                    var column = dataPoint.getString(VisualizationConstants.DATA_POINT);

                    columns.add(column);

                    if (eventColumns.containsKey(column) || column.equalsIgnoreCase("*"))
                    {
                        if (visualizationDataSource.getString(VisualizationConstants.TYPE).equalsIgnoreCase(VisualizationConstants.VisualizationDataSource.LOG.getName()) || visualizationDataSource.getString(VisualizationConstants.TYPE).equalsIgnoreCase(VisualizationDataSource.HEALTH_METRIC.getName()) || (visualizationDataSource.getString(CATEGORY).equalsIgnoreCase(VisualizationDataSource.LOG.getName()) && visualizationDataSource.getString(VisualizationConstants.TYPE).equalsIgnoreCase(VisualizationConstants.VisualizationDataSource.EVENT_HISTORY.getName())))
                        {
                            var ids = eventColumns.getJsonObject(column).getJsonArray(DatastoreConstants.MAPPER_PLUGIN_IDS);

                            eventCategory = eventColumns.getJsonObject(column).getString(DatastoreConstants.MAPPER_EVENT_CATEGORY, EMPTY_VALUE);

                            removeDefaultPluginIds(ids);

                            if (column.equalsIgnoreCase(EVENT) || ids.getValue(0).equals(DatastoreConstants.PluginId.GENERIC_LOG_EVENT.getName()) || visualizationDataSource.getString(VisualizationConstants.TYPE).equalsIgnoreCase(VisualizationConstants.VisualizationDataSource.EVENT_HISTORY.getName()))
                            {
                                eventHistory = true;

                                pluginIds.add(DatastoreConstants.PluginId.GENERIC_LOG_EVENT.getName() + DASH_SEPARATOR + VisualizationConstants.VisualizationDataSource.EVENT_HISTORY.getName());
                            }

                            else
                            {

                                pluginIds.add(ids.getValue(0) + DASH_SEPARATOR + eventCategory.replace(" ", ".").toLowerCase());
                            }
                        }

                        else if (visualizationDataSource.getString(VisualizationConstants.TYPE).equalsIgnoreCase(VisualizationDataSource.POLICY_RESULT.getName()))
                        {
                            pluginIds.add(DatastoreConstants.PluginId.POLICY_RESULT.getName() + DASH_SEPARATOR + eventCategory);
                        }

                        else if (visualizationDataSource.getString(VisualizationConstants.TYPE).equalsIgnoreCase(VisualizationDataSource.CORRELATED_METRIC.getName()))
                        {
                            pluginIds.add(DatastoreConstants.PluginId.CORRELATED_METRIC.getName() + DASH_SEPARATOR + eventCategory);
                        }

                        else if (visualizationDataSource.getString(VisualizationConstants.TYPE).equalsIgnoreCase(VisualizationDataSource.RUNBOOK_WORKLOG.getName()))
                        {
                            pluginIds.add(DatastoreConstants.PluginId.RUNBOOK_WORKLOG.getName() + DASH_SEPARATOR + eventCategory);
                        }

                        else if ((visualizationDataSource.getString(CATEGORY).equalsIgnoreCase(VisualizationDataSource.FLOW.getName()) && visualizationDataSource.getString(VisualizationConstants.TYPE).equalsIgnoreCase(VisualizationConstants.VisualizationDataSource.EVENT_HISTORY.getName())) || visualizationDataSource.getString(VisualizationConstants.TYPE).equalsIgnoreCase(VisualizationDataSource.FLOW.getName()))
                        {
                            var ids = eventColumns.getJsonObject(column).getJsonArray(DatastoreConstants.MAPPER_PLUGIN_IDS);

                            if (ids.getValue(0).equals(DatastoreConstants.PluginId.FLOW_EVENT_STAT.getName()))
                            {
                                pluginIds.add(DatastoreConstants.PluginId.FLOW_EVENT_STAT.getName() + DASH_SEPARATOR + VisualizationDataSource.FLOW.getName());
                            }
                            else
                            {
                                pluginIds.add(DatastoreConstants.PluginId.FLOW_EVENT.getName() + DASH_SEPARATOR + VisualizationDataSource.FLOW.getName());
                            }
                        }

                        else if ((visualizationDataSource.getString(CATEGORY).equalsIgnoreCase(VisualizationDataSource.AUDIT.getName()) && visualizationDataSource.getString(VisualizationConstants.TYPE).equalsIgnoreCase(VisualizationConstants.VisualizationDataSource.EVENT_HISTORY.getName())) || visualizationDataSource.getString(VisualizationConstants.TYPE).equalsIgnoreCase(VisualizationDataSource.AUDIT.getName()))
                        {
                            pluginIds.add(DatastoreConstants.PluginId.AUDIT_EVENT.getName() + DASH_SEPARATOR + VisualizationDataSource.AUDIT.getName());

                            filterRequired = false;
                        }

                        else if ((visualizationDataSource.getString(CATEGORY).equalsIgnoreCase(VisualizationDataSource.USER_NOTIFICATION.getName()) && visualizationDataSource.getString(VisualizationConstants.TYPE).equalsIgnoreCase(VisualizationConstants.VisualizationDataSource.EVENT_HISTORY.getName())) || visualizationDataSource.getString(VisualizationConstants.TYPE).equalsIgnoreCase(VisualizationConstants.VisualizationDataSource.USER_NOTIFICATION.getName()))
                        {
                            pluginIds.add(DatastoreConstants.PluginId.NOTIFICATION_EVENT.getName() + DASH_SEPARATOR + VisualizationDataSource.USER_NOTIFICATION.getName());

                            filterRequired = false;
                        }

                        else if (visualizationDataSource.getString(VisualizationConstants.TYPE).equalsIgnoreCase(VisualizationConstants.VisualizationDataSource.TRAP.getName()) || visualizationDataSource.getString(VisualizationConstants.TYPE).equalsIgnoreCase(VisualizationConstants.VisualizationDataSource.TRAP_FLAP.getName()) || visualizationDataSource.getString(VisualizationConstants.TYPE).equalsIgnoreCase(VisualizationConstants.VisualizationDataSource.TRAP_ACKNOWLEDGEMENT.getName()))
                        {
                            pluginIds.add(DatastoreConstants.PluginId.TRAP_EVENT.getName() + DASH_SEPARATOR + eventCategory);
                        }

                        else if ((visualizationDataSource.getString(VisualizationConstants.TYPE).equalsIgnoreCase(VisualizationConstants.VisualizationDataSource.POLICY.getName()) || visualizationDataSource.getString(VisualizationConstants.TYPE).equalsIgnoreCase(VisualizationConstants.VisualizationDataSource.POLICY_ACKNOWLEDGEMENT.getName())) && visualizationDataSource.getString(VisualizationConstants.CATEGORY).equalsIgnoreCase(VisualizationConstants.VisualizationDataSource.FLOW.getName()))
                        {
                            pluginIds.add(DatastoreConstants.PluginId.POLICY_FLOW.getName() + DASH_SEPARATOR + eventCategory);
                        }

                        else if ((visualizationDataSource.getString(VisualizationConstants.TYPE).equalsIgnoreCase(VisualizationConstants.VisualizationDataSource.POLICY.getName()) || visualizationDataSource.getString(VisualizationConstants.TYPE).equalsIgnoreCase(VisualizationConstants.VisualizationDataSource.POLICY_ACKNOWLEDGEMENT.getName())) && visualizationDataSource.getString(VisualizationConstants.CATEGORY).equalsIgnoreCase(VisualizationConstants.VisualizationDataSource.LOG.getName()))
                        {
                            pluginIds.add(DatastoreConstants.PluginId.POLICY_EVENT.getName() + DASH_SEPARATOR + eventCategory);
                        }

                        else if ((visualizationDataSource.getString(VisualizationConstants.TYPE).equalsIgnoreCase(VisualizationConstants.VisualizationDataSource.POLICY.getName()) || visualizationDataSource.getString(VisualizationConstants.TYPE).equalsIgnoreCase(VisualizationConstants.VisualizationDataSource.POLICY_ACKNOWLEDGEMENT.getName())) && visualizationDataSource.getString(VisualizationConstants.CATEGORY).equalsIgnoreCase(VisualizationConstants.VisualizationDataSource.TRAP.getName()))
                        {
                            pluginIds.add(DatastoreConstants.PluginId.POLICY_TRAP.getName() + DASH_SEPARATOR + eventCategory);
                        }

                        else if (visualizationDataSource.getString(VisualizationConstants.TYPE).equalsIgnoreCase(VisualizationDataSource.STATIC_METRIC.getName()))
                        {
                            pluginIds.add(DatastoreConstants.PluginId.STATIC_METRIC.getName() + DASH_SEPARATOR + eventCategory);
                        }
                        else if (visualizationDataSource.getString(VisualizationConstants.TYPE).equalsIgnoreCase(VisualizationDataSource.COMPLIANCE.getName()))
                        {
                            pluginIds.add(DatastoreConstants.PluginId.COMPLIANCE.getName() + DASH_SEPARATOR + eventCategory);

                            filterRequired = false;
                        }
                    }

                    if (context.containsKey(ENTITY_TYPE))//request from explorer dashboard with entity type to replace in filter
                    {
                        dataPoint.put(ENTITY_TYPE, context.getString(ENTITY_TYPE));

                        dataPoint.put(ENTITIES, context.getJsonArray(ENTITIES));
                    }

                    if (!pluginIds.isEmpty())
                    {
                        pluginIds.forEach(plugins::add);

                        if (dataPoint.containsKey(ENTITY_TYPE) && (dataPoint.getString(ENTITY_TYPE).equalsIgnoreCase(VisualizationConstants.VisualizationGrouping.EVENT_SOURCE.getName()) || dataPoint.getString(ENTITY_TYPE).equalsIgnoreCase(VisualizationConstants.VisualizationGrouping.EVENT_CATEGORY.getName()) || dataPoint.getString(ENTITY_TYPE).equalsIgnoreCase(VisualizationConstants.VisualizationGrouping.EVENT_SOURCE_TYPE.getName()) || dataPoint.getString(ENTITY_TYPE).equalsIgnoreCase(VisualizationConstants.VisualizationGrouping.GROUP.getName()) || dataPoint.getString(ENTITY_TYPE).equalsIgnoreCase(VisualizationConstants.VisualizationGrouping.MONITOR.getName())) && dataPoint.getJsonArray(ENTITIES) != null && !dataPoint.getJsonArray(ENTITIES).isEmpty())
                        {
                            eventSourceFilter = true;
                        }

                        aggregations += prepareEventDataSource(filterRequired, visualizationDataPoints, pluginIds, plugins, dataPoint, visualizationDataSource, user, entities, qualifiedSources, filteredEntities, logger);
                    }
                }
            }

            if (!visualizationDataPoints.isEmpty())
            {
                if (aggregations > VisualizationConstants.MAX_EVENT_AGGREGATION_FUNCTIONS)// combination of unique counters with selected aggregation should be less than 16
                {
                    error = ErrorMessageConstants.DATA_POINT_LIMIT_EXCEEDED;
                }

                else
                {
                    visualizationDataSource.put(ENTITIES, entities);

                    visualizationDataSource.put(VisualizationConstants.PLUGINS, new ArrayList<>(plugins));
                }
            }

            else
            {
                error = ErrorMessageConstants.NO_ENTITY_QUALIFIED;
            }

            visualizationDataSource.put(LogEngineConstants.EVENT_CATEGORY, eventCategory);

            if (filterRequired && !entities.isEmpty())
            {
                applyFilter(context, visualizationDataSource, entities.keySet(), eventSourceFilter, eventHistory, eventCategories);
            }
        }
        catch (Exception exception)
        {
            logger.error(exception);
        }

        return error;
    }

    private static int prepareEventDataSource(boolean filterRequired, JsonArray visualizationDataPoints, JsonArray pluginIds, Set<Object> plugins, JsonObject visualizationDataPoint, JsonObject visualizationDataSource, JsonObject user, Map<String, Object> entities, JsonArray qualifiedSources, Set<Object> filteredEntities, Logger logger)
    {
        var aggregationFuncs = 0;

        try
        {
            var qualifiedEntities = qualifyEventSources(visualizationDataSource, visualizationDataPoint, user.getJsonArray(User.USER_GROUPS), qualifiedSources, filteredEntities, pluginIds, logger);

            if (!qualifiedEntities.isEmpty())
            {
                visualizationDataPoint.put(VisualizationConstants.PLUGINS, qualifiedEntities.remove(VisualizationConstants.PLUGINS));

                if (visualizationDataPoint.getString(VisualizationConstants.AGGREGATOR).equalsIgnoreCase(DatastoreConstants.AggregationType.AVG.getName()))
                {
                    aggregationFuncs += 2;
                }

                else
                {
                    aggregationFuncs++;
                }

                entities.putAll(qualifiedEntities);

                plugins.addAll(entities.values());

                visualizationDataPoint.put(ENTITIES, qualifiedEntities);

                visualizationDataPoints.add(visualizationDataPoint);
            }

            else if (!filterRequired)
            {

                visualizationDataPoint.put(VisualizationConstants.PLUGINS, new ArrayList<>(plugins));

                visualizationDataPoints.add(visualizationDataPoint);
            }
        }

        catch (Exception exception)
        {
            logger.error(exception);
        }

        return aggregationFuncs;
    }

    /**
     * Applies filters to the given visualization data source based on the provided category, context, and filter sets.
     * <p>
     * This method updates the visualizationDataSource with filter conditions for severities, policies, tags, and entities.
     * It supports dynamic filter construction for different visualization types and categories, including metrics, events,
     * and network route metrics/events. The method also handles special cases for cumulative object status flaps and
     * ensures that the correct operands and operators are set for each filter condition.
     *
     * @param category                The visualization category (e.g., metric, event, etc.).
     * @param context                 The context object containing additional filter flags and user roles.
     * @param cache                   Indicates if the filter should use cached values.
     * @param visualizationDataSource The data source object to which filters will be applied.
     * @param severities              The set to collect severity filter values.
     * @param policies                The set to collect policy filter values.
     * @param entities                The set to collect entity filter values.
     */
    public static void applyFilter(String category, JsonObject context, boolean cache, JsonObject visualizationDataSource, Set<Object> severities, Set<Object> policies, Set<String> entities)
    {
        var filter = false;

        var qualifiedEntities = new ArrayList<Integer>();

        if (visualizationDataSource.containsKey(SEVERITY) && !visualizationDataSource.getJsonArray(SEVERITY).isEmpty())
        {
            filter = true;

            severities.addAll(visualizationDataSource.getJsonArray(SEVERITY).getList());
        }

        if (visualizationDataSource.containsKey(POLICIES) && !visualizationDataSource.getJsonArray(POLICIES).isEmpty())
        {
            filter = true;

            policies.addAll(visualizationDataSource.getJsonArray(POLICIES).getList());
        }

        if (visualizationDataSource.containsKey(TAGS) && !visualizationDataSource.getJsonArray(TAGS).isEmpty())
        {
            filter = true;

            if (category.equalsIgnoreCase(METRIC))
            {
                policies.addAll(MetricPolicyConfigStore.getStore().flatItemsByMultiValueFieldAny(PolicyEngineConstants.POLICY_TAGS, visualizationDataSource.getJsonArray(TAGS), ID).getList());
            }
            else if (category.equalsIgnoreCase(VisualizationDataSource.NETROUTE_METRIC.getName()) || category.equalsIgnoreCase(VisualizationDataSource.NETROUTE_EVENT.getName()))
            {
                policies.addAll(NetRoutePolicyConfigStore.getStore().flatItemsByMultiValueFieldAny(PolicyEngineConstants.POLICY_TAGS, visualizationDataSource.getJsonArray(TAGS), ID).getList());
            }
            else
            {
                policies.addAll(EventPolicyConfigStore.getStore().flatItemsByMultiValueFieldAny(PolicyEngineConstants.POLICY_TAGS, visualizationDataSource.getJsonArray(TAGS), ID).getList());
            }
        }

        if (category.equalsIgnoreCase(METRIC) && (context.getBoolean(OBJECT_FILTER, false) || (!cache && !context.getBoolean(VisualizationConstants.ADMIN_ROLE, false))) && !entities.isEmpty() && !visualizationDataSource.getString(VisualizationConstants.TYPE).equalsIgnoreCase(VisualizationConstants.VisualizationDataSource.POLICY_ACKNOWLEDGEMENT.getName()))
        {
            filter = true;

            qualifiedEntities = (ArrayList<Integer>) entities.stream().map(CommonUtil::getInteger).collect(Collectors.toList());
        }

        if (!cache && filter)
        {
            var filters = visualizationDataSource.containsKey(FILTERS) && !visualizationDataSource.getJsonObject(FILTERS).getJsonObject(DATA_FILTER).isEmpty() ? visualizationDataSource.getJsonObject(FILTERS) : new JsonObject(VisualizationConstants.VISUALIZATION_DATA_FILTER_JSON_TEMPLATE);

            var conditionGroup = filters.getJsonObject(DATA_FILTER).getJsonArray(CONDITION_GROUPS).getJsonObject(0);

            var conditions = !conditionGroup.getJsonArray(CONDITIONS).isEmpty() ? conditionGroup.getJsonArray(CONDITIONS) : new JsonArray();

            if (visualizationDataSource.getString(TYPE).equalsIgnoreCase(VisualizationDataSource.CUMULATIVE_OBJECT_STATUS_FLAP.getName()) && !conditions.isEmpty() && conditions.getJsonObject(0) != null && conditions.getJsonObject(0).containsKey(OPERAND) && conditions.getJsonObject(0).getString(OPERAND).equalsIgnoreCase(NMSConstants.INTERFACE))//TODO need proper condition
            {
                conditions.getJsonObject(0).put(OPERAND, INSTANCE);
            }

            if (severities != null && !severities.isEmpty())
            {
                conditions.add(new JsonObject().put(OPERAND, SEVERITY).put(VALUE, severities.size() > 1 ? new ArrayList<>(severities) : new ArrayList<>(severities).getFirst()).put(OPERATOR, severities.size() > 1 ? DatastoreConstants.ConditionGroup.IN.getName() : PolicyEngineConstants.Operator.EQUAL.getName()));
            }

            if (policies != null && !policies.isEmpty())
            {
                conditions.add(new JsonObject().put(OPERAND, PolicyEngineConstants.POLICY_ID).put(VALUE, policies.size() > 1 ? new ArrayList<>(policies) : new ArrayList<>(policies).getFirst()).put(OPERATOR, policies.size() > 1 ? DatastoreConstants.ConditionGroup.IN.getName() : PolicyEngineConstants.Operator.EQUAL.getName()));
            }

            if (!qualifiedEntities.isEmpty())
            {
                conditions.add(new JsonObject().put(OPERAND, AIOpsObject.OBJECT_ID).put(VALUE, qualifiedEntities.size() > 1 ? qualifiedEntities : qualifiedEntities.getFirst()).put(OPERATOR, qualifiedEntities.size() > 1 ? DatastoreConstants.ConditionGroup.IN.getName() : PolicyEngineConstants.Operator.EQUAL.getName()));
            }

            conditionGroup.put(CONDITIONS, conditions);

            visualizationDataSource.put(FILTERS, filters);
        }
    }

    public static Map<String, Object> qualifyEntities(boolean correlated, JsonObject context, JsonArray qualifiedGroups, Set<Integer> qualifiedObjects, Set filteredGroupEntities, Set filteredTagEntities, Logger logger, boolean archived)
    {
        Map<String, Object> qualifiedEntities = null;

        try
        {

            Map<String, String> entities = null;

            if (!context.containsKey(ENTITY_TYPE))
            {
                context.put(ENTITY_TYPE, "all");
            }

            var items = filterEntities(context, qualifiedGroups, qualifiedObjects, filteredGroupEntities, filteredTagEntities, logger, archived);

            var plugins = new ArrayList<String>();

            if (correlated)
            {
                plugins.add(DatastoreConstants.PluginId.CORRELATED_METRIC.getName() + DASH_SEPARATOR + VisualizationDataSource.CORRELATED_METRIC.getName());
            }

            else
            {
                plugins.add(DatastoreConstants.PluginId.METRIC_POLICY_DURATION_METRIC.getName() + DASH_SEPARATOR + VisualizationConstants.VisualizationDataSource.POLICY_FLAP.getName());
            }

            if (items != null && !items.isEmpty())
            {
                entities = new HashMap<>();

                for (var item : items)
                {
                    entities.put(CommonUtil.getString(item), plugins.getFirst());
                }
            }

            if (entities != null)
            {
                qualifiedEntities = new HashMap<>();

                qualifiedEntities.put(ENTITIES, entities);

                qualifiedEntities.put(VisualizationConstants.PLUGINS, plugins);
            }
        }

        catch (Exception exception)
        {
            logger.error(exception);
        }

        return qualifiedEntities;
    }

    //public static final Set<String> RESERVED_METRICS = Set.of("~duration");

    private static void removeDefaultPluginIds(JsonArray pluginIds)
    {
        var iterator = pluginIds.stream().iterator();

        while (iterator.hasNext())
        {
            var pluginId = CommonUtil.getInteger(iterator.next());

            if (pluginId == DatastoreConstants.PluginId.TRAP_EVENT.getName() || pluginId == DatastoreConstants.PluginId.FLOW_EVENT.getName() || pluginId == DatastoreConstants.PluginId.AUDIT_EVENT.getName() || pluginId == DatastoreConstants.PluginId.NOTIFICATION_EVENT.getName() || pluginId == DatastoreConstants.PluginId.POLICY_EVENT.getName() || pluginId == DatastoreConstants.PluginId.POLICY_FLOW.getName() || pluginId == DatastoreConstants.PluginId.POLICY_TRAP.getName())
            {
                iterator.remove();
            }
        }
    }

    //    -----------------------------------For Event
    private static Map<String, Object> qualifyEventSources(JsonObject visualizationDataSource, JsonObject context, JsonArray qualifiedGroups, JsonArray qualifiedSources, Set filteredEntities, JsonArray pluginIds, Logger logger)
    {
        List<String> items;

        var qualifiedEntities = new HashMap<String, Object>();

        try
        {

            var groupType = visualizationDataSource.getString(TYPE);

            if (!context.containsKey(ENTITY_TYPE))
            {
                context.put(ENTITY_TYPE, "all");
            }

            if (context.getString(ENTITY_TYPE).equalsIgnoreCase(VisualizationConstants.VisualizationGrouping.MONITOR.getName()) && context.getJsonArray(ENTITIES) != null && !context.getJsonArray(ENTITIES).isEmpty())
            {
                items = ObjectConfigStore.getStore().flatItemsByValues(ID, context.getJsonArray(ENTITIES), AIOpsObject.OBJECT_IP).stream().filter(qualifiedSources::contains).map(CommonUtil::getString).toList();
            }

            else if (context.getString(ENTITY_TYPE).equalsIgnoreCase(VisualizationConstants.VisualizationGrouping.EVENT_SOURCE.getName()) && context.getJsonArray(ENTITIES) != null && !context.getJsonArray(ENTITIES).isEmpty())
            {
                items = context.getJsonArray(ENTITIES).stream().filter(qualifiedSources::contains).map(CommonUtil::getString).toList();
            }

            else if (context.getString(ENTITY_TYPE).equalsIgnoreCase(VisualizationConstants.VisualizationGrouping.GROUP.getName()) && context.getJsonArray(ENTITIES) != null && !context.getJsonArray(ENTITIES).isEmpty())
            {
                filteredEntities.addAll(context.getJsonArray(ENTITIES).stream().toList());

                if (groupType.equalsIgnoreCase(VisualizationConstants.VisualizationDataSource.LOG.getName()))
                {
                    items = EventSourceConfigStore.getStore().getItemsByMultiValueFieldAny(LogEngineConstants.SOURCE_GROUPS, new JsonArray(context.getJsonArray(ENTITIES).stream().filter(qualifiedGroups::contains).distinct().toList()), EVENT_SOURCE).stream().filter(qualifiedSources::contains).map(CommonUtil::getString).toList();
                }

                else
                {
                    items = EventSourceConfigStore.getStore().flatItemsByMultiValueFieldAny(LogEngineConstants.SOURCE_GROUPS, new JsonArray(context.getJsonArray(ENTITIES).stream().filter(qualifiedGroups::contains).distinct().toList()), EVENT_SOURCE).stream().filter(qualifiedSources::contains).map(CommonUtil::getString).toList();
                }
            }

            else if (context.getString(ENTITY_TYPE).equalsIgnoreCase(VisualizationConstants.VisualizationGrouping.EVENT_SOURCE_TYPE.getName()) && context.getJsonArray(ENTITIES) != null && !context.getJsonArray(ENTITIES).isEmpty())
            {
                items = EventSourceConfigStore.getStore().getItemsByMultiValueFieldAny(PLUGIN_ID, new JsonArray(LogParserConfigStore.getStore().getItems().stream().map(JsonObject::mapFrom).filter(item -> context.getJsonArray(ENTITIES).contains(item.getString(LogParser.LOG_PARSER_SOURCE_TYPE))).map(item -> item.getInteger(PLUGIN_ID)).toList()), EVENT_SOURCE).getList();
            }

            else if (context.getString(ENTITY_TYPE).equalsIgnoreCase(VisualizationConstants.VisualizationGrouping.EVENT_CATEGORY.getName()) && context.getJsonArray(ENTITIES) != null && !context.getJsonArray(ENTITIES).isEmpty())
            {
                items = EventSourceConfigStore.getStore().flatItemsByMultiValueFieldAny(VisualizationConstants.VisualizationGrouping.EVENT_CATEGORY.getName(), context.getJsonArray(ENTITIES), EVENT_SOURCE).getList();
            }

            else
            {

                if ((visualizationDataSource.containsKey(CATEGORY) && visualizationDataSource.getString(CATEGORY).equalsIgnoreCase(EVENT_LOG) && groupType.equalsIgnoreCase(VisualizationDataSource.EVENT_HISTORY.getName()) || (groupType.equalsIgnoreCase(VisualizationConstants.VisualizationDataSource.LOG.getName()) || groupType.equalsIgnoreCase(VisualizationDataSource.HEALTH_METRIC.getName()))))
                {
                    var pluginId = CommonUtil.getInteger(pluginIds.getString(0).split(DASH_SEPARATOR)[0]);

                    if (pluginId == DatastoreConstants.PluginId.HEALTH_METRIC.getName() || pluginId == DatastoreConstants.PluginId.CACHE_DATASTORE_EVENT.getName() || pluginId == DatastoreConstants.PluginId.QUERY_STATS_DATASTORE_EVENT.getName() || pluginId == DatastoreConstants.PluginId.PENDING_FILES_DATASTORE_EVENT.getName())
                    {
                        items = qualifiedSources.stream().map(CommonUtil::getString).filter(EventSourceConfigStore.getStore().getItemsByMultiValueFields(EVENT_TYPE, VisualizationDataSource.HEALTH_METRIC.getName(), EVENT_SOURCE, LogEngineConstants.EVENT_CATEGORY, pluginId)::contains).toList();
                    }
                    else if (pluginId != DatastoreConstants.PluginId.GENERIC_LOG_EVENT.getName() && pluginId != DatastoreConstants.PluginId.LOG_EVENT_STAT.getName() && pluginId != DatastoreConstants.PluginId.FLOW_EVENT_STAT.getName())
                    {
                        items = qualifiedSources.stream().map(CommonUtil::getString).filter(EventSourceConfigStore.getStore().getItemsByMultiValueFields(EVENT_TYPE, VisualizationConstants.VisualizationDataSource.LOG.getName(), EVENT_SOURCE, LogEngineConstants.EVENT_CATEGORY, pluginId)::contains).toList();
                    }
                    else if (pluginId == DatastoreConstants.PluginId.FLOW_EVENT_STAT.getName())
                    {
                        items = qualifiedSources.stream().map(CommonUtil::getString).filter(EventSourceConfigStore.getStore().flatItemsByMultiValueField(EVENT_TYPE, VisualizationConstants.VisualizationDataSource.FLOW.getName(), EVENT_SOURCE)::contains).toList();
                    }
                    else
                    {
                        items = qualifiedSources.stream().map(CommonUtil::getString).filter(EventSourceConfigStore.getStore().flatItemsByMultiValueFields(EVENT_TYPE, VisualizationConstants.VisualizationDataSource.LOG.getName(), EVENT_SOURCE, LogEngineConstants.EVENT_CATEGORY)::contains).toList();
                    }
                }

                else if ((visualizationDataSource.containsKey(CATEGORY) && visualizationDataSource.getString(CATEGORY).equals(EVENT_FLOW)) || groupType.equalsIgnoreCase(VisualizationConstants.VisualizationDataSource.FLOW.getName()))
                {
                    items = qualifiedSources.stream().map(CommonUtil::getString).filter(EventSourceConfigStore.getStore().flatItemsByMultiValueField(EVENT_TYPE, VisualizationConstants.VisualizationDataSource.FLOW.getName(), EVENT_SOURCE)::contains).toList();
                }

                else if ((visualizationDataSource.containsKey(CATEGORY) && visualizationDataSource.getString(CATEGORY).equals(EVENT_TRAP)) || groupType.contains(VisualizationConstants.VisualizationDataSource.TRAP.getName()) || groupType.contains(VisualizationDataSource.TRAP_FLAP.getName()) || groupType.contains(VisualizationDataSource.TRAP_ACKNOWLEDGEMENT.getName()))//as in trap will not be considering groups will be qualifying all hosts from which trap received
                {
                    items = EventSourceConfigStore.getStore().flatItemsByMultiValueField(EVENT_TYPE, VisualizationConstants.VisualizationDataSource.TRAP.getName(), EVENT_SOURCE).getList();
                }

                else if (visualizationDataSource.getString(VisualizationConstants.TYPE).equalsIgnoreCase(VisualizationDataSource.POLICY_RESULT.getName()) || visualizationDataSource.getString(VisualizationConstants.TYPE).equalsIgnoreCase(VisualizationConstants.VisualizationDataSource.POLICY.getName()) || visualizationDataSource.getString(VisualizationConstants.TYPE).equalsIgnoreCase(VisualizationConstants.VisualizationDataSource.POLICY_ACKNOWLEDGEMENT.getName()))//as in policy will not be considering groups will be qualifying all hosts from which log or flow is received
                {
                    items = EventSourceConfigStore.getStore().flatItemsByMultiValueField(EVENT_TYPE, visualizationDataSource.getString(VisualizationConstants.CATEGORY).equalsIgnoreCase(VisualizationConstants.VisualizationDataSource.FLOW.getName()) ? VisualizationDataSource.FLOW.getName() : VisualizationDataSource.LOG.getName(), EVENT_SOURCE).getList();
                }

                else
                {
                    items = qualifiedSources.getList();
                }
            }

            if (!items.isEmpty())
            {
                var plugins = new HashSet<>();

                for (var item : items)
                {
                    for (var plugin : pluginIds)
                    {
                        qualifiedEntities.put(item, plugin);

                        plugins.add(plugin);
                    }
                }

                qualifiedEntities.put(VisualizationConstants.PLUGINS, new ArrayList<>(plugins));
            }
        }
        catch (Exception exception)
        {
            logger.error(exception);
        }

        return qualifiedEntities;
    }

    public static void applyFilter(JsonObject context, JsonObject visualizationDataSource, Set<String> entities, boolean eventSourceFilter, boolean eventHistory, Map<String, Set<String>> eventCategories)
    {
        var filters = visualizationDataSource.containsKey(FILTERS) && !visualizationDataSource.getJsonObject(FILTERS).getJsonObject(DATA_FILTER).isEmpty() ? visualizationDataSource.getJsonObject(FILTERS) : new JsonObject(VisualizationConstants.VISUALIZATION_DATA_FILTER_JSON_TEMPLATE);

        var conditionGroup = filters.getJsonObject(DATA_FILTER).getJsonArray(CONDITION_GROUPS).getJsonObject(0);

        var conditions = !conditionGroup.getJsonArray(CONDITIONS).isEmpty() ? conditionGroup.getJsonArray(CONDITIONS) : new JsonArray();

        if (visualizationDataSource.containsKey(FILTERS) && visualizationDataSource.getJsonObject(FILTERS).containsKey(RESULT_FILTER) && !filters.containsKey(RESULT_FILTER))
        {
            filters.put(RESULT_FILTER, visualizationDataSource.getJsonObject(FILTERS).getJsonObject(RESULT_FILTER));
        }

        if (visualizationDataSource.containsKey(FILTERS) && visualizationDataSource.getJsonObject(FILTERS).containsKey(DRILL_DOWN_FILTER))
        {
            filters.put(DRILL_DOWN_FILTER, visualizationDataSource.getJsonObject(FILTERS).getJsonObject(DRILL_DOWN_FILTER));
        }

        if ((visualizationDataSource.containsKey(FILTERS) && !filters.containsKey(DRILL_DOWN_FILTER)) || (visualizationDataSource.containsKey(FILTERS) && filters.containsKey(DRILL_DOWN_FILTER) && filters.getJsonObject(DRILL_DOWN_FILTER).isEmpty()))
        {
            filters.put(DRILL_DOWN_FILTER, new JsonObject(VisualizationConstants.VISUALIZATION_DRILL_DOWN_FILTER_JSON_TEMPLATE).getJsonObject(DRILL_DOWN_FILTER));
        }

        if (visualizationDataSource.containsKey(VisualizationConstants.FILTER_KEYS))//for flow purpose interface widget during interface drilldown will be taking filterkeys
        {
            filters = new JsonObject(VisualizationConstants.VISUALIZATION_DATA_FILTER_JSON_TEMPLATE);

            var operandValues = new ArrayList<>();

            visualizationDataSource.getJsonArray(VisualizationConstants.FILTER_KEYS).stream().map(CommonUtil::getString).forEach(key ->
            {
                if (key.contains("-"))
                {
                    operandValues.add(CommonUtil.getInteger(key.substring(key.lastIndexOf("-") + 1).trim()));
                }
            });

            if (!operandValues.isEmpty())
            {
                filters.getJsonObject(DATA_FILTER).put(CONDITION_GROUPS, new JsonArray().add(new JsonObject(VisualizationConstants.VISUALIZATION_CONDITION_GROUP_JSON_TEMPLATE).put(OPERATOR, DatastoreConstants.ConditionGroup.OR.getName()).put(CONDITIONS, new JsonArray().add(new JsonObject().put(OPERAND, FlowEngineConstants.SOURCE_INTERFACE_INDEX).put(VALUE, operandValues.size() > 1 ? operandValues : operandValues.getFirst()).put(OPERATOR, operandValues.size() > 1 ? DatastoreConstants.ConditionGroup.IN.getName() : PolicyEngineConstants.Operator.EQUAL.getName()))
                        .add(new JsonObject().put(OPERAND, FlowEngineConstants.DESTINATION_INTERFACE_INDEX).put(VALUE, operandValues.size() > 1 ? operandValues : operandValues.getFirst()).put(OPERATOR, operandValues.size() > 1 ? DatastoreConstants.ConditionGroup.IN.getName() : PolicyEngineConstants.Operator.EQUAL.getName())))));
            }

            if (!entities.isEmpty())
            {
                filters.getJsonObject(DATA_FILTER).getJsonArray(CONDITION_GROUPS).add(new JsonObject(VisualizationConstants.VISUALIZATION_CONDITION_GROUP_JSON_TEMPLATE)
                        .put(CONDITIONS, new JsonArray().add(new JsonObject().put(OPERAND, EVENT_SOURCE).put(VALUE, entities.size() > 1 ? new ArrayList<>(entities) : new ArrayList<>(entities).getFirst()).put(OPERATOR, entities.size() > 1 ? DatastoreConstants.ConditionGroup.IN.getName() : PolicyEngineConstants.Operator.EQUAL.getName()))));
            }
        }
        else
        {
            var drillDownConditionGroup = filters.getJsonObject(DRILL_DOWN_FILTER).getJsonArray(CONDITION_GROUPS).getJsonObject(0);

            var drillDownConditions = !drillDownConditionGroup.getJsonArray(CONDITIONS).isEmpty() ? drillDownConditionGroup.getJsonArray(CONDITIONS) : new JsonArray();

            if (!context.getBoolean(VisualizationConstants.ADMIN_ROLE, false) && !entities.isEmpty())
            {
                if (!drillDownConditions.isEmpty() && (!drillDownConditionGroup.getJsonArray(CONDITIONS).isEmpty() && !drillDownConditionGroup.getJsonArray(CONDITIONS).getJsonObject(0).getString(OPERAND).equalsIgnoreCase(EVENT_SOURCE)))
                {

                    filters.getJsonObject(DRILL_DOWN_FILTER).getJsonArray(CONDITION_GROUPS).add(new JsonObject(VisualizationConstants.VISUALIZATION_CONDITION_GROUP_JSON_TEMPLATE).put(CONDITIONS,
                            new JsonArray().add(new JsonObject().put(OPERAND, EVENT_SOURCE).put(VALUE, new ArrayList<>(entities)).put(OPERATOR, DatastoreConstants.ConditionGroup.IN.getName()))).getMap());

                }
                else if (drillDownConditions.isEmpty())
                {

                    drillDownConditions.add(new JsonObject().put(OPERAND, EVENT_SOURCE).put(VALUE, entities.size() > 1 ? new ArrayList<>(entities) : new ArrayList<>(entities).getFirst()).put(OPERATOR, entities.size() > 1 ? DatastoreConstants.ConditionGroup.IN.getName() : PolicyEngineConstants.Operator.EQUAL.getName()));

                    drillDownConditionGroup.put(CONDITIONS, drillDownConditions);
                }
            }

            else
            {
                if (eventSourceFilter)
                {
                    if (drillDownConditionGroup.size() > 1 && !drillDownConditions.isEmpty())
                    {
                        filters.getJsonObject(DRILL_DOWN_FILTER).getJsonArray(CONDITION_GROUPS).add(new JsonObject(VisualizationConstants.VISUALIZATION_CONDITION_GROUP_JSON_TEMPLATE).put(CONDITIONS,
                                new JsonArray().add(new JsonObject().put(OPERAND, EVENT_SOURCE).put(VALUE, entities.size() > 1 ? new ArrayList<>(entities) : new ArrayList<>(entities).getFirst()).put(OPERATOR, entities.size() > 1 ? DatastoreConstants.ConditionGroup.IN.getName() : PolicyEngineConstants.Operator.EQUAL.getName()))).getMap());
                    }

                    else
                    {
                        drillDownConditions.add(new JsonObject().put(OPERAND, EVENT_SOURCE).put(VALUE, entities.size() > 1 ? new ArrayList<>(entities) : new ArrayList<>(entities).getFirst()).put(OPERATOR, entities.size() > 1 ? DatastoreConstants.ConditionGroup.IN.getName() : PolicyEngineConstants.Operator.EQUAL.getName()));

                        drillDownConditionGroup.put(CONDITIONS, drillDownConditions);
                    }
                }

                else if (drillDownConditions.isEmpty())
                {
                    filters.put(DRILL_DOWN_FILTER, new JsonObject());
                }
            }

            if (conditions.isEmpty())
            {
                filters.put(DATA_FILTER, new JsonObject());
            }

            if (eventHistory && !filters.isEmpty() && ((filters.containsKey(DATA_FILTER) && !filters.getJsonObject(DATA_FILTER).isEmpty() && !filters.getJsonObject(DATA_FILTER).getJsonArray(CONDITION_GROUPS).isEmpty()) || (filters.containsKey(DRILL_DOWN_FILTER) && !filters.getJsonObject(DRILL_DOWN_FILTER).isEmpty() && !filters.getJsonObject(DRILL_DOWN_FILTER).getJsonArray(CONDITION_GROUPS).isEmpty())))
            {
                var categories = new JsonObject();

                var key = DATA_FILTER;
                //priority will be given to data filter..
                if ((!filters.containsKey(DATA_FILTER) || filters.getJsonObject(DATA_FILTER).isEmpty()) && (filters.containsKey(DRILL_DOWN_FILTER) && !filters.getJsonObject(DRILL_DOWN_FILTER).isEmpty()))
                {
                    key = DRILL_DOWN_FILTER;
                }

                var conditionGroups = filters.getJsonObject(key).getJsonArray(CONDITION_GROUPS);

                /*
                For Log Search Exclude function should not be included for categories.
                 */
                if (!conditionGroup.getString(FILTER).equalsIgnoreCase("exclude"))
                {
                    for (var i = 0; i < conditionGroups.size(); i++)
                    {
                        conditions = conditionGroups.getJsonObject(i).getJsonArray(CONDITIONS);

                        for (var j = 0; j < conditions.size(); j++)
                        {
                            var condition = conditions.getJsonObject(j);

                            if (condition.getString(OPERAND).equalsIgnoreCase(LogEngineConstants.EVENT_CATEGORY) || condition.getString(OPERAND).equalsIgnoreCase(LogEngineConstants.EVENT_SOURCE_TYPE))
                            {
                                if (condition.getString(OPERATOR).equalsIgnoreCase(PolicyEngineConstants.Operator.EQUAL.getName()))
                                {
                                    if (condition.getString(OPERAND).equalsIgnoreCase(LogEngineConstants.EVENT_CATEGORY))
                                    {
                                        for (var values : eventCategories.values())
                                        {
                                            values.stream().filter(item -> item.split(DASH_SEPARATOR)[1].equalsIgnoreCase(CommonUtil.getString(condition.getString(VALUE)))).forEach(item -> categories.put(item.toLowerCase().trim().replace(" ", ".").trim(), EMPTY_VALUE));
                                        }
                                    }

                                    else
                                    {
                                        eventCategories.get(condition.getString(VALUE)).forEach(item -> categories.put(item.toLowerCase().trim().replace(" ", ".").trim(), EMPTY_VALUE));
                                    }
                                }

                                else if (condition.getString(OPERATOR).equalsIgnoreCase(PolicyEngineConstants.Operator.IN.getName()))
                                {
                                    if (condition.getString(OPERAND).equalsIgnoreCase(LogEngineConstants.EVENT_CATEGORY))
                                    {
                                        condition.getJsonArray(VALUE).forEach(value ->
                                        {
                                            for (var values : eventCategories.values())
                                            {
                                                values.stream().filter(item -> item.split(DASH_SEPARATOR)[1].equalsIgnoreCase(CommonUtil.getString(value))).forEach(item -> categories.put(item.toLowerCase().trim().replace(" ", "."), EMPTY_VALUE));
                                            }
                                        });
                                    }

                                    else
                                    {
                                        condition.getJsonArray(VALUE).forEach(value -> eventCategories.get(CommonUtil.getString(value)).forEach(item -> categories.put(item.toLowerCase().trim().replace(" ", "."), EMPTY_VALUE)));
                                    }
                                }

                                else if (condition.getString(OPERATOR).equalsIgnoreCase(PolicyEngineConstants.Operator.CONTAINS.getName()))
                                {
                                    if (condition.getString(OPERAND).equalsIgnoreCase(LogEngineConstants.EVENT_CATEGORY))
                                    {
                                        for (var values : eventCategories.values())
                                        {
                                            values.stream().filter(item -> item.split(DASH_SEPARATOR)[1].toLowerCase().contains(CommonUtil.getString(condition.getString(VALUE)).toLowerCase())).forEach(item -> categories.put(item.toLowerCase().trim().replace(" ", "."), EMPTY_VALUE));
                                        }
                                    }

                                    else
                                    {
                                        eventCategories.keySet().stream().filter(item -> item.toLowerCase().contains(CommonUtil.getString(condition.getString(VALUE)).toLowerCase())).forEach(item -> categories.put(item.toLowerCase().trim().replace(" ", "."), EMPTY_VALUE));
                                    }
                                }

                                else if (condition.getString(OPERATOR).equalsIgnoreCase(PolicyEngineConstants.Operator.START_WITH.getName()))
                                {
                                    if (condition.getString(OPERAND).equalsIgnoreCase(LogEngineConstants.EVENT_CATEGORY))
                                    {
                                        for (var values : eventCategories.values())
                                        {
                                            values.stream().filter(item -> item.split(DASH_SEPARATOR)[1].toLowerCase().startsWith(CommonUtil.getString(condition.getString(VALUE)).toLowerCase())).forEach(item -> categories.put(item.toLowerCase().trim().replace(" ", "."), EMPTY_VALUE));
                                        }
                                    }

                                    else
                                    {
                                        eventCategories.keySet().stream().filter(item -> item.toLowerCase().startsWith(CommonUtil.getString(condition.getString(VALUE)).toLowerCase())).forEach(item -> categories.put(item.toLowerCase().trim().replace(" ", "."), EMPTY_VALUE));
                                    }
                                }

                                else if (condition.getString(OPERATOR).equalsIgnoreCase(PolicyEngineConstants.Operator.END_WITH.getName()))
                                {
                                    if (condition.getString(OPERAND).equalsIgnoreCase(LogEngineConstants.EVENT_CATEGORY))
                                    {
                                        for (var values : eventCategories.values())
                                        {
                                            values.stream().filter(item -> item.split(DASH_SEPARATOR)[1].toLowerCase().endsWith(CommonUtil.getString(condition.getString(VALUE)).toLowerCase())).forEach(item -> categories.put(item.toLowerCase().trim().replace(" ", "."), EMPTY_VALUE));
                                        }
                                    }

                                    else
                                    {

                                        eventCategories.keySet().stream().filter(item -> item.toLowerCase().endsWith(CommonUtil.getString(condition.getString(VALUE)).toLowerCase())).forEach(item -> categories.put(item.toLowerCase().trim().replace(" ", "."), EMPTY_VALUE));
                                    }
                                }
                            }
                        }
                    }
                }

                if (!categories.isEmpty())
                {
                    visualizationDataSource.put("categories", categories);
                }
            }
        }

        visualizationDataSource.put(FILTERS, filters);
    }

    public static Set<Integer> filterEntities(JsonObject context, JsonArray qualifiedGroups, Set<Integer> qualifiedObjects, Set filteredGroupEntities, Set filteredTagEntities, Logger logger, boolean archived)
    {
        Set<Integer> items = null;

        try
        {

            if (archived && context.getString(ENTITY_TYPE).equalsIgnoreCase(VisualizationConstants.VisualizationGrouping.MONITOR.getName()) && !context.getJsonArray(ENTITIES).isEmpty())
            {
                items = ArchivedObjectConfigStore.getStore().getObjectIdsByIds(context.getJsonArray(ENTITIES)).stream().map(CommonUtil::getInteger).collect(Collectors.toSet());
            }
            else if (archived)
            {
                items = ArchivedObjectConfigStore.getStore().getArchiveObjectIds().stream().map(CommonUtil::getInteger).collect(Collectors.toSet());
            }
            else if (context.getString(ENTITY_TYPE).equalsIgnoreCase(VisualizationConstants.VisualizationGrouping.MONITOR.getName()) && !context.getJsonArray(ENTITIES).isEmpty())
            {
                items = ObjectConfigStore.getStore().getObjectIdsByIds(context.getJsonArray(ENTITIES)).stream().map(CommonUtil::getInteger).filter(qualifiedObjects::contains).collect(Collectors.toSet());
            }
            else if (context.getString(ENTITY_TYPE).equalsIgnoreCase(VisualizationConstants.VisualizationGrouping.GROUP.getName()) && !context.getJsonArray(ENTITIES).isEmpty())
            {
                filteredGroupEntities.addAll(context.getJsonArray(ENTITIES).stream().toList());

                items = ObjectConfigStore.getStore().getObjectIdsByGroups(new JsonArray(context.getJsonArray(ENTITIES).stream().filter(qualifiedGroups::contains).distinct().toList())).stream().map(CommonUtil::getInteger).filter(qualifiedObjects::contains).collect(Collectors.toSet());
            }
            else if (context.getString(ENTITY_TYPE).equalsIgnoreCase(AIOpsObject.OBJECT_TYPE) && !context.getJsonArray(ENTITIES).isEmpty())
            {
                items = ObjectConfigStore.getStore().getObjectIdsByTypes(context.getJsonArray(ENTITIES)).stream().map(CommonUtil::getInteger).filter(qualifiedObjects::contains).collect(Collectors.toSet());
            }
            else if (context.getString(ENTITY_TYPE).equalsIgnoreCase(VisualizationConstants.VisualizationGrouping.CATEGORY.getName()) && !context.getJsonArray(ENTITIES).isEmpty())
            {
                items = ObjectConfigStore.getStore().getObjectIdsByCategories(context.getJsonArray(ENTITIES)).stream().map(CommonUtil::getInteger).filter(qualifiedObjects::contains).collect(Collectors.toSet());
            }
            else if (context.getString(ENTITY_TYPE).equalsIgnoreCase(VisualizationConstants.VisualizationGrouping.TAG.getName()) && !context.getJsonArray(ENTITIES).isEmpty())
            {
                filteredTagEntities.addAll(context.getJsonArray(ENTITIES).stream().toList());

                if (context.getString(DATA_POINT).contains(INSTANCE_SEPARATOR) || (context.containsKey(INSTANCE) && context.getBoolean(INSTANCE)))
                {
                    items = TagCacheStore.getStore().getInstanceIdsByTags(context.getString(DATA_POINT).split(INSTANCE_SEPARATOR)[0], context.getJsonArray(ENTITIES)).stream().map(CommonUtil::getInteger).filter(qualifiedObjects::contains).collect(Collectors.toSet());
                }
                else
                {
                    items = ObjectConfigStore.getStore().getObjectIdsByTags(context.getJsonArray(ENTITIES)).stream().map(CommonUtil::getInteger).filter(qualifiedObjects::contains).collect(Collectors.toSet());
                }
            }
            else
            {
                items = qualifiedObjects;
            }
        }
        catch (Exception exception)
        {
            logger.error(exception);
        }

        return items;
    }

    public static void update(JsonObject columns, String[] tokens, boolean metric)
    {
        var column = tokens[DatastoreConstants.MetricWriterOrdinal.COLUMN.ordinal()];

        if (!columns.containsKey(column))
        {
            columns.put(column, new JsonObject());
        }

        var mapper = columns.getJsonObject(column);

        var plugins = mapper.getJsonArray(DatastoreConstants.MAPPER_PLUGIN_IDS);

        if (plugins == null)
        {
            plugins = new JsonArray(new ArrayList<>(1));
        }

        if (!plugins.contains(CommonUtil.getInteger(tokens[1])))
        {
            mapper.put(DatastoreConstants.MAPPER_PLUGIN_IDS, plugins.add(CommonUtil.getInteger(tokens[1])));
        }

        var dataTypes = mapper.getJsonArray(MAPPER_DATA_CATEGORIES);

        if (dataTypes == null)
        {
            dataTypes = new JsonArray(new ArrayList<>(1));
        }

        if (!dataTypes.contains(CommonUtil.getInteger(tokens[0])))
        {
            mapper.put(MAPPER_DATA_CATEGORIES, dataTypes.add(CommonUtil.getInteger(tokens[0])));
        }

        if (plugins.contains(DatastoreConstants.PluginId.CORRELATED_METRIC.getName()))
        {
            mapper.put(DatastoreConstants.MAPPER_CORRELATED, tokens[3]);
        }
        else
        {
            mapper.put(DatastoreConstants.MAPPER_STATUS, tokens[3]);
        }

        if (!metric && tokens.length > 4)
        {
            mapper.put(DatastoreConstants.MAPPER_EVENT_CATEGORY, tokens[4]);
        }

        if (column.contains(GlobalConstants.INSTANCE_SEPARATOR))
        {
            mapper.put(DatastoreConstants.MAPPER_INSTANCE, column.split(GlobalConstants.INSTANCE_SEPARATOR)[0]);
        }

        if (mapper.containsKey(DatastoreConstants.MAPPER_CORRELATED) && mapper.getString(DatastoreConstants.MAPPER_CORRELATED).equalsIgnoreCase(GlobalConstants.YES))
        {
            mapper.put(DatastoreConstants.MAPPER_GROUP, tokens[DatastoreConstants.EventWriterOrdinal.GROUP.ordinal()]);
        }
    }

    public static void publish(JsonObject context, Logger logger, Map<Long, JsonObject> queryResults, Map<Long, JsonObject> queryRawContexts, Map<Long, JsonObject> queryContexts, Long queryId, Buffer buffer)
    {
        if (context.getBoolean("publish"))//if require event to be published to other events will be publishing it
        {
            if (queryResults.containsKey(queryId))
            {
                var result = VisualizationConstants.unpack(buffer, logger, false, queryContexts.get(queryId), false, true);

                if (result.containsKey(RESULT) && !result.getJsonArray(RESULT).isEmpty())
                {
                    queryResults.get(queryId).getJsonArray(RESULT).addAll(result.getJsonArray(RESULT));
                }

                Bootstrap.vertx().eventBus().send(context.getString(EVENT_TYPE), context.put(QUERY_PROGRESS, context.getInteger(QUERY_PROGRESS)).put("unpacked", true).put(RESULT, queryResults.get(queryId)));
            }
            else
            {
                Bootstrap.vertx().eventBus().send(context.getString(EVENT_TYPE), context.put(QUERY_PROGRESS, context.getInteger(QUERY_PROGRESS)).put(RESULT, buffer.getBytes()));
            }
        }
        else
        {

            if (queryContexts.containsKey(queryId) && queryContexts.get(queryId).containsKey(VISUALIZATION_DRILL_DOWN))
            {
                var result = VisualizationConstants.unpack(buffer, logger, false, queryContexts.get(queryId), false, true);

                if (result.containsKey(RESULT) && !result.getJsonArray(RESULT).isEmpty())
                {
                    if (queryResults.containsKey(queryId))
                    {
                        queryResults.get(queryId).getJsonArray(RESULT).addAll(result.getJsonArray(RESULT));
                    }
                    else
                    {
                        queryResults.put(queryId, result);
                    }
                }

                Bootstrap.vertx().eventBus().send(EVENT_VISUALIZATION, queryRawContexts.get(queryId));
            }
        }
    }

    public static void composeQueryResponse(JsonObject columnMappers, int queryProgress, JsonObject result, String visualizationCategory, long queryId, long subQueryId, JsonObject context, Logger logger, boolean qualifyUniqueColumns)
    {
        var composedResult = new JsonObject();

        try
        {
            if (CommonUtil.traceEnabled())
            {
                logger.trace("***** result: " + result.encode());

                logger.trace("***** context: " + context.encode());
            }

            var columns = qualifyUniqueColumns ? getColumns(result, logger) : new HashSet<String>();

            var type = context.getJsonArray(VISUALIZATION_DATA_SOURCES).getJsonObject(0).getString(CATEGORY, EMPTY_VALUE);

            var sortedRows = new ArrayList<JsonObject>();

            var sortBy = context.containsKey(SORT_BY) ? context.getString(SORT_BY) : EMPTY_VALUE;

            if (context.containsKey(VisualizationConstants.JOIN_TYPE) && context.getString(VisualizationConstants.JOIN_TYPE).equalsIgnoreCase(VisualizationConstants.VisualizationJoinType.JOIN_TYPE_ANY.getName()))
            {
                var results = ((JsonObject) result.getJsonObject(CommonUtil.getString(subQueryId)).remove(RESULT));

                if (results != null)
                {
                    for (var entry : results.getMap().entrySet())//main subquery result in which unique columns on whose result data is to be merged with other results
                    {
                        var rows = entry.getValue() instanceof ArrayList arrayList ? new JsonArray(arrayList) : (JsonArray) entry.getValue();

                        for (var i = 0; i < rows.size(); i++)
                        {
                            var row = new JsonObject().mergeIn(rows.getJsonObject(i));

                            var valid = false;

                            for (var value : result.getMap().values())
                            {
                                var subQueryResults = ((JsonObject) value).getJsonObject(RESULT, null);

                                if (subQueryResults != null && subQueryResults.containsKey(entry.getKey()))
                                {
                                    valid = true;

                                    row.mergeIn(subQueryResults.getJsonArray(entry.getKey()).getJsonObject(0));

                                    for (var column : columns)
                                    {
                                        if (!row.containsKey(column))
                                        {
                                            if (column.equalsIgnoreCase(sortBy))
                                            {
                                                row.put(column, 0);
                                            }
                                            else
                                            {
                                                row.put(column, EMPTY_VALUE);
                                            }
                                        }
                                    }
                                }
                            }

                            if (valid)
                            {
                                if (!sortBy.isEmpty())
                                {
                                    if (row.containsKey(sortBy))//if sorting column is not present then ignore the result..
                                    {
                                        sortedRows.add(row);
                                    }
                                }

                                else
                                {
                                    composedResult.put(VISUALIZATION_TYPE, type);

                                    compose(row, composedResult);
                                }
                            }
                        }
                    }
                }
            }
            else if (context.containsKey(VisualizationConstants.JOIN_TYPE) && context.getString(VisualizationConstants.JOIN_TYPE).equalsIgnoreCase(VisualizationConstants.VisualizationJoinType.JOIN_TYPE_ALL.getName()))
            {
                var rows = result.getJsonObject(CommonUtil.getString(subQueryId)).getJsonArray(RESULT);

                if (rows != null)
                {
                    for (var i = 0; i < rows.size(); i++)
                    {
                        var row = new JsonObject().mergeIn(rows.getJsonObject(i));

                        for (var value : result.getMap().values())
                        {
                            var subQueryResults = ((JsonObject) value).getJsonArray(RESULT, null);

                            if (subQueryResults != null)
                            {
                                for (var j = 0; j < subQueryResults.size(); j++)
                                {
                                    row.mergeIn(subQueryResults.getJsonObject(j));
                                }
                            }
                        }

                        if (!sortBy.isEmpty())
                        {
                            if (row.containsKey(sortBy))//if sorting column is not present then ignore the result..
                            {
                                sortedRows.add(row);
                            }
                        }
                        else
                        {
                            compose(row, composedResult);
                        }
                    }
                }
            }
            else if (!context.containsKey(VisualizationConstants.JOIN_TYPE) || (context.containsKey(VisualizationConstants.JOIN_TYPE) && context.getString(VisualizationConstants.JOIN_TYPE).equalsIgnoreCase(VisualizationJoinType.JOIN_TYPE_CUSTOM.getName())))
            {
                var rows = result.getJsonObject(CommonUtil.getString(subQueryId)).getJsonArray(RESULT);

                if (rows != null)
                {
                    //for special column will be building result by adding special column value
                    for (var i = 0; i < rows.size(); i++)
                    {
                        composedResult.put(VISUALIZATION_TYPE, type);

                        compose(new JsonObject().mergeIn(rows.getJsonObject(i)), composedResult);
                    }
                }
            }

            if (!sortedRows.isEmpty())
            {
                sortedRows.sort(Comparator.comparing(item -> JsonObject.mapFrom(item).getLong(sortBy)).reversed());

                for (var row : sortedRows)
                {
                    compose(row, composedResult);
                }
            }

            if ((!composedResult.isEmpty() && context.containsKey(VisualizationConstants.JOIN_RESULT)) || (composedResult.isEmpty() && context.containsKey(VisualizationConstants.JOIN_RESULT) && context.getString(VisualizationConstants.JOIN_RESULT).equalsIgnoreCase("inventory")))
            {
                join(context, composedResult, columnMappers, logger);
            }

            if (!composedResult.isEmpty())
            {
                if ((context.getString(VisualizationConstants.VISUALIZATION_CATEGORY).equalsIgnoreCase(VisualizationConstants.VisualizationCategory.TOP_N.getName()) || context.getString(VisualizationConstants.VISUALIZATION_CATEGORY).equalsIgnoreCase(VisualizationConstants.VisualizationCategory.HISTOGRAM.getName()) || context.getString(VisualizationConstants.VISUALIZATION_CATEGORY).equalsIgnoreCase(VisualizationCategory.STREAM.getName())) && !context.getString(VisualizationConstants.VISUALIZATION_TYPE).equalsIgnoreCase(VisualizationConstants.VisualizationCategory.GRID.getName()))
                {
                    var dataSource = context.getJsonArray(VisualizationConstants.VISUALIZATION_DATA_SOURCES).getJsonObject(0);

                    if ((context.getString(VisualizationConstants.VISUALIZATION_CATEGORY).equalsIgnoreCase(VisualizationCategory.HISTOGRAM.getName()) || context.getString(VisualizationConstants.VISUALIZATION_CATEGORY).equalsIgnoreCase(VisualizationCategory.STREAM.getName())) && dataSource.containsKey(VISUALIZATION_RESULT_BY) && !dataSource.getJsonArray(VISUALIZATION_RESULT_BY).isEmpty())
                    {
                        var groupingColumns = dataSource.getJsonArray(VISUALIZATION_RESULT_BY);

                        var category = dataSource.getString(CATEGORY, EMPTY_VALUE);

                        for (var i = 0; i < groupingColumns.size(); i++)
                        {
                            if (REPLACEABLE_COLUMNS.containsKey(groupingColumns.getString(i)))
                            {
                                resolve(composedResult, groupingColumns.getString(i), i, category);
                            }
                        }
                    }

                    else if (context.getString(VisualizationConstants.VISUALIZATION_CATEGORY).equalsIgnoreCase(VisualizationCategory.TOP_N.getName()) && context.containsKey(VISUALIZATION_RESULT_BY) && !context.getJsonArray(VISUALIZATION_RESULT_BY).isEmpty())
                    {
                        if (context.getJsonArray(VISUALIZATION_RESULT_BY).contains(PolicyEngineConstants.POLICY_ID))
                        {
                            //in topn alert group widget when there is policy id in grouping we need to showcase policy name so we do not require result of policy id as we resolve policy name so removing policy.id result
                            composedResult.remove(PolicyEngineConstants.POLICY_ID);
                        }
                        else if (context.getJsonArray(VISUALIZATION_RESULT_BY).contains(NetRoute.NETROUTE_ID))
                        {
                            composedResult.remove(NetRoute.NETROUTE_ID);
                        }
                    }

                    Bootstrap.vertx().eventBus().publish(EventBusConstants.EVENT_DATASTORE_QUERY_RESPONSE, CodecUtil.compress(VisualizationConstants.packChartResult(composedResult.put(QUERY_PROGRESS, queryProgress), VisualizationConstants.VisualizationCategoryOrdinal.VISUALIZATION_TOPN_CHART.ordinal(), queryId, subQueryId).getBytes()));
                }
                else if (context.getString(VISUALIZATION_NAME) != null && context.getString(VISUALIZATION_NAME).equalsIgnoreCase(VISUALIZATION_TYPE_TODAY_AVAILABILITY))
                {
                    Bootstrap.vertx().eventBus().publish(EventBusConstants.EVENT_DATASTORE_QUERY_RESPONSE, CodecUtil.compress(VisualizationConstants.packChartResult(composedResult.put(QUERY_PROGRESS, queryProgress), VisualizationCategoryOrdinal.VISUALIZATION_CHART.ordinal(), queryId, subQueryId).getBytes()));
                }
                else
                {
                    Bootstrap.vertx().eventBus().publish(EventBusConstants.EVENT_DATASTORE_QUERY_RESPONSE, CodecUtil.compress(VisualizationConstants.packGridResult(composedResult.put(QUERY_PROGRESS, queryProgress), VisualizationConstants.VisualizationCategoryOrdinal.VISUALIZATION_GRID.ordinal(), queryId, subQueryId).getBytes()));
                }
            }
            else
            {
                //send back empty result

                VisualizationConstants.send(visualizationCategory, VisualizationConstants.VisualizationCategory.GRID.getName(), null, String.format(ErrorMessageConstants.VISUALIZATION_QUERY_FAILED, "Preview Widget", ErrorMessageConstants.INVALID_NO_DATA_FOUND), queryId, subQueryId, logger, EVENT_DATASTORE_QUERY_RESPONSE);
            }
        }
        catch (Exception exception)
        {
            logger.trace("***** result: " + result.encode());

            logger.trace("***** context: " + context.encode());

            logger.trace("***** visualizationCategory: " + visualizationCategory);

            logger.error(exception);
        }
    }

    private static Set<String> getColumns(JsonObject result, Logger logger)
    {
        var columns = new HashSet<String>();

        try
        {
            for (var key : result.getMap().keySet())
            {
                var results = result.getJsonObject(key).getJsonObject(RESULT, null);

                if (results != null && !results.isEmpty())
                {
                    columns.addAll(((JsonArray) results.stream().findFirst().get().getValue()).getJsonObject(0).getMap().keySet());
                }
            }
        }
        catch (Exception exception)
        {
            logger.trace("***** result: " + result.encode());

            logger.error(exception);
        }

        return columns;
    }

    public static void compose(JsonObject row, JsonObject composedResult)
    {
        var type = composedResult.containsKey(VISUALIZATION_TYPE) ? composedResult.remove(VISUALIZATION_TYPE) : EMPTY_VALUE;

        row.forEach(entry ->
        {
            ((JsonArray) composedResult.getMap().computeIfAbsent(entry.getKey(), value -> new JsonArray())).add(entry.getValue());

            if (VisualizationConstants.REPLACEABLE_COLUMNS.containsKey(entry.getKey()))
            {
                var result = transformColumn(entry.getKey(), CommonUtil.getString(entry.getValue()), CommonUtil.getString(type));

                if (result != null)
                {
                    composedResult.getMap().computeIfAbsent(VisualizationConstants.REPLACEABLE_COLUMNS.get(entry.getKey()), value -> new JsonArray());

                    composedResult.getJsonArray(VisualizationConstants.REPLACEABLE_COLUMNS.get(entry.getKey())).add(result);
                }
            }
        });
    }

    private static void join(JsonObject context, JsonObject result, JsonObject columnMappers, Logger logger)
    {
        if (context.getString(VisualizationConstants.JOIN_RESULT).equalsIgnoreCase(VisualizationConstants.VisualizationDataSource.POLICY_STREAM.getName()))
        {
            var item = UserConfigStore.getStore().getItemByValue(User.USER_NAME, context.getString(User.USER_NAME));

            var dateFormat = DateTimeUtil.getDateFormat(item.getJsonObject(User.USER_PREFERENCES).getString(User.USER_PREFERENCE_DATE_TIME_FORMAT), item);

            if (context.containsKey(CATEGORY) && (context.getString(CATEGORY).equalsIgnoreCase(METRIC) || context.getString(CATEGORY).equalsIgnoreCase(VisualizationDataSource.NETROUTE_METRIC.getName())))
            {
                result.getMap().computeIfAbsent(END_TIME, value -> new JsonArray());

                result.getMap().computeIfAbsent(APIConstants.Entity.OBJECT.getName().toLowerCase(), value -> new JsonArray());

                result.remove(EVENT_ID);

                var values = (JsonArray) result.remove(TIMESTAMP);

                var objects = (JsonArray) result.remove(AIOpsObject.OBJECT_ID);

                result.getMap().computeIfAbsent(START_TIME, value -> new JsonArray());

                var durations = result.getJsonArray(DURATION);

                var firstTriggerTicks = result.getJsonArray(PolicyEngineConstants.POLICY_FIRST_TRIGGER_TICK);

                for (var index = 0; index < values.size(); index++)
                {
                    try
                    {

                        result.getJsonArray(APIConstants.Entity.OBJECT.getName().toLowerCase()).add(objects.getValue(index));

                        result.getJsonArray(START_TIME).add(dateFormat.format(new Date(values.getLong(index))));

                        result.getJsonArray(END_TIME).add(dateFormat.format(new Date(values.getLong(index) + (durations.getLong(index) * 1000))));

                        if (index >= firstTriggerTicks.size())
                        {
                            firstTriggerTicks.add(dateFormat.format(new Date()));
                        }
                        else
                        {
                            firstTriggerTicks.set(index, dateFormat.format(new Date(firstTriggerTicks.getLong(index))));
                        }
                    }

                    catch (Exception ignored)
                    {

                    }

                }
            }
            else
            {
                try
                {
                    result.put(PolicyEngineConstants.POLICY_FIRST_TRIGGER_TICK, new JsonArray());

                    result.remove(EVENT_ID);

                    var policyIds = result.getJsonArray(PolicyEngineConstants.POLICY_ID);

                    var ticks = result.getJsonArray(PolicyEngineConstants.POLICY_FIRST_TRIGGER_TICK);

                    if (context.getString(CATEGORY).equalsIgnoreCase(VisualizationDataSource.NETROUTE_EVENT.getName()) && result.containsKey(NetRoute.NETROUTE_ID))
                    {
                        for (var i = 0; i < result.getJsonArray(TIMESTAMP).size(); i++)
                        {
                            ticks.add(dateFormat.format(new Date(NetRoutePolicyFlapDurationCacheStore.getStore().getTriggerTicks(policyIds.getLong(i) + SEPARATOR + result.getJsonArray(NetRoute.NETROUTE_ID).getLong(i)).getLong(PolicyEngineConstants.POLICY_FIRST_TRIGGER_TICK))));
                        }
                    }
                    else
                    {
                        for (var i = 0; i < result.getJsonArray(TIMESTAMP).size(); i++)
                        {
                            ticks.add(dateFormat.format(new Date(EventPolicyCacheStore.getStore().getTriggerTicks(policyIds.getLong(i)).getLong(PolicyEngineConstants.POLICY_FIRST_TRIGGER_TICK))));
                        }
                    }
                }
                catch (Exception ignored)
                {

                }
            }
        }
        else if (context.getString(VisualizationConstants.JOIN_RESULT).equalsIgnoreCase(VisualizationConstants.VisualizationDataSource.CUMULATIVE_OBJECT_STATUS_FLAP.getName()))
        {
            var item = UserConfigStore.getStore().getItemByValue(User.USER_NAME, context.getString(User.USER_NAME));

            var dateFormat = DateTimeUtil.getDateFormat(item.getJsonObject(User.USER_PREFERENCES).getString(User.USER_PREFERENCE_DATE_TIME_FORMAT), item);

            result.getMap().computeIfAbsent(END_TIME, value -> new JsonArray());

            result.getMap().computeIfAbsent(APIConstants.Entity.OBJECT.getName().toLowerCase(), value -> new JsonArray());

            result.remove(EVENT_ID);

            var values = (JsonArray) result.remove(TIMESTAMP);

            var objects = (JsonArray) result.remove(AIOpsObject.OBJECT_ID);

            result.getMap().computeIfAbsent(START_TIME, value -> new JsonArray());

            var dataSources = context.getJsonArray(VisualizationConstants.VISUALIZATION_DATA_SOURCES).getJsonObject(0);

            JsonArray durations;

            if (dataSources.getString(TYPE).equalsIgnoreCase(VisualizationDataSource.HOURLY_OBJECT_STATUS_FLAP.getName()) && dataSources.containsKey(INSTANCE_TYPE) && !dataSources.getString(INSTANCE_TYPE).isEmpty())
            {
                durations = result.getJsonArray(dataSources.getString(INSTANCE_TYPE) + INSTANCE_SEPARATOR + DURATION);
            }
            else
            {
                durations = result.getJsonArray(DURATION);
            }

            for (var index = 0; index < values.size(); index++)
            {
                try
                {

                    result.getJsonArray(APIConstants.Entity.OBJECT.getName().toLowerCase()).add(objects.getValue(index));

                    result.getJsonArray(END_TIME).add(dateFormat.format(new Date(values.getLong(index) + (durations.getLong(index) * 1000))));

                    result.getJsonArray(START_TIME).add(dateFormat.format(new Date(values.getLong(index))));
                }

                catch (Exception ignored)
                {

                }
            }
        }

        else if (context.getString(VisualizationConstants.JOIN_RESULT).equalsIgnoreCase("integration.ticket"))
        {
            var values = result.getJsonArray(TIMESTAMP);

            var instance = !result.getJsonArray(INSTANCE + CARET_SEPARATOR + VALUE).getString(0).isEmpty();

            result.getMap().computeIfAbsent(IntegrationEngine.ACK_ID, value -> new JsonArray());

            for (var index = 0; index < values.size(); index++)
            {
                String policyKey;

                if (instance)
                {
                    policyKey = IntegrationCacheStore.getStore().getIntegrationType(result.getJsonArray(PolicyEngineConstants.POLICY_ID + CARET_SEPARATOR + VALUE).getString(index)) + SEPARATOR + result.getJsonArray(PolicyEngineConstants.POLICY_ID + CARET_SEPARATOR + VALUE).getValue(index)
                            + SEPARATOR + ObjectConfigStore.getStore().getIdByObjectId(result.getJsonArray(AIOpsObject.OBJECT_ID + CARET_SEPARATOR + VALUE).getInteger(index)) + SEPARATOR +
                            result.getJsonArray(PolicyEngineConstants.POLICY_TYPE + CARET_SEPARATOR + VALUE).getValue(index) + SEPARATOR + result.getJsonArray(METRIC + CARET_SEPARATOR + VALUE).getValue(index) + SEPARATOR +
                            result.getJsonArray(INSTANCE + CARET_SEPARATOR + VALUE).getValue(index) + SEPARATOR +
                            result.getJsonArray(SEVERITY + CARET_SEPARATOR + VALUE).getValue(index) + SEPARATOR + CommonUtil.getString(values.getLong(index) / 1000L);
                }
                else
                {
                    policyKey = IntegrationCacheStore.getStore().getIntegrationType(result.getJsonArray(PolicyEngineConstants.POLICY_ID + CARET_SEPARATOR + VALUE).getString(index)) + SEPARATOR + result.getJsonArray(PolicyEngineConstants.POLICY_ID + CARET_SEPARATOR + VALUE).getValue(index)
                            + SEPARATOR + ObjectConfigStore.getStore().getIdByObjectId(result.getJsonArray(AIOpsObject.OBJECT_ID + CARET_SEPARATOR + VALUE).getInteger(index)) + SEPARATOR +
                            result.getJsonArray(PolicyEngineConstants.POLICY_TYPE + CARET_SEPARATOR + VALUE).getValue(index) + SEPARATOR + result.getJsonArray(METRIC + CARET_SEPARATOR + VALUE).getValue(index) + SEPARATOR +
                            result.getJsonArray(SEVERITY + CARET_SEPARATOR + VALUE).getValue(index) + SEPARATOR + CommonUtil.getString(values.getLong(index) / 1000L);
                }

                result.getJsonArray(IntegrationEngine.ACK_ID).add(IntegrationCacheStore.getStore().getTicketId(policyKey));
            }
        }

        else if (context.getString(VisualizationConstants.JOIN_RESULT).equalsIgnoreCase("inventory"))
        {
            var queryApps = false; // Boolean to know if the inventory category needs Application data.

            var queryObjectSeverity = true; // it means it is an object level and false is instance level.

            if (CommonUtil.traceEnabled())
            {
                logger.trace("inventory result received::" + result);
            }

            var user = UserConfigStore.getStore().getItemByValue(User.USER_NAME, context.getString(User.USER_NAME, DEFAULT_USER));

            var userGroups = user.getJsonArray(User.USER_GROUPS);

            var containerTypeReport = context.containsKey(VisualizationConstants.CONTAINER_TYPE) && context.getString(VisualizationConstants.CONTAINER_TYPE) != null && context.getString(VisualizationConstants.CONTAINER_TYPE).equalsIgnoreCase(VisualizationConstants.CONTAINER_TYPE_REPORT);

            var inventory = VISUALIZATION_INVENTORY_WIDGETS.getOrDefault(context.getString(VISUALIZATION_NAME), VISUALIZATION_INVENTORY_WIDGETS.get("Server Inventory Summary"));

            var category = inventory.getString(CATEGORY);

            var queryCustomMonitoringFields = inventory.getBoolean("queryCustomMonitoringFields"); // Boolean to know if the inventory category needs Custom Monitoring data.

            var queryActiveAlerts = inventory.getBoolean("queryActiveAlerts"); // Boolean to know if the inventory category needs Active Alert data.

            if (context.getString(VISUALIZATION_NAME).equalsIgnoreCase("Service Check Inventory Summary"))//for extra fields according to visualization
            {
                result.put(PORT, new JsonArray());
            }
            else if (context.getString(VISUALIZATION_NAME).equalsIgnoreCase("Server Inventory Summary"))
            {
                result.put(NMSConstants.APPS, new JsonArray());

                queryApps = true;
            }

            JsonArray objectsByCategory;

            if (context.containsKey(VISUALIZATION_INVENTORY_TYPES) && !context.getJsonArray(VISUALIZATION_INVENTORY_TYPES).isEmpty())
            {
                objectsByCategory = ObjectConfigStore.getStore().getObjectIdsByTypes(context.getJsonArray(VISUALIZATION_INVENTORY_TYPES));
            }
            else
            {
                objectsByCategory = ObjectConfigStore.getStore().getObjectIdsByCategories(new JsonArray().add(category));
            }

            var entityQualified = true;

            if (!result.containsKey(APIConstants.Entity.OBJECT.getName().toLowerCase()))//in case of no result will get data from config objects
            {
                result.getMap().computeIfAbsent(APIConstants.Entity.OBJECT.getName().toLowerCase(), value -> new JsonArray());

                entityQualified = false;
            }

            var items = new HashSet<Integer>();

            if (!entityQualified && !inventory.getString(INSTANCE).isEmpty())//in case of interface process or service inventory if no result entity result received so need to send back empty result to UI
            {
                VisualizationConstants.send(context.getString(VISUALIZATION_CATEGORY, VisualizationCategory.GRID.getName()), VisualizationConstants.VisualizationCategory.GRID.getName(), null, String.format(ErrorMessageConstants.VISUALIZATION_QUERY_FAILED, "Inventory", ErrorMessageConstants.INVALID_NO_DATA_FOUND), context.getLong(QUERY_ID), context.getLong(VisualizationConstants.SUB_QUERY_ID), logger, EVENT_DATASTORE_QUERY_RESPONSE);
            }
            else
            {
                for (var index = 0; index < objectsByCategory.size(); index++)
                {
                    if (!user.getLong(ID).equals(DEFAULT_ID))
                    {
                        var item = ObjectConfigStore.getStore().getItem(ObjectConfigStore.getStore().getIdByObjectId(objectsByCategory.getInteger(index)), false);

                        if (userGroups.stream().anyMatch(item.getJsonArray(AIOpsObject.OBJECT_GROUPS)::contains))
                        {
                            items.add(objectsByCategory.getInteger(index));

                            if (!entityQualified)
                            {
                                result.getJsonArray(APIConstants.Entity.OBJECT.getName().toLowerCase()).add(objectsByCategory.getInteger(index));
                            }
                        }
                    }
                    else
                    {
                        items.add(objectsByCategory.getInteger(index));

                        if (!entityQualified)
                        {
                            result.getJsonArray(APIConstants.Entity.OBJECT.getName().toLowerCase()).add(objectsByCategory.getInteger(index));
                        }
                    }
                }

                var objects = result.getJsonArray(APIConstants.Entity.OBJECT.getName().toLowerCase());

                if (queryActiveAlerts)
                {
                    result.put(Severity.CRITICAL.name().toLowerCase(), new JsonArray()).put(Severity.DOWN.name().toLowerCase(), new JsonArray()).put(Severity.MAJOR.name().toLowerCase(), new JsonArray()).put(Severity.UNREACHABLE.name().toLowerCase(), new JsonArray()).put(Severity.WARNING.name().toLowerCase(), new JsonArray()).put(Severity.CLEAR.name().toLowerCase(), new JsonArray());
                }

                result.put(SEVERITY, new JsonArray());

                //Adding all common columns for all objects
                result.put(AIOpsObject.OBJECT_CREATION_TIME, new JsonArray()).put(AIOpsObject.OBJECT_EVENT_PROCESSORS, new JsonArray()).put(AIOpsObject.OBJECT_DISCOVERY_METHOD, new JsonArray()).put(AIOpsObject.OBJECT_HOST, new JsonArray()).put(AIOpsObject.OBJECT_CATEGORY, new JsonArray()).put(AIOpsObject.OBJECT_GROUPS, new JsonArray()).put(AIOpsObject.OBJECT_TAGS, new JsonArray())
                        .put(AIOpsObject.OBJECT_TARGET, new JsonArray()).put(AIOpsObject.OBJECT_NAME, new JsonArray()).put(AIOpsObject.OBJECT_VENDOR, new JsonArray()).put(AIOpsObject.OBJECT_TYPE, new JsonArray()).put(AIOpsObject.OBJECT_IP, new JsonArray()).put(STATUS, new JsonArray());

                var customMonitoringFields = new HashMap<String, String>();

                if (queryCustomMonitoringFields)
                {
                    //set Custom fields in result as it has dynamic keys.
                    for (var customMonitoringId : CustomMonitoringFieldConfigStore.getStore().getFieldIds())
                    {
                        customMonitoringFields.put(customMonitoringId, CustomMonitoringFieldConfigStore.getStore().getItem(CommonUtil.getLong(customMonitoringId), false).getString(CustomMonitoringField.CUSTOM_MONITORING_FIELD_NAME));

                        result.put(CustomMonitoringFieldConfigStore.getStore().getItem(CommonUtil.getLong(customMonitoringId)).getString(CustomMonitoringField.CUSTOM_MONITORING_FIELD_NAME), new JsonArray());
                    }
                }

                JsonArray instances = null;

                var instanceType = inventory.getString(INSTANCE, EMPTY_VALUE);

                if (!inventory.getString(INSTANCE).isEmpty())
                {
                    instances = result.getJsonArray(inventory.getString(INSTANCE));

                    result.put(INSTANCE_TAGS, new JsonArray());

                    queryObjectSeverity = false; //instance level data
                }

                for (var i = 0; i < objects.size(); i++)
                {
                    var object = CommonUtil.getInteger(objects.getValue(i));

                    var instance = EMPTY_VALUE;

                    if (instances != null)
                    {
                        instance = CommonUtil.getString(instances.getValue(i));

                        setTagInstances(result, object, instanceType.equalsIgnoreCase(NMSConstants.INTERFACE) ? result.getJsonArray("interface~index^last").getString(i) : instance, instanceType, containerTypeReport);
                    }

                    items.remove(object);

                    composeResult(result, customMonitoringFields, category, object, queryActiveAlerts, queryCustomMonitoringFields, queryApps, instance, queryObjectSeverity, containerTypeReport);
                }

                /*
                    If an object exists in the Object config store, but the corresponding data is not available in the database, we will map that object in columnMappers and then will call composeResult.
                 */
                if (!items.isEmpty() && queryObjectSeverity)
                {
                    var dataPoints = context.getJsonArray(VISUALIZATION_DATA_SOURCES).getJsonObject(0).getJsonArray(DATA_POINTS);

                    for (var item : items)
                    {
                        result.getJsonArray(APIConstants.Entity.OBJECT.getName().toLowerCase()).add(item);

                        for (var i = 0; i < dataPoints.size(); i++)
                        {
                            var dataPoint = dataPoints.getJsonObject(i);

                            var mapper = columnMappers.getJsonObject(dataPoint.getString(DATA_POINT));

                            if (mapper.getJsonArray(MAPPER_DATA_CATEGORIES).contains(CommonUtil.getInteger(DatastoreConstants.DataCategory.FLOAT.getName())))
                            {
                                result.getJsonArray(dataPoint.getString(DATA_POINT) + CARET_SEPARATOR + dataPoint.getString(AGGREGATOR)).add("-" + Float.MAX_VALUE);
                            }
                            else if (mapper.getJsonArray(MAPPER_DATA_CATEGORIES).contains(CommonUtil.getInteger(DatastoreConstants.DataCategory.NUMERIC.getName())))
                            {
                                result.getJsonArray(dataPoint.getString(DATA_POINT) + CARET_SEPARATOR + dataPoint.getString(AGGREGATOR)).add(Long.MIN_VALUE);
                            }
                            else
                            {
                                result.getJsonArray(dataPoint.getString(DATA_POINT) + CARET_SEPARATOR + dataPoint.getString(AGGREGATOR)).add(EMPTY_VALUE);
                            }
                        }

                        composeResult(result, customMonitoringFields, category, item, queryActiveAlerts, queryCustomMonitoringFields, queryApps, EMPTY_VALUE, true, containerTypeReport);
                    }

                }

                if (context.containsKey(VISUALIZATION_TAGS) && !context.getJsonArray(VISUALIZATION_TAGS).isEmpty())
                {
                    joinTags(result, context);
                }

                if (CommonUtil.traceEnabled())
                {
                    logger.trace("composed inventory result::" + result);
                }
            }
        }

        //To ensure accurate results and proper functionality in column settings, it is important to have "monitor" as "Result By" field. This will prevent users from adding "Tags" as a column if "Tags" or "Group" or else are already present in the "Result By" field.
        else if (context.getString(VisualizationConstants.JOIN_RESULT).equalsIgnoreCase(VisualizationGrouping.TAG.getName()) && (context.containsKey(VISUALIZATION_TAGS) && !context.getJsonArray(VISUALIZATION_TAGS).isEmpty()))
        {
            joinTags(result, context);
        }

        else if (context.getString(VisualizationConstants.JOIN_RESULT).equalsIgnoreCase("log.event"))
        {
            try
            {
                result.remove(EVENT_ID + CARET_SEPARATOR + VALUE);

                var columns = context.containsKey(VISUALIZATION_EXTRA_COLUMNS) ? context.getJsonArray(VISUALIZATION_EXTRA_COLUMNS) : null;

                var row = new JsonObject();

                if (columns != null && !columns.isEmpty() && result.containsKey(EVENT + CARET_SEPARATOR + VALUE))
                {
                    for (var i = 0; i < result.getJsonArray(TIMESTAMP).size(); i++)
                    {
                        VisualizationConstants.extractEvent(Buffer.buffer(result.getJsonArray(EVENT + CARET_SEPARATOR + VALUE).getBinary(i)), row, null, null, logger);

                        for (var j = 0; j < columns.size(); j++)
                        {
                            result.getMap().computeIfAbsent(columns.getString(j), val -> new JsonArray());

                            result.getJsonArray(columns.getString(j)).add(CommonUtil.getString(row.getValue(columns.getString(j), EMPTY_VALUE)));
                        }

                        row.clear();
                    }

                    result.remove(EVENT + CARET_SEPARATOR + VALUE);
                }
            }
            catch (Exception exception)
            {
                logger.error(exception);
            }
        }

        //config.event -> This event will extract event^value data into simpler rows.
        else if (context.getString(VisualizationConstants.JOIN_RESULT).equalsIgnoreCase("config.event"))
        {
            var composedResult = composeEventResponse(result.getJsonArray((ConfigConstants.CONFIG_EVENT + CARET_SEPARATOR + VALUE)), logger);

            for (var i = 0; i < composedResult.getJsonArray(ConfigConstants.CONFIG_EVENT + CARET_SEPARATOR + VALUE).size(); i++)
            {
                var row = composedResult.getJsonArray(ConfigConstants.CONFIG_EVENT + CARET_SEPARATOR + VALUE).getJsonObject(i);

                for (var key : ConfigConstants.CONFIG_ACTION_FIELDS) // We will be adding dummy value for the columns which are not present in the old data in DB
                {
                    if (row.containsKey(key))
                    {
                        if (result.containsKey(key))
                        {
                            result.getJsonArray(key).add(row.getValue(key));
                        }
                        else
                        {
                            result.getMap().computeIfAbsent(key, value -> new JsonArray().add(row.getValue(key)));
                        }
                    }
                    else
                    {
                        if (result.containsKey(key))
                        {
                            result.getJsonArray(key).add(EMPTY_VALUE);
                        }
                        else
                        {
                            result.getMap().computeIfAbsent(key, value -> new JsonArray().add(EMPTY_VALUE));
                        }
                    }
                }
            }

            //removing event^value from the map after fetching data from it.
            result.remove(ConfigConstants.CONFIG_EVENT + CARET_SEPARATOR + VALUE);
        }

        //object.columns -> This join result will be used to merge the config related info of object with widget query response.
        else if (context.getString(VisualizationConstants.JOIN_RESULT).equalsIgnoreCase("object.columns"))
        {
            joinColumns(result, context);
        }

        //  instance.ip -> This join will be used to get the instance ip of the instances.
        else if (context.getString(VisualizationConstants.JOIN_RESULT).equalsIgnoreCase("instance.ip"))
        {
            if (!context.getJsonArray(VISUALIZATION_RESULT_BY).isEmpty() && NMSConstants.INSTANCE_IP_METRICS.containsKey(context.getJsonArray(VISUALIZATION_RESULT_BY).getString(0)))
            {
                result.put(INSTANCE_IP, new JsonArray());

                for (var i = 0; i < result.getJsonArray(APIConstants.Entity.OBJECT.getName().toLowerCase()).size(); i++)
                {
                    result.getJsonArray(INSTANCE_IP).add(
                            ObjectCacheStore.getStore().getInstanceIP(
                                    ObjectConfigStore.getStore().getIdByObjectId(Integer.parseInt(result.getJsonArray(APIConstants.Entity.OBJECT.getName().toLowerCase()).getString(i))),
                                    result.getJsonArray(context.getJsonArray(VISUALIZATION_RESULT_BY).getString(0)).getString(i)));
                }
            }

            // In case of tag as column and instance.ip both are required but we can not use two use join types, we need to check for the tag as a column.
            if (context.containsKey(VISUALIZATION_TAGS) && !context.getJsonArray(VISUALIZATION_TAGS).isEmpty())
            {
                joinTags(result, context);
            }
        }

        /*
            Deleted instances are still shown in string-level counters because garbage data is not removed from the database.
            Similarly, in the switch port view widget, deleted interfaces appear as we query only string counters.
            However, when an interface is deleted, its instance is removed from the cache store.
            To ensure accuracy, the port view widget considers only instances available in the cache store.
         */

        else if (context.getString(VisualizationConstants.JOIN_RESULT).equalsIgnoreCase("port.view"))
        {
            var values = result.getJsonArray(INTERFACE);

            var items = ObjectStatusCacheStore.getStore().getInstanceItems(ObjectConfigStore.getStore().getIdByObjectId(CommonUtil.getInteger(result.getJsonArray(APIConstants.Entity.OBJECT.getName().toLowerCase()).getValue(0))), false);

            var indices = new HashSet<Integer>();

            var plugin = ObjectManagerCacheStore.getStore().getPluginIdByMetricPlugin(NMSConstants.MetricPlugin.SNMP_INTERFACE.getName());

            for (var i = 0; i < values.size(); i++)
            {
                if (items.containsKey(NMSConstants.INTERFACE + INSTANCE_SEPARATOR + values.getString(i) + INSTANCE_SEPARATOR + plugin))
                {
                    indices.add(i);
                }
            }

            if (!indices.isEmpty())
            {
                var record = new JsonObject();

                for (var entry : result.getMap().entrySet())
                {
                    var entities = ((JsonArray) entry.getValue());

                    record.getMap().computeIfAbsent(entry.getKey(), value -> new JsonArray());

                    for (var index : indices)
                    {
                        record.getJsonArray(entry.getKey()).add(entities.getValue(CommonUtil.getInteger(index)));
                    }
                }

                result.mergeIn(record);
            }
        }

        // Check if the join result type is "raw" and remove the "OBJECT_ID" key from the result if it exists.
        // This ensures that raw data does not include object.id-specific identifiers.
        else if (context.getString(VisualizationConstants.JOIN_RESULT).equalsIgnoreCase("raw"))
        {
            try
            {
                //For Instance Counter, we need to remove the instance as well as object.id
                for (var entry : result.getMap().keySet())
                {
                    if (entry.contains(INSTANCE_SEPARATOR))
                    {
                        var instance = entry.split(INSTANCE_SEPARATOR)[0];

                        if (result.containsKey(instance))
                        {
                            result.remove(instance);

                            break;
                        }
                    }
                }

                //For Scaler Counter, need to only remove the object.id
                if (result.containsKey(AIOpsObject.OBJECT_ID))
                {
                    result.remove(AIOpsObject.OBJECT_ID);
                }

            }
            catch (Exception exception)
            {
                logger.error(exception);
            }
        }
    }

    private static void setTagInstances(JsonObject result, int object, String instance, String instanceType, boolean containerTypeReport)
    {
        try
        {
            var builder = new StringBuilder();

            var tags = TagCacheStore.getStore().getTags(object + KEY_SEPARATOR + instanceType + KEY_SEPARATOR + instance);

            if (tags != null && !tags.isEmpty())
            {
                for (var tag : tags)
                {
                    if (!builder.isEmpty())
                    {
                        builder.append(containerTypeReport ? COMMA_SEPARATOR : VALUE_SEPARATOR); // Append Value separator if not the first string
                    }

                    builder.append(TagConfigStore.getStore().getTag(CommonUtil.getLong(tag)));
                }
            }
            else
            {
                builder.append(EMPTY_VALUE);
            }

            result.getJsonArray(INSTANCE_TAGS).add(builder);

        }
        catch (Exception ignored)
        {

        }
    }

    private static JsonObject composeEventResponse(JsonArray rows, Logger logger)
    {
        var result = new JsonObject();

        for (var i = 0; i < rows.size(); i++)
        {
            var event = new JsonObject();

            extractEvent(Buffer.buffer(rows.getBinary(i)), event, null, null, logger);

            event.remove(LogEngineConstants.EVENT_PATTERN_ID);

            VisualizationConstants.compose(new JsonObject().put(ConfigConstants.CONFIG_EVENT + CARET_SEPARATOR + VALUE, event), result);
        }

        return result;
    }

    private static void joinTags(JsonObject result, JsonObject context)
    {
        //HashSet is used for unique tags, if tags are same but have lowercase/uppercase, it will create packing error thus will only add only one of them.
        var visualizationTags = new HashSet<String>();

        for (var i = 0; i < context.getJsonArray(VISUALIZATION_TAGS).size(); i++)
        {
            //if tag is already a key in result, it would ignore the tag...
            if (!result.containsKey(context.getJsonArray(VISUALIZATION_TAGS).getString(i)))
            {
                visualizationTags.add(context.getJsonArray(VISUALIZATION_TAGS).getString(i).toLowerCase());
            }
        }

        if (result.containsKey(APIConstants.Entity.OBJECT.getName().toLowerCase()) && !result.getJsonArray(APIConstants.Entity.OBJECT.getName().toLowerCase()).isEmpty())
        {
            var instanceType = context.getJsonArray(VISUALIZATION_DATA_SOURCES).getJsonObject(0).getValue(INSTANCE_TYPE);

            var groupBy = context.getJsonArray(VISUALIZATION_DATA_SOURCES).getJsonObject(0).getString(GROUP_BY);

            var instanceTypes = new HashSet<String>();

            // we need to check if the instance is a string or an array of strings
            if (instanceType instanceof JsonArray instances)
            {
                for (var i = 0; i < instances.size(); i++)
                {
                    var type = instances.getString(i);

                    // if the instance empty and if group by is in PASSOVER_INSTANCES than we need to consider it as a object rather than instance
                    if (type != null && !type.isEmpty() && !PASSOVER_INSTANCES.contains(groupBy))
                    {
                        instanceTypes.add(type);
                    }
                }
            }
            else if ((instanceType instanceof String) && (!CommonUtil.getString(instanceType).isEmpty()))
            {
                instanceTypes.add(CommonUtil.getString(instanceType));
            }

            if (!instanceTypes.isEmpty())
            {
                var builder = new StringBuilder();

                var instance = EMPTY_VALUE;

                for (var i = 0; i < result.getJsonArray(APIConstants.Entity.OBJECT.getName().toLowerCase()).size(); i++)
                {
                    for (var visualizationTag : visualizationTags)
                    {
                        result.getMap().computeIfAbsent(visualizationTag, value -> new JsonArray());

                        JsonArray tags = null;

                        for (String type : instanceTypes)
                        {
                            /*
                             * In case of Inventory report, we will have VMs a column which includes all the instances of the object, and type of instance will be retrieved from the "group.by"
                             * In case of widget or availability Report, we will have instance.type as a column (i.e. "esxi.vm" as a column) and instance type will be retrieved from "visualization.result.by"
                             * */
                            if (context.getJsonArray(VISUALIZATION_DATA_SOURCES).getJsonObject(0).containsKey(VISUALIZATION_RESULT_BY) && context.getJsonArray(VISUALIZATION_DATA_SOURCES).getJsonObject(0).getJsonArray(VISUALIZATION_RESULT_BY).contains(type) && result.containsKey(type))
                            {
                                instance = CommonUtil.getString(result.getJsonArray(type).getValue(i));
                            }
                            else if (result.containsKey(groupBy))
                            {
                                instance = CommonUtil.getString(result.getJsonArray(groupBy).getValue(i));
                            }

                            if (context.containsKey(VisualizationConstants.CONTAINER_TYPE) && context.getString(VisualizationConstants.CONTAINER_TYPE) != null && context.getString(VisualizationConstants.CONTAINER_TYPE).equalsIgnoreCase(VisualizationConstants.CONTAINER_TYPE_REPORT))
                            {
                                /*
                                 * first we need to check for the combination of instance and object id to verify instance exist for the object id
                                 * i.e.  object.id is 22 and instance.types are ["esxi.vm", "citrixxen.vm", "hyperv.vm"], in such case we need to check for the which instance belongs to respected object.id
                                 *       here esxi.vm is instance of object.id 22, if we found tags for the instance (22 + esxi.vm) then we can break the loop
                                 * */
                                if (TagCacheStore.getStore().existTag(CommonUtil.getInteger(result.getJsonArray(APIConstants.Entity.OBJECT.getName().toLowerCase()).getValue(i)), type))
                                {
                                    tags = TagCacheStore.getStore().getInstanceTagKeys(CommonUtil.getInteger(result.getJsonArray(APIConstants.Entity.OBJECT.getName().toLowerCase()).getValue(i)), visualizationTag, instance, type);

                                    break;  // if we found the instance tag for the object id then we can break the loop
                                }
                            }
                            else
                            {
                                if (context.getJsonArray(VISUALIZATION_DATA_SOURCES).getJsonObject(0).getJsonArray(VISUALIZATION_RESULT_BY).contains(type) && result.containsKey(type))
                                {
                                    tags = TagCacheStore.getStore().getInstanceTagKeys(CommonUtil.getInteger(result.getJsonArray(APIConstants.Entity.OBJECT.getName().toLowerCase()).getValue(i)), visualizationTag, instance, type);
                                }
                                else
                                {
                                    //Result By - Monitor for Instance Counter will send all the instance tags.
                                    tags = TagCacheStore.getStore().getTagsByTagKey(CommonUtil.getInteger(result.getJsonArray(APIConstants.Entity.OBJECT.getName().toLowerCase()).getValue(i)), type, visualizationTag);
                                }
                                break;
                            }

                        }

                        if (tags != null)
                        {
                            for (var tag : tags)
                            {
                                if (!builder.isEmpty())
                                {
                                    builder.append(COMMA_SEPARATOR); // Append Value separator if not the first string
                                }

                                builder.append(tag);
                            }

                            result.getJsonArray(visualizationTag.toLowerCase()).add(builder.toString());

                            builder.setLength(0);

                        }
                        else
                        {
                            result.getJsonArray(visualizationTag.toLowerCase()).add(EMPTY_VALUE);
                        }
                    }
                }
            }
            else
            {
                for (var i = 0; i < result.getJsonArray(APIConstants.Entity.OBJECT.getName().toLowerCase()).size(); i++)
                {
                    for (var visualizationTag : visualizationTags)
                    {
                        result.getMap().computeIfAbsent(visualizationTag, value -> new JsonArray());

                        var tags = TagCacheStore.getStore().getObjectTagsByTagKey(CommonUtil.getInteger(result.getJsonArray(APIConstants.Entity.OBJECT.getName().toLowerCase()).getValue(i)), visualizationTag);

                        if (tags != null)
                        {
                            result.getJsonArray(visualizationTag.toLowerCase()).add(StringUtils.join(tags, COMMA_SEPARATOR));
                        }
                        else
                        {
                            result.getJsonArray(visualizationTag.toLowerCase()).add(EMPTY_VALUE);
                        }
                    }
                }
            }
        }
    }

    private static void joinColumns(JsonObject result, JsonObject context)
    {
        var containerTypeReport = context.containsKey(VisualizationConstants.CONTAINER_TYPE) && context.getString(VisualizationConstants.CONTAINER_TYPE) != null && context.getString(VisualizationConstants.CONTAINER_TYPE).equalsIgnoreCase(VisualizationConstants.CONTAINER_TYPE_REPORT);

        //Adding all common columns for all objects
        result.put(AIOpsObject.OBJECT_CREATION_TIME, new JsonArray()).put(AIOpsObject.OBJECT_EVENT_PROCESSORS, new JsonArray()).put(AIOpsObject.OBJECT_DISCOVERY_METHOD, new JsonArray()).put(AIOpsObject.OBJECT_HOST, new JsonArray()).put(AIOpsObject.OBJECT_CATEGORY, new JsonArray()).put(AIOpsObject.OBJECT_GROUPS, new JsonArray()).put(AIOpsObject.OBJECT_TAGS, new JsonArray()).put(AIOpsObject.OBJECT_TARGET, new JsonArray()).put(AIOpsObject.OBJECT_NAME, new JsonArray()).put(AIOpsObject.OBJECT_VENDOR, new JsonArray()).put(AIOpsObject.OBJECT_TYPE, new JsonArray()).put(AIOpsObject.OBJECT_IP, new JsonArray()).put(STATUS, new JsonArray());

        for (var i = 0; i < result.getJsonArray(APIConstants.Entity.OBJECT.getName().toLowerCase()).size(); i++)
        {
            setObjectColumns(result, ObjectConfigStore.getStore().getItemByObjectId(Integer.parseInt(result.getJsonArray(APIConstants.Entity.OBJECT.getName().toLowerCase()).getString(i))), containerTypeReport);
        }
    }

    /*
     This function would compose the result as per the objects and set custom monitoring fields, object columns, active alerts.
     */
    private static void composeResult(JsonObject result, Map<String, String> customMonitoringFields, String category, Integer object, boolean activeAlerts, boolean customMonitoring, boolean application, String instance, boolean queryObjectSeverity, boolean containerTypeReport)
    {
        var item = ObjectConfigStore.getStore().getItem(ObjectConfigStore.getStore().getIdByObjectId(object), false);

        if (item == null)
        {
            item = ArchivedObjectConfigStore.getStore().getItemByValue(AIOpsObject.OBJECT_ID, object);
        }

        if ((queryObjectSeverity || (instance != null && !instance.equalsIgnoreCase(EMPTY_VALUE))))
        {
            setSeverityColumns(result, item.getLong(ID), instance);
        }

        setObjectColumns(result, item, containerTypeReport);

        if (activeAlerts)
        {
            setPolicyStats(result, item.getLong(ID));
        }

        if (customMonitoring)
        {
            setCustomMonitoringFields(result, item, customMonitoringFields);
        }

        if (category.equalsIgnoreCase(NMSConstants.Category.SERVER.getName()) && application)
        {
            setApps(result, item.getLong(ID));
        }
        else if (category.equalsIgnoreCase(NMSConstants.Category.SERVICE_CHECK.getName()))
        {
            if (item.containsKey(AIOpsObject.OBJECT_CONTEXT) && item.getJsonObject(AIOpsObject.OBJECT_CONTEXT).containsKey(PORT))
            {
                result.getJsonArray(PORT).add(item.getJsonObject(AIOpsObject.OBJECT_CONTEXT).getInteger(PORT));
            }
            else
            {
                result.getJsonArray(PORT).add(NOT_AVAILABLE);
            }
        }
    }

    private static void setSeverityColumns(JsonObject result, Long id, String instance)
    {
        if (instance != null && !instance.isEmpty())
        {
            result.getJsonArray(SEVERITY).add(MetricPolicyCacheStore.getStore().getInstanceSeverity(id + SEPARATOR + instance) != null ? MetricPolicyCacheStore.getStore().getInstanceSeverity(id + SEPARATOR + instance) : Severity.UNKNOWN.name());
        }
        else
        {
            result.getJsonArray(SEVERITY).add(MetricPolicyCacheStore.getStore().getSeverity(id) != null ? MetricPolicyCacheStore.getStore().getSeverity(id) : Severity.UNKNOWN.name());
        }
    }

    private static void setObjectColumns(JsonObject result, JsonObject object, boolean containerTypeReport)
    {
        result.getJsonArray(AIOpsObject.OBJECT_CREATION_TIME).add(object.getValue(AIOpsObject.OBJECT_CREATION_TIME_SECONDS, 0));

        if (object.containsKey(AIOpsObject.OBJECT_EVENT_PROCESSORS))
        {
            var objectEventProcessors = object.getJsonArray(AIOpsObject.OBJECT_EVENT_PROCESSORS);

            var builder = new StringBuilder();

            if (!objectEventProcessors.isEmpty())
            {
                for (var objectEventProcessor : objectEventProcessors)
                {
                    if (!builder.isEmpty())
                    {
                        builder.append(COMMA_SEPARATOR); // Append comma separator if not the first string
                    }

                    builder.append(RemoteEventProcessorConfigStore.getStore().getItem(CommonUtil.getLong(objectEventProcessor)).getString(RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_IP));
                }
            }
            else
            {
                builder.append(EMPTY_VALUE);
            }

            result.getJsonArray(AIOpsObject.OBJECT_EVENT_PROCESSORS).add(builder);
        }
        else
        {
            result.getJsonArray(AIOpsObject.OBJECT_EVENT_PROCESSORS).add(EMPTY_VALUE);
        }

        result.getJsonArray(AIOpsObject.OBJECT_TARGET).add(object.getString(AIOpsObject.OBJECT_TARGET, EMPTY_VALUE));

        result.getJsonArray(AIOpsObject.OBJECT_DISCOVERY_METHOD).add(object.getString(AIOpsObject.OBJECT_DISCOVERY_METHOD, EMPTY_VALUE));

        result.getJsonArray(AIOpsObject.OBJECT_CATEGORY).add(object.getString(AIOpsObject.OBJECT_CATEGORY, EMPTY_VALUE));

        result.getJsonArray(AIOpsObject.OBJECT_HOST).add(object.getString(AIOpsObject.OBJECT_HOST, EMPTY_VALUE));

        result.getJsonArray(AIOpsObject.OBJECT_NAME).add(object.getString(AIOpsObject.OBJECT_NAME, EMPTY_VALUE));

        result.getJsonArray(AIOpsObject.OBJECT_TYPE).add(object.getString(AIOpsObject.OBJECT_TYPE, EMPTY_VALUE));

        result.getJsonArray(AIOpsObject.OBJECT_VENDOR).add(object.getString(AIOpsObject.OBJECT_VENDOR, EMPTY_VALUE));

        result.getJsonArray(AIOpsObject.OBJECT_IP).add(object.getString(AIOpsObject.OBJECT_IP, EMPTY_VALUE));

        result.getJsonArray(STATUS).add(ObjectStatusCacheStore.getStore().existItem(object.getLong(ID)) ? ObjectStatusCacheStore.getStore().getItem(object.getLong(ID)) : STATUS_UNKNOWN);

        if (object.containsKey(AIOpsObject.OBJECT_TAGS))
        {
            var tags = object.getJsonArray(AIOpsObject.OBJECT_TAGS);

            var builder = new StringBuilder();

            if (tags != null && !tags.isEmpty())
            {
                for (var tag : tags)
                {
                    if (!builder.isEmpty())
                    {
                        builder.append(containerTypeReport ? COMMA_SEPARATOR : VALUE_SEPARATOR); // Append Value separator if not the first string
                    }

                    builder.append(TagConfigStore.getStore().getTag(CommonUtil.getLong(tag)));
                }
            }
            else
            {
                builder.append(EMPTY_VALUE);
            }

            result.getJsonArray(AIOpsObject.OBJECT_TAGS).add(builder);
        }
        else
        {
            result.getJsonArray(AIOpsObject.OBJECT_TAGS).add(EMPTY_VALUE);
        }

        if (object.containsKey(AIOpsObject.OBJECT_GROUPS))
        {
            var builder = new StringBuilder();

            var groups = object.getJsonArray(AIOpsObject.OBJECT_GROUPS);

            if (!groups.isEmpty())
            {
                for (var group : groups)
                {
                    if (!builder.isEmpty())
                    {
                        builder.append(COMMA_SEPARATOR); // Append comma separator if not the first string
                    }

                    // In Report, we require to have group name instead of group ID
                    builder.append(containerTypeReport ? GroupConfigStore.getStore().getItem(CommonUtil.getLong(group), false).getString(Group.FIELD_GROUP_NAME) : group);
                }
            }
            else
            {
                builder.append(EMPTY_VALUE);
            }

            result.getJsonArray(AIOpsObject.OBJECT_GROUPS).add(builder);
        }
    }

    private static void setApps(JsonObject result, long id)
    {
        try
        {
            var builder = new StringBuilder();

            var apps = MetricConfigStore.getStore().getAppsByObjectId(id);

            if (apps != null && !apps.isEmpty())
            {
                for (var app : apps.getMap().entrySet())
                {
                    if (!builder.isEmpty())
                    {
                        builder.append(COMMA_SEPARATOR); // Append comma separator if not the first string
                    }

                    builder.append(CommonUtil.getString(app.getValue()));
                }
            }
            else
            {
                builder.append(EMPTY_VALUE);
            }

            result.getJsonArray(NMSConstants.APPS).add(builder);

        }
        catch (Exception ignored)
        {

        }
    }

    private static void setCustomMonitoringFields(JsonObject result, JsonObject object, Map<String, String> customMonitoringFields)
    {
        if (object.containsKey(AIOpsObject.OBJECT_CUSTOM_FIELDS))
        {
            var fields = object.getJsonObject(AIOpsObject.OBJECT_CUSTOM_FIELDS);

            for (var customMonitoringField : customMonitoringFields.entrySet())
            {
                if (fields.containsKey(customMonitoringField.getKey()))
                {
                    result.getJsonArray(customMonitoringField.getValue()).add(fields.getString(customMonitoringField.getKey()));
                }
                else
                {
                    result.getJsonArray(customMonitoringField.getValue()).add(EMPTY_VALUE);
                }
            }
        }
        else
        {
            for (var customMonitoringField : customMonitoringFields.entrySet())
            {
                result.getJsonArray(customMonitoringField.getValue()).add(EMPTY_VALUE);
            }
        }
    }

    private static void setPolicyStats(JsonObject result, long id)
    {
        var items = MetricPolicyCacheStore.getStore().getSeveritiesByObject(id);

        result.getJsonArray(Severity.CRITICAL.name().toLowerCase()).add(items.getValue(Severity.CRITICAL.name().toLowerCase()));

        result.getJsonArray(Severity.CLEAR.name().toLowerCase()).add(items.getValue(Severity.CLEAR.name().toLowerCase()));

        result.getJsonArray(Severity.MAJOR.name().toLowerCase()).add(items.getValue(Severity.MAJOR.name().toLowerCase()));

        result.getJsonArray(Severity.DOWN.name().toLowerCase()).add(items.getValue(Severity.DOWN.name().toLowerCase()));

        result.getJsonArray(Severity.UNREACHABLE.name().toLowerCase()).add(items.getValue(Severity.UNREACHABLE.name().toLowerCase()));

        result.getJsonArray(Severity.WARNING.name().toLowerCase()).add(items.getValue(Severity.WARNING.name().toLowerCase()));
    }

    public static String transformColumn(String column, String value, String type)
    {
        String result = null;

        if (column.equalsIgnoreCase(SNMPTrapProcessor.SNMP_TRAP_OID))
        {
            var profile = SNMPTrapProfileConfigStore.getStore().getTrapProfiles(value);

            if (profile != null)
            {
                result = profile.getString(SNMPTrapProfile.SNMP_TRAP_PROFILE_NAME);
            }
            else
            {
                result = value;
            }
        }
        else if (column.equalsIgnoreCase(PolicyEngineConstants.POLICY_ID))
        {
            if (type.equalsIgnoreCase(APIConstants.Entity.NETROUTE.getName()) || type.equalsIgnoreCase(VisualizationDataSource.NETROUTE_METRIC.getName()) || type.equalsIgnoreCase(VisualizationDataSource.NETROUTE_EVENT.getName()))
            {
                if (NetRoutePolicyConfigStore.getStore().existItem(CommonUtil.getLong(value)))
                {
                    result = NetRoutePolicyConfigStore.getStore().getItem(CommonUtil.getLong(value), false).getString(PolicyEngineConstants.POLICY_NAME);
                }
                else
                {
                    result = value;
                }
            }
            else
            {
                var policy = MetricPolicyConfigStore.getStore().getItem(CommonUtil.getLong(value));

                if (policy != null)
                {
                    result = policy.getString(PolicyEngineConstants.POLICY_NAME);
                }
                else
                {
                    policy = EventPolicyConfigStore.getStore().getItem(CommonUtil.getLong(value), false);

                    if (policy != null)
                    {
                        result = policy.getString(PolicyEngineConstants.POLICY_NAME);
                    }
                    else
                    {
                        result = value;
                    }
                }
            }
        }

        else if (column.equalsIgnoreCase(NetRoute.NETROUTE_ID))
        {
            var item = NetRouteConfigStore.getStore().getItem(CommonUtil.getLong(value));

            if (item != null)
            {
                result = item.getString(NetRoute.NETROUTE_NAME);
            }
            else
            {
                result = value;
            }
        }

        return result;
    }

    private static void resolve(JsonObject result, String column, int index, String category)
    {
        var iterator = result.iterator();

        var partialResult = new JsonObject();

        while (iterator.hasNext())
        {
            var value = iterator.next();

            var tokens = value.getKey().split(CARET_SEPARATOR_WITH_ESCAPE);

            if (tokens.length > 1)
            {
                var transformedValue = transformColumn(column, tokens[index], category);

                if (transformedValue != null)
                {
                    partialResult.put(value.getKey().replace(tokens[index], transformedValue), value.getValue());

                    iterator.remove();
                }
            }
        }

        if (!partialResult.isEmpty())
        {
            result.mergeIn(partialResult);
        }
    }

    public static void send(Logger logger, JsonArray ids)
    {
        var context = new HashMap<Integer, Object>();

        for (var entry : DataRetentionPolicyConfigStore.getStore().getItem().getJsonObject(DataRetentionPolicy.DATA_RETENTION_POLICY_CONTEXT))
        {
            if (!entry.getKey().equalsIgnoreCase("CONFIG"))//as Config is internal component so need to send its data to DB
            {
                context.put(DatastoreConstants.DatastoreType.valueOf(entry.getKey()).ordinal(), entry.getValue());
            }
        }

        var buffer = Buffer.buffer();

        if (CommonUtil.traceEnabled())
        {
            logger.trace("Data Retention Policy columns updated sending to DB:" + JsonObject.mapFrom(context).encodePrettily());
        }

        buffer.appendByte(DatastoreConstants.OperationType.STORE_RETENTION.getName()).appendBytes((JsonObject.mapFrom(context)).encode().getBytes());

        for (var id : ids)
        {
            var item = RemoteEventProcessorConfigStore.getStore().getItem(CommonUtil.getLong(id));

            if (item != null && item.getString(REMOTE_EVENT_PROCESSOR_TYPE).equalsIgnoreCase(BootstrapType.DATASTORE.name()))
            {
                Bootstrap.vertx().eventBus().send(EVENT_PUBLICATION_DATASTORE_WRITE, new JsonObject()
                        .put(EventBusConstants.EVENT_COPY_REQUIRED, false).put(EventBusConstants.EVENT_TOPIC, DATASTORE_CONNECTION_ALIVE_TOPIC)
                        .put(REMOTE_EVENT_PROCESSOR_TOPIC, DATASTORE_OPERATION_TOPIC)
                        .put(REMOTE_EVENT_PROCESSOR_UUID, item.getString(REMOTE_EVENT_PROCESSOR_UUID))
                        .put(EVENT_CONTEXT, buffer.getBytes()));
            }
        }
    }

    public static void setQueryParameters(JsonObject visualizationContext, JsonObject subQueryContext, Set<String> aggregations, JsonObject queryContext)
    {
        var priority = QueryPriority.P0.getName();

        if (visualizationContext.containsKey(PolicyEngineConstants.POLICY_ID))
        {
            priority = QueryPriority.P3.getName();
        }
        else if (visualizationContext.containsKey(ReportConstants.REPORT_ID))
        {
            priority = QueryPriority.P4.getName();
        }
        else if (visualizationContext.containsKey(VisualizationConstants.VISUALIZATION_STREAMING))
        {
            priority = QueryPriority.P2.getName();
        }

        subQueryContext.put(QUERY_PRIORITY, priority);

        subQueryContext.put(QUERY_CREATION_TIME, DateTimeUtil.currentSeconds());

        var visualizationDataSources = subQueryContext.getJsonObject(VisualizationConstants.VISUALIZATION_DATA_SOURCES);

        if (visualizationDataSources.getString(VisualizationConstants.TYPE).equalsIgnoreCase(VisualizationDataSource.POLICY.getName()))
        {
            if (visualizationDataSources.getString(VisualizationConstants.CATEGORY).equalsIgnoreCase(METRIC))
            {
                visualizationDataSources.put(VisualizationConstants.TYPE, DatastoreConstants.DatastoreType.METRIC_POLICY.ordinal());
            }

            else
            {
                visualizationDataSources.put(VisualizationConstants.TYPE, DatastoreConstants.DatastoreType.EVENT_POLICY.ordinal());
            }
            //MOTADATA-2396
            if (visualizationDataSources.getString(CATEGORY).equalsIgnoreCase(VisualizationDataSource.TRAP.getName()) && (visualizationDataSources.containsKey(VisualizationConstants.VISUALIZATION_DRILL_DOWN) && visualizationDataSources.getString(VISUALIZATION_DRILL_DOWN).equalsIgnoreCase(YES)))
            {
                subQueryContext.put(VISUALIZATION_DRILL_DOWN, YES);
            }
        }

        else if (visualizationDataSources.getString(VisualizationConstants.TYPE).equalsIgnoreCase(VisualizationDataSource.POLICY_ACKNOWLEDGEMENT.getName()))
        {
            if (visualizationDataSources.getString(VisualizationConstants.CATEGORY).equalsIgnoreCase(METRIC))
            {
                visualizationDataSources.put(VisualizationConstants.TYPE, DatastoreConstants.DatastoreType.METRIC_POLICY_ACKNOWLEDGEMENT.ordinal());
            }

            else
            {
                visualizationDataSources.put(VisualizationConstants.TYPE, DatastoreConstants.DatastoreType.EVENT_POLICY_ACKNOWLEDGEMENT.ordinal());
            }
        }

        else if (visualizationDataSources.getString(VisualizationConstants.TYPE).equalsIgnoreCase(VisualizationDataSource.EVENT_HISTORY.getName()))
        {
            if (visualizationDataSources.getString(CATEGORY).equalsIgnoreCase(VisualizationDataSource.USER_NOTIFICATION.getName()))
            {
                visualizationDataSources.put(VisualizationConstants.TYPE, DatastoreConstants.DatastoreType.NOTIFICATION.ordinal());
            }

            else if (visualizationDataSources.getString(CATEGORY).equalsIgnoreCase(VisualizationDataSource.AUDIT.getName()))
            {
                visualizationDataSources.put(VisualizationConstants.TYPE, DatastoreConstants.DatastoreType.AUDIT.ordinal());
            }

            else if (visualizationDataSources.getString(CATEGORY).equalsIgnoreCase(VisualizationDataSource.FLOW.getName()))
            {
                visualizationDataSources.put(VisualizationConstants.TYPE, DatastoreConstants.DatastoreType.FLOW.ordinal());
            }

            else if (visualizationDataSources.getString(CATEGORY).equalsIgnoreCase(VisualizationDataSource.PERFORMANCE_METRIC.getName()))
            {
                visualizationDataSources.put(VisualizationConstants.TYPE, DatastoreConstants.DatastoreType.METRIC_POLICY.ordinal());
            }

            else if (visualizationDataSources.getString(CATEGORY).equalsIgnoreCase(VisualizationDataSource.LOG.getName()))
            {
                visualizationDataSources.put(VisualizationConstants.TYPE, DatastoreConstants.DatastoreType.LOG.ordinal());
            }

            subQueryContext.put(VISUALIZATION_DRILL_DOWN, YES);
        }

        else
        {
            visualizationDataSources.put(VisualizationConstants.TYPE, DATASTORE_TYPES.get(visualizationDataSources.getString(TYPE)));
        }

        if (aggregations != null && aggregations.size() == 1 && !visualizationDataSources.getString(TYPE).equalsIgnoreCase(CommonUtil.getString(DatastoreConstants.DatastoreType.OBJECT_STATUS_FLAP_METRIC.ordinal())))
        {
            for (var aggregation : aggregations)
            {
                if (aggregation.equalsIgnoreCase(EMPTY_VALUE))
                {
                    if (!visualizationDataSources.getString(VisualizationConstants.TYPE).equalsIgnoreCase(VisualizationDataSource.EVENT_HISTORY.getName()))
                    {
                        subQueryContext.put(QUERY_ABORT_REQUIRED, YES);
                    }

                    subQueryContext.put(VISUALIZATION_DRILL_DOWN, YES);

                    if (queryContext != null)
                    {
                        queryContext.put(VISUALIZATION_DRILL_DOWN, YES);
                    }
                }
            }
        }
    }

    protected static void validateFilters(JsonObject filters)
    {
        if (filters != null && !filters.isEmpty())
        {
            var filter = filters.getJsonObject(DATA_FILTER);

            if (filter != null && !filter.isEmpty() && filter.containsKey(CONDITION_GROUPS))
            {
                var groups = filter.getJsonArray(CONDITION_GROUPS);

                if (groups.size() > 3)
                {
                    for (var i = 3; i < groups.size(); i++)
                    {
                        groups.remove(i);
                    }
                }
            }
        }
    }

    // this method converts Long object.id into the small object.id in filters
    protected static void enrichFilters(JsonObject filters)
    {
        if (filters != null && !filters.isEmpty())
        {
            var filter = filters.getJsonObject(DATA_FILTER);

            if (filter != null && !filter.isEmpty() && filter.containsKey(CONDITION_GROUPS))
            {
                var groups = filter.getJsonArray(CONDITION_GROUPS);

                for (var index = 0; index < groups.size(); index++)
                {
                    var group = groups.getJsonObject(index);

                    if (group.containsKey(CONDITIONS))
                    {
                        var conditions = group.getJsonArray(CONDITIONS);

                        for (var j = 0; j < conditions.size(); j++)
                        {
                            var condition = conditions.getJsonObject(j);

                            if (condition.containsKey(OPERAND) && condition.getString(OPERAND).equalsIgnoreCase(AIOpsObject.OBJECT_ID))
                            {
                                condition.put(VALUE, ObjectConfigStore.getStore().getObjectId(condition.getLong(VALUE)));
                            }
                        }
                    }
                }
            }
        }
    }

    /*
      This function will set filteredEntities from TagCacheStore, as when the data of result By and InstanceType is same, thus filtering out and put it in entity.keys.
     */
    public static void setTagInstanceEntities(JsonArray pluginIds, String column, Map<String, String> entities, Map<String, String> entityKeys, JsonObject filteredEntities, Map<String, Set<Integer>> pluginEntities, Set<Object> plugins)
    {
        for (var i = 0; i < filteredEntities.getJsonArray(ID).size(); i++)
        {
            entityKeys.put(filteredEntities.getJsonArray(ID).getString(i) + CARET_SEPARATOR + filteredEntities.getJsonArray(INSTANCE).getString(i) + CARET_SEPARATOR + column, pluginIds.getLong(0) + DASH_SEPARATOR + MetricConfigStore.getStore().getMetricName(pluginIds.getInteger(0)));

            entities.put(filteredEntities.getJsonArray(ID).getString(i), pluginIds.getLong(0) + DASH_SEPARATOR + MetricConfigStore.getStore().getMetricName(pluginIds.getInteger(0)));

            plugins.add(pluginIds.getLong(0) + DASH_SEPARATOR + MetricConfigStore.getStore().getMetricName(pluginIds.getInteger(0)));

            pluginEntities.get(pluginIds.getLong(0) + DASH_SEPARATOR + MetricConfigStore.getStore().getMetricName(pluginIds.getInteger(0))).add(CommonUtil.getInteger(filteredEntities.getJsonArray(ID).getString(i)));

        }
    }

    /*
     Set entities as per MetricConfigStore for InstanceKeys, if only single monitor is qualified then will be sending entity keys also.
     */
    public static void setEntities(JsonArray pluginIds, String column, Map<String, String> entities, Map<String, String> entityKeys, Map<String, Set<Integer>> pluginEntities, Set<Object> plugins, JsonArray instanceKeys, Set<Integer> items)
    {
        for (var item : MetricConfigStore.getStore().getInstanceObjectsByPlugins(items, pluginIds))
        {
            if (item.getJsonArray(NMSConstants.OBJECTS) != null)
            {
                for (var instance : item.getJsonArray(NMSConstants.OBJECTS))
                {
                    if (!instanceKeys.isEmpty())
                    {
                        if (instanceKeys.contains(instance))
                        {
                            entityKeys.put(item.getInteger(AIOpsObject.OBJECT_ID) + CARET_SEPARATOR + instance + CARET_SEPARATOR + column, item.getInteger(PLUGIN_ID) + DASH_SEPARATOR + MetricConfigStore.getStore().getMetricName(item.getInteger(PLUGIN_ID)));
                        }
                    }
                    else
                    {
                        entityKeys.put(item.getInteger(AIOpsObject.OBJECT_ID) + CARET_SEPARATOR + instance + CARET_SEPARATOR + column, item.getInteger(PLUGIN_ID) + DASH_SEPARATOR + MetricConfigStore.getStore().getMetricName(item.getInteger(PLUGIN_ID)));
                    }
                }

                entities.put(CommonUtil.getString(item.getValue(AIOpsObject.OBJECT_ID)), item.getInteger(PLUGIN_ID) + DASH_SEPARATOR + MetricConfigStore.getStore().getMetricName(item.getInteger(PLUGIN_ID)));

                plugins.add(item.getInteger(PLUGIN_ID) + DASH_SEPARATOR + MetricConfigStore.getStore().getMetricName(item.getInteger(PLUGIN_ID)));

                pluginEntities.get(item.getInteger(PLUGIN_ID) + DASH_SEPARATOR + MetricConfigStore.getStore().getMetricName(item.getInteger(PLUGIN_ID))).add(item.getInteger(AIOpsObject.OBJECT_ID));
            }
        }
    }

    /*
        Set Entities as per Archived if not then we will be taking data from ObjectConfigStore and then set entities.
     */
    public static void setEntities(JsonArray pluginIds, boolean archived, Set<Integer> items, Map<String, String> entities, Set<Object> plugins, Map<String, Set<Integer>> pluginEntities)
    {
        for (var pluginId : pluginIds)
        {
            var plugin = CommonUtil.getInteger(pluginId);

            for (var item : archived ? ArchivedObjectConfigStore.getStore().getItemsByPlugin(items, plugin) : ObjectConfigStore.getStore().getItemsByPlugin(items, plugin))
            {
                entities.put(CommonUtil.getString(item), plugin + DASH_SEPARATOR + MetricConfigStore.getStore().getMetricName(plugin));

                //entityKeys.put(item + CARET_SEPARATOR + column, plugin + DASH_SEPARATOR + MetricConfigStore.getStore().getMetricName(plugin));

                plugins.add(plugin + DASH_SEPARATOR + MetricConfigStore.getStore().getMetricName(plugin));

                pluginEntities.get(plugin + DASH_SEPARATOR + MetricConfigStore.getStore().getMetricName(plugin)).add(CommonUtil.getInteger(item));
            }
        }
    }

    public static void extractEvent(Buffer buffer, JsonObject event, Map<String, TopKUtil> topKObjects, Map<String, Integer> counts, Logger logger)
    {
        var position = 0;

        for (var j = 0; j < buffer.length(); j = position)
        {
            try
            {
                var ordinal = buffer.getIntLE(position);

                if (EventOrdinalCacheStore.getStore().getValue(ordinal) != null)
                {
                    position += 4;
                }

                else
                {
                    position += 2;

                    ordinal = buffer.getIntLE(position);

                    position += 4;
                }


                var key = EventOrdinalCacheStore.getStore().getValue(ordinal).split(SEPARATOR_WITH_ESCAPE)[1];

                var length = buffer.getShortLE(position);

                position += 2;

                var value = buffer.getString(position, position + length);

                position += length;

                if (topKObjects != null)
                {
                    topKObjects.computeIfAbsent(key, val -> new TopKUtil(10));

                    topKObjects.get(key).add(value);

                    counts.put(key, counts.getOrDefault(key, 0) + 1);
                }

                event.put(key, value);
            }

            catch (Exception exception)
            {
                logger.error(exception);
            }
        }
    }

    /**
     * Defines how data from different sources is joined in visualizations.
     * <p>
     * This enum specifies the join strategy used when combining data from multiple sources:
     * <ul>
     *   <li>JOIN_TYPE_ANY - Includes results that match any of the specified conditions</li>
     *   <li>JOIN_TYPE_CUSTOM - Uses a custom join strategy defined in the visualization context</li>
     *   <li>JOIN_TYPE_ALL - Includes only results that match all specified conditions</li>
     * </ul>
     */
    public enum VisualizationJoinType
    {
        /**
         * Join type that includes results matching any condition
         */
        JOIN_TYPE_ANY("any"),

        /**
         * Join type that uses a custom join strategy
         */
        JOIN_TYPE_CUSTOM("custom"),

        /**
         * Join type that includes only results matching all conditions
         */
        JOIN_TYPE_ALL("all");

        private static final Map<String, VisualizationJoinType> VALUES = Collections.unmodifiableMap(Arrays.stream(values())
                .collect(Collectors.toMap(VisualizationJoinType::getName, e -> e)));
        private final String name;

        VisualizationJoinType(String name)
        {
            this.name = name;
        }

        public static VisualizationJoinType valueOfName(String name)
        {
            return VALUES.get(name);
        }

        public String getName()
        {
            return name;
        }
    }

    /**
     * Defines grouping options for visualization data.
     * <p>
     * This enum specifies how data should be grouped in visualizations:
     * <ul>
     *   <li>MONITOR - Group by monitoring entity</li>
     *   <li>GROUP - Group by defined groups</li>
     *   <li>TAG - Group by tags</li>
     *   <li>EVENT_SOURCE - Group by event source</li>
     *   <li>EVENT_CATEGORY - Group by event category</li>
     *   <li>EVENT_SOURCE_TYPE - Group by event source type</li>
     *   <li>CATEGORY - Group by general category</li>
     * </ul>
     */
    public enum VisualizationGrouping
    {
        /**
         * Group by monitoring entity
         */
        MONITOR("monitor"),

        /**
         * Group by defined groups
         */
        GROUP("group"),

        /**
         * Group by tags
         */
        TAG("tag"),

        /**
         * Group by event source
         */
        EVENT_SOURCE("event.source"),

        /**
         * Group by event category
         */
        EVENT_CATEGORY("event.category"),

        /**
         * Group by event source type
         */
        EVENT_SOURCE_TYPE("event.source.type"),

        /**
         * Group by general category
         */
        CATEGORY("category");

        private static final Map<String, VisualizationGrouping> VALUES = Collections.unmodifiableMap(Arrays.stream(values())
                .collect(Collectors.toMap(VisualizationGrouping::getName, e -> e)));
        private final String name;

        VisualizationGrouping(String name)
        {
            this.name = name;
        }

        public static VisualizationGrouping valueOfName(String name)
        {
            return VALUES.get(name);
        }

        public String getName()
        {
            return name;
        }
    }

    /**
     * Defines available data sources for visualizations.
     * <p>
     * This enum specifies the different types of data that can be used as sources for visualizations.
     * Each data source provides specific types of information that can be visualized in different ways.
     * <p>
     * Data sources include metrics, events, logs, availability information, and various other types
     * of monitoring and operational data collected by the system.
     */
    public enum VisualizationDataSource
    {
        /**
         * Object availability status data
         */
        OBJECT_AVAILABILITY("availability"),

        /**
         * Performance metric data
         */
        PERFORMANCE_METRIC("metric"),

        /**
         * Log data
         */
        LOG("log"),

        /**
         * Flow data
         */
        FLOW("flow"),

        /**
         * SNMP trap data
         */
        TRAP("trap"),

        /**
         * Policy data
         */
        POLICY("policy"),

        /**
         * Audit log data
         */
        AUDIT("audit"),

        /**
         * User notification data
         */
        USER_NOTIFICATION("notification"),

        /**
         * Event history data
         */
        EVENT_HISTORY("event.history"),

        /**
         * Cumulative object status flap data
         */
        CUMULATIVE_OBJECT_STATUS_FLAP("status.flap"),

        /**
         * Policy flap data
         */
        POLICY_FLAP("policy.flap"),

        /**
         * Trap flap data
         */
        TRAP_FLAP("trap.flap"),

        /**
         * Policy stream data
         */
        POLICY_STREAM("policy.stream"),

        /**
         * Policy trigger tick data
         */
        POLICY_TRIGGER_TICK("policy.trigger.tick"),

        /**
         * Trap acknowledgement data
         */
        TRAP_ACKNOWLEDGEMENT("trap.acknowledgement"),

        /**
         * Policy acknowledgement data
         */
        POLICY_ACKNOWLEDGEMENT("policy.acknowledgement"),

        /**
         * Correlated metric data
         */
        CORRELATED_METRIC("correlated.metric"),

        /**
         * Runbook worklog data
         */
        RUNBOOK_WORKLOG("runbook.worklog"),

        /**
         * Policy result data
         */
        POLICY_RESULT("policy.result"),

        /**
         * Correlation worklog data
         */
        CORRELATION_WORKLOG("correlation.worklog"),

        /**
         * Application performance monitoring data
         */
        APM("apm"),

        /**
         * Static configuration metric data (cannot be renamed due to migration issues)
         */
        STATIC_METRIC("config"),

        /**
         * Health metric data
         */
        HEALTH_METRIC("health.metric"),

        CONFIG_METRIC("config.metric"),//for ncm cache level data as in ncm explorer will be using this metric as discussed with @Viram

        COMPLIANCE("compliance"),

        COMPLIANCE_TRAIL("compliance.trail"),

        COMPLIANCE_STATS_ENTITY("compliance.stats.entity"),

        COMPLIANCE_STATS_POLICY("compliance.stats.policy"),

        NETROUTE_EVENT("netroute.event"),

        NETROUTE_METRIC("netroute.metric"),

        NETROUTE_AVAILABILITY("netroute.availability"),

        HOURLY_OBJECT_STATUS_FLAP("hourly.status.flap");

        private static final Map<String, VisualizationDataSource> VALUES = Collections.unmodifiableMap(Arrays.stream(values()).collect(Collectors.toMap(VisualizationDataSource::getName, e -> e)));

        private final String name;

        VisualizationDataSource(String name)
        {
            this.name = name;
        }

        public static VisualizationDataSource valueOfName(String name)
        {
            return VALUES.get(name);
        }

        public String getName()
        {
            return name;
        }
    }

    public enum Template
    {
        WINDOWS("Windows"),
        LINUX("Linux"),
        WINDOWS_AGENT("Windows Agent"),
        LINUX_AGENT("Linux Agent"),
        IBM_AIX("IBM AIX"),
        IBM_AIX_AGENT("IBM AIX Agent"),
        HP_UX("HP-UX"),
        SOLARIS("Solaris"),
        WINDOWS_CLUSTER("Windows Cluster"),
        ACTIVE_DIRECTORY("Active Directory"),
        APACHE_HTTP("Apache HTTP"),
        BIND9("Bind9"),
        MICROSOFT_IIS("Microsoft IIS"),
        NGINX("Nginx"),
        DOTNET("Dotnet"),
        IBM_DB2("IBM Db2"),
        ORACLE_DATABASE("Oracle Database"),
        SQL_SERVER("SQL Server"),
        MYSQL("MySQL"),
        MARIADB("MariaDB"),
        POSTGRESQL("PostgreSQL"),
        WINDOWS_DNS("Windows DNS"),
        WINDOWS_DHCP("Windows DHCP"),
        EXCHANGE_MAILBOX("Exchange Mailbox"),
        EXCHANGE_CLIENT_ACCESS_ROLE("Exchange Client Access Role"),
        EXCHANGE_EDGE_TRANSPORT_ROLE("Exchange Edge Transport Role"),
        EXCHANGE_MAILBOX_ROLE("Exchange Mailbox Role"),
        HA_PROXY("HAProxy"),
        IBM_MQ("IBM MQ"),
        MSMQ("MSMQ"),
        RABBITMQ("RabbitMQ"),
        APACHE_MQ("Apache MQ"),
        ORACLE_WEBLOGIC("Oracle WebLogic"),
        LIGHTTPD("Light Httpd"),
        LINUX_DHCP("Linux DHCP"),
        APACHE_TOMCAT("Apache Tomcat"),
        IBM_WEBSPHERE("IBM WebSphere"),
        WILDFLY("WildFly"),
        WINDOWS_RDP("Windows RDP"),
        SYBASE("Sybase"),
        SAP_HANA("SAP HANA"),
        SAP_MAXDB("SAP MaxDB"),
        CERTIFICATE("SSL Certificate"),
        CISCO_UCS("Cisco UCS"),
        CISCO_WIRELESS("Cisco Wireless"),
        DNS("DNS"),
        DOMAIN("Domain"),
        EMAIL("Email"),
        ZIMBRA("Zimbra"),
        VMWARE_ESXI("VMware ESXi"),
        VCENTER("vCenter"),
        HYPER_V("Hyper-V"),
        HYPER_V_CLUSTER("Hyper-V Cluster"),
        CITRIX_XEN("Citrix Xen"),
        CITRIX_XEN_CLUSTER("Citrix Xen Cluster"),
        VMWARE_ESXI_VM("VMware ESXi Vm"),
        HYPER_V_VM("Hyper-V Vm"),
        CITRIX_XEN_VM("Citrix Xen Vm"),
        NTP("NTP"),
        FTP("FTP"),
        OFFICE_365("Office 365"),
        ONEDRIVE("OneDrive"),
        PING("Ping"),
        PORT("Port"),
        RADIUS("RADIUS"),
        URL("URL"),
        RUCKUS_WIRELESS("Ruckus Wireless"),
        ARUBA_WIRELESS("Aruba Wireless"),
        EXCHANGE_ONLINE("Exchange Online"),
        SHAREPOINT_ONLINE("SharePoint Online"),
        MICROSOFT_TEAMS("Microsoft Teams"),
        MICROSOFT_DYNAMICS_CRM("Microsoft Dynamics CRM"),
        AWS_CLOUD("AWS Cloud"),
        AMAZON_DYNAMO_DB("Amazon DynamoDB"),
        AMAZON_EBS("Amazon EBS"),
        AMAZON_EC2("Amazon EC2"),
        AWS_ELB("AWS ELB"),
        AWS_ELB_NETWORK("AWS ELB Network"),
        AWS_ELB_APPLICATION("AWS ELB Application"),
        AWS_ELB_CLASSIC("AWS ELB Classic"),
        AMAZON_RDS("Amazon RDS"),
        AMAZON_S3("Amazon S3"),
        AMAZON_SNS("Amazon SNS"),
        AMAZON_CLOUD_FRONT("Amazon CloudFront"),
        AWS_AUTO_SCALING("AWS Auto Scaling"),
        AWS_LAMBDA("AWS Lambda"),
        AMAZON_SQS("Amazon SQS"),
        AWS_ELASTIC_BEANSTALK("AWS Elastic Beanstalk"),
        AMAZON_DOCUMENTDB("Amazon DocumentDB"),
        AZURE_CLOUD("Azure Cloud"),
        AZURE_COSMOS_DB("Azure Cosmos DB"),
        AZURE_SQL_DATABASE("Azure SQL Database"),
        AZURE_STORAGE("Azure Storage"),
        AZURE_VM("Azure VM"),
        AZURE_WEB_APP("Azure WebApp"),
        AZURE_SERVICE_BUS("Azure Service Bus"),
        AZURE_APPLICATION_GATEWAY("Azure Application Gateway"),
        AZURE_FUNCTION("Azure Function"),
        AZURE_LOAD_BALANCER("Azure Load Balancer"),
        AZURE_VM_SCALE_SET("Azure VM Scale Set"),
        AZURE_CDN("Azure CDN"),
        AZURE_MYSQL("Azure MySQL Server"),
        SYMANTEC_MESSAGING_GATEWAY("Symantec Corporation Email Gateway"),

        IBM_TAPE_LIBRARY("IBM Tape Library"),
        IRONPORT_MESSAGING_GATEWAY("IronPort Systems Email Gateway"),
        AZURE_POSTGRESQL("Azure PostgreSQL Server"),
        CISCO_SYSTEMS_SWITCH("Cisco Systems Switch"),
        JUNIPER_NETWORKS_SWITCH("Juniper Networks Switch"),
        CISCO_SYSTEMS_ROUTER("Cisco Systems Router"),

        CISCO_SYSTEMS_ROUTER_L3("Cisco Systems Router L3"),
        CISCO_SYSTEMS_FIREWALL("Cisco Systems Firewall"),
        CISCO_SYSTEMS_SWITCH_STACK("Cisco Systems Switch Stack"),

        CISCO_SYSTEMS_SWITCH_STACK_L3("Cisco Systems Switch Stack L3"),
        CISCO_WIRELESS_ACCESS_POINT("Cisco Wireless Access Point"),
        ARUBA_WIRELESS_ACCESS_POINT("Aruba Wireless Access Point"),
        RUCKUS_WIRELESS_ACCESS_POINT("Ruckus Wireless Access Point"),
        PALO_ALTO_NETWORKS_FIREWALL("Palo Alto Networks Firewall"),
        CHECK_POINT_FIREWALL("Check Point Firewall"),
        CHECK_POINT_FIREWALL_L3("Check Point Firewall L3"),
        FORTINET_FIREWALL("Fortinet Firewall"),
        HEWLETT_PACKARD_ENTERPRISE_SWITCH("Hewlett Packard Enterprise Switch"),

        HEWLETT_PACKARD_ENTERPRISE_SWITCH_L3("Hewlett Packard Enterprise Switch L3"),

        JUNIPER_NETWORKS_ROUTER("Juniper Networks Router"),
        GENERIC_SNMP_DEVICE("Generic Snmp Device"),
        ARISTA_NETWORKS_SWITCH("Arista Networks Switch"),

        ARISTA_NETWORKS_SWITCH_L3("Arista Networks Switch L3"),
        ARUBA_NETWORKS_SWITCH("Aruba Networks Switch"),

        ARUBA_NETWORKS_SWITCH_L3("Aruba Networks Switch L3"),
        H3C_SWITCH("H3C Switch"),

        H3C_SWITCH_L3("H3C Switch L3"),
        HUAWEI_SWITCH("Huawei Switch"),

        HUAWEI_SWITCH_L3("Huawei Switch L3"),
        INTEL_CORPORATION_SWITCH("Intel Corporation Switch"),

        INTEL_CORPORATION_SWITCH_L3("Intel Corporation Switch L3"),
        NETGEAR_SWITCH("Netgear Switch"),

        NETGEAR_SWITCH_L3("Netgear Switch L3"),
        ALTEON_NETWORKS("Alteon Networks Load Balancer"),
        RADWARE("Radware Load Balancer"),
        SONICWALL("SonicWall Firewall"),
        INOCINC("INOC Inc. Firewall"),

        CISCO_SYSTEMS_SWITCH_L3("Cisco Systems Switch L3"),

        JUNIPER_NETWORKS_SWITCH_L3("Juniper Networks Switch L3"),

        JUNIPER_NETWORKS_ROUTER_L3("Juniper Networks Router L3"),

        EXTREME_NETWORKS_SWITCH("Extreme Networks Switch"),

        EXTREME_NETWORKS_SWITCH_L3("Extreme Networks Switch L3"),

        EXTREME_NETWORKS_ROUTER("Extreme Networks Router"),

        EXTREME_NETWORKS_ROUTER_L3("Extreme Networks Router L3"),

        NETSCALER("Netscaler Load Balancer"),

        F5_LOAD_BALANCER("F5 Networks Load Balancer"),

        DLINK_SWITCH("D-Link Switch"),

        DLINK_SWITCH_L3("D-Link Switch L3"),

        DELL_HARDWARE_SENSOR("Dell Hardware Sensor"),

        HEWLETT_PACKARD_ENTERPRISE_HARDWARE_SENSOR("Hewlett Packard Enterprise Hardware Sensor"),

        PRISM("Prism"),

        NUTANIX("Nutanix"),

        NUTANIX_VM("Nutanix VM"),

        CISCO_VMANAGE("Cisco vManage"),

        CISCO_VSMART("Cisco vSmart"),

        CISCO_VEDGE("Cisco vEdge"),

        CISCO_VBOND("Cisco vBond"),

        CISCO_MERAKI("Cisco Meraki"),

        CISCO_MERAKI_SECURITY("Cisco Meraki Security"),

        CISCO_MERAKI_SWITCH("Cisco Meraki Switch"),

        CISCO_MERAKI_RADIO("Cisco Meraki Radio"),
        CISCO_SYSTEMS_FIREWALL_CISCO_ISE("Cisco Systems Firewall Cisco ISE"),
        CISCO_SYSTEMS_FIREWALL_CISCO_FIREPOWER("Cisco Systems Firewall Firepower"),
        EXTREME_NETWORKS_SWITCH_STACK("Extreme Networks Switch Stack"),
        LOG_ANALYTICS("Log Analytics"),
        TRAP_ANALYTICS("Trap Analytics"),
        FLOW_ANALYTICS("Flow Analytics"),
        NETAPP_ONTAP_CLUSTER("NetApp ONTAP Cluster"),
        HPE_STORE_ONCE("HPE StoreOnce"),
        CISCO_ACI("Cisco ACI"),
        DOCKER("Docker"),
        HPE_PRIMERA("HPE Primera"),
        HPE_3PAR("HPE 3PAR"),
        DELL_EMC_UNITY("Dell EMC Unity"),
        WINDOWS_SNMP("Windows Windows (SNMP)"),
        LINUX_SNMP("Linux Linux (SNMP)"),
        EMAIL_GATEWAY("Barracuda Networks Email Gateway"),
        MOXA_SWITCH("Moxa Switch"),
        TANZU_KUBERNETES("Tanzu Kubernetes"),
        HEWLETT_PACKARD_ENTERPRISE_HARDWARE_SENSOR_ONBOARD_ADMINISTRATOR("Hewlett Packard Enterprise Hardware Sensor Onboard Administrator"),
        IBM_AS_400("IBM AS/400"),
        NSXT("NSXT");


        public static final Map<String, Long> templates = Map.<String, Long>ofEntries(

                entry(Template.ACTIVE_DIRECTORY.getName(), 10000000000001L),
                entry(Template.APACHE_TOMCAT.getName(), 10000000000002L),
                entry(Template.AWS_AUTO_SCALING.getName(), 10000000000003L),
                entry(Template.AMAZON_CLOUD_FRONT.getName(), 10000000000004L),
                entry(Template.AMAZON_DOCUMENTDB.getName(), 10000000000005L),
                entry(Template.AMAZON_DYNAMO_DB.getName(), 10000000000006L),
                entry(Template.AMAZON_EBS.getName(), 10000000000007L),
                entry(Template.AMAZON_EC2.getName(), 10000000000008L),
                entry(Template.AWS_ELASTIC_BEANSTALK.getName(), 10000000000009L),
                entry(Template.AWS_LAMBDA.getName(), 10000000000010L),
                entry(Template.AMAZON_RDS.getName(), 10000000000011L),
                entry(Template.AMAZON_S3.getName(), 10000000000012L),
                entry(Template.AMAZON_SNS.getName(), 10000000000013L),
                entry(Template.AMAZON_SQS.getName(), 10000000000014L),
                entry(Template.AZURE_APPLICATION_GATEWAY.getName(), 10000000000015L),
                entry(Template.AZURE_CDN.getName(), 10000000000016L),
                entry(Template.AZURE_COSMOS_DB.getName(), 10000000000017L),
                entry(Template.AZURE_FUNCTION.getName(), 10000000000018L),
                entry(Template.AZURE_LOAD_BALANCER.getName(), 10000000000019L),
                entry(Template.AZURE_SQL_DATABASE.getName(), 10000000000020L),
                entry(Template.AZURE_VM_SCALE_SET.getName(), 10000000000021L),
                entry(Template.AZURE_SERVICE_BUS.getName(), 10000000000022L),
                entry(Template.AZURE_VM.getName(), 10000000000023L),
                entry(Template.AZURE_WEB_APP.getName(), 10000000000024L),
                entry(Template.BIND9.getName(), 10000000000025L),
                entry(Template.HA_PROXY.getName(), 10000000000026L),
                entry(Template.IBM_DB2.getName(), 10000000000029L),
                entry(Template.IBM_MQ.getName(), 10000000000034L),
                entry(Template.IBM_WEBSPHERE.getName(), 10000000000035L),
                entry(Template.LIGHTTPD.getName(), 10000000000036L),
                entry(Template.LINUX_DHCP.getName(), 10000000000037L),
                entry(Template.MICROSOFT_IIS.getName(), 10000000000038L),
                entry(Template.MSMQ.getName(), 10000000000039L),
                entry(Template.SQL_SERVER.getName(), 10000000000040L),
                entry(Template.MYSQL.getName(), 10000000000047L),
                entry(Template.NGINX.getName(), 10000000000050L),
                entry(Template.OFFICE_365.getName(), 10000000000051L),
                entry(Template.EXCHANGE_ONLINE.getName(), 10000000000052L),
                entry(Template.ONEDRIVE.getName(), 10000000000053L),
                entry(Template.SHAREPOINT_ONLINE.getName(), 10000000000054L),
                entry(Template.MICROSOFT_TEAMS.getName(), 10000000000055L),
                entry(Template.ORACLE_DATABASE.getName(), 10000000000056L),
                entry(Template.POSTGRESQL.getName(), 10000000000065L),
                entry(Template.RABBITMQ.getName(), 10000000000069L),
                entry(Template.SAP_HANA.getName(), 10000000000070L),
                entry(Template.SYBASE.getName(), 10000000000074L),
                entry(Template.VCENTER.getName(), 10000000000075L),
                entry(Template.VMWARE_ESXI.getName(), 10000000000076L),
                entry(Template.HYPER_V.getName(), 10000000000077L),
                entry(Template.HYPER_V_CLUSTER.getName(), 10000000000078L),
                entry(Template.CITRIX_XEN.getName(), 10000000000079L),
                entry(Template.CITRIX_XEN_CLUSTER.getName(), 10000000000080L),
                entry(Template.CITRIX_XEN_VM.getName(), 10000000000081L),
                entry(Template.VMWARE_ESXI_VM.getName(), 10000000000082L),
                entry(Template.HYPER_V_VM.getName(), 10000000000083L),
                entry(Template.WILDFLY.getName(), 10000000000084L),
                entry(Template.WINDOWS_CLUSTER.getName(), 10000000000085L),
                entry(Template.WINDOWS_DHCP.getName(), 10000000000086L),
                entry(Template.WINDOWS_DNS.getName(), 10000000000087L),
                entry(Template.WINDOWS_RDP.getName(), 10000000000088L),
                entry(Template.CISCO_WIRELESS.getName(), 10000000000089L),
                entry(Template.RUCKUS_WIRELESS.getName(), 10000000000092L),
                entry(Template.ARUBA_WIRELESS.getName(), 10000000000095L),
                entry(Template.APACHE_HTTP.getName(), 10000000000098L),
                entry(Template.WINDOWS.getName(), 10000000000099L),
                entry(Template.LINUX.getName(), 10000000000100L),
                entry(Template.HP_UX.getName(), 10000000000101L),
                entry(Template.IBM_AIX.getName(), 10000000000102L),
                entry(Template.SOLARIS.getName(), 10000000000103L),
                entry(Template.URL.getName(), 10000000000104L),
                entry(Template.DOMAIN.getName(), 10000000000105L),
                entry(Template.PORT.getName(), 10000000000106L),
                entry(Template.PING.getName(), 10000000000107L),
                entry(Template.FTP.getName(), 10000000000108L),
                entry(Template.DNS.getName(), 10000000000109L),
                entry(Template.RADIUS.getName(), 10000000000110L),
                entry(Template.NTP.getName(), 10000000000111L),
                entry(Template.EMAIL.getName(), 10000000000112L),
                entry(Template.CERTIFICATE.getName(), 10000000000113L),
                entry(Template.WINDOWS_AGENT.getName(), 10000000000114L),
                entry(Template.LINUX_AGENT.getName(), 10000000000115L),
                entry(Template.CISCO_SYSTEMS_SWITCH.getName(), 10000000000116L),
                entry(Template.JUNIPER_NETWORKS_SWITCH.getName(), 10000000000117L),
                entry(Template.CISCO_SYSTEMS_ROUTER.getName(), 10000000000120L),
                entry(Template.JUNIPER_NETWORKS_ROUTER.getName(), 10000000000221L),
                entry(Template.CISCO_SYSTEMS_FIREWALL.getName(), 10000000000121L),
                entry(Template.PALO_ALTO_NETWORKS_FIREWALL.getName(), 10000000000122L),
                entry(Template.CHECK_POINT_FIREWALL.getName(), 10000000000123L),
                entry(Template.CHECK_POINT_FIREWALL_L3.getName(), 10000000000123L),
                entry(Template.FORTINET_FIREWALL.getName(), 10000000000124L),
                entry(Template.GENERIC_SNMP_DEVICE.getName(), 10000000000155L),
                entry(Template.AWS_ELB_NETWORK.getName(), 10000000000156L),
                entry(Template.AWS_ELB_APPLICATION.getName(), 10000000000157L),
                entry(Template.AWS_ELB_CLASSIC.getName(), 10000000000158L),
                entry(Template.AZURE_STORAGE.getName(), 10000000000159L),
                entry(Template.SYMANTEC_MESSAGING_GATEWAY.getName(), 10000000000164L),
                entry(Template.EXCHANGE_EDGE_TRANSPORT_ROLE.getName(), 10000000000166L),
                entry(Template.EXCHANGE_MAILBOX.getName(), 10000000000167L),
                entry(Template.EXCHANGE_CLIENT_ACCESS_ROLE.getName(), 10000000000202L),
                entry(Template.EXCHANGE_MAILBOX_ROLE.getName(), 10000000000167L),
                entry(Template.SAP_MAXDB.getName(), 10000000000169L),
                entry(Template.MARIADB.getName(), 10000000000170L),
                entry(Template.APACHE_MQ.getName(), 10000000000174L),
                entry(Template.DOTNET.getName(), 10000000000175L),
                entry(Template.ZIMBRA.getName(), 10000000000176L),
                entry(Template.CISCO_UCS.getName(), 10000000000177L),
                entry(Template.ARISTA_NETWORKS_SWITCH.getName(), 10000000000179L),
                entry(Template.ARUBA_NETWORKS_SWITCH.getName(), 10000000000181L),
                entry(Template.H3C_SWITCH.getName(), 10000000000183L),
                entry(Template.HEWLETT_PACKARD_ENTERPRISE_SWITCH.getName(), 10000000000185L),
                entry(Template.HUAWEI_SWITCH.getName(), 10000000000188L),
                entry(Template.INTEL_CORPORATION_SWITCH.getName(), 10000000000190L),
                entry(Template.NETGEAR_SWITCH.getName(), 10000000000192L),
                entry(Template.AZURE_POSTGRESQL.getName(), 10000000000194L),
                entry(Template.AZURE_MYSQL.getName(), 10000000000195L),
                entry(Template.CISCO_SYSTEMS_SWITCH_STACK.getName(), 10000000000196L),
                entry(Template.CISCO_WIRELESS_ACCESS_POINT.getName(), 10000000000205L),
                entry(Template.ARUBA_WIRELESS_ACCESS_POINT.getName(), 10000000000206L),
                entry(Template.RUCKUS_WIRELESS_ACCESS_POINT.getName(), 10000000000207L),
                entry(Template.ALTEON_NETWORKS.getName(), 10000000000203L),
                entry(Template.RADWARE.getName(), 10000000000203L),
                entry(Template.SONICWALL.getName(), 10000000000208L),
                entry(Template.INOCINC.getName(), 10000000000208L),
                entry(Template.CISCO_SYSTEMS_SWITCH_L3.getName(), 10000000000119L),
                entry(Template.JUNIPER_NETWORKS_SWITCH_L3.getName(), 10000000000118L),
                entry(Template.EXTREME_NETWORKS_SWITCH.getName(), 10000000000211L),
                entry(Template.EXTREME_NETWORKS_SWITCH_L3.getName(), 10000000000214L),
                entry(Template.EXTREME_NETWORKS_ROUTER.getName(), 10000000000218L),
                entry(Template.EXTREME_NETWORKS_ROUTER_L3.getName(), 10000000000233L),
                entry(Template.JUNIPER_NETWORKS_ROUTER_L3.getName(), 10000000000230L),
                entry(Template.CISCO_SYSTEMS_ROUTER_L3.getName(), 10000000000236L),
                entry(Template.CISCO_SYSTEMS_SWITCH_STACK_L3.getName(), 10000000000224L),
                entry(Template.ARISTA_NETWORKS_SWITCH_L3.getName(), 10000000000239L),
                entry(Template.ARUBA_NETWORKS_SWITCH_L3.getName(), 10000000000245L),
                entry(Template.H3C_SWITCH_L3.getName(), 10000000000242L),
                entry(Template.HEWLETT_PACKARD_ENTERPRISE_SWITCH_L3.getName(), 10000000000248L),
                entry(Template.HUAWEI_SWITCH_L3.getName(), 10000000000252L),
                entry(Template.INTEL_CORPORATION_SWITCH_L3.getName(), 10000000000255L),
                entry(Template.NETGEAR_SWITCH_L3.getName(), 10000000000258L),
                entry(Template.NETSCALER.getName(), 10000000000261L),
                entry(Template.F5_LOAD_BALANCER.getName(), 10000000000262L),
                entry(Template.DLINK_SWITCH.getName(), 10000000000263L),
                entry(Template.DLINK_SWITCH_L3.getName(), 10000000000265L),
                entry(Template.DELL_HARDWARE_SENSOR.getName(), 10000000000290L),
                entry(Template.HEWLETT_PACKARD_ENTERPRISE_HARDWARE_SENSOR.getName(), 10000000000289L),
                entry(Template.IBM_TAPE_LIBRARY.getName(), 10000000000286L),
                entry(Template.IRONPORT_MESSAGING_GATEWAY.getName(), 10000000000287L),
                entry(Template.PRISM.getName(), 10000000000295L),
                entry(Template.NUTANIX.getName(), 10000000000298L),
                entry(Template.NUTANIX_VM.getName(), 10000000000301L),
                entry(Template.CISCO_VMANAGE.getName(), 10000000000302L),
                entry(Template.CISCO_VSMART.getName(), 10000000000305L),
                entry(Template.CISCO_VBOND.getName(), 10000000000306L),
                entry(Template.CISCO_VEDGE.getName(), 10000000000307L),
                entry(Template.IBM_AIX_AGENT.getName(), 10000000000311L),
                entry(Template.CISCO_MERAKI.getName(), 10000000000320L),
                entry(Template.CISCO_MERAKI_SECURITY.getName(), 10000000000315L),
                entry(Template.CISCO_MERAKI_SWITCH.getName(), 10000000000316L),
                entry(Template.CISCO_MERAKI_RADIO.getName(), 10000000000319L),
                entry(Template.CISCO_SYSTEMS_FIREWALL_CISCO_ISE.getName(), 10000000000322L),
                entry(Template.CISCO_SYSTEMS_FIREWALL_CISCO_FIREPOWER.getName(), 10000000000326L),
                entry(Template.EXTREME_NETWORKS_SWITCH_STACK.getName(), 10000000000329L),
                entry(Template.LOG_ANALYTICS.getName(), 10000000000334L),
                entry(Template.FLOW_ANALYTICS.getName(), 10000000000335L),
                entry(Template.TRAP_ANALYTICS.getName(), 10000000000336L),
                entry(Template.NETAPP_ONTAP_CLUSTER.getName(), 10000000000337L),
                entry(Template.HPE_STORE_ONCE.getName(), 10000000000342L),
                entry(Template.CISCO_ACI.getName(), 10000000000345L),
                entry(Template.DOCKER.getName(), 10000000000360L),
                entry(Template.HPE_PRIMERA.getName(), 10000000000361L),
                entry(Template.HPE_3PAR.getName(), 10000000000369L),
                entry(Template.DELL_EMC_UNITY.getName(), 10000000000380L),
                entry(Template.WINDOWS_SNMP.getName(), 10000000000387L),
                entry(Template.LINUX_SNMP.getName(), 10000000000395L),
                entry(Template.MOXA_SWITCH.getName(), 10000000000367L),
                entry(Template.HEWLETT_PACKARD_ENTERPRISE_HARDWARE_SENSOR_ONBOARD_ADMINISTRATOR.getName(), 10000000000375L),
                entry(Template.EMAIL_GATEWAY.getName(), 10000000000400L),
                entry(Template.TANZU_KUBERNETES.getName(), 10000000000404L),
                entry(Template.IBM_AS_400.getName(), 10000000000390L),
                entry(Template.NSXT.getName(), 10000000000408L));


        //Last template id 10000000000411

        private static final Map<String, Template> VALUES = Collections.unmodifiableMap(Arrays.stream(values()).collect(Collectors.toMap(Template::getName, e -> e)));
        private final String name;

        Template(String name)
        {
            this.name = name;
        }

        public static Template valueOfName(String name)
        {
            return VALUES.get(name);
        }

        public static long getTemplate(String name)
        {
            return templates.getOrDefault(name, DEFAULT_NETWORK_TEMPLATE);
        }

        public String getName()
        {
            return name;
        }
    }


    /**
     * Defines ordinal values for visualization categories at the database level.
     * <p>
     * This enum is used for database-level constants representing different types of visualizations.
     * Each constant represents a specific type of visualization that can be stored and retrieved
     * from the database.
     */
    public enum VisualizationCategoryOrdinal
    {
        /**
         * Gauge visualization type
         */
        VISUALIZATION_GAUGE,

        /**
         * Top-N chart visualization type
         */
        VISUALIZATION_TOPN_CHART,

        /**
         * Top-N grid visualization type
         */
        VISUALIZATION_TOPN_GRID,

        /**
         * Histogram visualization type
         */
        VISUALIZATION_HISTOGRAM,

        /**
         * Grid visualization type
         */
        VISUALIZATION_GRID,

        /**
         * Chart visualization type
         */
        VISUALIZATION_CHART,

        /**
         * Metric history visualization type
         */
        VISUALIZATION_METRIC_HISTORY,

        /**
         * Status flap history visualization type
         */
        VISUALIZATION_STATUS_FLAP_HISTORY,

        /**
         * Trap history visualization type
         */
        VISUALIZATION_TRAP_HISTORY,

        /**
         * Policy flap history visualization type
         */
        VISUALIZATION_POLICY_FLAP_HISTORY,

        /**
         * Audit history visualization type
         */
        VISUALIZATION_AUDIT_HISTORY,

        /**
         * User notification history visualization type
         */
        VISUALIZATION_USER_NOTIFICATION_HISTORY,

        /**
         * Event history visualization type
         */
        VISUALIZATION_EVENT_HISTORY
    }

    /**
     * Defines categories of visualizations available in the system.
     * <p>
     * This enum specifies the different types of visualizations that can be created and displayed.
     * Each category represents a specific visualization format with its own rendering and data
     * processing requirements.
     */
    public enum VisualizationCategory
    {
        /**
         * Histogram chart visualization
         */
        HISTOGRAM("Chart"),

        /**
         * Grid/table visualization
         */
        GRID("Grid"),

        /**
         * Top-N ranking visualization
         */
        TOP_N("TopN"),

        /**
         * Gauge/meter visualization
         */
        GAUGE("Gauge"),

        /**
         * Sankey diagram visualization
         */
        SANKEY("Sankey"),

        /**
         * Heat map visualization
         */
        HEAT_MAP("HeatMap"),

        /**
         * Custom chart visualization
         */
        CHART("Custom"),

        /**
         * Data stream visualization
         */
        STREAM("Stream"),

        /**
         * Active alerts visualization
         */
        ACTIVE_ALERT("Active Alerts"),

        /**
         * Geographic map visualization
         */
        MAP("Map"),

        /**
         * Forecast/prediction visualization
         */
        FORECAST("Forecast"),

        /**
         * Baseline comparison visualization
         */
        BASELINE("Baseline"),

        /**
         * Anomaly detection visualization
         */
        ANOMALY("Anomaly");

        private static final Map<String, VisualizationCategory> VALUES = Collections.unmodifiableMap(Arrays.stream(values()).collect(Collectors.toMap(VisualizationCategory::getName, e -> e)));
        private final String name;

        VisualizationCategory(String name)
        {
            this.name = name;
        }

        public static VisualizationCategory valueOfName(String name)
        {
            return VALUES.get(name);
        }

        public String getName()
        {
            return name;
        }
    }

    /**
     * Defines types of visualization results that can be returned.
     * <p>
     * This enum specifies the different formats in which visualization results can be returned:
     * <ul>
     *   <li>COMPACTED - Results are compressed to minimize size</li>
     *   <li>NORMALIZED - Results are standardized for consistent processing</li>
     *   <li>EXTENDED - Results include additional details and metadata</li>
     * </ul>
     */
    public enum VisualizationResultType
    {
        /**
         * Compacted result format with minimal size
         */
        COMPACTED(0),

        /**
         * Normalized result format with standardized structure
         */
        NORMALIZED(1),

        /**
         * Extended result format with additional details
         */
        EXTENDED(2);

        private static final Map<Integer, VisualizationResultType> VALUES = Collections.unmodifiableMap(Arrays.stream(values()).collect(Collectors.toMap(VisualizationResultType::getName, e -> e)));

        private final int name;

        VisualizationResultType(int name)
        {
            this.name = name;
        }

        public static VisualizationResultType valueOfName(int name)
        {
            return VALUES.get(name);
        }

        public int getName()
        {
            return name;
        }
    }

    /**
     * Defines time ranges for visualizations.
     * <p>
     * This enum specifies the different time ranges that can be used for visualizations.
     * Each time range has a name and a duration in minutes. A duration of -1 indicates
     * a special time range that requires custom calculation (like "today" or "this month").
     * <p>
     * The time ranges include fixed durations (like "last 5 minutes") and relative periods
     * (like "today" or "this month").
     */
    public enum VisualizationTimeline
    {
        /**
         * Last 5 minutes (5 minutes)
         */
        LAST_5_MINUTES("-5m", 5),

        /**
         * Last 15 minutes (15 minutes)
         */
        LAST_15_MINUTES("-15m", 15),

        /**
         * Last 30 minutes (30 minutes)
         */
        LAST_30_MINUTES("-30m", 30),

        /**
         * Last 1 hour (60 minutes)
         */
        LAST_1_HOUR("-1h", 60),

        /**
         * Last 6 hours (360 minutes)
         */
        LAST_6_HOURS("-6h", 360),

        /**
         * Last 12 hours (720 minutes)
         */
        LAST_12_HOURS("-12h", 720),

        /**
         * Last 24 hours (1440 minutes)
         */
        LAST_24_HOURS("-24h", 1440),

        /**
         * Last 48 hours (2880 minutes)
         */
        LAST_48_HOURS("-48h", 2880),

        /**
         * Today (from midnight to now)
         */
        TODAY("today", -1),

        /**
         * Yesterday (previous day)
         */
        YESTERDAY("yesterday", -1),

        /**
         * Last week (previous calendar week)
         */
        LAST_WEEK("last.week", -1),

        /**
         * Last month (previous calendar month)
         */
        LAST_MONTH("last.month", -1),

        /**
         * Last quarter (previous calendar quarter)
         */
        LAST_QUARTER("last.quarter", -1),

        /**
         * Last year (previous calendar year)
         */
        LAST_YEAR("last.year", -1),

        /**
         * This week (current calendar week)
         */
        THIS_WEEK("this.week", -1),

        /**
         * This month (current calendar month)
         */
        THIS_MONTH("this.month", -1),

        /**
         * This quarter (current calendar quarter)
         */
        THIS_QUARTER("this.quarter", -1),

        /**
         * This year (current calendar year)
         */
        THIS_YEAR("this.year", -1),

        /**
         * Last day (1440 minutes)
         */
        LAST_DAY("-1d", 1440),

        /**
         * Last 2 days (2880 minutes)
         */
        LAST_2_DAYS("-2d", 2880),

        /**
         * Last 7 days (10080 minutes)
         */
        LAST_7_DAYS("-7d", 10080),      //  7 * 24 * 60 = 10080

        /**
         * Last 14 days (20160 minutes)
         */
        LAST_14_DAYS("-14d", 20160),    // 14 * 24 * 60 = 20160

        /**
         * Last 15 days (21600 minutes)
         */
        LAST_15_DAYS("-15d", 21600),    // 15 * 24 * 60 = 21600

        /**
         * Last 30 days (43200 minutes)
         */
        LAST_30_DAYS("-30d", 43200),    // 30 * 24 * 60 = 43200

        /**
         * Last 60 days (86400 minutes)
         */
        LAST_60_DAYS("-60d", 86400),    // 60 * 24 * 60 = 86400

        /**
         * Last 90 days (129600 minutes)
         */
        LAST_90_DAYS("-90d", 129600),   // 90 * 24 * 60 = 129600

        /**
         * Custom time range (requires explicit start and end times)
         */
        CUSTOM("custom", -1);

        private static final Map<String, VisualizationTimeline> VALUES = Collections.unmodifiableMap(Arrays.stream(values()).collect(Collectors.toMap(VisualizationTimeline::getName, e -> e)));

        private final String name;

        private final int duration;

        VisualizationTimeline(String name, int duration)
        {
            this.name = name;

            this.duration = duration;
        }

        public static VisualizationTimeline valueOfName(String name)
        {
            return VALUES.get(name);
        }

        public Integer getDuration()
        {
            return duration;
        }

        public String getName()
        {
            return name;
        }
    }

    /**
     * Defines status types for availability calculations and visualizations.
     * <p>
     * This enum specifies the different status types that can be used to categorize
     * time periods in availability calculations and visualizations.
     */
    public enum StatusType
    {
        /**
         * Time period when the monitored entity was up and functioning normally
         */
        UPTIME("uptime"),

        /**
         * Time period when the monitored entity was down or not functioning
         */
        DOWNTIME("downtime"),

        /**
         * Time period when the monitored entity was suspended
         */
        SUSPENDTIME("suspendtime"),

        /**
         * Time period when the status of the monitored entity was unknown
         */
        UNKNOWNTIME("unknowntime"),

        /**
         * Time period when the monitored entity was in maintenance mode
         */
        MAINTENANCETIME("maintenancetime"),

        /**
         * Time period when the monitored entity was disabled
         */
        DISABLETIME("disabletime"),

        /**
         * Time period when the monitored entity was unreachable
         */
        UNREACHABLETIME("unreachabletime");

        private static final Map<String, StatusType> VALUES = Collections.unmodifiableMap(Arrays.stream(values()).collect(Collectors.toMap(StatusType::getName, e -> e)));

        private final String name;

        StatusType(String name)
        {
            this.name = name;
        }

        public static StatusType valueOfName(String name)
        {
            return VALUES.get(name);
        }

        public String getName()
        {
            return name;
        }
    }

    /**
     * Defines priority levels for visualization queries.
     * <p>
     * This enum specifies the different priority levels that can be assigned to visualization queries.
     * Lower numbers indicate higher priority (P0 is highest priority, P4 is lowest).
     * <p>
     * Query priorities are used to determine the order in which queries are processed when
     * multiple queries are pending.
     */
    public enum QueryPriority
    {
        /**
         * Highest priority (0)
         */
        P0(0),

        /**
         * High priority (1)
         */
        P1(1),

        /**
         * Medium priority (2)
         */
        P2(2),

        /**
         * Low priority (3)
         */
        P3(3),

        /**
         * Lowest priority (4)
         */
        P4(4);

        private final int priority;

        QueryPriority(int name)
        {
            this.priority = name;
        }

        public int getName()
        {
            return priority;
        }
    }
}
