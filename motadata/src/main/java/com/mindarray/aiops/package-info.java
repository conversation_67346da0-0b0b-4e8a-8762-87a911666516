/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

/**
 * The aiops (Artificial Intelligence for IT Operations) package provides functionality for intelligent
 * monitoring, dependency management, and correlation of IT infrastructure components.
 * <p>
 * This package implements various AI/ML-driven capabilities to enhance IT operations:
 * <ul>
 *   <li>Dependency Management: Tracks relationships between different IT components</li>
 *   <li>Availability Correlation: Correlates availability events to identify root causes</li>
 *   <li>Topology Mapping: Creates visual representations of infrastructure dependencies</li>
 * </ul>
 * <p>
 * Key components in this package:
 * <ul>
 *   <li>{@link com.mindarray.aiops.AIOpsConstants}: Constants used throughout the aiops package</li>
 *   <li>{@link com.mindarray.aiops.AvailabilityCorrelationEngine}: Engine for correlating availability events</li>
 *   <li>{@link com.mindarray.aiops.DependencyManager}: Manages dependencies between IT components</li>
 *   <li>{@link com.mindarray.aiops.DependencyQueryProcessor}: Processes dependency queries</li>
 * </ul>
 * <p>
 * The aiops package is designed to reduce alert noise, improve incident response times,
 * and provide better visibility into complex IT environments by understanding the
 * relationships between components and intelligently correlating events.
 *
 * @since 1.0
 */
package com.mindarray.aiops;