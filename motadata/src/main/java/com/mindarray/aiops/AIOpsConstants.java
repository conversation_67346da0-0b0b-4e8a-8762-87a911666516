/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

/*
	Change Logs:
	Date			Author			    Notes
*   19-May-2025     Sankalp             MOTADATA-5939: Cisco ACI Topology support
*/

package com.mindarray.aiops;

import com.mindarray.api.DependencyMapper;
import com.mindarray.nms.NMSConstants;
import io.vertx.core.json.JsonObject;

import java.util.Arrays;
import java.util.Collections;
import java.util.Map;
import java.util.stream.Collectors;

import static com.mindarray.GlobalConstants.EMPTY_VALUE;

/**
 * Constants and utility methods used throughout the AIOps package.
 * <p>
 * This class defines various constants, enums, and utility methods related to:
 * <ul>
 *   <li>Dependency management - levels, formats, types, and operations</li>
 *   <li>Correlation - types, probe types, and related constants</li>
 *   <li>Application levels and hierarchies</li>
 * </ul>
 * <p>
 * The constants and enums in this class help standardize the representation of
 * dependencies and correlations throughout the system, ensuring consistent
 * handling of relationships between IT components.
 */
public final class AIOpsConstants
{


    /**
     * Filter used to specify dependency constraints in queries.
     */
    public static final String DEPENDENCY_FILTER = "dependency.filter";

    /**
     * Constants related to dependency management.
     * <p>
     * These constants define the keys used in JSON objects for dependency operations,
     * including source and destination identifiers, relationship types, and hierarchy
     * information.
     */
    public static final String DEPENDENCY_LEVEL = "dependency.level";

    /**
     * Field that identifies the specific dependency instance or attribute.
     */
    public static final String DEPENDENCY_DEPENDENT_FIELD = "dependency.dependent.field";

    /**
     * Source object identifier in a dependency relationship.
     */
    public static final String DEPENDENCY_SOURCE = "dependency.source";

    /**
     * Port or interface identifier for the source object.
     */
    public static final String DEPENDENCY_SOURCE_PORT = "dependency.source.port";

    /**
     * Instance identifier for a specific dependency.
     */
    public static final String DEPENDENCY_INSTANCE = "dependency.instance";

    /**
     * Destination object identifier in a dependency relationship.
     */
    public static final String DEPENDENCY_DESTINATION = "dependency.destination";

    /**
     * Port or interface identifier for the destination object.
     */
    public static final String DEPENDENCY_DESTINATION_PORT = "dependency.destination.port";

    /**
     * Parent object identifier in a hierarchical dependency relationship.
     */
    public static final String DEPENDENCY_PARENT = "dependency.parent";

    /**
     * Children objects in a hierarchical dependency relationship.
     */
    public static final String DEPENDENCY_CHILDREN = "dependency.children";

    /**
     * Link identifier connecting source and destination in a dependency.
     */
    public static final String DEPENDENCY_CONNECTED_LINK = "dependency.connected.link";

    /**
     * Entity identifier used in dependency operations.
     */
    public static final String ENTITY_ID = "entity.id";

    /**
     * Format specification for dependency results (flat or hierarchical).
     */
    public static final String DEPENDENCY_FORMAT = "dependency.format";

    /**
     * Collection of instances related to a dependency.
     */
    public static final String INSTANCES = "instances";

    /**
     * Type of dependency (local domain or cross domain).
     */
    public static final String DEPENDENCY_TYPE = "dependency.type";

    /**
     * Operation to perform on dependencies (add, remove, etc.).
     */
    public static final String DEPENDENCY_OPERATION = "dependency.operation";

    /**
     * Maps application types to their corresponding dependency levels.
     * <p>
     * This map defines the hierarchical relationship between different types of applications:
     * <ul>
     *   <li>Level 0: Database applications (Oracle, SQL Server, MySQL, etc.)</li>
     *   <li>Level 1: Middleware applications (Tomcat, WebSphere, message queues, etc.)</li>
     *   <li>Level 2: Web server applications (IIS, Apache HTTP, Nginx, etc.)</li>
     *   <li>Level 3: Service applications (DNS, DHCP, Active Directory, etc.)</li>
     * </ul>
     * <p>
     * These levels are used for dependency correlation and impact analysis, where
     * higher-level applications typically depend on lower-level applications.
     */
    public static final Map<String, Byte> LEVELS_BY_APPLICATIONS = Map.ofEntries(Map.entry(NMSConstants.Type.ORACLE_DATABASE.getName(), AIOpsConstants.DependencyLevel.ZERO.getName()),
            Map.entry(NMSConstants.Type.SQL_SERVER.getName(), AIOpsConstants.DependencyLevel.ZERO.getName()),
            Map.entry(NMSConstants.Type.POSTGRESQL.getName(), AIOpsConstants.DependencyLevel.ZERO.getName()),
            Map.entry(NMSConstants.Type.MYSQL.getName(), AIOpsConstants.DependencyLevel.ZERO.getName()),
            Map.entry(NMSConstants.Type.MARIADB.getName(), AIOpsConstants.DependencyLevel.ZERO.getName()),
            Map.entry(NMSConstants.Type.IBM_DB2.getName(), AIOpsConstants.DependencyLevel.ZERO.getName()),
            Map.entry(NMSConstants.Type.SYBASE.getName(), AIOpsConstants.DependencyLevel.ZERO.getName()),
            Map.entry(NMSConstants.Type.SAP_HANA.getName(), AIOpsConstants.DependencyLevel.ZERO.getName()),
            Map.entry(NMSConstants.Type.SAP_MAXDB.getName(), AIOpsConstants.DependencyLevel.ZERO.getName()),

            Map.entry(NMSConstants.Type.APACHE_TOMCAT.getName(), AIOpsConstants.DependencyLevel.ONE.getName()),
            Map.entry(NMSConstants.Type.IBM_WEBSPHERE.getName(), AIOpsConstants.DependencyLevel.ONE.getName()),
            Map.entry(NMSConstants.Type.IBM_MQ.getName(), AIOpsConstants.DependencyLevel.ONE.getName()),
            Map.entry(NMSConstants.Type.MSMQ.getName(), AIOpsConstants.DependencyLevel.ONE.getName()),
            Map.entry(NMSConstants.Type.RABBITMQ.getName(), AIOpsConstants.DependencyLevel.ONE.getName()),
            Map.entry(NMSConstants.Type.WILDFLY.getName(), AIOpsConstants.DependencyLevel.ONE.getName()),

            Map.entry(NMSConstants.Type.MICROSOFT_IIS.getName(), AIOpsConstants.DependencyLevel.TWO.getName()),
            Map.entry(NMSConstants.Type.APACHE_HTTP.getName(), AIOpsConstants.DependencyLevel.TWO.getName()),
            Map.entry(NMSConstants.Type.LIGHTTPD.getName(), AIOpsConstants.DependencyLevel.TWO.getName()),
            Map.entry(NMSConstants.Type.HA_PROXY.getName(), AIOpsConstants.DependencyLevel.TWO.getName()),
            Map.entry(NMSConstants.Type.NGINX.getName(), AIOpsConstants.DependencyLevel.TWO.getName()),

            Map.entry(NMSConstants.Type.ZIMBRA.getName(), AIOpsConstants.DependencyLevel.THREE.getName()),
            Map.entry(NMSConstants.Type.WINDOWS_RDP.getName(), AIOpsConstants.DependencyLevel.THREE.getName()),
            Map.entry(NMSConstants.Type.ACTIVE_DIRECTORY.getName(), AIOpsConstants.DependencyLevel.THREE.getName()),
            Map.entry(NMSConstants.Type.BIND9.getName(), AIOpsConstants.DependencyLevel.THREE.getName()),
            Map.entry(NMSConstants.Type.WINDOWS_DNS.getName(), AIOpsConstants.DependencyLevel.THREE.getName()),
            Map.entry(NMSConstants.Type.LINUX_DHCP.getName(), AIOpsConstants.DependencyLevel.THREE.getName()),
            Map.entry(NMSConstants.Type.WINDOWS_DHCP.getName(), AIOpsConstants.DependencyLevel.THREE.getName()),
            Map.entry(NMSConstants.Type.EXCHANGE_CLIENT_ACCESS_ROLE.getName(), AIOpsConstants.DependencyLevel.THREE.getName()),
            Map.entry(NMSConstants.Type.EXCHANGE_EDGE_TRANSPORT_ROLE.getName(), AIOpsConstants.DependencyLevel.THREE.getName()),
            Map.entry(NMSConstants.Type.EXCHANGE_MAILBOX.getName(), AIOpsConstants.DependencyLevel.THREE.getName()),
            Map.entry(NMSConstants.Type.EXCHANGE_MAILBOX_ROLE.getName(), AIOpsConstants.DependencyLevel.THREE.getName()));

    /**
     * Flag indicating whether to process dependencies recursively.
     * <p>
     * When set to "yes", the system will traverse the entire dependency tree.
     * When set to "no", only immediate dependencies are processed.
     */
    public static final String RECURSIVE_DEPENDENCIES = "recursive.dependencies";

    /**
     * Constants related to correlation functionality.
     * <p>
     * These constants define the keys used in JSON objects for correlation operations,
     * including correlation types, affected objects, and correlation context.
     */

    /**
     * Type of correlation being performed (availability, rule, etc.).
     */
    public static final String CORRELATION_TYPE = "correlation.type";

    /**
     * List of objects that have come back online after being down.
     */
    public static final String CORRELATED_UP_OBJECTS = "correlated.up.objects";

    /**
     * Specific instance being correlated (e.g., interface, process).
     */
    public static final String CORRELATION_INSTANCE = "correlation.instance";

    /**
     * List of objects that are down due to a failure.
     */
    public static final String CORRELATED_DOWN_OBJECTS = "correlated.down.objects";

    /**
     * List of objects that are unreachable due to network issues.
     */
    public static final String CORRELATED_UNREACHABLE_OBJECTS = "correlated.unreachable.objects";

    /**
     * Collection of objects involved in a correlation.
     */
    public static final String CORRELATION_OBJECTS = "correlation.objects";

    /**
     * Mapping of correlation relationships between objects.
     */
    public static final String CORRELATION_MAP = "correlation.map";

    /**
     * Logs of correlation activities for debugging and auditing.
     */
    public static final String CORRELATION_LOGS = "correlation.logs";


    /**
     * Additional correlation-related constants.
     */

    /**
     * Root object that triggered the correlation.
     */
    public static final String CORRELATION_ROOT_OBJECT = "correlation.root.object";

    /**
     * List of applications affected by a correlation.
     */
    public static final String CORRELATED_APPLICATIONS = "correlated.applications";

    /**
     * List of probes currently running as part of correlation.
     */
    public static final String CORRELATION_RUNNING_PROBES = "correlation.running.probes";

    /**
     * Query parameters for retrieving correlated objects.
     */
    public static final String CORRELATED_OBJECT_QUERY = "correlated.object.query";

    /**
     * Type of availability probe (object up/down, instance up/down).
     */
    public static final String AVAILABILITY_PROBE_TYPE = "availability.probe.type";

    /**
     * Probe used for correlation.
     */
    public static final String CORRELATION_PROBE = "correlation.probe";

    /**
     * Maximum number of correlated object names to include in notifications.
     */
    public static final int MAX_CORRELATED_OBJECT_NAMES = 10;

    /**
     * Message indicating that a pending instance down request was found.
     */
    public static final String PENDING_REQUEST_FOUND = "pending instance down request found, hence rerouted to Metric Policy Inspector ....";

    /**
     * Private constructor to prevent instantiation of this utility class.
     */
    private AIOpsConstants()
    {
        // Utility class should not be instantiated
    }

    /**
     * Checks if the given dependency level is an application level.
     * <p>
     * Application levels are defined as levels 0-3, representing:
     * <ul>
     *   <li>Level 0: Database level applications</li>
     *   <li>Level 1: Middleware level applications (Queue and Applications)</li>
     *   <li>Level 2: Web and Proxy server applications</li>
     *   <li>Level 3: Service applications (DNS, DHCP, etc.)</li>
     * </ul>
     *
     * @param level The dependency level to check
     * @return true if the level is an application level (0-3), false otherwise
     */
    public static boolean hasApplicationLevel(byte level)
    {
        return level == DependencyLevel.ZERO.getName() || level == DependencyLevel.ONE.getName() || level == DependencyLevel.TWO.getName() || level == DependencyLevel.THREE.getName();
    }

    /**
     * Checks if the given metric plugin has cross-domain dependency properties.
     * <p>
     * Cross-domain dependencies represent relationships between components in different
     * domains, such as processes connecting to databases or applications on different systems.
     *
     * @param metricPlugin The metric plugin name to check
     * @return true if the metric plugin has cross-domain dependency properties, false otherwise
     */
    public static boolean hasCrossDomainDependencyProp(String metricPlugin)
    {
        return metricPlugin.equalsIgnoreCase(NMSConstants.MetricPlugin.LINUX_PROCESS.getName()) || metricPlugin.equalsIgnoreCase(NMSConstants.MetricPlugin.WINDOWS_PROCESS.getName())
                || metricPlugin.equalsIgnoreCase(NMSConstants.MetricPlugin.HP_UX_PROCESS.getName()) || metricPlugin.equalsIgnoreCase(NMSConstants.MetricPlugin.IBM_AIX_PROCESS.getName())
                || metricPlugin.equalsIgnoreCase(NMSConstants.MetricPlugin.SOLARIS_PROCESS.getName()) || metricPlugin.equalsIgnoreCase(NMSConstants.MetricPlugin.ORACLE_SESSION.getName())
                || metricPlugin.equalsIgnoreCase(NMSConstants.MetricPlugin.SAP_HANA_SESSION.getName()) || metricPlugin.equalsIgnoreCase(NMSConstants.MetricPlugin.SQL_SERVER_SESSION.getName())
                || metricPlugin.equalsIgnoreCase(NMSConstants.MetricPlugin.POSTGRESQL_SESSION.getName()) || metricPlugin.equalsIgnoreCase(NMSConstants.MetricPlugin.IBM_DB2_SESSION.getName());
    }

    /**
     * Checks if the given metric plugin has dependency properties.
     * <p>
     * This method determines whether a metric plugin can have dependencies,
     * either as an instance (e.g., VM, process, interface) or as a parent object
     * (e.g., cluster, cloud account).
     *
     * @param metricPlugin The metric plugin name to check
     * @param instance     Whether to check for instance-level dependencies (true) or object-level dependencies (false)
     * @return true if the metric plugin has dependency properties, false otherwise
     */
    public static boolean hasDependencyProp(String metricPlugin, boolean instance)
    {
        if (instance)
        {
            return metricPlugin.equalsIgnoreCase(NMSConstants.MetricPlugin.VMWARE_ESXI_VM.getName()) || metricPlugin.equalsIgnoreCase(NMSConstants.MetricPlugin.CITRIX_XEN_VM.getName())
                    || metricPlugin.equalsIgnoreCase(NMSConstants.MetricPlugin.NUTANIX_VM.getName()) || metricPlugin.equalsIgnoreCase(NMSConstants.MetricPlugin.HYPER_V_VM.getName())
                    || metricPlugin.equalsIgnoreCase(NMSConstants.MetricPlugin.SNMP_INTERFACE.getName()) || metricPlugin.equalsIgnoreCase(NMSConstants.MetricPlugin.LINUX_PROCESS.getName())
                    || metricPlugin.equalsIgnoreCase(NMSConstants.MetricPlugin.WINDOWS_PROCESS.getName()) || metricPlugin.equalsIgnoreCase(NMSConstants.MetricPlugin.HP_UX_PROCESS.getName())
                    || metricPlugin.equalsIgnoreCase(NMSConstants.MetricPlugin.IBM_AIX_PROCESS.getName()) || metricPlugin.equalsIgnoreCase(NMSConstants.MetricPlugin.SOLARIS_PROCESS.getName())
                    || metricPlugin.equalsIgnoreCase(NMSConstants.MetricPlugin.RUCKUS_WIRELESS_ACCESS_POINT.getName()) || metricPlugin.equalsIgnoreCase(NMSConstants.MetricPlugin.ARUBA_WIRELESS_ACCESS_POINT.getName())
                    || metricPlugin.equalsIgnoreCase(NMSConstants.MetricPlugin.CISCO_WIRELESS_ACCESS_POINT.getName()) || metricPlugin.equalsIgnoreCase(NMSConstants.MetricPlugin.CISCO_VEDGE_TUNNEL.getName());
        }

        else
        {
            return metricPlugin.equalsIgnoreCase(NMSConstants.MetricPlugin.AWS_CLOUD.getName()) || metricPlugin.equalsIgnoreCase(NMSConstants.MetricPlugin.AZURE_CLOUD.getName()) ||
                    metricPlugin.equalsIgnoreCase(NMSConstants.MetricPlugin.WINDOWS_CLUSTER.getName()) ||
                    metricPlugin.equalsIgnoreCase(NMSConstants.MetricPlugin.PRISM_CLUSTER.getName()) ||
                    metricPlugin.equalsIgnoreCase(NMSConstants.MetricPlugin.PRISM.getName()) ||
                    metricPlugin.equalsIgnoreCase(NMSConstants.MetricPlugin.HYPER_V_CLUSTER.getName()) || metricPlugin.equalsIgnoreCase(NMSConstants.MetricPlugin.VCENTER.getName())
                    || metricPlugin.equalsIgnoreCase(NMSConstants.MetricPlugin.VCENTER_CLUSTER.getName()) || metricPlugin.equalsIgnoreCase(NMSConstants.MetricPlugin.VCENTER_DATA_CENTER.getName())
                    || metricPlugin.equalsIgnoreCase(NMSConstants.MetricPlugin.CITRIX_XEN_CLUSTER.getName()) || metricPlugin.equalsIgnoreCase(NMSConstants.MetricPlugin.CISCO_WIRELESS.getName())
                    || metricPlugin.equalsIgnoreCase(NMSConstants.MetricPlugin.CISCO_WIRELESS_CLIENT.getName())
                    || metricPlugin.equalsIgnoreCase(NMSConstants.MetricPlugin.ARUBA_WIRELESS.getName()) || metricPlugin.equalsIgnoreCase(NMSConstants.MetricPlugin.ARUBA_WIRELESS_CLIENT.getName())
                    || metricPlugin.equalsIgnoreCase(NMSConstants.MetricPlugin.RUCKUS_WIRELESS.getName()) || metricPlugin.equalsIgnoreCase(NMSConstants.MetricPlugin.RUCKUS_WIRELESS_CLIENT.getName())
                    || metricPlugin.equalsIgnoreCase(NMSConstants.MetricPlugin.POSTGRESQL_SESSION.getName()) || metricPlugin.equalsIgnoreCase(NMSConstants.MetricPlugin.IBM_DB2_SESSION.getName())
                    || metricPlugin.equalsIgnoreCase(NMSConstants.MetricPlugin.SAP_HANA_SESSION.getName()) || metricPlugin.equalsIgnoreCase(NMSConstants.MetricPlugin.ORACLE_SESSION.getName())
                    || metricPlugin.equalsIgnoreCase(NMSConstants.MetricPlugin.SQL_SERVER_SESSION.getName()) ||
                    metricPlugin.equalsIgnoreCase(NMSConstants.MetricPlugin.CISCO_UCS.getName()) || metricPlugin.equalsIgnoreCase(NMSConstants.MetricPlugin.CISCO_UCS_FABRIC_INTERCONNECT.getName())
                    || metricPlugin.equalsIgnoreCase(NMSConstants.MetricPlugin.CISCO_VMANAGE.getName()) || metricPlugin.equalsIgnoreCase(NMSConstants.MetricPlugin.CISCO_ACI.getName())
                    || metricPlugin.equalsIgnoreCase(NMSConstants.MetricPlugin.CISCO_UCS_RACK_MOUNT.getName()) || metricPlugin.equalsIgnoreCase(NMSConstants.MetricPlugin.CISCO_UCS_CHASSIS.getName())
                    || hasDependencyProp(metricPlugin, true);
        }

    }

    /**
     * Gets the dependency level for a network dependency based on the link layer.
     * <p>
     * This method extracts the dependency level from the dependency mapper context
     * in the item. For network dependencies, the level is determined by the link layer:
     * <ul>
     *   <li>Layer 2 (L2) dependencies (CDP, LLDP, SPM) are assigned level 6</li>
     *   <li>Layer 3 (L3) dependencies (IS-IS, OSPF, BGP) are assigned level 5</li>
     * </ul>
     *
     * @param item   The item containing dependency information
     * @param remove Whether to remove the dependency mapper context after extraction
     * @return The dependency level (5 for L3, 6 for L2)
     */
    public static Byte getDependencyLevel(JsonObject item, boolean remove)
    {
        if (item.containsKey(DependencyMapper.DEPENDENCY_MAPPER_CONTEXT))
        {
            item.mergeIn(item.getJsonObject(DependencyMapper.DEPENDENCY_MAPPER_CONTEXT));

            if (remove)
            {
                item.remove(DependencyMapper.DEPENDENCY_MAPPER_CONTEXT);
            }
        }

        return item.getString(DependencyMapper.DEPENDENCY_MAPPER_LINK_LAYER).equalsIgnoreCase(DependencyMapper.DependencyMapperLinkLayer.L2.getName()) ? AIOpsConstants.DependencyLevel.SIX.getName() : AIOpsConstants.DependencyLevel.FIVE.getName();
    }

    /**
     * Gets the metric plugin name for a given object type.
     * <p>
     * This method maps virtualization and cloud object types to their corresponding
     * metric plugin names. For example, VMWARE_ESXI maps to VMWARE_ESXI_VM.
     *
     * @param type The object type name
     * @return The corresponding metric plugin name, or EMPTY_VALUE if no mapping exists
     */
    public static String getMetricPlugin(String type)
    {
        return switch (NMSConstants.Type.valueOfName(type))
        {
            case VMWARE_ESXI -> NMSConstants.MetricPlugin.VMWARE_ESXI_VM.getName();
            case HYPER_V -> NMSConstants.MetricPlugin.HYPER_V_VM.getName();
            case CITRIX_XEN -> NMSConstants.MetricPlugin.CITRIX_XEN_VM.getName();
            case NUTANIX -> NMSConstants.MetricPlugin.NUTANIX_VM.getName();
            case AMAZON_EC2 -> NMSConstants.MetricPlugin.AMAZON_EC2.getName();
            case AZURE_VM -> NMSConstants.MetricPlugin.AZURE_VM.getName();
            default -> EMPTY_VALUE;
        };
    }

    /**
     * Defines the format of dependency results returned by the system.
     * <p>
     * The dependency format determines how dependency relationships are structured
     * in query results, with options for flat connections or hierarchical relationships.
     */
    public enum DependencyFormat
    {
        /**
         * Raw dependency connections without hierarchy.
         * <p>
         * This format is primarily used for root cause analysis (RCA) where
         * the raw connections between components are needed.
         */
        FLAT("flat"),

        /**
         * Child-to-parent hierarchical format.
         * <p>
         * This format organizes dependencies from a child object upward to its
         * parents and ancestors, showing what a component depends on.
         */
        BOTTOM_TOP_HIERARCHY("bottom.top.hierarchy"),

        /**
         * Parent-to-child hierarchical format.
         * <p>
         * This format organizes dependencies from a parent object downward to its
         * children and descendants, showing what depends on a component.
         */
        TOP_BOTTOM_HIERARCHY("top.bottom.hierarchy");

        private static final Map<String, DependencyFormat> VALUES = Collections.unmodifiableMap(Arrays.stream(values())
                .collect(Collectors.toMap(DependencyFormat::getName, e -> e)));
        private final String name;

        DependencyFormat(String name)
        {
            this.name = name;
        }

        /**
         * Gets the DependencyFormat enum value for the given name.
         *
         * @param name The string name of the dependency format
         * @return The corresponding DependencyFormat enum value, or null if not found
         */
        public static DependencyFormat valueOfName(String name)
        {
            return VALUES.get(name);
        }

        /**
         * Gets the string name of this dependency format.
         *
         * @return The string name of this dependency format
         */
        public String getName()
        {
            return name;
        }
    }

    /**
     * Defines the hierarchical levels of dependencies in the system.
     * <p>
     * The dependency level represents the position of a component in the overall
     * IT infrastructure hierarchy, from application-level components (levels 0-3)
     * to infrastructure components (levels 4-12).
     * <p>
     * These levels are used for dependency mapping, correlation, and impact analysis.
     */
    public enum DependencyLevel
    {
        /**
         * Special level used to maintain parent relationships.
         * <p>
         * This level is used to track upward dependencies (parent objects).
         */
        MINUS_ONE((byte) -1),

        /**
         * Database level applications.
         * <p>
         * Includes Oracle, SQL Server, MySQL, PostgreSQL, and other database systems.
         */
        ZERO((byte) 0),

        /**
         * Middleware level applications.
         * <p>
         * Includes application servers (Tomcat, WebSphere), message queues (IBM MQ, RabbitMQ),
         * and other middleware components.
         */
        ONE((byte) 1),

        /**
         * Web and proxy server applications.
         * <p>
         * Includes web servers (IIS, Apache HTTP, Nginx) and proxy servers (HAProxy).
         */
        TWO((byte) 2),

        /**
         * Service applications.
         * <p>
         * Includes infrastructure services like DNS, DHCP, Active Directory, and Exchange.
         */
        THREE((byte) 3),

        /**
         * Instance level dependencies.
         * <p>
         * Includes processes, interfaces, Docker instances, and interface colors.
         */
        FOUR((byte) 4),

        /**
         * Network layer 3 dependencies.
         * <p>
         * Includes routing protocols like IS-IS, OSPF, and BGP.
         */
        FIVE((byte) 5),

        /**
         * Network layer 2 dependencies.
         * <p>
         * Includes discovery protocols like CDP, LLDP, and SPM.
         */
        SIX((byte) 6),

        /**
         * Operating system level dependencies.
         * <p>
         * Includes servers, network devices, and edge routers at the OS level.
         */
        SEVEN((byte) 7),

        /**
         * Virtualization instance level dependencies.
         * <p>
         * Includes cloud services, virtual machines, and Cisco UCS FEX.
         */
        EIGHT((byte) 8),

        /**
         * Virtualization host level dependencies.
         * <p>
         * Includes ESXi hosts, Docker engines, Kubernetes nodes, and Cisco UCS chassis servers.
         */
        NINE((byte) 9),

        /**
         * Resource pool level dependencies.
         * <p>
         * Includes clusters, pools, regions, resources, Cisco UCS rack mounts, and Cisco vSmart/vBond.
         */
        TEN((byte) 10),

        /**
         * Storage level dependencies.
         * <p>
         * Includes storage systems and arrays.
         */
        ELEVEN((byte) 11),

        /**
         * Account/data center level dependencies.
         * <p>
         * Includes cloud accounts, virtualization data centers, and Cisco UCS fabric interconnects/vManage.
         */
        TWELVE((byte) 12);

        private static final Map<Byte, DependencyLevel> VALUES = Collections.unmodifiableMap(Arrays.stream(values())
                .collect(Collectors.toMap(DependencyLevel::getName, e -> e)));
        private final Byte name;

        DependencyLevel(Byte name)
        {
            this.name = name;
        }

        /**
         * Gets the DependencyLevel enum value for the given byte value.
         *
         * @param name The byte value of the dependency level
         * @return The corresponding DependencyLevel enum value, or null if not found
         */
        public static DependencyLevel valueOfName(Byte name)
        {
            return VALUES.get(name);
        }

        /**
         * Gets the byte value of this dependency level.
         *
         * @return The byte value of this dependency level
         */
        public Byte getName()
        {
            return name;
        }
    }

    /**
     * Defines the types of dependencies based on their domain scope.
     * <p>
     * Dependencies can exist within the same domain (local) or across different
     * domains (cross-domain), which affects how they are processed and correlated.
     */
    public enum DependencyType
    {
        /**
         * Dependencies within the same domain.
         * <p>
         * Local domain dependencies represent relationships between components
         * in the same logical or physical domain, such as network devices in the
         * same network segment or VMs on the same hypervisor.
         */
        LOCAL_DOMAIN("local.domain"),

        /**
         * Dependencies across different domains.
         * <p>
         * Cross-domain dependencies represent relationships between components
         * in different logical or physical domains, such as applications connecting
         * to databases on different systems or services spanning multiple networks.
         */
        CROSS_DOMAIN("cross.domain");

        private static final Map<String, DependencyType> VALUES = Collections.unmodifiableMap(Arrays.stream(values())
                .collect(Collectors.toMap(DependencyType::getName, e -> e)));
        private final String name;

        DependencyType(String name)
        {
            this.name = name;
        }

        /**
         * Gets the DependencyType enum value for the given name.
         *
         * @param name The string name of the dependency type
         * @return The corresponding DependencyType enum value, or null if not found
         */
        public static DependencyType valueOfName(String name)
        {
            return VALUES.get(name);
        }

        /**
         * Gets the string name of this dependency type.
         *
         * @return The string name of this dependency type
         */
        public String getName()
        {
            return name;
        }
    }

    /**
     * Defines the operations that can be performed on dependencies.
     * <p>
     * These operations control how dependencies are added, removed, or modified
     * in the dependency management system.
     */
    public enum DependencyOperation
    {
        /**
         * Adds a single dependency relationship.
         * <p>
         * Used for mapping simple relationships like process-to-application
         * or network interface-to-neighbor device.
         */
        ADD("add"),

        /**
         * Adds multiple dependencies at once.
         * <p>
         * Used for mapping multiple related entities like processes,
         * virtual machines, or network interfaces.
         */
        ADD_MULTIPLES("add.multiples"),

        /**
         * Adds dependencies with additional context.
         * <p>
         * Used for mapping relationships with polling or topology scanner
         * result contexts, providing richer dependency information.
         */
        ADD_MAP("add.map"),

        /**
         * Removes a single dependency relationship.
         */
        REMOVE("remove"),

        /**
         * Removes a parent dependency relationship.
         * <p>
         * Specifically targets and removes parent-child relationships.
         */
        REMOVE_PARENT("remove.parent"),

        /**
         * Removes multiple dependency relationships at once.
         * <p>
         * Used for bulk removal of related dependencies.
         */
        REMOVE_MULTIPLES("remove.multiples");

        private static final Map<String, DependencyOperation> VALUES = Collections.unmodifiableMap(Arrays.stream(values())
                .collect(Collectors.toMap(DependencyOperation::getName, e -> e)));
        private final String name;

        DependencyOperation(String name)
        {
            this.name = name;
        }

        /**
         * Gets the DependencyOperation enum value for the given name.
         *
         * @param name The string name of the dependency operation
         * @return The corresponding DependencyOperation enum value, or null if not found
         */
        public static DependencyOperation valueOfName(String name)
        {
            return VALUES.get(name);
        }

        /**
         * Gets the string name of this dependency operation.
         *
         * @return The string name of this dependency operation
         */
        public String getName()
        {
            return name;
        }
    }

    /**
     * Defines the types of correlation that can be performed in the system.
     * <p>
     * Correlation types determine how events and alerts are analyzed and grouped
     * to identify relationships and reduce alert noise.
     */
    public enum CorrelationType
    {
        /**
         * Availability-based correlation.
         * <p>
         * This correlation type analyzes availability events (up/down) to identify
         * root causes of failures and suppress secondary alerts. It uses dependency
         * information to determine which components are unavailable due to upstream
         * failures versus those that have actual issues.
         */
        AVAILABILITY("availability"),

        /**
         * Rule-based correlation.
         * <p>
         * This correlation type uses predefined rules to correlate events based on
         * specific criteria like event type, source, timing, and other attributes.
         * It allows for more flexible correlation logic beyond dependency-based analysis.
         */
        RULE("rule");

        private static final Map<String, CorrelationType> VALUES = Collections.unmodifiableMap(Arrays.stream(values())
                .collect(Collectors.toMap(CorrelationType::getName, e -> e)));
        private final String name;

        CorrelationType(String name)
        {
            this.name = name;
        }

        /**
         * Gets the CorrelationType enum value for the given name.
         *
         * @param name The string name of the correlation type
         * @return The corresponding CorrelationType enum value, or null if not found
         */
        public static CorrelationType valueOfName(String name)
        {
            return VALUES.get(name);
        }

        /**
         * Gets the string name of this correlation type.
         *
         * @return The string name of this correlation type
         */
        public String getName()
        {
            return name;
        }
    }

    /**
     * Defines the types of availability probes used in correlation.
     * <p>
     * Availability probes represent different states of objects and instances
     * in the system, which are used to trigger correlation analysis and determine
     * the impact of availability changes.
     */
    public enum AvailabilityProbeType
    {
        /**
         * Indicates that an object has come back online.
         * <p>
         * This probe type is triggered when a previously down object becomes available
         * again. It initiates correlation to update the status of dependent objects
         * that may have been marked as affected by the object's downtime.
         */
        OBJECT_UP("object.up"),

        /**
         * Indicates that an object has gone offline.
         * <p>
         * This probe type is triggered when an object becomes unavailable. It initiates
         * correlation to identify dependent objects that may be affected by the outage
         * and to determine if the object is down due to its own issues or because of
         * upstream failures.
         */
        OBJECT_DOWN("object.down"),

        /**
         * Indicates that an instance has come back online.
         * <p>
         * This probe type is triggered when a previously down instance (like an interface,
         * process, or VM) becomes available again. It initiates correlation to update
         * the status of dependent objects and instances.
         */
        INSTANCE_UP("instance.up"),

        /**
         * Indicates that an instance has gone offline.
         * <p>
         * This probe type is triggered when an instance becomes unavailable. It initiates
         * correlation to identify dependent objects and instances that may be affected
         * by the outage and to determine the root cause of the failure.
         */
        INSTANCE_DOWN("instance.down");

        private static final Map<String, AvailabilityProbeType> VALUES = Collections.unmodifiableMap(Arrays.stream(values())
                .collect(Collectors.toMap(AvailabilityProbeType::getName, e -> e)));
        private final String name;

        AvailabilityProbeType(String name)
        {
            this.name = name;
        }

        /**
         * Gets the AvailabilityProbeType enum value for the given name.
         *
         * @param name The string name of the availability probe type
         * @return The corresponding AvailabilityProbeType enum value, or null if not found
         */
        public static AvailabilityProbeType valueOfName(String name)
        {
            return VALUES.get(name);
        }

        /**
         * Gets the string name of this availability probe type.
         *
         * @return The string name of this availability probe type
         */
        public String getName()
        {
            return name;
        }
    }
}
