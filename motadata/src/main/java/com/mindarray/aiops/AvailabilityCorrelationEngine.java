/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

/*
	Change Logs:
	Date			Author			    Notes
	26-Feb-2025		Darshan Parmar		MOTADATA-5215: SonarQube Suggestions Resolution
*/
package com.mindarray.aiops;

import com.mindarray.Bootstrap;
import com.mindarray.ErrorCodes;
import com.mindarray.ErrorMessageConstants;
import com.mindarray.InfoMessageConstants;
import com.mindarray.agent.AgentConstants;
import com.mindarray.api.AIOpsObject;
import com.mindarray.api.APIConstants;
import com.mindarray.api.Agent;
import com.mindarray.api.Metric;
import com.mindarray.eventbus.EventBusConstants;
import com.mindarray.eventbus.EventEngine;
import com.mindarray.ha.HAConstants;
import com.mindarray.nms.NMSConstants;
import com.mindarray.runbook.Runbook;
import com.mindarray.store.*;
import com.mindarray.util.CodecUtil;
import com.mindarray.util.CommonUtil;
import com.mindarray.util.DateTimeUtil;
import com.mindarray.util.Logger;
import io.vertx.core.AbstractVerticle;
import io.vertx.core.Future;
import io.vertx.core.Promise;
import io.vertx.core.buffer.Buffer;
import io.vertx.core.eventbus.DeliveryOptions;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;

import java.util.*;

import static com.mindarray.GlobalConstants.*;
import static com.mindarray.aiops.AIOpsConstants.AvailabilityProbeType.*;
import static com.mindarray.api.RunbookPlugin.RUNBOOK_PLUGIN_ENTITIES;
import static com.mindarray.api.RunbookPlugin.RUNBOOK_PLUGIN_VARIABLES;
import static com.mindarray.eventbus.EventBusConstants.*;
import static com.mindarray.nms.NMSConstants.*;

/**
 * Engine for correlating availability events in the system.
 * <p>
 * The AvailabilityCorrelationEngine is responsible for:
 * <ul>
 *   <li>Processing availability events (object up/down, instance up/down)</li>
 *   <li>Correlating events to identify root causes of availability issues</li>
 *   <li>Building and traversing dependency hierarchies to determine affected components</li>
 *   <li>Managing the state of objects and instances during availability events</li>
 *   <li>Suspending and enabling metrics based on availability status</li>
 * </ul>
 * <p>
 * This engine reduces alert noise by identifying the root cause of cascading failures
 * and suppressing secondary alerts. It uses dependency information to determine which
 * components are unavailable due to upstream failures versus those that have actual issues.
 */

public class AvailabilityCorrelationEngine extends AbstractVerticle
{
    private static final Logger LOGGER = new Logger(AvailabilityCorrelationEngine.class, MOTADATA_AIOPS, "Availability Correlation Engine");
    private final DeliveryOptions deliveryOptions = new DeliveryOptions().setSendTimeout(300000L);
    // String key = if instance name available -> id + separator + instance else id
    // Map <String,JsonArray> = key = probe type and value []= progress []
    private final Map<String, Map<String, JsonArray>> correlationLogs = new HashMap<>();
    // String key = if instance name available -> id + separator + instance else id
    // Map <String,Map<String,List<Object>> = key = probe type and value {}=  "correlated.down.objects" -> [] || "correlated.unreachable.objects" -> []metr
    private final Map<String, Map<String, Map<String, List<Object>>>> correlations = new HashMap<>();
    // key =  probe type and value = object id
    private final Map<String, Set<Object>> runningProbes = new HashMap<>();
    // key = id, value = down instances (interfaces)
    private final Map<Long, List<String>> downObjects = new HashMap<>();                             // keep track of down instances and avoid duplicate requests
    private String replyTopic;
    private EventEngine eventEngine;
    private boolean writePending = false;
    private boolean dirty = false;

    /**
     * Initializes the AvailabilityCorrelationEngine and sets up event bus consumers.
     * <p>
     * This method performs the following initialization tasks:
     * <ul>
     *   <li>Sets up the reply topic for event responses</li>
     *   <li>Initializes running probes for each availability probe type</li>
     *   <li>Registers event bus consumers for various events:</li>
     *   <ul>
     *     <li>Change notifications (object/instance deletions, cache updates)</li>
     *     <li>Availability events (object up/down, instance up/down)</li>
     *     <li>Correlation data read/write operations</li>
     *   </ul>
     *   <li>Deserializes existing correlation data from disk if available</li>
     * </ul>
     * <p>
     * The method handles various types of availability events and routes them to the
     * appropriate correlation handlers based on the event type and context.
     *
     * @param startPromise Promise to be completed when initialization is done
     * @throws Exception If an error occurs during initialization
     */
    @Override
    public void start(Promise<Void> startPromise) throws Exception
    {
        replyTopic = config().getString(EVENT_TYPE) + EventBusConstants.EVENT_REPLY;

        for (var correlationProbe : AIOpsConstants.AvailabilityProbeType.values())
        {
            runningProbes.put(correlationProbe.getName(), new HashSet<>());
        }

        vertx.eventBus().<JsonObject>localConsumer(EVENT_CHANGE_NOTIFICATION, message ->
        {
            try
            {
                var event = message.body();

                if (event.containsKey(CHANGE_NOTIFICATION_TYPE))
                {
                    if (ChangeNotificationType.valueOf(event.getString(CHANGE_NOTIFICATION_TYPE)) == ChangeNotificationType.DELETE_OBJECT)
                    {
                        var object = event.getJsonObject(OBJECT);

                        var objects = new HashSet<Long>();

                        //object delete

                        if (object != null && object.containsKey(AIOpsObject.OBJECT_CATEGORY) && !object.getString(AIOpsObject.OBJECT_CATEGORY).equalsIgnoreCase(Category.SERVICE_CHECK.getName()))
                        {
                            for (var entry : correlations.entrySet())
                            {
                                if (entry.getKey().startsWith(CommonUtil.getString(object.getLong(ID))))
                                {

                                    if (entry.getValue().get(OBJECT_DOWN.getName()) != null)
                                    {
                                        update(entry.getValue().get(OBJECT_DOWN.getName()), objects);
                                    }

                                    if (entry.getValue().get(INSTANCE_DOWN.getName()) != null)
                                    {
                                        update(entry.getValue().get(INSTANCE_DOWN.getName()), objects);
                                    }
                                }
                            }
                        }

                        // instance delete
                        if (event.containsKey(METRIC_INSTANCES))
                        {
                            var key = CommonUtil.getString(event.getLong(ID));

                            var instances = event.getJsonArray(METRIC_INSTANCES);

                            for (var index = 0; index < instances.size(); index++)
                            {
                                var tokens = instances.getString(index).split(DASH_SEPARATOR);

                                if (correlations.containsKey(key + VALUE_SEPARATOR + tokens[tokens.length - 1]))
                                {
                                    var context = correlations.get(key + VALUE_SEPARATOR + tokens[tokens.length - 1]).get(INSTANCE_DOWN.getName());

                                    if (context != null && !context.isEmpty())
                                    {
                                        update(context, objects);
                                    }

                                    if (downObjects.containsKey(event.getLong(ID)))
                                    {
                                        downObjects.get(event.getLong(ID)).remove(tokens[tokens.length - 1]);
                                    }
                                }
                            }
                        }

                        if (!objects.isEmpty())
                        {
                            objects.forEach(id ->
                            {
                                enableMetric(id, null, null, object);

                                downObjects.remove(id);
                            });
                        }
                    }
                    else if (ChangeNotificationType.valueOf(event.getString(CHANGE_NOTIFICATION_TYPE)) == ChangeNotificationType.UPDATE_CACHE)
                    {
                        if (dirty)
                        {
                            dirty = false;

                            HAConstants.notifyObserver(new JsonObject().put(HAConstants.CACHE_NAME, config().getString(EventBusConstants.EVENT_ROUTER_CONFIG)).put(RESULT, vertx.fileSystem().readFileBlocking(CURRENT_DIR + PATH_SEPARATOR + CONFIG_DIR + PATH_SEPARATOR + config().getString(EventBusConstants.EVENT_ROUTER_CONFIG))));
                        }
                        else
                        {
                            if (CommonUtil.traceEnabled())
                            {
                                LOGGER.trace("correlation not dirty");
                            }
                        }
                    }
                }
            }
            catch (Exception exception)
            {
                LOGGER.error(exception);
            }
        }).exceptionHandler(LOGGER::error);

        vertx.eventBus().<byte[]>localConsumer(config().getString(EventBusConstants.EVENT_TYPE) + ".write", message ->
        {
            LOGGER.info(String.format("writing correlated objects into config for %s with flag %s", config().getString(EventBusConstants.EVENT_ROUTER_CONFIG), !writePending));

            if (!writePending)
            {
                writePending = true;

                // need to persist the result of correlations , runningProbes and downObjects ,
                // otherwise it may give wrong result if system restarts

                var items = new HashMap<>();

                if (!correlations.isEmpty())
                {
                    items.put("correlations", correlations);
                }

                if (!runningProbes.isEmpty())
                {
                    var updated = false;

                    for (var correlationProbe : AIOpsConstants.AvailabilityProbeType.values())
                    {
                        if (!runningProbes.get(correlationProbe.getName()).isEmpty())
                        {
                            updated = true;

                            break;
                        }
                    }

                    if (updated)
                    {
                        items.put("running.probes", runningProbes);
                    }
                }

                if (!downObjects.isEmpty())
                {
                    items.put("down.objects", downObjects);
                }

                vertx.<byte[]>executeBlocking(promise ->
                {
                    byte[] bytes = null;

                    try
                    {
                        var valid = false;

                        if (!items.isEmpty())
                        {
                            bytes = CodecUtil.serialize(items);

                            if (bytes != null && bytes.length > 0)
                            {
                                valid = true;

                                bytes = CodecUtil.compress(bytes);

                                LOGGER.info(String.format("updating new correlated objects of %s bytes into config %s", bytes.length, config().getString(EventBusConstants.EVENT_ROUTER_CONFIG)));

                                vertx.fileSystem().writeFileBlocking(CURRENT_DIR + PATH_SEPARATOR + CONFIG_DIR + PATH_SEPARATOR + config().getString(EventBusConstants.EVENT_ROUTER_CONFIG), Buffer.buffer(bytes));

                                LOGGER.info(String.format("new correlated objects updated into config %s", config().getString(EventBusConstants.EVENT_ROUTER_CONFIG)));
                            }
                        }

                        if (!valid && CommonUtil.traceEnabled())
                        {
                            LOGGER.trace(String.format("%s to write dependency for %s ", ErrorMessageConstants.ITEM_NOT_FOUND, config().getString(EVENT_ROUTER_CONFIG)));
                        }
                    }
                    catch (Exception exception)
                    {
                        LOGGER.error(exception);
                    }

                    promise.complete(bytes);

                }, false, result ->
                {
                    message.reply(result.result());

                    writePending = false;
                });
            }
            else
            {
                message.reply(null);
            }
        }).exceptionHandler(LOGGER::error);

        vertx.eventBus().<JsonObject>localConsumer(config().getString(EVENT_TYPE) + ".read", message ->
                vertx.<JsonArray>executeBlocking(promise ->
                {
                    var event = message.body();

                    var filters = new HashSet<Long>();

                    writePending = true;

                    LOGGER.info(String.format("correlation engine consumer started successfully for %s into %s", event.getString(EVENT_TYPE), config().getString(EVENT_TYPE)));

                    try
                    {
                        var file = CURRENT_DIR + PATH_SEPARATOR + CONFIG_DIR + PATH_SEPARATOR + replace(event.getString(EVENT_TYPE));

                        var buffer = vertx.fileSystem().existsBlocking(file) ? vertx.fileSystem().readFileBlocking(file) : null;

                        if (buffer != null && buffer.length() > 0)
                        {
                            var entries = (Map<String, Object>) CodecUtil.deserialize(CodecUtil.toBytes(buffer.getBytes()), Map.class);

                            if (entries != null)
                            {
                                deserialize(entries, filters);
                            }

                            LOGGER.info(String.format("correlated objects for %s inserted successfully into %s", event.getString(EVENT_TYPE), config().getString(EVENT_TYPE)));
                        }
                    }
                    catch (Exception exception)
                    {
                        LOGGER.error(exception);
                    }

                    promise.complete(new JsonArray(new ArrayList<>(filters)));
                }, false, result ->
                {
                    writePending = false;

                    message.reply(result.result());

                })).exceptionHandler(LOGGER::error);

        // return running correlation objects based on probe type
        vertx.eventBus().<JsonObject>localConsumer(config().getString(EVENT_TYPE) + EVENT_QUERY, message ->
        {
            var event = message.body();

            if (event.getString(AIOpsConstants.CORRELATED_OBJECT_QUERY).equalsIgnoreCase(YES))
            {
                // if yes, then return all down and unreachable objects from correlation

                var items = new JsonArray();

                try
                {
                    for (var entry : correlations.values())
                    {
                        for (var nestedEntry : entry.entrySet())
                        {
                            if (nestedEntry.getKey().equalsIgnoreCase(INSTANCE_DOWN.getName()) || nestedEntry.getKey().equalsIgnoreCase(OBJECT_DOWN.getName()))
                            {
                                var item = nestedEntry.getValue();

                                if (item.get(AIOpsConstants.CORRELATED_DOWN_OBJECTS) != null)
                                {
                                    item.get(AIOpsConstants.CORRELATED_DOWN_OBJECTS).stream().filter(id -> !items.contains(CommonUtil.getLong(id)))
                                            .map(CommonUtil::getLong).forEach(items::add);
                                }

                                if (item.get(AIOpsConstants.CORRELATED_UNREACHABLE_OBJECTS) != null)
                                {
                                    item.get(AIOpsConstants.CORRELATED_UNREACHABLE_OBJECTS).stream().filter(id -> !items.contains(CommonUtil.getLong(id)))
                                            .map(CommonUtil::getLong).forEach(items::add);
                                }
                            }
                        }
                    }
                }
                catch (Exception exception)
                {
                    LOGGER.error(exception);
                }

                message.reply(items);
            }
            else
            {
                // return all the objects which contains in running probes

                message.reply(new JsonArray(event.getJsonArray(AIOpsConstants.AVAILABILITY_PROBE_TYPE).stream().filter(runningProbes::containsKey).flatMap(item -> runningProbes.get(CommonUtil.getString(item))
                        .stream()).toList()));
            }
        }).exceptionHandler(LOGGER::error);

        eventEngine = new EventEngine().setEventType(config().getString(EVENT_TYPE)).setLogger(LOGGER).setPersistEventOffset(true)
                .setBlockingEvent(true).setEventQueueSize(1).setEventHandler(event ->
                {
                    try
                    {
                        String key;

                        if (checkPingStatus(event.getLong(Metric.METRIC_OBJECT)))
                        {
                            var id = event.getLong(Metric.METRIC_OBJECT);

                            var instanceName = event.getString(AIOpsConstants.CORRELATION_INSTANCE);

                            if (CommonUtil.isNotNullOrEmpty(instanceName) && NMSConstants.NETWORK_DEVICES.contains(event.getString(AIOpsObject.OBJECT_TYPE)))
                            {
                                // might be a chance that instance name contains "-" so split with first index will not work so after split with "-" set last value of token in instance name
                                var tokens = instanceName.split(DASH_SEPARATOR);

                                instanceName = tokens[tokens.length - 1];

                                event.put(AIOpsConstants.CORRELATION_INSTANCE, instanceName);
                            }

                            key = instanceName != null ? CommonUtil.getString(id + VALUE_SEPARATOR + instanceName) : CommonUtil.getString(id);

                            var category = NMSConstants.Category.valueOfName(event.getString(AIOpsObject.OBJECT_CATEGORY));

                            var probeType = event.getString(AIOpsConstants.AVAILABILITY_PROBE_TYPE);

                            LOGGER.info(String.format("request received to run correlation %s for object %s", probeType, event.getString(AIOpsObject.OBJECT_NAME)));

                            if (!correlationLogs.containsKey(key))
                            {
                                correlationLogs.put(key, new HashMap<>());
                            }

                            if (!correlationLogs.get(key).containsKey(probeType))
                            {
                                correlationLogs.get(key).put(probeType, new JsonArray());

                                var instance = instanceName;

                                getRunningProbes(probeType).onComplete(response ->
                                {
                                    var logs = this.correlationLogs.get(key).get(probeType);

                                    var probes = response.result();

                                    logs.add(String.format("running correlation objects for correlation %s are %s at %s", probeType, probes, DateTimeUtil.timestamp()));

                                    event.put(AIOpsConstants.CORRELATION_RUNNING_PROBES, probes)
                                            .put(AIOpsObject.OBJECT_CATEGORY, category == Category.SERVER ? Category.NETWORK : category);

                                    // if already running then failed
                                    if (!probes.contains(id))
                                    {
                                        var promise = Promise.<Boolean>promise();

                                        if (probeType.equalsIgnoreCase(OBJECT_DOWN.getName()) || probeType.equalsIgnoreCase(INSTANCE_DOWN.getName()))
                                        {
                                            vertx.eventBus().<JsonArray>request(config().getString(EVENT_TYPE) + EVENT_QUERY,
                                                    new JsonObject().put(EVENT_QUERY_BULK, YES).put(AIOpsConstants.CORRELATED_OBJECT_QUERY, YES),
                                                    deliveryOptions, reply ->
                                                    {
                                                        if (reply.succeeded())
                                                        {
                                                            try
                                                            {
                                                                var result = reply.result().body();

                                                                if (!result.contains(id))           // current object should not be correlated
                                                                {
                                                                    if (!downObjects.containsKey(id) || (!downObjects.get(id).contains(instance)))
                                                                    {
                                                                        promise.complete(true);
                                                                    }
                                                                    else
                                                                    {
                                                                        promise.complete(false);

                                                                        correlations.get(key).get(probeType).forEach(event::put);

                                                                        discard(event, key, String.format("duplicate request found with key : %s", key), STATUS_FAIL);
                                                                    }
                                                                }
                                                                else
                                                                {
                                                                    promise.complete(false);

                                                                    if (correlations.containsKey(key) && correlations.get(key).containsKey(probeType))
                                                                    {
                                                                        correlations.get(key).get(probeType).forEach(event::put);

                                                                        updateInstanceStatus(event, logs);

                                                                        discard(event, key, String.format("%s already correlated", event.getString(AIOpsObject.OBJECT_NAME)), STATUS_FAIL);
                                                                    }
                                                                    else
                                                                    {
                                                                        discard(event, key, String.format("%s already correlated", event.getString(AIOpsObject.OBJECT_NAME)), STATUS_ABORT);
                                                                    }
                                                                }
                                                            }
                                                            catch (Exception exception)
                                                            {
                                                                LOGGER.error(exception);

                                                                promise.complete(false);

                                                                vertx.eventBus().send(replyTopic, event.put(STATUS, STATUS_FAIL)
                                                                        .put(ERROR_CODE, ErrorCodes.ERROR_CODE_INTERNAL_ERROR)
                                                                        .put(MESSAGE, String.format(ErrorMessageConstants.CORRELATION_FAILED, event.getString(AIOpsObject.OBJECT_NAME), exception.getMessage()))
                                                                        .put(ERROR, CommonUtil.formatStackTrace(exception.getStackTrace())));
                                                            }
                                                        }
                                                        else
                                                        {
                                                            LOGGER.error(reply.cause());

                                                            promise.complete(false);

                                                            vertx.eventBus().send(replyTopic, event.put(STATUS, STATUS_FAIL)
                                                                    .put(ERROR_CODE, ErrorCodes.ERROR_CODE_INTERNAL_ERROR)
                                                                    .put(MESSAGE, String.format(ErrorMessageConstants.CORRELATION_FAILED, event.getString(AIOpsObject.OBJECT_NAME), ErrorMessageConstants.INTERNAL_ERROR))
                                                                    .put(ERROR, CommonUtil.formatStackTrace(reply.cause().getStackTrace())));
                                                        }
                                                    });
                                        }
                                        else
                                        {
                                            promise.complete(true);
                                        }

                                        promise.future().onComplete(result ->
                                        {
                                            if (result.succeeded() && result.result().equals(true))
                                            {
                                                var future = Promise.<JsonObject>promise();

                                                event.remove(AIOpsObject.OBJECT_EVENT_PROCESSORS);

                                                switch (valueOfName(probeType))
                                                {
                                                    case OBJECT_UP ->
                                                    {
                                                        event.put(AIOpsConstants.CORRELATED_UP_OBJECTS, new JsonArray());

                                                        runCorrelation(event, key, logs, future, OBJECT_DOWN.getName(), new JsonArray(), category);
                                                    }

                                                    case OBJECT_DOWN ->
                                                    {
                                                        event.put(AIOpsConstants.CORRELATED_DOWN_OBJECTS, new JsonArray())
                                                                .put(AIOpsConstants.CORRELATED_UNREACHABLE_OBJECTS, new JsonArray()).put(AIOpsConstants.CORRELATED_UP_OBJECTS, new JsonArray());

                                                        runCorrelation(event, id, probeType, category, logs, future);
                                                    }

                                                    case INSTANCE_UP ->
                                                    {
                                                        event.put(AIOpsConstants.CORRELATED_UP_OBJECTS, new JsonArray()).put(AIOpsConstants.CORRELATED_APPLICATIONS, new JsonArray());

                                                        runCorrelation(event, key, logs, future, INSTANCE_DOWN.getName(), new JsonArray(), category);
                                                    }

                                                    case INSTANCE_DOWN ->
                                                    {
                                                        event.put(AIOpsConstants.CORRELATED_DOWN_OBJECTS, new JsonArray()).put(AIOpsConstants.CORRELATED_UNREACHABLE_OBJECTS, new JsonArray())
                                                                .put(AIOpsConstants.CORRELATED_APPLICATIONS, new JsonArray()).put(AIOpsConstants.CORRELATED_UP_OBJECTS, new JsonArray());

                                                        runCorrelation(event, id, probeType, category, logs, future);
                                                    }

                                                    default ->
                                                            future.complete(event.put(STATUS, STATUS_ABORT).put(MESSAGE, String.format(ErrorMessageConstants.CORRELATION_FAILED, event.getString(AIOpsObject.OBJECT_NAME), "Invalid correlation probe type")));
                                                }
                                                complete(future, key, probeType, logs, instance, event);
                                            }
                                        });
                                    }
                                    else
                                    {
                                        discard(event, key, String.format(ErrorMessageConstants.CORRELATION_FAILED, event.getString(AIOpsObject.OBJECT_NAME), "same object found on running correlations of any engine"), STATUS_ABORT);
                                    }
                                });
                            }
                            else
                            {
                                discard(event, key, String.format(ErrorMessageConstants.CORRELATION_FAILED, event.getString(AIOpsObject.OBJECT_NAME), "same object found on running correlations of any engine"), STATUS_ABORT);
                            }
                        }
                        else
                        {
                            discard(event, EMPTY_VALUE, String.format(InfoMessageConstants.PING_IS_DISABLED, event.getString(AIOpsObject.OBJECT_NAME)), STATUS_FAIL); // invalid already running
                        }
                    }
                    catch (Exception exception)
                    {
                        LOGGER.error(exception);

                        vertx.eventBus().send(replyTopic, event);
                    }

                }).start(vertx, startPromise);
    }

    private void remove(JsonArray probeTypes, JsonObject result)
    {
        var unreachableObjects = result.getJsonArray(AIOpsConstants.CORRELATED_UNREACHABLE_OBJECTS);

        var downObjects = result.getJsonArray(AIOpsConstants.CORRELATED_DOWN_OBJECTS);

        for (var index = 0; index < probeTypes.size(); index++)
        {
            var probeType = probeTypes.getString(index);

            unreachableObjects.forEach(id -> runningProbes.get(probeType).remove(id));

            downObjects.forEach(id -> runningProbes.get(probeType).remove(id));

            runningProbes.get(probeType).remove(result.getLong(Metric.METRIC_OBJECT));                                  // for instance down
        }
    }

    private void discard(JsonObject event, String key, String message, String status)
    {
        LOGGER.warn(message);

        correlationLogs.remove(key);

        event.put(STATUS, status).put(MESSAGE, message);

        vertx.eventBus().send(replyTopic, event);
    }

    private void deserialize(Map<String, Object> entries, Set<Long> filters)
    {
        entries.forEach((key, items) ->
        {

            if (key.equalsIgnoreCase("correlations"))
            {
                var values = (Map<String, Map<String, Map<String, List<Object>>>>) items;

                values.forEach((id, objects) ->
                {
                    filters.add(id.contains(VALUE_SEPARATOR) ? CommonUtil.getLong(id.split(VALUE_SEPARATOR_WITH_ESCAPE)[0]) : CommonUtil.getLong(id));

                    correlations.put(id, objects);
                });
            }
            else if (key.equalsIgnoreCase("running.probes"))
            {
                var values = (Map<String, List<Object>>) items;

                for (var probeType : values.entrySet())
                {
                    runningProbes.put(probeType.getKey(), new HashSet<>());

                    for (var id : probeType.getValue())
                    {
                        runningProbes.get(probeType.getKey()).add(id);
                    }
                }
            }
            else if (key.equalsIgnoreCase("down.objects"))
            {
                var values = (Map<Long, List<String>>) items;

                for (var entry : values.entrySet())
                {
                    var id = CommonUtil.getLong(entry.getKey());

                    downObjects.computeIfAbsent(id, value -> new ArrayList<>()).addAll(entry.getValue());
                }
            }
        });
    }

    private void update(Map<String, List<Object>> context, Set<Long> objects)
    {
        if (context.get(AIOpsConstants.CORRELATED_DOWN_OBJECTS) != null)
        {
            context.get(AIOpsConstants.CORRELATED_DOWN_OBJECTS).stream().map(CommonUtil::getLong).forEach(objects::add);
        }

        if (context.get(AIOpsConstants.CORRELATED_UNREACHABLE_OBJECTS) != null)
        {
            context.get(AIOpsConstants.CORRELATED_UNREACHABLE_OBJECTS).stream().map(CommonUtil::getLong).forEach(objects::add);
        }
    }

    private void complete(Promise<JsonObject> future, String key, String probeType, JsonArray correlationLogs, String instanceName, JsonObject event)
    {
        future.future().onComplete(asyncResult ->
        {
            try
            {
                var result = asyncResult.result();

                result.put(AIOpsConstants.CORRELATION_LOGS, correlationLogs);

                this.correlationLogs.get(key).remove(probeType); // remove current correlation context

                if (this.correlationLogs.get(key).isEmpty())
                {
                    this.correlationLogs.remove(key);
                }

                if (CommonUtil.debugEnabled())
                {
                    LOGGER.debug(String.format("correlation %s result for object %s is %s", probeType, result.getString(AIOpsObject.OBJECT_NAME), CommonUtil.removeSensitiveFields(result, true).encodePrettily()));
                }

                var correlation = new HashMap<String, List<Object>>();

                var items = result.getJsonArray(AIOpsConstants.CORRELATED_DOWN_OBJECTS);

                var correlationKey = key;

                if (items != null && !items.isEmpty())
                {
                    correlationKey = instanceName != null ? CommonUtil.getString(items.getLong(0) + VALUE_SEPARATOR + instanceName) : CommonUtil.getString(items.getLong(0));

                    correlation.put(AIOpsConstants.CORRELATED_DOWN_OBJECTS, items.stream().toList());

                    runningProbes.get(probeType).addAll(items.stream().toList());

                    dirty = true;
                }

                items = result.getJsonArray(AIOpsConstants.CORRELATED_UNREACHABLE_OBJECTS);

                if (items != null && !items.isEmpty())
                {
                    correlation.put(AIOpsConstants.CORRELATED_UNREACHABLE_OBJECTS, items.stream().toList());

                    runningProbes.get(probeType).addAll(items.stream().toList());

                    dirty = true;
                }

                items = result.getJsonArray(AIOpsConstants.CORRELATED_UP_OBJECTS);

                if (items != null && !items.isEmpty())
                {
                    correlation.put(AIOpsConstants.CORRELATED_UP_OBJECTS, items.stream().toList());
                }

                items = result.getJsonArray(AIOpsConstants.CORRELATED_APPLICATIONS);

                if (items != null && !items.isEmpty())
                {
                    correlation.put(AIOpsConstants.CORRELATED_APPLICATIONS, items.stream().toList());
                }

                if (result.containsKey(STATUS) && result.getString(STATUS).equalsIgnoreCase(STATUS_ABORT))
                {
                    result.getJsonArray(AIOpsConstants.CORRELATION_OBJECTS).stream().forEach(item -> this.runningProbes.get(probeType).remove(item));

                    dirty = true;
                }
                else if ((probeType.equalsIgnoreCase(OBJECT_DOWN.getName()) || probeType.equalsIgnoreCase(AIOpsConstants.AvailabilityProbeType.INSTANCE_DOWN.getName())))
                {
                    correlations.computeIfAbsent(correlationKey, value -> new HashMap<>()).put(probeType, correlation); // add correlated objects/applications for future references ex.. on interface up probe type we will use correlated objects/applications which we correlated on same interface down time

                    remove(new JsonArray().add(OBJECT_UP.getName()).add(INSTANCE_UP.getName()), result);                                             // need to remove entry from opposite probe type, otherwise it will not give proper result when object/instance gets up

                    dirty = true;
                }

                result.remove(AIOpsConstants.CORRELATION_OBJECTS);

                vertx.eventBus().send(replyTopic, result);
            }
            catch (Exception exception)
            {
                LOGGER.error(exception);

                vertx.eventBus().send(replyTopic, event.put(STATUS, STATUS_FAIL)
                        .put(ERROR_CODE, ErrorCodes.ERROR_CODE_INTERNAL_ERROR)
                        .put(MESSAGE, String.format(ErrorMessageConstants.CORRELATION_FAILED, event.getString(AIOpsObject.OBJECT_NAME), ErrorMessageConstants.INTERNAL_ERROR))
                        .put(ERROR, CommonUtil.formatStackTrace(exception.getStackTrace())));
            }
        });
    }

    /**
     * Runs correlation for "up" events (when objects or instances come back online).
     * <p>
     * This method processes "up" events (object up or instance up) and retrieves previously
     * correlated resources from the corresponding "down" event. It performs the following tasks:
     * <ul>
     *   <li>Retrieves previously correlated objects from the opposite probe type (e.g., OBJECT_DOWN for OBJECT_UP)</li>
     *   <li>Adds these objects to the running probes for the current probe type</li>
     *   <li>Adds the objects to the IDs array for further processing</li>
     *   <li>Cleans up correlation data that is no longer needed</li>
     * </ul>
     * <p>
     * This method is essential for ensuring that when components come back online,
     * all the dependent components that were previously marked as affected are properly
     * re-evaluated and their status is updated.
     *
     * @param event               The event containing information about the up object/instance
     * @param key                 The correlation key (object ID + optional instance name)
     * @param correlationLogs     Logs of correlation activities
     * @param future              Promise to complete with correlation results
     * @param correlatedProbeType The opposite probe type (e.g., OBJECT_DOWN for OBJECT_UP)
     * @param ids                 Array to populate with correlated object IDs
     * @param category            The category of the object (network, server, etc.)
     */
    private void runCorrelation(JsonObject event, String key, JsonArray correlationLogs, Promise<JsonObject> future, String correlatedProbeType, JsonArray ids, NMSConstants.Category category)
    {
        try
        {
            var probeType = event.getString(AIOpsConstants.AVAILABILITY_PROBE_TYPE);

            var id = event.getLong(Metric.METRIC_OBJECT);

            var probes = event.getJsonArray(AIOpsConstants.CORRELATION_RUNNING_PROBES);

            if (correlations.containsKey(key) && correlations.get(key).containsKey(correlatedProbeType))
            {
                var correlatedObject = correlations.get(key).get(correlatedProbeType);

                if (correlatedObject.get(AIOpsConstants.CORRELATED_DOWN_OBJECTS) != null)
                {
                    correlatedObject.get(AIOpsConstants.CORRELATED_DOWN_OBJECTS).stream().filter(item -> !probes.contains(CommonUtil.getLong(item))).map(CommonUtil::getLong).forEach(item ->
                    {
                        // Do not add object to UP probe type ,which has instance down correlation then instance up request of that object will be discarded.

                        if (!downObjects.containsKey(item) || downObjects.get(item).isEmpty())
                        {
                            this.runningProbes.get(probeType).add(item);
                        }

                        ids.add(item);
                    });
                }

                if (correlatedObject.get(AIOpsConstants.CORRELATED_UNREACHABLE_OBJECTS) != null)
                {
                    correlatedObject.get(AIOpsConstants.CORRELATED_UNREACHABLE_OBJECTS).stream().filter(item -> !probes.contains(CommonUtil.getLong(item))).map(CommonUtil::getLong).forEach(item ->
                    {
                        // Do not add object to UP probe type ,which has instance down correlation then instance up request of that object will be discarded.

                        if (!downObjects.containsKey(item) || downObjects.get(item).isEmpty())
                        {
                            this.runningProbes.get(probeType).add(item);
                        }

                        ids.add(item);

                    });
                }

                correlatedObject.remove(AIOpsConstants.CORRELATED_DOWN_OBJECTS);

                correlatedObject.remove(AIOpsConstants.CORRELATED_UNREACHABLE_OBJECTS);

                correlatedObject.remove(AIOpsConstants.CORRELATED_UP_OBJECTS);

                correlatedObject.remove(AIOpsConstants.CORRELATED_APPLICATIONS);

                dirty = true;

                var valid = false;

                for (var correlation : correlations.get(key).entrySet())
                {
                    for (var probe : correlation.getValue().entrySet())
                    {
                        if (!probe.getValue().isEmpty())
                        {
                            valid = true;

                            break;
                        }
                    }
                }

                if (!valid)
                {
                    correlations.remove(key);
                }
            }
            else
            {
                if (CommonUtil.debugEnabled())
                {
                    LOGGER.debug(String.format("key %s not found for %s", key, probeType));
                }
            }

            if (!ids.contains(id) && correlatedProbeType.equalsIgnoreCase(OBJECT_DOWN.getName()))
            {
                ids.add(id);
            }

            if (downObjects.containsKey(id))
            {
                downObjects.get(id).remove(event.getString(AIOpsConstants.CORRELATION_INSTANCE));           // remove instance when it gets up

                if (downObjects.get(id).isEmpty())
                {
                    downObjects.remove(id);

                    dirty = true;
                }
            }

            LOGGER.info(String.format("enabling metrics for : %s with key : %s", ObjectConfigStore.getStore().getObjectNames(ids), key));

            // if correlated resources available in down probe type then mark all that resources up
            for (var index = 0; index < ids.size(); index++)
            {
                enableMetric(ids.getLong(index), correlationLogs, correlatedProbeType, event);
            }

            event.put(AIOpsConstants.CORRELATED_UP_OBJECTS, new JsonArray(new ArrayList<Long>(1)).add(id)).put(AIOpsConstants.CORRELATION_LOGS, correlationLogs);

            if (correlatedProbeType.equalsIgnoreCase(AIOpsConstants.AvailabilityProbeType.OBJECT_UP.getName()))
            {
                future.complete(event);
            }
            else if (category == NMSConstants.Category.SERVER) // means process up event
            {
                getApps(event, id).onComplete(result ->
                {
                    if (result.succeeded())
                    {
                        var apps = result.result();

                        event.put(AIOpsConstants.CORRELATED_APPLICATIONS, apps);

                        if (!apps.isEmpty())
                        {
                            var items = MetricConfigStore.getStore().getItemsByObject(id);

                            for (var index = 0; index < apps.size(); index++)
                            {
                                enableMetric(items, apps.getString(index), correlationLogs, id);
                            }
                        }
                    }
                    else
                    {
                        correlationLogs.add(String.format("failed to find applications for object %s with process %s and reason: %s at %s", event.getString(AIOpsObject.OBJECT_NAME), event.getString(AIOpsConstants.CORRELATION_INSTANCE), CommonUtil.formatStackTrace(result.cause().getStackTrace()), DateTimeUtil.timestamp()));
                    }

                    future.complete(event);
                });
            }
            else
            {
                future.complete(event);
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }

    private Future<JsonArray> getApps(JsonObject event, long id)
    {
        var promise = Promise.<JsonArray>promise();

        vertx.eventBus().<JsonObject>request(EventBusConstants.EVENT_DEPENDENCY_QUERY,
                new JsonObject().mergeIn(event).put(EVENT_TYPE, EVENT_AVAILABILITY_CORRELATION).put(AIOpsConstants.ENTITY_ID, id).put(EVENT_TYPE, EVENT_AVAILABILITY_CORRELATION)
                        .put(AIOpsConstants.RECURSIVE_DEPENDENCIES, NO).put(AIOpsConstants.DEPENDENCY_FORMAT, AIOpsConstants.DependencyFormat.TOP_BOTTOM_HIERARCHY.getName()).put(EVENT_REPLY, YES).put(AIOpsConstants.DEPENDENCY_TYPE, AIOpsConstants.DependencyType.LOCAL_DOMAIN.getName()),
                deliveryOptions,
                reply ->
                {
                    if (reply.succeeded())
                    {
                        var apps = new JsonArray();

                        var response = reply.result().body();

                        var result = response.getJsonObject(RESULT);

                        if (result != null)
                        {
                            var items = result.getJsonArray(AIOpsConstants.DEPENDENCY_CHILDREN);

                            if (items != null)
                            {
                                for (var index = 0; index < items.size(); index++)
                                {
                                    var item = items.getJsonObject(index);

                                    if (AIOpsConstants.hasApplicationLevel(CommonUtil.getByteValue(item.getValue(AIOpsConstants.DEPENDENCY_LEVEL)))
                                            && CommonUtil.isNotNullOrEmpty(item.getString(AIOpsConstants.DEPENDENCY_CONNECTED_LINK))
                                            && item.getString(AIOpsConstants.DEPENDENCY_CONNECTED_LINK).equalsIgnoreCase(event.getString(AIOpsConstants.CORRELATION_INSTANCE)) // connected link is same as requested correlation instance
                                            && !apps.contains(item.getString(AIOpsConstants.DEPENDENCY_DESTINATION)))
                                    {
                                        apps.add(item.getString(AIOpsConstants.DEPENDENCY_DESTINATION));
                                    }
                                }
                            }
                        }

                        promise.complete(apps);
                    }
                    else
                    {
                        LOGGER.error(reply.cause());

                        promise.fail(reply.cause());
                    }
                });

        return promise.future();
    }

    // This method is used to run correlation for down objects/instances
    private void runCorrelation(JsonObject event, long id, String probeType, NMSConstants.Category category, JsonArray correlationLogs, Promise<JsonObject> promise)
    {
        // first get Immediate Neighbors
        // from that, if any immediate neighbour (parent/child) is down then go for n level nodes

        var parents = new ArrayList<Long>();

        var children = new ArrayList<Long>();

        event.put(AIOpsConstants.CORRELATION_OBJECTS, new JsonArray());

        getImmediateNeighbors(new JsonObject().mergeIn(event).put(AIOpsConstants.ENTITY_ID, id).put(AIOpsConstants.RECURSIVE_DEPENDENCIES, NO),
                parents, children, correlationLogs, event, category)
                .onComplete(response ->
                        probeObjectAvailability(event, parents, children, correlationLogs, probeType, category).onComplete(result ->
                        {
                            if (result.succeeded())
                            {
                                // 1st condition : either immediate parent not found or all immediate parent are up
                                // 2nd : for instance down do not need to suspend object metrics

                                if (parents.isEmpty() && probeType.equalsIgnoreCase(OBJECT_DOWN.getName()))
                                {
                                    update(event, AIOpsConstants.CORRELATED_DOWN_OBJECTS, event.getLong(Metric.METRIC_OBJECT), correlationLogs);
                                }

                                var futures = new ArrayList<Future<Void>>();

                                futures.add(runCorrelation(event, correlationLogs, probeType, parents, true));

                                futures.add(runCorrelation(event, correlationLogs, probeType, children, false));

                                // improvement : if in any scenario, we failed to find parent then abort the request and enable all the metric

                                Future.join(futures).onComplete(asyncResponse ->
                                {
                                    if (asyncResponse.succeeded())
                                    {
                                        if (probeType.equalsIgnoreCase(AIOpsConstants.AvailabilityProbeType.INSTANCE_DOWN.getName()))
                                        {
                                            downObjects.computeIfAbsent(id, value -> new ArrayList<>()).add(event.getString(AIOpsConstants.CORRELATION_INSTANCE));

                                            runCorrelation(event, correlationLogs, id, category).onComplete(asyncResult -> promise.complete(asyncResult.result()));
                                        }
                                        else
                                        {
                                            updateInstanceStatus(event, correlationLogs);

                                            runCorrelation(correlationLogs, id, AIOpsConstants.getMetricPlugin(event.getString(AIOpsObject.OBJECT_TYPE)), event);

                                            promise.complete(event);
                                        }
                                    }
                                    else
                                    {
                                        LOGGER.warn(asyncResponse.cause().getMessage());

                                        event.put(MESSAGE, asyncResponse.cause().getMessage()).put(STATUS, STATUS_ABORT);

                                        promise.complete(event);
                                    }
                                });
                            }
                            else
                            {
                                LOGGER.warn(result.cause().getMessage());

                                event.put(MESSAGE, result.cause().getMessage()).put(STATUS, STATUS_ABORT);

                                promise.complete(event);
                            }
                        }));
    }

    //This method is used to run correlation for down instances
    private Future<JsonObject> runCorrelation(JsonObject event, JsonArray correlationLogs, long id, NMSConstants.Category category)
    {
        var promise = Promise.<JsonObject>promise();

        // means process down so unreachable metric
        if (category == NMSConstants.Category.SERVER)
        {
            getApps(event, id).onComplete(result ->
            {
                var instanceName = event.getString(AIOpsConstants.CORRELATION_INSTANCE);

                if (result.succeeded())
                {
                    var apps = result.result();

                    event.put(AIOpsConstants.CORRELATED_APPLICATIONS, apps);

                    if (!apps.isEmpty()) // if applications is found then mark all the metrics to suspend
                    {
                        var metrics = MetricConfigStore.getStore().getItemsByObject(id);

                        for (var index = 0; index < apps.size(); index++)
                        {
                            suspendMetric(metrics, apps.getString(index), correlationLogs, id);
                        }
                    }
                }
                else
                {
                    correlationLogs.add(String.format("failed to find applications for object %s with process %s and reason: %s at %s", event.getString(AIOpsObject.OBJECT_NAME), instanceName, CommonUtil.formatStackTrace(result.cause().getStackTrace()), DateTimeUtil.timestamp()));
                }

                promise.complete(event);
            });
        }
        else
        {
            promise.complete(event);
        }
        return promise.future();
    }

    //This method is used to run correlation for down objects
    private void runCorrelation(JsonArray correlationLogs, long id, String metricPlugin, JsonObject event)
    {
        try
        {
            if (!metricPlugin.equalsIgnoreCase(EMPTY_VALUE) && event.getString(AIOpsConstants.AVAILABILITY_PROBE_TYPE).equalsIgnoreCase(OBJECT_DOWN.getName()))                                      // if instance is down , no need to suspend rest of object's metric group
            {
                var metric = MetricConfigStore.getStore().getItem(MetricConfigStore.getStore().getItemByMetricPlugin(id, metricPlugin));

                if (metric != null)
                {
                    // list down objects
                    if (metric.getJsonObject(Metric.METRIC_CONTEXT) != null && metric.getJsonObject(Metric.METRIC_CONTEXT).getJsonArray(NMSConstants.OBJECTS) != null)
                    {
                        //compare object in object collection
                        var targets = new JsonArray(metric.getJsonObject(Metric.METRIC_CONTEXT).getJsonArray(NMSConstants.OBJECTS).stream()
                                .filter(item -> JsonObject.mapFrom(item).getString(AIOpsObject.OBJECT_IP) != null).map(item -> JsonObject.mapFrom(item).getString(AIOpsObject.OBJECT_IP)).toList());

                        if (!targets.isEmpty())
                        {
                            // if object is available and category is server then mark down all metric state to suspended on that object
                            ObjectConfigStore.getStore().getItemsByValues(AIOpsObject.OBJECT_IP, targets).stream().filter(item -> JsonObject.mapFrom(item).getString(AIOpsObject.OBJECT_CATEGORY).equalsIgnoreCase(NMSConstants.Category.SERVER.getName()))
                                    .map(item -> JsonObject.mapFrom(item).getLong(ID)).toList().forEach(object -> suspendMetric(object, correlationLogs, false));
                        }
                    }
                    else if (metric.getString(Metric.METRIC_CATEGORY).equalsIgnoreCase(NMSConstants.Category.CLOUD.getName()))
                    {
                        // if object is available and category is server then mark down all metric state to suspended on that object
                        ObjectConfigStore.getStore().getItemsByValue(AIOpsObject.OBJECT_IP, event.containsKey(AIOpsObject.OBJECT_IP) ? event.getString(AIOpsObject.OBJECT_IP) : event.getString(AIOpsObject.OBJECT_TARGET))
                                .stream().filter(item -> JsonObject.mapFrom(item).getString(AIOpsObject.OBJECT_CATEGORY).equalsIgnoreCase(NMSConstants.Category.SERVER.getName()))
                                .map(item -> JsonObject.mapFrom(item).getLong(ID)).toList().forEach(object -> suspendMetric(object, correlationLogs, false));
                    }
                    else
                    {
                        correlationLogs.add(String.format("failed to find virtual instances/metrics %s of object %s at %s", metricPlugin, ObjectConfigStore.getStore().getObjectName(id), DateTimeUtil.timestamp()));
                    }
                }
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }

    //This method is used to run correlation for all connected parent/child objects
    // parent  -> if parent objects
    private Future<Void> runCorrelation(JsonObject event, JsonArray correlationLogs, String probeType, List<Long> objects, boolean parent)
    {
        var promise = Promise.<Void>promise();

        try
        {
            if (!objects.isEmpty())
            {
                var parents = new HashMap<Long, List<Long>>();

                var children = new HashMap<Long, List<Long>>();

                var futures = new ArrayList<Future<Void>>();

                if (parent)
                {
                    for (var object : objects)
                    {
                        parents.put(object, new ArrayList<>());

                        futures.add(buildParentDependencies(new JsonObject().mergeIn(event).put(AIOpsConstants.ENTITY_ID, object), object, parents, correlationLogs));
                    }
                }
                else
                {
                    for (var object : objects)
                    {
                        children.put(object, new ArrayList<>());

                        futures.add(buildChildrenDependencies(new JsonObject().mergeIn(event).put(AIOpsConstants.ENTITY_ID, object), object, children, object, correlationLogs));
                    }
                }

                Future.join(futures).onComplete(asyncResult ->
                {
                    var probes = new ArrayList<Future<Void>>();

                    probeHierarchies(event, parents, correlationLogs, probeType, probes, true);

                    probeHierarchies(event, children, correlationLogs, probeType, probes, false);

                    Future.all(probes).onComplete(result ->
                    {

                        if (result.succeeded())
                        {
                            promise.complete();
                        }
                        else
                        {
                            promise.fail(result.cause());
                        }

                    });
                });
            }
            else
            {
                promise.complete();
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);

            promise.fail(exception);
        }

        return promise.future();
    }

    //prepare qualified objects and check ping availability based on requested parent/child objects
    private void probeHierarchies(JsonObject event, Map<Long, List<Long>> objects, JsonArray correlationLogs, String probeType, List<Future<Void>> futures, boolean parent)
    {

        for (var entry : objects.entrySet())
        {
            var qualifiedObjects = new ArrayList<Long>();

            qualifiedObjects.add(entry.getKey());

            qualifiedObjects.addAll(entry.getValue());

            futures.add(probeObjectAvailability(event, qualifiedObjects, correlationLogs, probeType, false, parent));
        }
    }

    /**
     * Checks ping availability for parent and child objects during correlation.
     * <p>
     * This method verifies the availability of parent and child objects by sending
     * ping probes to them. It helps determine which objects are actually down versus
     * those that appear down due to network connectivity issues.
     * <p>
     * The method performs the following tasks:
     * <ul>
     *   <li>Skips ping checks for cloud objects (as they don't respond to ICMP)</li>
     *   <li>Sends ping probes to parent objects to check their availability</li>
     *   <li>Sends ping probes to child objects to check their availability</li>
     *   <li>Checks for pending requests that might affect the availability status</li>
     * </ul>
     * <p>
     * This method is crucial for accurate correlation, as it helps distinguish between
     * objects that are truly down and those that appear down due to parent failures.
     *
     * @param event           The event containing correlation information
     * @param parents         List of parent object IDs to check
     * @param children        List of child object IDs to check
     * @param correlationLogs Logs of correlation activities
     * @param probeType       The type of probe (OBJECT_DOWN, INSTANCE_DOWN, etc.)
     * @param category        The category of the object (network, server, etc.)
     * @return A Future that completes when the availability checks are done
     */
    private Future<Void> probeObjectAvailability(JsonObject event, List<Long> parents, List<Long> children, JsonArray correlationLogs, String probeType, Category category)
    {
        var promise = Promise.<Void>promise();

        var futures = new ArrayList<Future<Void>>();

        var previousObjects = new ArrayList<>(parents);

        if (category != NMSConstants.Category.CLOUD) // ignore ping check on cloud objects
        {
            if (!parents.isEmpty())
            {
                futures.add(probeObjectAvailability(event, parents, correlationLogs, probeType, true, true));
            }

            if (!children.isEmpty())
            {
                futures.add(probeObjectAvailability(event, children, correlationLogs, probeType, true, false));
            }
        }

        Future.join(futures).onComplete(result -> checkPendingRequest(event, parents, previousObjects, event.getLong(Metric.METRIC_OBJECT), correlationLogs, promise, true));

        return promise.future();
    }

    private void checkPendingRequest(JsonObject event, List<Long> downObjects, List<Long> previousObjects, long downObject, JsonArray logs, Promise<Void> promise, boolean immediateParent)
    {
        if (event.getString(AIOpsConstants.AVAILABILITY_PROBE_TYPE).equalsIgnoreCase(OBJECT_DOWN.getName()))
        {
            var futures = new ArrayList<Future<Void>>();

            var items = new HashMap<Long, JsonArray>();         // id -> interfaces

            filter(previousObjects, downObjects);

            logs.add(String.format("querying %s for %s at %s", ObjectConfigStore.getStore().getObjectNames(new JsonArray(previousObjects)), ObjectConfigStore.getStore().getObjectName(downObject), DateTimeUtil.timestamp()));

            for (var object : previousObjects)
            {
                futures.add(query(ObjectConfigStore.getStore().getItem(object), downObject, items));
            }

            Future.join(futures).onComplete(result -> checkStatus(event, items, downObjects, logs, promise, immediateParent));
        }
        else
        {
            promise.complete();
        }
    }

    private void checkStatus(JsonObject event, Map<Long, JsonArray> items, List<Long> downObjects, JsonArray logs, Promise<Void> promise, boolean immediateParent)
    {
        if (!items.isEmpty())
        {
            var futures = new ArrayList<Future<Void>>();

            for (var item : items.entrySet())
            {
                futures.add(getInstanceStatus(item.getKey(), item.getValue(), logs).onFailure(throwable -> getInstanceStatus(item.getKey(), item.getValue())));
            }

            Future.join(futures).onComplete(result ->
            {
                var down = false;

                for (var item : items.entrySet())
                {
                    if (item.getValue() != null && !item.getValue().isEmpty())
                    {
                        down = true;

                        route(item.getKey(), item.getValue());
                    }
                }

                if (!down)                                                                              // if all up interface then normal flow continues..
                {
                    if (immediateParent)
                    {
                        promise.complete();
                    }
                    else
                    {
                        markDownLastLeafObject(downObjects, downObjects.getLast(), event, logs, promise, true);
                    }
                }
                else
                {
                    promise.fail(AIOpsConstants.PENDING_REQUEST_FOUND);
                }
            });
        }
        else
        {
            promise.complete();
        }
    }

    // It will check the live status of instance (interface) using runbook
    private Future<Void> getInstanceStatus(long id, JsonArray objects, JsonArray correlationsLogs)
    {
        var promise = Promise.<Void>promise();

        try
        {
            var metric = MetricConfigStore.getStore().getItem(MetricConfigStore.getStore().getItemByMetricPlugin(id, MetricPlugin.SNMP_INTERFACE.getName()));

            if (metric != null)
            {
                var event = new JsonObject()
                        .mergeIn(ObjectConfigStore.getStore().getItem(id))
                        .mergeIn(RunbookPluginConfigStore.getStore().getItem(Runbook.RunbookPluginId.INTERFACE_STATUS.getName()))
                        .put(Metric.METRIC_CREDENTIAL_PROFILE, metric.getLong(Metric.METRIC_CREDENTIAL_PROFILE))
                        .put(Metric.METRIC_TYPE, metric.getString(Metric.METRIC_TYPE))
                        .put(EVENT_ID, CommonUtil.newEventId())
                        .put(EventBusConstants.EVENT_TYPE, EventBusConstants.EVENT_RUNBOOK)
                        .put(EventBusConstants.EVENT_REPLY, YES)
                        .put(AIOpsObject.OBJECT_DISCOVERY_METHOD, NMSConstants.DiscoveryMethod.REMOTE.name())
                        .put(RUNBOOK_PLUGIN_VARIABLES, new JsonObject().put(OBJECTS, objects))
                        .put(RUNBOOK_PLUGIN_ENTITIES, new JsonArray(new ArrayList<>(1)).add(id));

                correlationsLogs.add(String.format("checking status for %s of %s at %s", objects, ObjectConfigStore.getStore().getObjectName(id), DateTimeUtil.timestamp()));

                Bootstrap.vertx().eventBus().<JsonObject>request(EVENT_RUNBOOK, event, reply ->
                {

                    try
                    {
                        if (reply.succeeded())
                        {
                            var contexts = reply.result().body().getJsonArray(EVENT_REPLY_CONTEXTS);

                            if (contexts != null && !contexts.isEmpty())
                            {
                                var result = contexts.getJsonObject(0);

                                if (result.getString(STATUS).equalsIgnoreCase(STATUS_SUCCEED))
                                {
                                    result = result.getJsonObject(RESULT);

                                    if (CommonUtil.traceEnabled())
                                    {
                                        LOGGER.trace(String.format("runbook result : %s for object %s", result.encodePrettily(), ObjectConfigStore.getStore().getObjectName(id)));

                                    }
                                    result.stream().forEach(item ->
                                    {
                                        if (CommonUtil.getString(item.getValue()).equalsIgnoreCase(STATUS_UP))
                                        {
                                            correlationsLogs.add(String.format("%s is up at %s", item.getKey(), DateTimeUtil.timestamp()));

                                            objects.remove(item.getKey());
                                        }
                                        else
                                        {
                                            correlationsLogs.add(String.format("%s is down %s", item.getKey(), DateTimeUtil.timestamp()));
                                        }
                                    });

                                    promise.complete();
                                }
                                else
                                {
                                    if (CommonUtil.debugEnabled())
                                    {
                                        LOGGER.debug(String.format("interface status runbook failed : reason : %s ", result.getString(MESSAGE)));
                                    }

                                    promise.fail(result.getString(MESSAGE));
                                }
                            }
                            else
                            {
                                if (CommonUtil.debugEnabled())
                                {
                                    LOGGER.debug("runbook failed, reply contexts not found");
                                }

                                promise.fail("runbook failed, reply contexts not found");
                            }
                        }
                        else
                        {
                            LOGGER.error(reply.cause());

                            promise.fail(reply.cause());
                        }
                    }
                    catch (Exception exception)
                    {
                        LOGGER.error(exception);

                        promise.fail(exception);
                    }
                });
            }
            else
            {
                promise.complete();
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);

            promise.fail(exception);
        }

        return promise.future();
    }

    // In worst case,If runbook failed to respond, then we'll make decision base on previous status
    private void getInstanceStatus(long id, JsonArray objects)
    {
        var items = ObjectStatusCacheStore.getStore().getInstanceItems(id, false);

        if (items != null)
        {
            items.forEach((key, value) ->
            {
                var tokens = key.split(INSTANCE_SEPARATOR)[1].split(DASH_SEPARATOR);

                var instanceName = tokens[tokens.length - 1];

                if (objects.contains(instanceName) && value.equalsIgnoreCase(STATUS_UP))
                {
                    objects.remove(instanceName);
                }
            });
        }
    }

    private void route(long id, JsonArray interfaces)
    {
        LOGGER.info(String.format("%s are down interfaces of %s", interfaces, ObjectConfigStore.getStore().getObjectName(id)));

        var item = ObjectConfigStore.getStore().getItem(id);

        var context = new JsonObject().put(INTERFACE, new JsonArray());

        for (var index = 0; index < interfaces.size(); index++)
        {
            var tokens = interfaces.getString(index).split(DASH_SEPARATOR);

            if (!downObjects.containsKey(id) || !downObjects.get(id).contains(tokens[1]))                                                                              // avoid duplicate request
            {
                context.getJsonArray(INTERFACE).add(new JsonObject()
                        .put(INTERFACE, interfaces.getString(index))
                        .put(INTERFACE_NAME, tokens[0])
                        .put(INTERFACE + INSTANCE_SEPARATOR + STATUS, STATUS_DOWN)
                        .put(AIOpsObject.OBJECT_NAME, tokens[1])
                        .put(STATUS, STATUS_DOWN));
            }
            else
            {
                if (CommonUtil.debugEnabled())
                {
                    LOGGER.debug(String.format("request already sent of %s interface of %s object", interfaces.getString(index), item.getString(AIOpsObject.OBJECT_NAME)));
                }
            }
        }

        Bootstrap.vertx().eventBus().send(EVENT_METRIC_POLICY, new JsonObject()
                .put(Metric.METRIC_OBJECT, id).put(Metric.METRIC_TYPE, item.getString(AIOpsObject.OBJECT_TYPE))
                .put(PLUGIN_ID, ObjectManagerCacheStore.getStore().getPluginIdByMetricPlugin(NMSConstants.MetricPlugin.SNMP_INTERFACE.getName()))
                .put(AIOpsObject.OBJECT_GROUPS, item.getJsonArray(AIOpsObject.OBJECT_GROUPS)).put(AIOpsObject.OBJECT_ID, item.getInteger(AIOpsObject.OBJECT_ID))
                .put(Metric.METRIC_PLUGIN, MetricPlugin.AVAILABILITY.getName()).put(AIOpsObject.OBJECT_CATEGORY, item.getString(AIOpsObject.OBJECT_CATEGORY))
                .put(RESULT, context));
    }

    private Future<Void> query(JsonObject item, long downObject, Map<Long, JsonArray> objects)
    {
        var promise = Promise.<Void>promise();

        vertx.eventBus().<JsonObject>request(EVENT_DEPENDENCY_QUERY,
                new JsonObject().put(AIOpsConstants.ENTITY_ID, CommonUtil.getLong(item.getLong(ID))).put(AIOpsObject.OBJECT_CATEGORY, NMSConstants.Category.valueOfName(item.getString(AIOpsObject.OBJECT_CATEGORY)))
                        .put(AIOpsObject.OBJECT_TYPE, item.getString(AIOpsObject.OBJECT_TYPE))
                        .put(EVENT_REPLY, YES).put(AIOpsConstants.DEPENDENCY_TYPE, AIOpsConstants.DependencyType.LOCAL_DOMAIN.getName())
                        .put(AIOpsConstants.RECURSIVE_DEPENDENCIES, NO)
                        .put(AIOpsConstants.DEPENDENCY_FORMAT, AIOpsConstants.DependencyFormat.TOP_BOTTOM_HIERARCHY.getName()),
                deliveryOptions,
                reply ->
                {
                    try
                    {
                        if (reply.succeeded())
                        {
                            var responses = reply.result().body().getJsonObject(RESULT).getJsonArray(AIOpsConstants.DEPENDENCY_CHILDREN);

                            var metricId = MetricConfigStore.getStore().getItemByMetricPlugin(item.getLong(ID), NMSConstants.MetricPlugin.SNMP_INTERFACE.getName());

                            if (metricId != NOT_AVAILABLE)
                            {
                                var metric = MetricConfigStore.getStore().getItem(metricId);

                                var items = MetricConfigStore.getStore().getObjects(metric.getJsonObject(Metric.METRIC_CONTEXT), AIOpsObject.OBJECT_NAME, INTERFACE);

                                for (var index = 0; index < responses.size(); index++)
                                {
                                    var response = responses.getJsonObject(index);

                                    if (response.getLong(AIOpsConstants.DEPENDENCY_DESTINATION).equals(downObject))
                                    {
                                        var instance = items.getString(response.getString(AIOpsConstants.DEPENDENCY_CONNECTED_LINK));

                                        if (instance != null)
                                        {
                                            objects.computeIfAbsent(response.getLong(AIOpsConstants.DEPENDENCY_SOURCE), value -> new JsonArray()).add(instance);
                                        }
                                        else
                                        {
                                            LOGGER.warn(String.format("%s link not found for %s object", response.getString(AIOpsConstants.DEPENDENCY_CONNECTED_LINK), item.getString(AIOpsObject.OBJECT_NAME)));
                                        }
                                    }
                                }
                            }

                            promise.complete();
                        }
                        else
                        {
                            LOGGER.error(reply.cause());

                            promise.fail(reply.cause());
                        }
                    }
                    catch (Exception exception)
                    {
                        LOGGER.error(exception);

                        promise.fail(exception);
                    }
                });

        return promise.future();
    }

    private long filter(List<Long> previous, List<Long> objects)
    {
        for (var object : objects)
        {
            previous.remove(object);
        }

        return !previous.isEmpty() ? previous.getFirst() : DUMMY_ID;
    }


    /**
     * Checks the availability of objects by sending ping probes.
     * <p>
     * This method verifies the availability of objects by sending ping probes using
     * a runbook. It can operate in two modes:
     * <ul>
     *   <li>Immediate neighbor probe mode: Checks ping availability for immediate neighbors only</li>
     *   <li>Full probe mode: Checks ping availability for all specified objects</li>
     * </ul>
     * <p>
     * The method performs the following tasks:
     * <ul>
     *   <li>Retrieves the ping runbook configuration</li>
     *   <li>Creates a correlation context with the necessary parameters</li>
     *   <li>Executes the ping probe against the specified objects</li>
     *   <li>For parent objects, identifies the last reachable object in the path</li>
     *   <li>For child objects, marks the last leaf object as down if necessary</li>
     * </ul>
     * <p>
     * This method is crucial for determining the actual network connectivity status
     * of objects during correlation, helping to distinguish between network failures
     * and actual device failures.
     *
     * @param event                  The event containing correlation information
     * @param objects                List of object IDs to check
     * @param correlationLogs        Logs of correlation activities
     * @param probeType              The type of probe (OBJECT_DOWN, INSTANCE_DOWN, etc.)
     * @param immediateNeighborProbe Whether to check only immediate neighbors (true) or all objects (false)
     * @param parent                 Whether the objects are parents (true) or children (false)
     * @return A Future that completes when the availability checks are done
     */
    private Future<Void> probeObjectAvailability(JsonObject event, List<Long> objects, JsonArray correlationLogs, String probeType, boolean immediateNeighborProbe, boolean parent)
    {
        // It will simply ping the objects by runbook

        var promise = Promise.<Void>promise();

        var runbook = RunbookPluginConfigStore.getStore().getItem(Runbook.RunbookPluginId.PING.getName());

        if (runbook != null)
        {
            if (immediateNeighborProbe)
            {
                var correlation = new JsonObject().mergeIn(event).mergeIn(runbook)
                        .put(RUNBOOK_PLUGIN_ENTITIES, new JsonArray(new ArrayList<>(objects)))
                        .put(EventBusConstants.EVENT_REPLY, YES)
                        .put(EventBusConstants.EVENT_ID, CommonUtil.newEventId()).put(EventBusConstants.EVENT_TYPE, EventBusConstants.EVENT_RUNBOOK);

                runPingProbe(correlation, probeType, correlationLogs, objects, false, true).onComplete(result -> promise.complete());
            }
            else
            {
                var correlationContext = new JsonObject().mergeIn(event).mergeIn(runbook)
                        .put(EventBusConstants.EVENT_REPLY, YES)
                        .put(EventBusConstants.EVENT_ID, CommonUtil.newEventId()).put(EventBusConstants.EVENT_TYPE, EventBusConstants.EVENT_RUNBOOK);

                if (parent)
                {
                    var previous = new ArrayList<>(objects);

                    correlationContext.put(RUNBOOK_PLUGIN_ENTITIES, new JsonArray(objects));

                    runPingProbe(correlationContext, probeType, correlationLogs, objects, true, true).onComplete(result ->
                    {
                        if (!objects.isEmpty())
                        {
                            if (probeType.equalsIgnoreCase(OBJECT_DOWN.getName()) && previous.size() != objects.size())
                            {
                                var lastUpObject = filter(previous, objects);

                                var parents = new ArrayList<Long>(1);                        // for last up object/parent will check its connected interface status with child

                                parents.add(lastUpObject);

                                checkPendingRequest(event, objects, parents, objects.getLast(), correlationLogs, promise, false);
                            }
                            else
                            {
                                markDownLastLeafObject(objects, objects.getLast(), event, correlationLogs, promise, true);
                            }
                        }
                        else
                        {
                            promise.complete();
                        }
                    });
                }
                else
                {
                    var firstConnectedObject = objects.getFirst(); // first connected child object

                    correlationContext.put(RUNBOOK_PLUGIN_ENTITIES, new JsonArray(new ArrayList<Long>(1)).add(firstConnectedObject));

                    runPingProbe(correlationContext, probeType, correlationLogs, objects, false, false).onComplete(asyncResult ->
                    {
                        if (objects.contains(firstConnectedObject)) // means first object is down
                        {
                            correlationContext.put(RUNBOOK_PLUGIN_ENTITIES, new JsonArray(objects));

                            runPingProbe(correlationContext, probeType, correlationLogs, objects, true, false).onComplete(result ->
                            {
                                if (result.succeeded())
                                {
                                    markDownLastLeafObject(objects, firstConnectedObject, event, correlationLogs, promise, false);
                                }
                                else
                                {
                                    promise.complete();
                                }
                            });
                        }
                        else
                        {
                            promise.complete();
                        }
                    });
                }
            }
        }
        else
        {
            LOGGER.warn("failed to find ping runbook...");

            promise.complete();
        }

        return promise.future();
    }

    // update metric state and correlation state based on requested correlated object
    private void markDownLastLeafObject(List<Long> objects, long leafObject, JsonObject event, JsonArray correlationLogs, Promise<Void> promise, boolean parent)
    {
        if (parent)
        {
            update(event, AIOpsConstants.CORRELATED_DOWN_OBJECTS, leafObject, correlationLogs).onComplete(result -> promise.complete());

            if (leafObject != event.getLong(Metric.METRIC_OBJECT))
            {
                update(event, AIOpsConstants.CORRELATED_UNREACHABLE_OBJECTS, event.getLong(Metric.METRIC_OBJECT), correlationLogs);
            }
        }
        else
        {
            update(event, AIOpsConstants.CORRELATED_UNREACHABLE_OBJECTS, leafObject, correlationLogs);
        }

        for (var object : objects)
        {
            update(event, AIOpsConstants.CORRELATED_UNREACHABLE_OBJECTS, object, correlationLogs);
        }

        if (!parent)
        {
            promise.complete();
        }
    }

    private Future<Void> update(JsonObject event, String correlationState, long id, JsonArray correlationLogs)
    {
        var promise = Promise.<Void>promise();

        try
        {
            // for cyclic dependency parent is qualified in child as well as in parent also so need this condition to remove
            if (correlationState.equalsIgnoreCase(AIOpsConstants.CORRELATED_DOWN_OBJECTS) && event.getJsonArray(AIOpsConstants.CORRELATED_UNREACHABLE_OBJECTS).contains(id))
            {
                event.getJsonArray(AIOpsConstants.CORRELATED_UNREACHABLE_OBJECTS).remove(id);
            }

            if (!event.getJsonArray(correlationState).contains(id) && !event.getJsonArray(correlationState.equalsIgnoreCase(AIOpsConstants.CORRELATED_DOWN_OBJECTS) ? AIOpsConstants.CORRELATED_UNREACHABLE_OBJECTS : AIOpsConstants.CORRELATED_DOWN_OBJECTS).contains(id) && !event.getJsonArray(AIOpsConstants.CORRELATED_UP_OBJECTS).contains(id))
            {
                LOGGER.info(String.format("updating status for object :%s  with status ; %s ", ObjectConfigStore.getStore().getItem(id), correlationState.equalsIgnoreCase(AIOpsConstants.CORRELATED_DOWN_OBJECTS) ? STATUS_DOWN : STATUS_UNREACHABLE));

                ObjectStatusCacheStore.getStore().updateItem(id, correlationState.equalsIgnoreCase(AIOpsConstants.CORRELATED_DOWN_OBJECTS) ? STATUS_DOWN : STATUS_UNREACHABLE, DateTimeUtil.currentSeconds());

                event.getJsonArray(correlationState).add(id);

                suspendMetric(id, correlationLogs, correlationState.equalsIgnoreCase(AIOpsConstants.CORRELATED_DOWN_OBJECTS));

                var object = new JsonObject().mergeIn(event).mergeIn(ObjectConfigStore.getStore().getItem(id));

                if (object != null)
                {
                    object.put(Metric.METRIC_OBJECT, object.getLong(ID));

                    // if unreachable object's is esxi / cloud instances find it's connected objects provisioned into system or not and if it's provisioned down them also
                    runCorrelation(correlationLogs, object.getLong(ID), AIOpsConstants.getMetricPlugin(object.getString(AIOpsObject.OBJECT_TYPE)), object);

                    if (!event.getLong(Metric.METRIC_OBJECT).equals(id))
                    {
                        updateInstanceStatus(object, correlationLogs);
                    }
                }

                if (correlationState.equalsIgnoreCase(AIOpsConstants.CORRELATED_DOWN_OBJECTS) && !event.getLong(Metric.METRIC_OBJECT).equals(id))
                {
                    vertx.eventBus().<JsonObject>request(EventBusConstants.EVENT_DEPENDENCY_QUERY,
                            new JsonObject().mergeIn(event).put(EVENT_REPLY, YES).put(EVENT_TYPE, EVENT_AVAILABILITY_CORRELATION).put(APIConstants.ENTITY_ID, id).put(AIOpsConstants.DEPENDENCY_TYPE, AIOpsConstants.DependencyType.LOCAL_DOMAIN.getName())
                                    .put(AIOpsConstants.DEPENDENCY_FORMAT, AIOpsConstants.DependencyFormat.TOP_BOTTOM_HIERARCHY.getName()),
                            deliveryOptions,
                            reply ->
                            {
                                var children = new ArrayList<Long>();

                                try
                                {
                                    if (reply.succeeded())
                                    {
                                        var result = reply.result().body().getJsonObject(RESULT);

                                        if (result != null)
                                        {
                                            qualifyChildren(result, event, children, true, true, correlationLogs, event.getJsonArray(AIOpsConstants.CORRELATION_RUNNING_PROBES));
                                        }
                                    }

                                    var context = new JsonObject().mergeIn(event).mergeIn(RunbookPluginConfigStore.getStore().getItem(Runbook.RunbookPluginId.PING.getName()))
                                            .put(EventBusConstants.EVENT_REPLY, YES).put(RUNBOOK_PLUGIN_ENTITIES, new JsonArray(children))
                                            .put(EventBusConstants.EVENT_ID, CommonUtil.newEventId()).put(EventBusConstants.EVENT_TYPE, EventBusConstants.EVENT_RUNBOOK);

                                    runPingProbe(context, event.getString(AIOpsConstants.AVAILABILITY_PROBE_TYPE), correlationLogs, children, false, true).onComplete(asyncResult ->
                                    {
                                        correlationLogs.add(String.format("updating children %s of parent %s at %s", ObjectConfigStore.getStore().getObjectNames(new JsonArray(new ArrayList<>(children))), ObjectConfigStore.getStore().getObjectName(id), DateTimeUtil.timestamp()));

                                        this.runningProbes.get(event.getString(AIOpsConstants.AVAILABILITY_PROBE_TYPE)).addAll(children);

                                        children.forEach(child -> update(event, AIOpsConstants.CORRELATED_UNREACHABLE_OBJECTS, child, correlationLogs));

                                        promise.complete();
                                    });

                                }
                                catch (Exception exception)
                                {
                                    LOGGER.error(exception);
                                }
                            });
                }
                else
                {
                    promise.complete();
                }
            }
            else
            {
                promise.complete();
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);

            promise.fail(exception);
        }

        return promise.future();
    }

    // This method is used to run ping probe
    // bulkRequest -> true -> all objects probe and false -> only one directly connected object
    private Future<Void> runPingProbe(JsonObject event, String probeType, JsonArray correlationLogs, List<Long> objects, boolean bulkRequest, boolean parent)
    {
        var promise = Promise.<Void>promise();

        var probes = this.runningProbes.get(probeType);

        vertx.eventBus().<JsonObject>request(EventBusConstants.EVENT_RUNBOOK,
                event,
                deliveryOptions,
                reply ->
                {
                    try
                    {
                        if (reply.succeeded())
                        {
                            var responses = reply.result().body().getJsonArray(EventBusConstants.EVENT_REPLY_CONTEXTS);

                            if (responses != null && !responses.isEmpty())
                            {
                                for (var index = 0; index < responses.size(); index++)
                                {
                                    var response = responses.getJsonObject(index);

                                    if (response.getString(STATUS).equalsIgnoreCase(STATUS_SUCCEED))
                                    {
                                        event.getJsonArray(AIOpsConstants.CORRELATED_UP_OBJECTS).add(response.getLong(Metric.METRIC_OBJECT));

                                        probes.remove(response.getLong(Metric.METRIC_OBJECT)); // remove up objects from running correlations

                                        correlationLogs.add(String.format("object %s is up at %s", response.getString(AIOpsObject.OBJECT_NAME), DateTimeUtil.timestamp()));
                                    }
                                    else
                                    {
                                        correlationLogs.add(String.format("object %s is down/unreachable at %s", response.getString(AIOpsObject.OBJECT_NAME), DateTimeUtil.timestamp()));
                                    }

                                    event.getJsonArray(AIOpsConstants.CORRELATION_OBJECTS).add(response.getLong(Metric.METRIC_OBJECT));
                                }
                            }
                            else
                            {
                                LOGGER.info("Invalid runbook response");
                            }
                        }
                        else
                        {
                            LOGGER.error(reply.cause());

                            correlationLogs.add(String.format("getting exception %s for objects %s availability", reply.cause(), objects));
                        }

                        var iterator = objects.iterator();

                        var up = false;

                        while (iterator.hasNext())
                        {
                            var object = iterator.next();

                            if (bulkRequest)
                            {
                                if (parent)
                                {
                                    // if we found any object up so from that all above object will be removed.

                                    if (up)
                                    {
                                        iterator.remove();
                                    }
                                    else if (event.getJsonArray(AIOpsConstants.CORRELATED_UP_OBJECTS).contains(object))
                                    {
                                        up = true;

                                        iterator.remove();
                                    }
                                }
                                else
                                {
                                    if (event.getJsonArray(AIOpsConstants.CORRELATED_UP_OBJECTS).contains(object))
                                    {
                                        iterator.remove();
                                    }
                                }
                            }
                            else if (event.getJsonArray(AIOpsConstants.CORRELATED_UP_OBJECTS).contains(object))
                            {
                                iterator.remove();
                            }
                        }
                    }
                    catch (Exception exception)
                    {
                        LOGGER.error(exception);
                    }
                    finally
                    {
                        promise.complete();
                    }
                });

        return promise.future();
    }

    //This method is used to build n level child from dependencies
    //dependentObjects -> key is first level child object
    private Future<Void> buildChildrenDependencies(JsonObject eventContext, long object, Map<Long, List<Long>> adjunctChildren, long childId, JsonArray correlationLogs)
    {
        var promise = Promise.<Void>promise();

        var probes = eventContext.getJsonArray(AIOpsConstants.CORRELATION_RUNNING_PROBES);

        var probeType = eventContext.getString(AIOpsConstants.AVAILABILITY_PROBE_TYPE);

        this.runningProbes.get(probeType).add(childId);

        correlationLogs.add(String.format("finding n level child for object %s from dependencies at %s", ObjectConfigStore.getStore().getObjectName(childId), DateTimeUtil.timestamp()));

        vertx.eventBus().<JsonObject>request(EventBusConstants.EVENT_DEPENDENCY_QUERY,
                eventContext.put(EVENT_REPLY, YES).put(AIOpsConstants.DEPENDENCY_TYPE, AIOpsConstants.DependencyType.LOCAL_DOMAIN.getName())
                        .put(AIOpsConstants.DEPENDENCY_FORMAT, AIOpsConstants.DependencyFormat.TOP_BOTTOM_HIERARCHY.getName()),
                deliveryOptions,
                reply ->
                {
                    try
                    {
                        var children = new ArrayList<Long>();

                        if (reply.succeeded())
                        {
                            var result = reply.result().body().getJsonObject(RESULT);

                            if (result != null)
                            {
                                qualifyChildren(result, adjunctChildren, object, children, childId, probes, correlationLogs);
                            }
                        }

                        if (!children.isEmpty())
                        {
                            adjunctChildren.get(object).addAll(children);

                            this.runningProbes.get(probeType).addAll(children);

                            correlationLogs.add(String.format("n level child for object %s from dependencies are %s at %s", ObjectConfigStore.getStore().getObjectName(childId), ObjectConfigStore.getStore().getObjectNames(new JsonArray(new ArrayList<>(children))), DateTimeUtil.timestamp()));
                        }
                        else
                        {
                            correlationLogs.add(String.format("n level child not found for object %s from dependencies at %s", ObjectConfigStore.getStore().getObjectName(childId), DateTimeUtil.timestamp()));
                        }
                    }
                    catch (Exception exception)
                    {
                        LOGGER.error(exception);
                    }

                    promise.complete();
                });

        return promise.future();
    }

    private void qualifyChildren(JsonObject result, Map<Long, List<Long>> adjunctChildren, long object, List<Long> children, long childId, JsonArray runningProbes, JsonArray correlationLogs)
    {
        try
        {
            var items = result.getJsonArray(AIOpsConstants.DEPENDENCY_CHILDREN);

            if (items != null && !items.isEmpty())
            {
                for (var index = 0; index < items.size(); index++)
                {
                    var child = items.getJsonObject(index);

                    if (child.getValue(AIOpsConstants.DEPENDENCY_DESTINATION) instanceof Long)
                    {
                        var adjunctChild = child.getLong(AIOpsConstants.DEPENDENCY_DESTINATION);

                        var item = ObjectConfigStore.getStore().getItem(adjunctChild);

                        if (item != null)
                        {
                            if (!checkPingStatus(adjunctChild))
                            {
                                correlationLogs.add(String.format(InfoMessageConstants.PING_IS_DISABLED, item.getString(AIOpsObject.OBJECT_NAME)));

                                break;
                            }

                            // for virtualization (vcenter, hyper-v cluster and citrix xen cluster if switch is connected with them then it's not right way to down that vcenter's esxi so need to check type of that object)
                            if (!NMSConstants.isHCICluster(item.getString(AIOpsObject.OBJECT_TYPE))
                                    && NMSConstants.isHypervisor(item.getString(AIOpsObject.OBJECT_TYPE))
                                    && !NMSConstants.isVirtualizationCluster(item.getString(AIOpsObject.OBJECT_TYPE))
                                    && adjunctChild > DUMMY_ID && object != adjunctChild && !children.contains(adjunctChild) && !adjunctChildren.get(object).contains(adjunctChild) && eligibleChild(adjunctChild, childId, runningProbes))
                            {
                                children.add(adjunctChild);

                                qualifyChildren(child, adjunctChildren, object, children, childId, runningProbes, correlationLogs);
                            }
                        }
                    }
                }
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }

    private void qualifyChildren(JsonObject result, JsonObject event, List<Long> adjunctChildren, boolean recursive, boolean passOverInstance, JsonArray correlationLogs, JsonArray probes)
    {
        try
        {
            var children = result.getJsonArray(AIOpsConstants.DEPENDENCY_CHILDREN);

            if (children != null && !children.isEmpty())
            {
                for (var index = 0; index < children.size(); index++)
                {
                    var child = children.getJsonObject(index);

                    var tokens = child.getString(AIOpsConstants.DEPENDENCY_CONNECTED_LINK).split(DASH_SEPARATOR);

                    if (child.getValue(AIOpsConstants.DEPENDENCY_DESTINATION) instanceof Long
                            && (passOverInstance || (CommonUtil.isNullOrEmpty(event.getString(AIOpsConstants.CORRELATION_INSTANCE)) || (tokens.length > 1 && event.getString(AIOpsConstants.CORRELATION_INSTANCE).equalsIgnoreCase(tokens[1])))))
                    {
                        var adjunctChild = child.getLong(AIOpsConstants.DEPENDENCY_DESTINATION);

                        var item = ObjectConfigStore.getStore().getItem(adjunctChild);

                        if (item != null)
                        {
                            if (!checkPingStatus(adjunctChild))
                            {
                                correlationLogs.add(String.format(InfoMessageConstants.PING_IS_DISABLED, item.getString(AIOpsObject.OBJECT_NAME)));

                                break;
                            }

                            // for virtualization (vcenter, hyper-v cluster and citrix xen cluster if switch is connected with them then it's not right way to down that vcenter's esxi so need to check type of that object)
                            if (!NMSConstants.isHCICluster(item.getString(AIOpsObject.OBJECT_TYPE)) &&
                                    NMSConstants.isHypervisor(item.getString(AIOpsObject.OBJECT_TYPE))
                                    && !NMSConstants.isVirtualizationCluster(item.getString(AIOpsObject.OBJECT_TYPE))
                                    && adjunctChild > DUMMY_ID && !event.getLong(Metric.METRIC_OBJECT).equals(adjunctChild) && !adjunctChildren.contains(adjunctChild) && !probes.contains(adjunctChild))
                            {
                                adjunctChildren.add(adjunctChild);

                                if (recursive)
                                {
                                    qualifyChildren(child, event, adjunctChildren, true, true, correlationLogs, probes);
                                }
                            }
                        }
                    }
                }
            }

        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }

    /**
     * Builds a hierarchy of parent dependencies for an object.
     * <p>
     * This method queries the dependency manager to find all parent objects (upward dependencies)
     * for the specified object. It builds a multi-level hierarchy of parent dependencies,
     * which is essential for understanding the impact of failures in parent components.
     * <p>
     * The method performs the following tasks:
     * <ul>
     *   <li>Adds the parent object to the running probes for the current probe type</li>
     *   <li>Queries the dependency manager for parent dependencies using BOTTOM_TOP_HIERARCHY format</li>
     *   <li>Processes the results to identify valid parent objects</li>
     *   <li>Adds these parent objects to the parents map and running probes</li>
     *   <li>Logs the identified parent objects for correlation tracking</li>
     * </ul>
     * <p>
     * This method is crucial for root cause analysis, as it helps identify whether an object
     * is down due to its own issues or because a parent component has failed.
     *
     * @param eventContext    The event context containing correlation information
     * @param parent          The ID of the parent object for which to build dependencies
     * @param parents         Map where key is the first level parent object and value is a list of its parents
     * @param correlationLogs Logs of correlation activities
     * @return A Future that completes when the parent dependencies have been built
     */
    private Future<Void> buildParentDependencies(JsonObject eventContext, long parent, Map<Long, List<Long>> parents, JsonArray correlationLogs)
    {
        var promise = Promise.<Void>promise();

        var probes = eventContext.getJsonArray(AIOpsConstants.CORRELATION_RUNNING_PROBES);

        var probeType = eventContext.getString(AIOpsConstants.AVAILABILITY_PROBE_TYPE);

        this.runningProbes.get(probeType).add(parent);

        correlationLogs.add(String.format("finding n level parent for object %s from dependencies at %s", ObjectConfigStore.getStore().getObjectName(parent), DateTimeUtil.timestamp()));

        vertx.eventBus().<JsonObject>request(EventBusConstants.EVENT_DEPENDENCY_QUERY,
                eventContext.put(EVENT_REPLY, YES).put(EVENT_TYPE, EVENT_AVAILABILITY_CORRELATION).put(AIOpsConstants.DEPENDENCY_TYPE, AIOpsConstants.DependencyType.LOCAL_DOMAIN.getName()).put(AIOpsConstants.DEPENDENCY_FORMAT, AIOpsConstants.DependencyFormat.BOTTOM_TOP_HIERARCHY.getName()),
                deliveryOptions,
                reply ->
                {
                    try
                    {
                        var adjunctParents = new LinkedList<Long>();

                        if (reply.succeeded())
                        {
                            var result = reply.result().body().getJsonObject(RESULT);

                            if (result != null)
                            {
                                qualifyParents(result, parents, parent, adjunctParents, probes, correlationLogs);
                            }
                        }

                        if (!adjunctParents.isEmpty())
                        {
                            parents.get(parent).addAll(adjunctParents);

                            this.runningProbes.get(probeType).addAll(adjunctParents);

                            correlationLogs.add(String.format("n level parent for object %s from dependencies are %s at %s", ObjectConfigStore.getStore().getObjectName(parent), ObjectConfigStore.getStore().getObjectNames(new JsonArray(new ArrayList<>(adjunctParents))), DateTimeUtil.timestamp()));
                        }
                        else
                        {
                            correlationLogs.add(String.format("n level parent not found for object %s from dependencies at %s", ObjectConfigStore.getStore().getObjectName(parent), DateTimeUtil.timestamp()));
                        }
                    }
                    catch (Exception exception)
                    {
                        LOGGER.error(exception);
                    }

                    promise.complete();
                });

        return promise.future();
    }

    private void qualifyParents(JsonObject result, Map<Long, List<Long>> parents, long parent, LinkedList<Long> adjunctParents, JsonArray runningProbes, JsonArray correlationLogs)
    {
        try
        {
            var items = result.getJsonArray(AIOpsConstants.DEPENDENCY_PARENT);

            if (items != null && !items.isEmpty())
            {
                for (var index = 0; index < items.size(); index++)
                {
                    var item = items.getJsonObject(index);

                    var adjunctParent = CommonUtil.getLong(item.getLong(AIOpsConstants.DEPENDENCY_DESTINATION));

                    if (!checkPingStatus(adjunctParent))
                    {
                        correlationLogs.add(String.format(InfoMessageConstants.PING_IS_DISABLED, ObjectConfigStore.getStore().getObjectName(adjunctParent)));

                        break;
                    }

                    if (adjunctParent > DUMMY_ID && !adjunctParents.contains(adjunctParent) && !parents.get(parent).contains(adjunctParent) && eligibleParent(adjunctParent, runningProbes))
                    {
                        if (parent != adjunctParent)
                        {
                            adjunctParents.add(adjunctParent);

                            qualifyParents(item, parents, parent, adjunctParents, runningProbes, correlationLogs);
                        }
                        else // for cyclic dependency if parent is same
                        {
                            adjunctParents.removeLast();

                            break;
                        }
                    }
                }
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }

    private void qualifyParents(JsonObject result, JsonObject event, List<Long> parents, JsonArray correlationLogs, JsonArray probes)
    {
        try
        {
            var items = result.getJsonArray(AIOpsConstants.DEPENDENCY_PARENT);

            if (items != null && !items.isEmpty())
            {
                for (var index = 0; index < items.size(); index++)
                {
                    var item = items.getJsonObject(index);

                    var parent = CommonUtil.getLong(item.getLong(AIOpsConstants.DEPENDENCY_DESTINATION));

                    if (!checkPingStatus(parent))
                    {
                        correlationLogs.add(String.format(InfoMessageConstants.PING_IS_DISABLED, ObjectConfigStore.getStore().getObjectName(parent)));

                        break;
                    }

                    if (parent > DUMMY_ID && event.getLong(Metric.METRIC_OBJECT) != parent && !parents.contains(parent) && !probes.contains(parent))
                    {
                        parents.add(parent);
                    }
                }
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }

    //This method is used to build immediate neighbors for requested object
    private Future<Void> getImmediateNeighbors(JsonObject context, List<Long> parents, List<Long> children, JsonArray correlationLogs, JsonObject event, NMSConstants.Category category)
    {
        var promise = Promise.<Void>promise();

        // no need to find parent/child for cloud/virtualization/HCI
        if (category != NMSConstants.Category.CLOUD && category != Category.VIRTUALIZATION && category != Category.HCI)
        {
            var futures = new ArrayList<Future<Void>>();

            var probeType = event.getString(AIOpsConstants.AVAILABILITY_PROBE_TYPE);

            // for instance down do not need to go upside and check parent
            if (probeType.equalsIgnoreCase(AIOpsConstants.AvailabilityProbeType.OBJECT_DOWN.getName()))
            {
                futures.add(getImmediateNeighbors(context, event, parents, children, correlationLogs, true, probeType));
            }

            if (NMSConstants.NETWORK_DEVICES.contains(context.getString(AIOpsObject.OBJECT_TYPE)))
            {
                futures.add(getImmediateNeighbors(context, event, parents, children, correlationLogs, false, probeType));
            }

            Future.join(futures).onComplete(result -> promise.complete());
        }
        else
        {
            promise.complete();
        }

        return promise.future();
    }

    private Future<Void> getImmediateNeighbors(JsonObject context, JsonObject event, List<Long> parents, List<Long> children, JsonArray correlationLogs, boolean parent, String probeType)
    {
        var promise = Promise.<Void>promise();

        var objectName = event.getString(AIOpsObject.OBJECT_NAME);

        vertx.eventBus().<JsonObject>request(EventBusConstants.EVENT_DEPENDENCY_QUERY,
                context.put(EVENT_REPLY, YES).put(AIOpsConstants.RECURSIVE_DEPENDENCIES, NO).put(AIOpsConstants.DEPENDENCY_TYPE, AIOpsConstants.DependencyType.LOCAL_DOMAIN.getName())
                        .put(EVENT_TYPE, EVENT_AVAILABILITY_CORRELATION).put(AIOpsConstants.DEPENDENCY_FORMAT, parent ? AIOpsConstants.DependencyFormat.BOTTOM_TOP_HIERARCHY.getName() : AIOpsConstants.DependencyFormat.TOP_BOTTOM_HIERARCHY.getName()),
                deliveryOptions,
                reply ->
                {
                    try
                    {
                        if (reply.succeeded())
                        {
                            var result = reply.result().body().getJsonObject(RESULT);

                            if (parent)
                            {
                                qualifyParents(result, event, parents, correlationLogs, event.getJsonArray(AIOpsConstants.CORRELATION_RUNNING_PROBES));
                            }
                            else
                            {
                                qualifyChildren(result, event, children, false, false, correlationLogs, event.getJsonArray(AIOpsConstants.CORRELATION_RUNNING_PROBES));
                            }

                            if (parent && !parents.isEmpty())
                            {
                                correlationLogs.add(String.format("immediate parents for object %s are %s at %s", objectName, ObjectConfigStore.getStore().getObjectNames(new JsonArray(parents)), DateTimeUtil.timestamp()));

                                runningProbes.get(probeType).addAll(parents);
                            }

                            if (!parent && !children.isEmpty())
                            {
                                correlationLogs.add(String.format("immediate children for object %s are %s at %s", objectName, ObjectConfigStore.getStore().getObjectNames(new JsonArray(children)), DateTimeUtil.timestamp()));

                                runningProbes.get(probeType).addAll(children);
                            }
                        }
                        else
                        {
                            correlationLogs.add(String.format("failed to find immediate %s for object %s, reason: %s at %s", parent ? "parent" : "child", objectName, reply.cause(), DateTimeUtil.timestamp()));
                        }
                    }
                    catch (Exception exception)
                    {
                        LOGGER.error(exception);

                        correlationLogs.add(String.format("failed to find immediate %s for object %s, reason: %s at %s", parent ? "parent" : "child", objectName, exception.getMessage(), DateTimeUtil.timestamp()));
                    }

                    promise.complete();
                });

        return promise.future();
    }

    //This method is used to return all the running correlation objects for requested probe type across different engines
    private Future<JsonArray> getRunningProbes(String probeType)
    {
        var promise = Promise.<JsonArray>promise();

        var probes = switch (AIOpsConstants.AvailabilityProbeType.valueOfName(probeType))
        {
            case OBJECT_UP, INSTANCE_UP ->
                    new JsonArray(new ArrayList<String>(2)).add(AIOpsConstants.AvailabilityProbeType.OBJECT_UP.getName()).add(AIOpsConstants.AvailabilityProbeType.INSTANCE_UP.getName());

            case OBJECT_DOWN, INSTANCE_DOWN ->
                    new JsonArray(new ArrayList<String>(2)).add(OBJECT_DOWN.getName()).add(AIOpsConstants.AvailabilityProbeType.INSTANCE_DOWN.getName());
        };

        vertx.eventBus().<JsonArray>request(config().getString(EVENT_TYPE) + EventBusConstants.EVENT_QUERY,
                new JsonObject().put(EVENT_QUERY_BULK, YES).put(AIOpsConstants.AVAILABILITY_PROBE_TYPE, probes).put(AIOpsConstants.CORRELATED_OBJECT_QUERY, NO),
                deliveryOptions, reply -> promise.complete(reply.result().body()));

        return promise.future();
    }

    // check parent eligibility
    private boolean eligibleParent(long id, JsonArray runningProbes)
    {
        var object = ObjectConfigStore.getStore().getItem(id);

        return object != null && !runningProbes.contains(object.getLong(ID)) && !object.getString(AIOpsObject.OBJECT_CATEGORY).equalsIgnoreCase(NMSConstants.Category.SERVICE_CHECK.getName());
    }

    // check child eligibility
    private boolean eligibleChild(long id, long parent, JsonArray runningProbes)
    {
        var object = ObjectConfigStore.getStore().getItem(id);

        return object != null && !object.getLong(ID).equals(parent) && !runningProbes.contains(object.getLong(ID))
                && !object.getString(AIOpsObject.OBJECT_CATEGORY).equalsIgnoreCase(NMSConstants.Category.SERVICE_CHECK.getName());
    }

    private void updateInstanceStatus(JsonObject event, JsonArray correlationLogs)
    {
        try
        {
            LOGGER.info(String.format("updating status of %s object's instances", event.getString(AIOpsObject.OBJECT_NAME)));

            for (var item : MetricConfigStore.getStore().getItemsByObject(event.getLong(Metric.METRIC_OBJECT)))
            {
                // if base instance plugin than get all child resource of that metric plugin and mark all resources as a down
                updateDiscoverableInstances(event.getLong(Metric.METRIC_OBJECT), item.getString(Metric.METRIC_PLUGIN), item, event, correlationLogs, STATUS_UNREACHABLE);
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }

    private void updateDiscoverableInstances(long id, String metricPlugin, JsonObject item, JsonObject event, JsonArray correlationLogs, String state)
    {
        try
        {
            if (DISCOVERABLE_INSTANCE_PLUGINS.containsKey(metricPlugin))
            {
                var instances = MetricConfigStore.getStore().getMetricInstancesByInstanceType(id, DISCOVERABLE_INSTANCE_PLUGINS.get(metricPlugin));

                var instance = DISCOVERABLE_INSTANCES.get(metricPlugin);

                if (instance != null && !instances.isEmpty())
                {
                    var items = new JsonArray();

                    for (var index = 0; index < instances.size(); index++)
                    {
                        items.add(new JsonObject().put(instance, instances.getString(index)).put(STATUS, state));
                    }

                    if (correlationLogs != null)
                    {
                        correlationLogs.add(String.format("object %s instances %s changed to %s at %s", ObjectConfigStore.getStore().getObjectName(id), instance, state, DateTimeUtil.timestamp()));
                    }

                    var result = new JsonObject().put(instance, items);

                    if (!event.containsKey(AIOpsConstants.INSTANCES))
                    {
                        event.put(AIOpsConstants.INSTANCES, new JsonArray());
                    }

                    event.getJsonArray(AIOpsConstants.INSTANCES).add(result);

                    vertx.eventBus().send(EVENT_METRIC_ENRICHER, new JsonObject().mergeIn(event).mergeIn(item).put(PLUGIN_ID, item.getJsonObject(Metric.METRIC_CONTEXT).getInteger(PLUGIN_ID)).put(AIOpsObject.OBJECT_ID, event.getInteger(AIOpsObject.OBJECT_ID)).put(AIOpsObject.OBJECT_GROUPS, event.getJsonArray(AIOpsObject.OBJECT_GROUPS)).put(RESULT, result));
                }
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }

    }

    //This method is used to suspended metric state to requested applications
    private void suspendMetric(List<JsonObject> items, String app, JsonArray correlationLogs, long id)
    {
        var objectName = ObjectConfigStore.getStore().getObjectName(id);

        items.stream().filter(item -> item.getString(Metric.METRIC_TYPE).equalsIgnoreCase(app))
                .filter(item -> item.getString(Metric.METRIC_STATE).equalsIgnoreCase(NMSConstants.State.ENABLE.name())).toList().forEach(metric ->
                {
                    correlationLogs.add(String.format("object %s metric %s state changed to unreachable and deleting metric from polling scheduler at %s", objectName, metric.getString(Metric.METRIC_NAME), DateTimeUtil.timestamp()));

                    vertx.eventBus().send(EventBusConstants.EVENT_METRIC_SUSPEND, metric);
                });
    }

    //This method is used to enable metric state to requested applications
    private void enableMetric(List<JsonObject> items, String app, JsonArray correlationLogs, long id)
    {
        var objectName = ObjectConfigStore.getStore().getObjectName(id);

        items.stream().filter(item -> item.getString(Metric.METRIC_TYPE).equalsIgnoreCase(app))
                .filter(item -> item.getString(Metric.METRIC_STATE).equalsIgnoreCase(NMSConstants.State.SUSPEND.name())).toList().forEach(metric ->
                {
                    correlationLogs.add(String.format("enabling metric %s of object %s at %s", metric.getString(Metric.METRIC_NAME), objectName, DateTimeUtil.timestamp()));

                    vertx.eventBus().send(EventBusConstants.EVENT_METRIC_ENABLE, metric);
                });
    }

    //This method is used to enable all metric state to requested object
    private void enableMetric(long id, JsonArray correlationLogs, String correlatedProbeType, JsonObject event)
    {
        try
        {
            var objectName = ObjectConfigStore.getStore().getObjectName(id);

            ObjectStatusCacheStore.getStore().updateItem(id, STATUS_UNKNOWN, DateTimeUtil.currentSeconds());

            for (var item : MetricConfigStore.getStore().getItemsByObject(id))
            {
                // only enable that metric with suspend state...do not change in disable metrics
                if (item.getString(Metric.METRIC_STATE).equalsIgnoreCase(NMSConstants.State.SUSPEND.name()))
                {
                    if (correlationLogs != null)
                    {
                        correlationLogs.add(String.format("enabling metric %s of object %s at %s", item.getString(Metric.METRIC_NAME), objectName, DateTimeUtil.timestamp()));
                    }

                    vertx.eventBus().send(EventBusConstants.EVENT_METRIC_ENABLE, item);

                    updateDiscoverableInstances(id, item.getString(Metric.METRIC_PLUGIN), item, event, correlationLogs, STATUS_UNKNOWN);
                }
            }

            if (correlatedProbeType != null)
            {
                runningProbes.get(correlatedProbeType).remove(id);
            }
            else
            {
                for (var correlationProbe : AIOpsConstants.AvailabilityProbeType.values())
                {
                    runningProbes.get(correlationProbe.getName()).remove(id);
                }
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }

    //This method is used to suspend all metric state to requested object
    private void suspendMetric(long id, JsonArray correlationLogs, boolean correlated)
    {
        var objectName = ObjectConfigStore.getStore().getObjectName(id);

        for (var item : MetricConfigStore.getStore().getItemsByObject(id))
        {
            // 1. not down availability metric of parent object and for child or correlated object suspend availability also
            // 2. filter only enable metric and do not change disable/suspend/maintenance metric
            if ((!item.getString(Metric.METRIC_PLUGIN).equalsIgnoreCase(NMSConstants.MetricPlugin.AVAILABILITY.getName()) || !correlated)
                    && item.getString(Metric.METRIC_STATE).equalsIgnoreCase(NMSConstants.State.ENABLE.name()))
            {
                correlationLogs.add(String.format("object %s metric %s state changed to suspended and deleting metric from polling scheduler at %s", objectName, item.getString(Metric.METRIC_NAME), DateTimeUtil.timestamp()));

                vertx.eventBus().send(EventBusConstants.EVENT_METRIC_SUSPEND, item);
            }
        }
    }

    // if PING_CHECK_STATUS is "no" then stop finding parent/children
    // correlation will not work on those objects
    private boolean checkPingStatus(long id)
    {
        var object = ObjectConfigStore.getStore().getItem(id);

        if (object.containsKey(AIOpsObject.OBJECT_CONTEXT))
        {
            return !ObjectConfigStore.getStore().getItem(id).getJsonObject(AIOpsObject.OBJECT_CONTEXT).containsKey(PING_CHECK_STATUS) || ObjectConfigStore.getStore().getItem(id).getJsonObject(AIOpsObject.OBJECT_CONTEXT).getString(PING_CHECK_STATUS).equalsIgnoreCase(YES);
        }
        // for agent , we don't have object.context so , we need to check its agent.status.type
        else if (object.containsKey(AIOpsObject.OBJECT_AGENT) && AgentConfigStore.getStore().existItem(object.getLong(AIOpsObject.OBJECT_AGENT)))
        {
            return AgentConfigStore.getStore().getItem(object.getLong(AIOpsObject.OBJECT_AGENT)).getString(Agent.AGENT_STATUS_TYPE).equalsIgnoreCase(AgentConstants.AgentStatusType.PING.getName());
        }

        return false;
    }

    @Override
    public void stop(Promise<Void> promise)
    {
        eventEngine.stop(vertx, promise);
    }
}
