/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

package com.mindarray;

import com.mindarray.eventbus.*;
import com.mindarray.flow.FlowProcessor;
import com.mindarray.log.LogParser;
import com.mindarray.util.*;
import io.vertx.core.Future;
import io.vertx.core.Promise;
import io.vertx.core.json.JsonObject;

import java.util.ArrayList;

import static com.mindarray.eventbus.EventBusConstants.EVENT_CONFIG_INIT;

public class BootstrapEventProcessor
{
    private static final Logger LOGGER = new Logger(BootstrapEventProcessor.class, GlobalConstants.MOTADATA_SYSTEM, "Bootstrap Event Processor");

    private static void startRemoteEventProcessor()
    {
        Bootstrap.startEngine(new RemoteEventChangeNotificationHandler(), RemoteEventChangeNotificationHandler.class.getSimpleName(), null)
                .onComplete(response ->
                {
                    if (response.succeeded())
                    {
                        var asyncResults = new ArrayList<Future<Object>>();

                        var subscriberPort = CommonUtil.getEventSubscriberPort();

                        for (var index = 0; index < MotadataConfigUtil.getEventSubscribers(); index++)
                        {
                            var promise = Promise.promise();

                            asyncResults.add(promise.future());

                            Bootstrap.startEngine(new RemoteEventSubscriber(subscriberPort + index), RemoteEventSubscriber.class.getSimpleName() + " " + (subscriberPort + index), null).onComplete(result ->
                            {
                                if (result.succeeded())
                                {
                                    promise.complete();
                                }
                                else
                                {
                                    promise.fail(result.cause());
                                }
                            });
                        }
                        Future.join(asyncResults).onComplete(asyncResponse ->
                        {
                            if (asyncResponse.succeeded())
                            {
                                try
                                {
                                    EventBusConstants.setRemoteEventProcessorRegistrationId();

                                    var futures = new ArrayList<Future<Object>>();

                                    var forwarderPort = CommonUtil.getEventPublisherPort();

                                    for (var index = 0; index < MotadataConfigUtil.getEventPublishers(); index++)
                                    {
                                        var promise = Promise.promise();

                                        futures.add(promise.future());

                                        Bootstrap.startEngine(new RemoteEventForwarder(forwarderPort + index), RemoteEventForwarder.class.getSimpleName() + " " + (forwarderPort + index), null).onComplete(asyncResult ->
                                        {
                                            if (asyncResult.succeeded())
                                            {
                                                promise.complete();
                                            }
                                            else
                                            {
                                                promise.fail(asyncResult.cause());
                                            }
                                        });
                                    }
                                    Future.join(futures).onComplete(result ->
                                    {
                                        if (result.failed())
                                        {
                                            Bootstrap.stop(result.cause());
                                        }
                                        else
                                        {
                                            try
                                            {
                                                LOGGER.info(String.format("%s request to register...", Bootstrap.bootstrapType()));

                                                EventBusConstants.register(new CipherUtil());
                                            }
                                            catch (Exception exception)
                                            {
                                                LOGGER.error(exception);
                                            }

                                            LOGGER.info(String.format(InfoMessageConstants.MOTADATA_ENGINE_START_SUCCEEDED, Bootstrap.bootstrapType()));
                                        }
                                    });
                                }
                                catch (Exception exception)
                                {
                                    Bootstrap.stop(exception);
                                }
                            }
                            else
                            {
                                Bootstrap.stop(asyncResponse.cause());
                            }
                        });
                    }
                    else
                    {
                        Bootstrap.stop(response.cause());
                    }
                });

        Bootstrap.vertx().eventBus().<JsonObject>localConsumer(EVENT_CONFIG_INIT, message ->
        {

            if (message.body() != null)
            {
                Bootstrap.startEngine(new LocalEventRouter(EventBusConstants.EVENT_LOG, EventBusConstants.EVENT_SOURCE, MotadataConfigUtil.getLogParsers(), LogParser.class.getCanonicalName(), true, true, EventBusConstants.EventRouter.ROUND_ROBIN), LogParser.class.getSimpleName(), null)
                        .compose(future -> Bootstrap.startEngine(new LocalEventChangeNotificationHandler(), LocalEventChangeNotificationHandler.class.getSimpleName(), null))
                        .compose(future -> Bootstrap.startEngine(new RemoteSessionManager(), RemoteSessionManager.class.getSimpleName(), null))
                        .compose(future -> Bootstrap.startEngine(new HealthUtil(), HealthUtil.class.getSimpleName(), null))
                        .compose(future -> Bootstrap.startWorkerEngine(new JVMStatUtil(), JVMStatUtil.class.getSimpleName(), null))
                        .compose(future -> Bootstrap.startWorkerEngine(new DiagnosticUtil(), DiagnosticUtil.class.getSimpleName(), null))
                        .onComplete(result ->
                        {
                            if (result.failed())
                            {
                                Bootstrap.stop(result.cause());
                            }
                            else
                            {
                                LogUtil.resetLogLevel(MotadataConfigUtil.getLogLevelResetTimerSeconds());

                                if (LicenseUtil.LICENSE_EDITION.get() == LicenseUtil.LicenseEdition.OBSERVABILITY)
                                {
                                    Bootstrap.startEngine(new LocalEventRouter(EventBusConstants.EVENT_FLOW, "peer_ip_src", MotadataConfigUtil.getFlowProcessors(), FlowProcessor.class.getCanonicalName(), true, true, EventBusConstants.EventRouter.ROUND_ROBIN), FlowProcessor.class.getSimpleName(), null).onComplete(asyncResult ->
                                    {

                                        if (asyncResult.succeeded())
                                        {
                                            LOGGER.info(String.format(InfoMessageConstants.MOTADATA_ENGINE_START_SUCCEEDED, Bootstrap.bootstrapType()));
                                        }
                                        else
                                        {
                                            Bootstrap.stop(asyncResult.cause());
                                        }
                                    });
                                }
                                else
                                {
                                    LOGGER.info(String.format(InfoMessageConstants.MOTADATA_ENGINE_START_SUCCEEDED, Bootstrap.bootstrapType()));
                                }
                            }
                        });
            }
        });
    }

    /*
     *   Port details for Agent/RemoteEventProcessor:
     *
     *   event.publisher.port : start from 9449
     *   event.subscriber.port :start from 9444
     * */

    public void start()
    {
        try
        {
            Bootstrap.logSystemConfig();

            PortUtil.init();

            WebClientUtil.init();

            ProcessUtil.setProcessors();

            startRemoteEventProcessor();
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }
}
