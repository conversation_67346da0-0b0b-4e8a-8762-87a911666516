/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

/*
 *     Change Logs:
 *     Date            Author         Notes
 *     2-Apr-2025      Bharat         MOTADATA-5637: Domain Mapping in Flow - Initial Version
 */

package com.mindarray.api;

import com.mindarray.store.FlowDomainMapperConfigStore;
import com.mindarray.util.Logger;

import static com.mindarray.GlobalConstants.MOTADATA_API;

public class FlowDomainMapper extends AbstractAPI
{
    public static final String FLOW_DOMAIN_MAPPER_NAME = "flow.domain.mapper.name";

    public static final String FLOW_DOMAIN_MAPPER_CATEGORY = "flow.domain.mapper.category"; //IP-Addresses

    public static final String FLOW_DOMAIN_MAPPER_GROUP = "flow.domain.mapper.group"; //IP-Addresses

    private static final Logger LOGGER = new Logger(ApplicationMapper.class, MOTADATA_API, "Flow Domain Mapper API");

    public FlowDomainMapper()
    {
        super("flow-domain-mappers", FlowDomainMapperConfigStore.getStore(), LOGGER);
    }
}
