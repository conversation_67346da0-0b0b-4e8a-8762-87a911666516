/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

package com.mindarray.api;

import com.mindarray.Bootstrap;
import com.mindarray.ErrorCodes;
import com.mindarray.ErrorMessageConstants;
import com.mindarray.GlobalConstants;
import com.mindarray.eventbus.EventBusConstants;
import com.mindarray.store.LogParserConfigStore;
import com.mindarray.store.LogParserPluginConfigStore;
import com.mindarray.util.CommonUtil;
import com.mindarray.util.Logger;
import io.vertx.core.Future;
import io.vertx.core.Promise;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import io.vertx.ext.web.Router;
import io.vertx.ext.web.RoutingContext;
import org.apache.http.HttpStatus;

import static com.mindarray.GlobalConstants.*;
import static com.mindarray.api.APIConstants.FILENAME;
import static com.mindarray.api.APIConstants.RESPONSE_CODE;
import static com.mindarray.api.LogParser.LOG_PARSER_NAME;
import static com.mindarray.api.LogParser.LOG_PARSER_PLUGIN;
import static com.mindarray.eventbus.EventBusConstants.*;
import static com.mindarray.eventbus.EventBusConstants.ChangeNotificationType.ADD_LOG_PARSER_PLUGIN;

public class LogParserPlugin extends AbstractAPI
{
    public static final String LOG_PARSER_PLUGIN_NAME = "log.parser.plugin.name";
    public static final String LOG_PARSER_PLUGIN_CONTEXT = "log.parser.plugin.context";
    public static final String LOG_PARSER_PLUGIN_SCRIPT = "log.parser.plugin.script";
    private static final Logger LOGGER = new Logger(LogParserPlugin.class, MOTADATA_API, "Log Parser Plugin API");

    public LogParserPlugin()
    {
        super("log-parser-plugins", LogParserPluginConfigStore.getStore(), new Logger(LogParserPlugin.class, MOTADATA_API, "Log Parser Plugin API"));
    }

    @Override
    public void init(Router router)
    {
        try
        {
            super.init(router);
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }

    @Override
    protected Future<JsonObject> beforeCreate(RoutingContext routingContext)
    {
        var promise = Promise.<JsonObject>promise();

        var error = ErrorMessageConstants.TIMED_OUT;

        try
        {
            var requestBody = routingContext.body().asJsonObject();

            var fileName = requestBody.getString(LOG_PARSER_PLUGIN_NAME).replace("-", "").replace(" ", "");

            Bootstrap.vertx().eventBus().<JsonObject>request(EVENT_CHANGE_LOCAL_NOTIFICATION, requestBody.put(FILENAME, fileName).put(EVENT_REPLY, YES).put(CHANGE_NOTIFICATION_TYPE, ADD_LOG_PARSER_PLUGIN), reply ->
            {

                if (reply.succeeded())
                {
                    var event = reply.result().body();

                    if (STATUS_SUCCEED.equalsIgnoreCase(event.getString(STATUS)))
                    {
                        promise.complete(event);
                    }
                    else
                    {
                        if (event.containsKey(ERROR) && ErrorMessageConstants.LOG_PARSER_INVALID_PLUGIN_METHOD.equalsIgnoreCase(event.getString(ERROR)))
                        {
                            this.send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_BAD_REQUEST).put(GlobalConstants.STATUS, GlobalConstants.STATUS_FAIL)
                                    .put(MESSAGE, ErrorMessageConstants.LOG_PARSER_INVALID_PLUGIN_METHOD).put(ERROR_CODE, ErrorCodes.ERROR_CODE_BAD_REQUEST)
                                    .put(GlobalConstants.ERROR, ErrorMessageConstants.LOG_PARSER_INVALID_PLUGIN_METHOD));

                            promise.fail(error);
                        }
                        else
                        {
                            this.send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_BAD_REQUEST).put(GlobalConstants.STATUS, GlobalConstants.STATUS_FAIL)
                                    .put(MESSAGE, error).put(ERROR_CODE, ErrorCodes.ERROR_CODE_BAD_REQUEST).put(GlobalConstants.ERROR, error));

                            promise.fail(error);
                        }
                    }

                }
                else
                {
                    this.send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_BAD_REQUEST).put(GlobalConstants.STATUS, GlobalConstants.STATUS_FAIL)
                            .put(MESSAGE, error).put(ERROR_CODE, ErrorCodes.ERROR_CODE_BAD_REQUEST).put(GlobalConstants.ERROR, error));

                    promise.fail(error);
                }
            });
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);

            promise.fail(exception.getMessage());
        }

        return promise.future();
    }

    @Override
    protected Future<Void> afterCreate(JsonObject entity, RoutingContext routingContext)
    {
        this.send(routingContext, entity);

        Bootstrap.vertx().eventBus().publish(EventBusConstants.EVENT_CHANGE_NOTIFICATION, entity.put(EventBusConstants.CHANGE_NOTIFICATION_TYPE, ADD_LOG_PARSER_PLUGIN.name()));

        return Future.succeededFuture();
    }

    @Override
    protected Future<Void> afterDelete(JsonObject entity, RoutingContext routingContext)
    {
        this.send(routingContext, entity);

        Bootstrap.vertx().eventBus().publish(EventBusConstants.EVENT_CHANGE_NOTIFICATION, entity.put(EventBusConstants.CHANGE_NOTIFICATION_TYPE, EventBusConstants.ChangeNotificationType.DELETE_LOG_PARSER_PLUGIN.name()));

        return Future.succeededFuture();
    }

    @Override
    protected Future<JsonObject> getReferencesPreHook(RoutingContext routingContext, JsonObject response)
    {
        try
        {
            response = new JsonObject();

            var items = LogParserConfigStore.getStore().getItemsByValue(LOG_PARSER_PLUGIN, CommonUtil.getLong(routingContext.request().getParam(ID)));

            var entities = new JsonArray();

            if (items != null && !items.isEmpty())
            {

                for (var index = 0; index < items.size(); index++)
                {
                    entities.add(new JsonObject().put(LOG_PARSER_NAME, items.getJsonObject(index).getString(LOG_PARSER_NAME)));
                }

                response.put(APIConstants.Entity.LOG_PARSER.getName(), entities);
            }

        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }

        return Future.succeededFuture(response);
    }
}
