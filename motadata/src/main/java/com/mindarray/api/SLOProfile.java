package com.mindarray.api;

import com.mindarray.*;
import com.mindarray.db.DBConstants;
import com.mindarray.store.SLOProfileConfigStore;
import com.mindarray.util.CommonUtil;
import com.mindarray.util.Logger;
import io.vertx.core.Future;
import io.vertx.core.Promise;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import io.vertx.ext.web.Router;
import io.vertx.ext.web.RoutingContext;
import org.apache.http.HttpStatus;

import static com.mindarray.ErrorMessageConstants.INTERNAL_SERVER_EXCEPTION;
import static com.mindarray.GlobalConstants.*;
import static com.mindarray.api.APIConstants.Entity;
import static com.mindarray.api.APIConstants.RESPONSE_CODE;
import static org.apache.http.HttpStatus.SC_BAD_REQUEST;

public class SLOProfile extends AbstractAPI
{
    private static final Logger LOGGER = new Logger(SLOProfile.class, MOTADATA_API, "SLO Profile API");

    public SLOProfile()
    {
        super("slo-profiles", SLOProfileConfigStore.getStore(), LOGGER);
    }

    public static final String SLO_START_DATE = "slo.start.date";
    public static final String SLO_LAST_ACTIVE_CYCLE_START_DATE = "slo.last.active.cycle.start.date";
    public static final String SLO_CONTEXT = "slo.context";
    public static final String SLO_ARCHIVED = "slo.archived";
    public static final String SLO_NAME = "slo.name";
    public static final String SLO_TYPE = "slo.type";

    // keys inside context
    public static final String SLO_FREQUENCY = "slo.frequency";
    public static final String SLO_SEVERITY = "slo.severity";
    public static final String SLO_TARGET = "slo.target";
    public static final String SLO_WARNING = "slo.warning";
    public static final String SLO_OBJECT_TYPE = "slo.object.type";

    @Override
    protected Future<JsonObject> beforeCreate(RoutingContext routingContext)
    {
        var promise = Promise.<JsonObject>promise();

        var items = SLOProfileConfigStore.getStore().getItems();

        var requestBody = routingContext.body().asJsonObject();

        var valid = true;

        for (var index = 0; index < items.size(); index++)
        {
            var item = items.getJsonObject(index);

            if ((!item.containsKey(SLO_ARCHIVED) || item.getString(SLO_ARCHIVED).equalsIgnoreCase(NO)) && item.getString(SLO_NAME).equalsIgnoreCase(requestBody.getString(SLO_NAME)))
            {
                valid = false;

                send(routingContext, new JsonObject().put(STATUS, STATUS_FAIL).put(ERROR_CODE, ErrorCodes.ERROR_CODE_BAD_REQUEST)
                        .put(RESPONSE_CODE, SC_BAD_REQUEST).put(MESSAGE, String.format(ErrorMessageConstants.API_FIELD_UNIQUE_RULE, SLO_NAME)));

                promise.fail(String.format(ErrorMessageConstants.API_FIELD_UNIQUE_RULE, SLO_NAME));

                break;
            }
        }

        if (valid)
        {
            promise.complete(requestBody.put(SLO_ARCHIVED, NO));
        }

        return promise.future();
    }

    @Override
    public void init(Router router)
    {
        try
        {
            super.init(router);
        }

        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }

    @Override
    protected void delete(RoutingContext routingContext)
    {
        try
        {
            var schema = CommonUtil.getEntitySchema(endpoint);

            if (schema != null)
            {
                var table = schema.getString(APIConstants.ENTITY_TABLE);

                this.beforeDelete(routingContext).compose(parameters ->
                        Future.<JsonObject>future(promise ->
                        {
                            var id = CommonUtil.getLong(routingContext.request().getParam(GlobalConstants.ID));

                            var item = this.configStore.getItem(id);

                            if (item != null && !item.isEmpty())
                            {
                                item.getJsonObject(SLO_CONTEXT).put(ENTITIES, new JsonArray());

                                Bootstrap.configDBService().update(table,
                                        new JsonObject().put(DBConstants.FIELD_NAME, GlobalConstants.ID).put(VALUE, id),
                                        item.put(SLO_ARCHIVED, YES),
                                        routingContext.user().principal().getString(User.USER_NAME), routingContext.request().remoteAddress().host(),
                                        result ->
                                        {
                                            if (result.succeeded())
                                            {
                                                if (!result.result().isEmpty())
                                                {
                                                    this.configStore.updateItem(id);

                                                    promise.complete(new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_OK).put(GlobalConstants.STATUS, STATUS_SUCCEED)
                                                            .put(GlobalConstants.MESSAGE, String.format(InfoMessageConstants.ENTITY_DELETED, schema.getString(APIConstants.ENTITY_NAME))).put(ID, id));

                                                }
                                                else
                                                {
                                                    this.send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_BAD_REQUEST)
                                                            .put(GlobalConstants.MESSAGE, String.format(ErrorMessageConstants.ENTITY_DELETE_FAILED, schema.getString(APIConstants.ENTITY_NAME), UNKNOWN)).put(GlobalConstants.STATUS, STATUS_FAIL));

                                                    promise.fail(String.format(ErrorMessageConstants.ENTITY_DELETE_FAILED, schema.getString(APIConstants.ENTITY_NAME), UNKNOWN));
                                                }
                                            }
                                            else
                                            {
                                                this.send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_INTERNAL_SERVER_ERROR)
                                                        .put(GlobalConstants.STATUS, STATUS_FAIL)
                                                        .put(GlobalConstants.MESSAGE, String.format(ErrorMessageConstants.ENTITY_DELETE_FAILED, schema.getString(APIConstants.ENTITY_NAME), result.cause().getMessage()))
                                                        .put(ERROR_CODE, ErrorCodes.ERROR_CODE_INTERNAL_ERROR)
                                                        .put(GlobalConstants.ERROR, CommonUtil.formatStackTrace(result.cause().getStackTrace())));

                                                promise.fail(result.cause());

                                            }
                                        });
                            }
                            else
                            {
                                this.send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_INTERNAL_SERVER_ERROR).put(GlobalConstants.STATUS, GlobalConstants.STATUS_FAIL)
                                        .put(GlobalConstants.MESSAGE, String.format(ErrorMessageConstants.ENTITY_DELETE_FAILED, schema.getString(APIConstants.ENTITY_NAME), String.format(ErrorMessageConstants.ITEM_NOT_FOUND_IN_STORE, Entity.SLO_PROFILE.getName()))));

                                promise.fail(String.format(ErrorMessageConstants.ITEM_NOT_FOUND_IN_STORE, Entity.SLO_PROFILE.getName()));
                            }
                        }).compose(entity -> this.afterDelete(entity, routingContext))
                );
            }

            else
            {
                this.send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_BAD_REQUEST).put(GlobalConstants.STATUS, GlobalConstants.STATUS_FAIL).put(GlobalConstants.MESSAGE, String.format(ErrorMessageConstants.SCHEMA_FILE_NOT_FOUND, endpoint)));
            }
        }

        catch (Exception exception)
        {
            LOGGER.error(exception);

            this.send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_INTERNAL_SERVER_ERROR).put(GlobalConstants.STATUS, GlobalConstants.STATUS_FAIL)
                    .put(ERROR, CommonUtil.formatStackTrace(exception.getStackTrace()))
                    .put(ERROR_CODE, ErrorCodes.ERROR_CODE_INTERNAL_ERROR)
                    .put(MESSAGE, String.format(INTERNAL_SERVER_EXCEPTION, exception.getMessage())));
        }
    }
}
