/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

package com.mindarray.api;

import com.mindarray.config.ConfigConstants;
import com.mindarray.store.ObjectConfigStore;
import com.mindarray.store.StorageProfileConfigStore;
import com.mindarray.util.Logger;
import io.vertx.core.Future;
import io.vertx.core.json.JsonObject;
import io.vertx.ext.web.Router;
import io.vertx.ext.web.RoutingContext;

import java.util.Arrays;
import java.util.Collections;
import java.util.Map;
import java.util.stream.Collectors;

import static com.mindarray.GlobalConstants.MOTADATA_API;

public class StorageProfile extends AbstractAPI
{
    public static final String STORAGE_PROFILE_NAME = "storage.profile.name";
    public static final String STORAGE_PROFILE_PROTOCOL = "storage.profile.protocol";
    public static final String STORAGE_PROFILE_CONTEXT = "storage.profile.context";
    private static final Logger LOGGER = new Logger(StorageProfile.class, MOTADATA_API, "Storage Profile API");

    public StorageProfile()
    {
        super("storage-profiles", StorageProfileConfigStore.getStore(), LOGGER);
    }

    @Override
    public void init(Router router)
    {
        try
        {
            super.init(router);

            router.get("/" + endpoint + "/:id/references").handler(this::getReferences);
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }

    @Override
    protected Future<JsonObject> getReferencesPreHook(RoutingContext routingContext, JsonObject response)
    {
        try
        {
            if (response != null && !response.isEmpty() && response.containsKey(APIConstants.Entity.CONFIGURATION.getName()))
            {
                var entities = response.getJsonArray(APIConstants.Entity.CONFIGURATION.getName());

                for (var index = 0; index < entities.size(); index++)
                {
                    var item = entities.getJsonObject(index);

                    var object = ObjectConfigStore.getStore().getItemByValue(AIOpsObject.OBJECT_ID, item.getInteger(Configuration.CONFIG_OBJECT));

                    ConfigConstants.removeGarbageFields(object);

                    item.mergeIn(object);
                }
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }

        return Future.succeededFuture(response);
    }

    public enum StorageProtocol
    {
        TFTP("TFTP"),
        FTP("FTP"),
        SFTP("SCP/SFTP"),
        LOCAL("LOCAL"),
        NONE("NONE");

        private static final Map<String, StorageProtocol> VALUES = Collections.unmodifiableMap(Arrays.stream(values())
                .collect(Collectors.toMap(StorageProtocol::getName, entity -> entity)));
        private final String name;

        StorageProtocol(String name)
        {
            this.name = name;
        }

        public static StorageProtocol valueOfName(String name)
        {
            return VALUES.get(name);
        }

        public String getName()
        {
            return name;
        }
    }


}
