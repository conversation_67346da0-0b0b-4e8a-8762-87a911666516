/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

/*  Change Logs:
 *  Date			Author		    Notes
 *  23-Apr-2025		sankalp		    MOTADATA-4883 : Added data security based on user group for reference entities
 */

package com.mindarray.api;

import com.mindarray.*;
import com.mindarray.db.DBConstants;
import com.mindarray.store.GroupConfigStore;
import com.mindarray.store.ObjectConfigStore;
import com.mindarray.store.TopologyPluginConfigStore;
import com.mindarray.store.UserConfigStore;
import com.mindarray.util.CommonUtil;
import com.mindarray.util.LicenseUtil;
import com.mindarray.util.Logger;
import io.vertx.core.Future;
import io.vertx.core.Promise;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import io.vertx.ext.web.Router;
import io.vertx.ext.web.RoutingContext;
import org.apache.http.HttpStatus;

import java.util.ArrayList;

import static com.mindarray.GlobalConstants.*;
import static com.mindarray.api.APIConstants.RESPONSE_CODE;
import static com.mindarray.api.User.USER_ID;
import static com.mindarray.api.User.USER_NAME;

public class TopologyPlugin extends AbstractAPI
{
    public static final String TOPOLOGY_PLUGIN_NAME = "topology.plugin.name";
    public static final String TOPOLOGY_PLUGIN_VENDOR = "topology.plugin.vendor";
    public static final String TOPOLOGY_PLUGIN_MAKE_MODEL = "topology.plugin.make.model";
    public static final String TOPOLOGY_PLUGIN_ENTITY_TYPE = "topology.plugin.entity.type";
    public static final String TOPOLOGY_PLUGIN_ENTITIES = "topology.plugin.entities";
    public static final String TOPOLOGY_PLUGIN_TYPE = "topology.plugin.type"; // SNMP/Custom
    public static final String TOPOLOGY_PLUGIN_CONTEXT = "topology.plugin.context";
    public static final String TOPOLOGY_PLUGIN_ENTRY_POINTS = "topology.plugin.entry.points";
    public static final String TOPOLOGY_PLUGIN_CREDENTIAL_PROFILE = "topology.plugin.credential.profile"; //topology.plugin.context
    public static final String TOPOLOGY_PLUGIN_PROTOCOL = "topology.plugin.protocol"; // topology.plugin.context
    public static final String TOPOLOGY_PLUGIN_VARIABLES = "topology.plugin.variables"; // topology.plugin.context
    public static final String TOPOLOGY_PLUGIN_DISCOVERY = "topology.plugin.discovery";
    public static final String TOPOLOGY_PLUGIN_FILTER_TARGET_TYPE = "topology.plugin.filter.target.type";
    public static final String TOPOLOGY_PLUGIN_FILTER_TARGETS = "topology.plugin.filter.targets";
    private static final Logger LOGGER = new Logger(TopologyPlugin.class, MOTADATA_API, "Topology Plugin API");

    public TopologyPlugin()
    {
        super("topology-plugins", TopologyPluginConfigStore.getStore(), LOGGER);
    }

    @Override
    public void init(Router router)
    {
        try
        {
            super.init(router);

            router.get("/" + endpoint + "/:id/references").handler(this::getReferences);

            router.put("/" + endpoint + "/:id/unassign").handler(this::unassign);

            router.put("/" + endpoint + "/:id/assign").handler(this::assign);
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }

    @Override
    protected Future<JsonObject> beforeCreate(RoutingContext routingContext)
    {
        var promise = Promise.<JsonObject>promise();

        if (LicenseUtil.LICENSE_EDITION.get() == LicenseUtil.LicenseEdition.ESSENTIAL)
        {
            promise.fail(String.format(ErrorMessageConstants.ENTITY_CREATE_FAILED, APIConstants.Entity.METRIC_PLUGIN.getName(), "Feature Required Either Hybrid or Observability License"));
        }
        else
        {
            promise.complete(routingContext.body().asJsonObject());
        }

        return promise.future();
    }

    @Override
    protected Future<JsonObject> beforeUpdate(RoutingContext routingContext)
    {
        var requestBody = routingContext.body().asJsonObject();

        if (!requestBody.containsKey(TopologyPlugin.TOPOLOGY_PLUGIN_VARIABLES))
        {
            requestBody.put(DBConstants.GARBAGE_FIELDS, new JsonArray().add(TopologyPlugin.TOPOLOGY_PLUGIN_VARIABLES));
        }

        return Future.succeededFuture(requestBody);
    }

    private void assign(RoutingContext routingContext)
    {
        try
        {
            var schema = CommonUtil.getEntitySchema(endpoint);

            if (schema != null)
            {
                var item = TopologyPluginConfigStore.getStore().getItem(CommonUtil.getLong(routingContext.request().getParam(GlobalConstants.ID)));

                if (item.containsKey(TopologyPlugin.TOPOLOGY_PLUGIN_CONTEXT))
                {
                    item.mergeIn(item.getJsonObject(TopologyPlugin.TOPOLOGY_PLUGIN_CONTEXT));

                    item.remove(TopologyPlugin.TOPOLOGY_PLUGIN_CONTEXT);
                }

                var entities = item.containsKey(TOPOLOGY_PLUGIN_ENTITIES) ? item.getJsonArray(TOPOLOGY_PLUGIN_ENTITIES) : new JsonArray(new ArrayList(1));

                // if entity.type is monitor than monitor id else group id
                var updatedIds = routingContext.body().asJsonObject().getJsonArray(APIConstants.REQUEST_PARAM_IDS);

                var ids = new JsonArray();

                for (var index = 0; index < updatedIds.size(); index++)
                {
                    var id = updatedIds.getLong(index);

                    if (!entities.contains(id))
                    {
                        ids.add(id);
                    }
                }

                if (!ids.isEmpty())
                {
                    updateItem(item, entities.addAll(ids), routingContext).onComplete(result -> this.send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_OK).put(GlobalConstants.STATUS, STATUS_SUCCEED)
                            .put(GlobalConstants.MESSAGE, String.format(InfoMessageConstants.ASSIGN_SUCCEEDED, APIConstants.Entity.TOPOLOGY_PLUGIN.getName()))));
                }
                else
                {
                    this.send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_OK).put(GlobalConstants.STATUS, STATUS_SUCCEED)
                            .put(GlobalConstants.MESSAGE, String.format(InfoMessageConstants.ASSIGN_SUCCEEDED, APIConstants.Entity.TOPOLOGY_PLUGIN.getName())));
                }
            }
            else
            {
                this.send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_BAD_REQUEST).put(ERROR_CODE, ErrorCodes.ERROR_CODE_INTERNAL_ERROR).put(GlobalConstants.STATUS, GlobalConstants.STATUS_FAIL).put(GlobalConstants.MESSAGE, String.format(ErrorMessageConstants.SCHEMA_FILE_NOT_FOUND, endpoint)));
            }
        }
        catch (Exception exception)
        {
            APIUtil.sendResponse(exception, routingContext);
        }
    }

    private void unassign(RoutingContext routingContext)
    {
        try
        {
            var schema = CommonUtil.getEntitySchema(endpoint);

            if (schema != null)
            {
                var item = TopologyPluginConfigStore.getStore().getItem(CommonUtil.getLong(routingContext.request().getParam(GlobalConstants.ID)));

                var changed = false;

                var entities = item.getJsonArray(TOPOLOGY_PLUGIN_ENTITIES);

                if (entities != null)
                {
                    var updatedIds = routingContext.body().asJsonObject().getJsonArray(APIConstants.REQUEST_PARAM_IDS);

                    if (item.containsKey(TopologyPlugin.TOPOLOGY_PLUGIN_CONTEXT))
                    {
                        item.mergeIn(item.getJsonObject(TopologyPlugin.TOPOLOGY_PLUGIN_CONTEXT));

                        item.remove(TopologyPlugin.TOPOLOGY_PLUGIN_CONTEXT);
                    }

                    for (var index = 0; index < updatedIds.size(); index++)
                    {
                        var id = updatedIds.getLong(index);

                        if (entities.contains(id))
                        {
                            changed = true;

                            entities.remove(id);
                        }
                    }
                }

                if (changed)
                {
                    updateItem(item, entities, routingContext).onComplete(result -> this.send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_OK)
                            .put(GlobalConstants.STATUS, STATUS_SUCCEED).put(GlobalConstants.MESSAGE, String.format(InfoMessageConstants.UNASSIGNED_SUCCEEDED, APIConstants.Entity.TOPOLOGY_PLUGIN.getName()))));
                }
                else
                {
                    this.send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_OK)
                            .put(GlobalConstants.STATUS, STATUS_SUCCEED).put(GlobalConstants.MESSAGE, String.format(InfoMessageConstants.UNASSIGNED_SUCCEEDED, APIConstants.Entity.TOPOLOGY_PLUGIN.getName())));
                }
            }
            else
            {
                this.send(routingContext, new JsonObject().put(ERROR_CODE, ErrorCodes.ERROR_CODE_INTERNAL_ERROR).put(RESPONSE_CODE, HttpStatus.SC_BAD_REQUEST).put(GlobalConstants.STATUS, GlobalConstants.STATUS_FAIL).put(GlobalConstants.MESSAGE, String.format(ErrorMessageConstants.SCHEMA_FILE_NOT_FOUND, endpoint)));
            }
        }
        catch (Exception exception)
        {
            APIUtil.sendResponse(exception, routingContext);
        }
    }

    private Future<Void> updateItem(JsonObject item, JsonArray entities, RoutingContext routingContext)
    {
        var promise = Promise.<Void>promise();

        Bootstrap.configDBService().update(DBConstants.TBL_TOPOLOGY_PLUGIN,
                new JsonObject().put(DBConstants.FIELD_NAME, ID).put(VALUE, item.getLong(ID)),
                new JsonObject().put(TOPOLOGY_PLUGIN_ENTITIES, entities),
                routingContext.user().principal().getString(USER_NAME) != null ? routingContext.user().principal().getString(USER_NAME) : DEFAULT_USER,
                routingContext.request().remoteAddress().host() != null ? routingContext.request().remoteAddress().host() : SYSTEM_REMOTE_ADDRESS,
                future -> TopologyPluginConfigStore.getStore().updateItem(item.getLong(ID)).onComplete(result -> promise.complete()));

        return promise.future();
    }

    @Override
    protected Future<JsonObject> getReferencesPreHook(RoutingContext routingContext, JsonObject response)
    {
        try
        {
            var item = TopologyPluginConfigStore.getStore().getItem(CommonUtil.getLong(routingContext.request().getParam(ID)));

            var userGroups = UserConfigStore.getStore().getUserGroupsById(routingContext.user().principal().getLong(ID));

            if (item != null && item.getJsonArray(TOPOLOGY_PLUGIN_ENTITIES) != null && !item.getJsonArray(TOPOLOGY_PLUGIN_ENTITIES).isEmpty())
            {
                if (item.getString(TOPOLOGY_PLUGIN_ENTITY_TYPE).equalsIgnoreCase(APIConstants.Entity.OBJECT.getName()))
                {
                    response.put(APIConstants.Entity.OBJECT.getName(), ObjectConfigStore.getStore().getItems(ObjectConfigStore.getStore().getIdsByGroups(userGroups, item.getJsonArray(TOPOLOGY_PLUGIN_ENTITIES))));
                }
                else
                {
                    response.put(APIConstants.Entity.GROUP.getName(), GroupConfigStore.getStore().getItems(GroupConfigStore.getStore().getQualifiedGroups(item.getJsonArray(TOPOLOGY_PLUGIN_ENTITIES), userGroups)));
                }
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
        return Future.succeededFuture(response);
    }

    @Override
    protected Future<JsonObject> getEntityCountPreHook(JsonObject response)
    {
        try
        {
            var items = TopologyPluginConfigStore.getStore().getItems();

            var userGroups = new JsonArray();

            if (response.containsKey(USER_ID))
            {
                userGroups = UserConfigStore.getStore().getUserGroupsById(response.getLong(USER_ID));
            }

            for (var index = 0; index < items.size(); index++)
            {
                var item = items.getJsonObject(index);

                var id = CommonUtil.getString(item.getLong(ID));

                var references = 0;

                if (item.getJsonArray(TOPOLOGY_PLUGIN_ENTITIES) != null && !item.getJsonArray(TOPOLOGY_PLUGIN_ENTITIES).isEmpty())
                {
                    if (item.getString(TOPOLOGY_PLUGIN_ENTITY_TYPE).equalsIgnoreCase(APIConstants.Entity.OBJECT.getName()))
                    {
                        references = ObjectConfigStore.getStore().getIdsByGroups(userGroups, item.getJsonArray(TOPOLOGY_PLUGIN_ENTITIES)).size();
                    }

                    else
                    {
                        references = GroupConfigStore.getStore().getQualifiedGroups(item.getJsonArray(TOPOLOGY_PLUGIN_ENTITIES), userGroups).size();
                    }
                }

                response.put(id, response.containsKey(id) ? response.getInteger(id) + references : references);
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }

        return Future.succeededFuture(response);
    }
}
