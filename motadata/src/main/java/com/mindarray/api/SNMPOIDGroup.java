/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

/*
 *	Change Logs:
 *	Date			Author			    Notes
 *  28-Feb-2025		Darshan Parmar		MOTADATA-5215: SonarQube Suggestions Resolution
 */
package com.mindarray.api;

import com.mindarray.ErrorCodes;
import com.mindarray.ErrorMessageConstants;
import com.mindarray.GlobalConstants;
import com.mindarray.db.DBConstants;
import com.mindarray.eventbus.EventBusConstants;
import com.mindarray.nms.NMSConstants;
import com.mindarray.store.*;
import com.mindarray.util.CommonUtil;
import com.mindarray.util.Logger;
import io.vertx.core.Future;
import io.vertx.core.Promise;
import io.vertx.core.buffer.Buffer;
import io.vertx.core.json.JsonObject;
import io.vertx.ext.web.RoutingContext;

import java.util.ArrayList;
import java.util.UUID;

import static com.mindarray.GlobalConstants.*;
import static com.mindarray.api.User.USER_NAME;
import static org.apache.http.HttpStatus.SC_BAD_REQUEST;

public class SNMPOIDGroup extends AbstractAPI
{
    public static final String OID_NAME = "oid.name";
    public static final String OID_GROUP_NAME = "oid.group.name";
    public static final String OID = "oid";
    public static final String OID_TYPE = "oid.type";
    public static final String VENDOR = "vendor";
    public static final String PARENT_OID = "parent.oid";
    public static final String PARENT_NAME = "parent.name";
    public static final int MAX_SEARCH = 1000;
    private static final Logger LOGGER = new Logger(SNMPOIDGroup.class, MOTADATA_API, "SNMP OID Group API");

    public SNMPOIDGroup()
    {
        super("snmp-oid-groups", SNMPOIDGroupConfigStore.getStore(), LOGGER);
    }

    @Override
    protected Future<JsonObject> beforeCreate(RoutingContext routingContext)
    {
        routingContext.setBody(Buffer.buffer(routingContext.body().asJsonObject()
                .put(DBConstants.FIELD_TYPE, DBConstants.ENTITY_TYPE_USER)
                .put(NMSConstants.SNMP_OID_GROUP_DEVICE_TYPE, SNMPDeviceCatalogConfigStore.getStore().getItem(routingContext.body().asJsonObject().getLong(SNMPDeviceCatalog.SNMP_DEVICE_CATALOG_ID)).getString(SNMPDeviceCatalog.SNMP_DEVICE_CATALOG_TYPE))
                .put(NMSConstants.SNMP_OID_GROUP_ID, CommonUtil.getString(UUID.randomUUID())).toString()));

        return doPostUniqueNameValidation(routingContext, APIConstants.REQUEST_CREATE);
    }

    @Override
    protected Future<JsonObject> beforeUpdate(RoutingContext routingContext)
    {
        return doPostUniqueNameValidation(routingContext, APIConstants.REQUEST_UPDATE);
    }

    private Future<JsonObject> doPostUniqueNameValidation(RoutingContext routingContext, String requestType)
    {
        var promise = Promise.<JsonObject>promise();

        try
        {
            //In request body one params snmp.device.catalog (Catalog id) required
            var requestBody = routingContext.body().asJsonObject();

            var deviceCatalog = SNMPDeviceCatalogConfigStore.getStore().getItem(requestBody.getLong(SNMPDeviceCatalog.SNMP_DEVICE_CATALOG_ID));

            if (deviceCatalog != null)
            {
                var valid = true;

                //list down all oid groups with requested body device type (ex. type is switch than list down only that oid groups that has switch device type) and compare group name for uniqueness
                var oidGroups = SNMPOIDGroupConfigStore.getStore().getItemsByValue(NMSConstants.SNMP_OID_GROUP_DEVICE_TYPE, deviceCatalog.getString(SNMPDeviceCatalog.SNMP_DEVICE_CATALOG_TYPE));

                for (var index = 0; index < oidGroups.size(); index++)
                {
                    if (valid)
                    {
                        var item = oidGroups.getJsonObject(index);

                        if (item.getString(NMSConstants.SNMP_OID_GROUP_NAME).equalsIgnoreCase(routingContext.body().asJsonObject().getString(NMSConstants.SNMP_OID_GROUP_NAME)))
                        {
                            valid = requestType.equalsIgnoreCase(APIConstants.REQUEST_UPDATE)
                                    && item.getLong(GlobalConstants.ID).equals(CommonUtil.getLong(routingContext.request().getParam(ID)));
                        }
                    }
                }

                if (valid)
                {
                    promise.complete(routingContext.body().asJsonObject());
                }
                else
                {
                    promise.fail(String.format(ErrorMessageConstants.SNMP_OID_GROUP_UNIQUE_ERROR, requestBody.getString(NMSConstants.SNMP_OID_GROUP_NAME), requestBody.getString(NMSConstants.SNMP_OID_GROUP_DEVICE_TYPE)));

                    this.send(routingContext, new JsonObject().put(APIConstants.RESPONSE_CODE, SC_BAD_REQUEST).put(GlobalConstants.STATUS, GlobalConstants.STATUS_FAIL).put(ERROR_CODE, ErrorCodes.ERROR_CODE_INTERNAL_ERROR)
                            .put(GlobalConstants.MESSAGE, String.format(ErrorMessageConstants.SNMP_OID_GROUP_UNIQUE_ERROR, requestBody.getString(NMSConstants.SNMP_OID_GROUP_NAME), requestBody.getString(NMSConstants.SNMP_OID_GROUP_DEVICE_TYPE))));
                }
            }
            else
            {
                promise.fail(String.format(ErrorMessageConstants.ITEM_NOT_FOUND_IN_STORE, APIConstants.Entity.SNMP_OID_GROUP.getName()));

                this.send(routingContext, new JsonObject().put(APIConstants.RESPONSE_CODE, SC_BAD_REQUEST).put(GlobalConstants.STATUS, GlobalConstants.STATUS_FAIL).put(ERROR_CODE, ErrorCodes.ERROR_CODE_NO_ITEM_FOUND).put(GlobalConstants.MESSAGE, String.format(ErrorMessageConstants.ITEM_NOT_FOUND_IN_STORE, APIConstants.Entity.SNMP_OID_GROUP.getName())));
            }
        }
        catch (Exception exception)
        {
            promise.fail(exception.getCause());

            APIUtil.sendResponse(exception, routingContext);
        }

        return promise.future();
    }

    @Override
    protected Future<Void> afterDeleteAll(JsonObject entity, RoutingContext routingContext)
    {
        try
        {
            //list down metrics with deleted oid groups and unprovision it...also delete it from template oid group cache store
            var items = MetricConfigStore.getStore().getItemsByMapMultiValueField(Metric.METRIC_CONTEXT, APIConstants.ENTITY_ID, entity.getJsonArray(APIConstants.REQUEST_PARAM_IDS));

            var metrics = new ArrayList<JsonObject>();

            for (var index = 0; index < items.size(); index++)
            {
                var item = items.getJsonObject(index);

                SNMPTemplateOIDGroupCacheStore.getStore().deleteItem(item.getJsonObject(Metric.METRIC_CONTEXT).getString(NMSConstants.SNMP_OID_GROUP_ID));

                metrics.add(new JsonObject().put(USER_NAME, routingContext.user().principal().getString(USER_NAME)).put(ID, item.getLong(ID)));
            }

            NMSConstants.updateMetrics(metrics, EventBusConstants.EVENT_METRIC_UNPROVISION);
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }

        return super.afterDelete(entity, routingContext);
    }

    @Override
    protected Future<Void> afterCreate(JsonObject entity, RoutingContext routingContext)
    {
        this.send(routingContext, entity);

        updateGroup(entity, APIConstants.REQUEST_CREATE);

        return Future.succeededFuture();
    }

    @Override
    protected Future<JsonObject> afterUpdate(JsonObject entity, RoutingContext routingContext)
    {
        this.send(routingContext, entity);

        updateGroup(entity, APIConstants.REQUEST_UPDATE);

        return Future.succeededFuture();
    }

    private void updateGroup(JsonObject entity, String requestType)
    {
        try
        {
            var item = SNMPOIDGroupConfigStore.getStore().getItem(entity.getLong(ID));

            SNMPTemplateOIDGroupCacheStore.getStore().updateItem(item.getString(NMSConstants.SNMP_OID_GROUP_ID), item); //update cache store

            if (requestType.equalsIgnoreCase(APIConstants.REQUEST_CREATE)) // if new metric group created then provision that metric for assigned objects of that device catalog
            {
                var objects = ObjectConfigStore.getStore().flatItems(AIOpsObject.OBJECT_SNMP_DEVICE_CATALOG, item.getLong(SNMPDeviceCatalog.SNMP_DEVICE_CATALOG_ID), ID);

                if (!objects.isEmpty())
                {
                    NMSConstants.sendSNMPMetricProvisionEvent(new JsonObject().put(NMSConstants.OBJECTS, objects)
                            .put(DBConstants.FIELD_TYPE, DBConstants.ENTITY_TYPE_USER)
                            .put(SNMPDeviceCatalog.SNMP_DEVICE_CATALOG_ID, item.getLong(SNMPDeviceCatalog.SNMP_DEVICE_CATALOG_ID))
                            .put(NMSConstants.SNMP_OID_GROUP, item));
                }
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }

}
