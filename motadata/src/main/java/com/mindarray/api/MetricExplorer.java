/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

/*
 *	Change Logs:
 *	Date			Author		Notes
 *  15-Apr-2025     Bharat      MOTADATA-5822: Metric Explorer Enhancements | Initial Version
 */
package com.mindarray.api;

import com.mindarray.GlobalConstants;
import com.mindarray.store.MetricExplorerConfigStore;
import com.mindarray.util.Logger;
import io.vertx.core.json.Json;
import io.vertx.core.json.JsonObject;
import io.vertx.ext.web.Router;
import io.vertx.ext.web.RoutingContext;
import org.apache.http.HttpStatus;

import static com.mindarray.GlobalConstants.*;
import static com.mindarray.api.APIConstants.RESPONSE_CODE;

public class MetricExplorer extends AbstractAPI
{
    public static final String METRIC_EXPLORER_NAME = "metric.explorer.name";
    public static final String METRIC_EXPLORER_OBJECT_ID = "metric.explorer.object.id";
    public static final String METRIC_EXPLORER_OBJECT_TYPE = "metric.explorer.object.type";
    public static final String METRIC_EXPLORER_GLOBAL_VIEW_ENABLED = "metric.explorer.global.view.enabled";
    private static final Logger LOGGER = new Logger(MetricExplorer.class, MOTADATA_API, "Metric Explorer API");

    public MetricExplorer()
    {
        super("metric-explorers", MetricExplorerConfigStore.getStore(), LOGGER);
    }

    @Override
    public void init(Router router)
    {
        super.init(router);
    }

    @Override
    protected void getAll(RoutingContext routingContext)
    {

        try
        {
            if (routingContext.request().getParam(FILTER) != null)
            {
                var context = JsonObject.mapFrom(Json.decodeValue(routingContext.request().getParam(FILTER)));

                if (context.containsKey(METRIC_EXPLORER_OBJECT_ID) && context.containsKey(METRIC_EXPLORER_OBJECT_TYPE))
                {
                    var items = MetricExplorerConfigStore.getStore().getItems(context.getLong(METRIC_EXPLORER_OBJECT_ID), context.getString(METRIC_EXPLORER_OBJECT_TYPE));

                    this.send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_OK).put(GlobalConstants.STATUS, STATUS_SUCCEED).put(GlobalConstants.RESULT, items));
                }
                else
                {
                    this.send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_OK).put(GlobalConstants.STATUS, STATUS_SUCCEED).put(GlobalConstants.RESULT, MetricExplorerConfigStore.getStore().getItemsByValue(METRIC_EXPLORER_GLOBAL_VIEW_ENABLED, YES)));
                }
            }
            else
            {
                this.send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_OK).put(GlobalConstants.STATUS, STATUS_SUCCEED).put(GlobalConstants.RESULT, MetricExplorerConfigStore.getStore().getItemsByValue(METRIC_EXPLORER_GLOBAL_VIEW_ENABLED, YES)));
            }
        }
        catch (Exception exception)
        {
            APIUtil.sendResponse(exception, routingContext);
        }
    }

}
