/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

package com.mindarray.api;

import com.mindarray.Bootstrap;
import com.mindarray.GlobalConstants;
import com.mindarray.db.DBConstants;
import com.mindarray.store.CredentialProfileConfigStore;
import com.mindarray.store.MailServerConfigStore;
import com.mindarray.util.Logger;
import io.vertx.core.Future;
import io.vertx.core.Promise;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import io.vertx.ext.web.RoutingContext;
import org.apache.http.HttpStatus;

import static com.mindarray.GlobalConstants.*;
import static com.mindarray.api.APIConstants.RESPONSE_CODE;
import static com.mindarray.api.CredentialProfile.CREDENTIAL_PROFILE_CONTEXT;
import static com.mindarray.api.CredentialProfile.DEFAULT_EMAIL_CREDENTIAL_PROFILE;

public class MailServerConfiguration extends AbstractAPI
{
    public static final String MAIL_SERVER_USERNAME = "mail.server.username";
    public static final String MAIL_SERVER_PROTOCOL = "mail.server.protocol";
    public static final String MAIL_SERVER_AUTH_STATUS = "mail.server.authentication";
    public static final String MAIL_SERVER_CREDENTIAL_PROFILE = "mail.server.credential.profile";
    public static final String MAIL_SERVER_PORT = "mail.server.port";
    public static final String MAIL_SERVER_HOST = "mail.server.host";
    public static final String MAIL_SERVER_SENDER = "mail.server.sender";
    public static final String MAIL_SERVER_PASSWORD = "mail.server.password";
    public static final String MAIL_SERVER_PROTOCOL_SSL = "SSL";
    public static final String MAIL_SERVER_PROTOCOL_TLS = "TLS";
    private static final Logger LOGGER = new Logger(MailServerConfiguration.class, MOTADATA_API, "Mail Server Configuration API");

    public MailServerConfiguration()
    {
        super("mail-server-configuration", MailServerConfigStore.getStore(), new Logger(MailServerConfiguration.class, MOTADATA_API, "Mail Server Configuration API"));

    }

    @Override
    protected Future<JsonObject> beforeUpdate(RoutingContext routingContext)
    {
        var promise = Promise.<JsonObject>promise();

        var requestBody = routingContext.body().asJsonObject();

        var item = CredentialProfileConfigStore.getStore().getItem(requestBody.getLong(MAIL_SERVER_CREDENTIAL_PROFILE));

        if (item != null)
        {
            item.getJsonObject(CREDENTIAL_PROFILE_CONTEXT)
                    .put(USERNAME, requestBody.getString(MAIL_SERVER_USERNAME));

            if (DEFAULT_EMAIL_CREDENTIAL_PROFILE == item.getLong(ID))
            {
                item.getJsonObject(CREDENTIAL_PROFILE_CONTEXT).put(PASSWORD, requestBody.getString(MAIL_SERVER_PASSWORD));
            }

            // remove username password from mail config as it's stored in credential profile config
            requestBody.remove(MAIL_SERVER_USERNAME);

            requestBody.remove(MAIL_SERVER_PASSWORD);

            Bootstrap.configDBService().update(DBConstants.TBL_CREDENTIAL_PROFILE,
                    new JsonObject().put(DBConstants.FIELD_NAME, GlobalConstants.ID).put(GlobalConstants.VALUE, requestBody.getLong(MAIL_SERVER_CREDENTIAL_PROFILE)),
                    item, GlobalConstants.DEFAULT_USER, GlobalConstants.SYSTEM_REMOTE_ADDRESS, result ->
                    {
                        if (result.succeeded())
                        {
                            CredentialProfileConfigStore.getStore().updateItem(requestBody.getLong(MAIL_SERVER_CREDENTIAL_PROFILE)).onComplete(
                                    asyncResult ->
                                    {
                                        if (asyncResult.succeeded())
                                        {
                                            promise.complete(requestBody);
                                        }
                                        else
                                        {
                                            promise.fail(asyncResult.cause());

                                            LOGGER.warn(String.format("Error while updating default email credential profile store. Reason : %s", asyncResult.cause()));
                                        }
                                    });
                        }
                        else
                        {
                            promise.fail(result.cause());

                            LOGGER.warn(String.format("Error while updating default email credential profile. Reason : %s", result.cause()));
                        }
                    });
        }

        return promise.future();
    }

    @Override
    protected Future<JsonObject> afterUpdate(JsonObject entity, RoutingContext routingContext)
    {

        this.send(routingContext, entity);

        MailServerConfigStore.getStore().updateStore(); //update mail server config store

        return Future.succeededFuture();
    }

    @Override
    protected Future<Void> afterGetAll(JsonArray entities, RoutingContext routingContext)
    {
        try
        {
            var entity = entities.getJsonObject(0);

            var item = CredentialProfileConfigStore.getStore().getItem(entity.getLong(MAIL_SERVER_CREDENTIAL_PROFILE));

            if (item != null)
            {
                // add username in context as it's in credential profile config

                entity.put(MAIL_SERVER_USERNAME, item.getJsonObject(CREDENTIAL_PROFILE_CONTEXT).getString(USERNAME));

            }

            this.send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_OK).put(GlobalConstants.STATUS, GlobalConstants.STATUS_SUCCEED).put(GlobalConstants.RESULT, entities));

            return Future.succeededFuture();
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);

            return Future.failedFuture("failed while fetching mail configuration");
        }
    }
}
