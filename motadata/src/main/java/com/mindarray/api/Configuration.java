/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

/*
 *     Change Logs:
 *     Date            Author          Notes
 *     7-Apr-2025      Vismit          MOTADATA-5613: Added config operation id in the context for the bulk mail notification tracking
 */

package com.mindarray.api;

import com.mindarray.*;
import com.mindarray.config.ConfigConstants;
import com.mindarray.db.DBConstants;
import com.mindarray.eventbus.EventBusConstants;
import com.mindarray.job.JobScheduler;
import com.mindarray.nms.NMSConstants;
import com.mindarray.store.*;
import com.mindarray.util.CodecUtil;
import com.mindarray.util.CommonUtil;
import com.mindarray.util.Logger;
import com.mindarray.visualization.VisualizationConstants;
import io.vertx.core.Future;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import io.vertx.ext.web.Router;
import io.vertx.ext.web.RoutingContext;
import org.apache.http.HttpStatus;

import java.util.Set;
import java.util.stream.Collectors;

import static com.mindarray.GlobalConstants.*;
import static com.mindarray.api.APIConstants.*;
import static com.mindarray.api.User.USER_NAME;
import static org.apache.http.HttpStatus.SC_BAD_REQUEST;

public class Configuration extends AbstractAPI
{
    public static final String CONFIG_MANAGEMENT_STATUS = "config.management.status";

    public static final String CONFIG_OBJECT = "config.object";
    public static final String CONFIG_CREDENTIAL_PROFILE = "config.credential.profile";
    public static final String CONFIG_STORAGE_PROFILE = "config.storage.profile";
    public static final String CONFIG_LAST_ACTION_PERFORMED = "config.last.action.performed";
    public static final String CONFIG_LAST_ACTION_TIME = "config.last.action.time";
    public static final String CONFIG_LAST_ACTION_STATUS = "config.last.action.status";
    public static final String CONFIG_LAST_BACKUP_STATUS = "config.last.backup.status";
    public static final String CONFIG_LAST_BACKUP_TIME = "config.last.backup.time";
    public static final String CONFIG_LAST_DISCOVERY_STATUS = "config.last.discovery.status";
    public static final String CONFIG_LAST_DISCOVERY_TIME = "config.last.discovery.time";
    public static final String CONFIG_LAST_ACTION_ERROR_MESSAGE = "config.last.action.error.message";
    public static final String CONFIG_CONTEXT = "config.context";
    public static final String CONFIG_CREDENTIAL_STATUS = "config.credential.status";
    public static final String CONFIG_WORK_LOGS = "config.work.logs";
    public static final String CONFIG_TEMPLATE = "config.template";
    public static final String CONFIG_BACKUP_FILE_STARTUP_CURRENT_VERSION = "config.startup.file.current.version";
    public static final String CONFIG_BACKUP_FILE_RUNNING_CURRENT_VERSION = "config.running.file.current.version";
    public static final String CONFIG_BACKUP_FILE_STARTUP_MIN_VERSION = "config.startup.file.min.version";
    public static final String CONFIG_BACKUP_FILE_RUNNING_MIN_VERSION = "config.running.file.min.version";
    public static final String CONFIG_BACKUP_FILE_RUNNING_WITH_STARTUP_CONFLICT_STATUS = "config.running.startup.file.conflict.status";
    public static final String CONFIG_BACKUP_FILE_RUNNING_WITH_BASELINE_CONFLICT_STATUS = "config.running.baseline.file.conflict.status";
    public static final String CONFIG_BACKUP_FILE_BASELINE_VERSION = "config.backup.file.baseline.version";
    public static final String CONFIG_BACKUP_FILE_CONTENT = "config.backup.file.content";
    public static final String CONFIG_BACKUP_TIME = "config.backup.time"; // Used while storing backup result
    public static final String CONFIG_OPERATION_OUTPUT = "config.operation.output";
    public static final String CONFIG_LAST_UPGRADE_STATUS = "config.last.upgrade.status";
    public static final String CONFIG_LAST_UPGRADE_TIME = "config.last.upgrade.time";
    public static final String CONFIG_INFO = "config.info";
    private static final Logger LOGGER = new Logger(Configuration.class, GlobalConstants.MOTADATA_API, "Configuration API");

    public Configuration()
    {
        super("configurations", ConfigurationConfigStore.getStore(), LOGGER);
    }

    @Override
    public void init(Router router)
    {
        try
        {
            // Direct create and delete config will not allow
            super.init(router, Set.of(REQUEST_CREATE, REQUEST_DELETE));

            router.get("/" + endpoint).handler(this::getAll);

            router.get("/" + endpoint + "/:id").handler(this::validate).handler(this::get);

            router.post("/" + endpoint + "/update").handler(this::updateAll); // Update All config

            router.put("/" + endpoint + "/:id/manage").handler(this::updateStatus); // Update manage config status

            router.put("/" + endpoint + "/:id/assign-baseline").handler(this::assignBaseline); // Assign baseline version

            router.put("/" + endpoint + "/:id/remove-baseline").handler(this::removeBaseline); // Remove baseline version

            router.get("/" + endpoint + "/:id/backup-content").handler(this::getFileContent); // Retrieve Backup File Content

            router.post("/" + endpoint + "/execute-operation").handler(this::execute); // Sending re-run discovery, backup, sync and restore request
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }

    @Override
    protected void get(RoutingContext routingContext)
    {
        try
        {
            var id = CommonUtil.getLong(routingContext.request().getParam(GlobalConstants.ID));

            var item = ConfigurationConfigStore.getStore().getItem(id);

            if (item != null && !item.isEmpty())
            {
                enrich(item, SchedulerConfigStore.getStore().getSchedulerObjects(Scheduler.SCHEDULER_CONTEXT, NMSConstants.OBJECTS, new JsonArray().add(item.getLong(ID)), JobScheduler.JobType.CONFIG_BACKUP_OPERATION.getName()));

                this.send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_OK).
                        put(GlobalConstants.STATUS, STATUS_SUCCEED).put(GlobalConstants.RESULT, item));
            }
            else
            {
                this.send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_OK).put(GlobalConstants.STATUS, STATUS_SUCCEED).put(GlobalConstants.RESULT, new JsonObject()));
            }
        }
        catch (Exception exception)
        {
            APIUtil.sendResponse(exception, routingContext);
        }
    }

    @Override
    protected void getAll(RoutingContext routingContext)
    {
        var entities = new JsonArray();

        try
        {
            var category = CommonUtil.getString(routingContext.request().getParam(AIOpsObject.OBJECT_CATEGORY));

            var backupRequired = CommonUtil.getString(routingContext.request().getParam(ConfigConstants.CONFIG_BACKUP_REQUIRED));

            var user = UserConfigStore.getStore().getItem(routingContext.user().principal().getLong(ID));

            if (user.getLong(ID).equals(DEFAULT_ID))
            {
                user.put(User.USER_GROUPS, GroupConfigStore.getStore().flatItems(ID));
            }

            var objectIds = routingContext.request().getParam(VisualizationConstants.ADMIN_ROLE) != null &&
                    routingContext.request().getParam(VisualizationConstants.ADMIN_ROLE).equalsIgnoreCase(YES) ?
                    ObjectConfigStore.getStore().getObjectIds() : ObjectConfigStore.getStore().getObjectIdsByGroups(user.getJsonArray(User.USER_GROUPS));

            if (objectIds != null && !objectIds.isEmpty() && CommonUtil.isNotNullOrEmpty(category))
            {
                var items = backupRequired != null && backupRequired.equals(YES) ? ConfigurationConfigStore.getStore().getItemsByStatus(objectIds, new JsonArray().add(category), STATUS_SUCCEED) : ConfigurationConfigStore.getStore().flatItemsByMultipleValue(CONFIG_OBJECT, objectIds, AIOpsObject.OBJECT_CATEGORY, category);

                if (items != null && !items.isEmpty())
                {
                    var schedulers = backupRequired != null && backupRequired.equals(YES) ? null : SchedulerConfigStore.getStore().getSchedulerObjects(Scheduler.SCHEDULER_CONTEXT, NMSConstants.OBJECTS, new JsonArray(items.stream().parallel().map(item -> JsonObject.mapFrom(item).getLong(ID)).collect(Collectors.toList())), JobScheduler.JobType.CONFIG_BACKUP_OPERATION.getName());

                    for (var index = 0; index < items.size(); index++)
                    {
                        var item = items.getJsonObject(index);

                        enrich(item, schedulers);

                        entities.add(item);
                    }
                }
            }

            this.send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_OK).put(GlobalConstants.STATUS, STATUS_SUCCEED).put(GlobalConstants.RESULT, entities));
        }
        catch (Exception exception)
        {
            APIUtil.sendResponse(exception, routingContext);
        }
    }

    @Override
    protected Future<JsonObject> beforeUpdate(RoutingContext routingContext)
    {
        return Future.succeededFuture(ConfigurationConfigStore.getStore().getItem(CommonUtil.getLong(routingContext.request().getParam(GlobalConstants.ID))).mergeIn(routingContext.body().asJsonObject()));
    }

    private void enrich(JsonObject item, JsonArray schedulers)
    {
        try
        {
            item.put(ConfigConstants.CONFIG_BACKUP_SCHEDULER, schedulers != null && schedulers.contains(item.getLong(GlobalConstants.ID)) ? YES : NO);

            if (!item.containsKey(Configuration.CONFIG_CREDENTIAL_STATUS))
            {
                item.put(Configuration.CONFIG_CREDENTIAL_STATUS, ConfigConstants.STATUS_NOT_APPLICABLE);
            }

            if (item.containsKey(Configuration.CONFIG_TEMPLATE))
            {
                var template = ConfigTemplateConfigStore.getStore().getItem(item.getLong(Configuration.CONFIG_TEMPLATE));

                item.put(ConfigTemplate.CONFIG_TEMPLATE_NAME, template.getString(ConfigTemplate.CONFIG_TEMPLATE_NAME))
                        .put(ConfigTemplate.CONFIG_TEMPLATE_OS_TYPE, template.getString(ConfigTemplate.CONFIG_TEMPLATE_OS_TYPE));
            }
            else
            {
                item.put(ConfigTemplate.CONFIG_TEMPLATE_NAME, ConfigConstants.STATUS_NOT_ASSIGNED);
            }

            var runningOperation = ConfigurationCacheStore.getStore().getRunningOperation(item.getLong(ID));

            if (runningOperation != null)
            {
                item.mergeIn(runningOperation);
            }

            var object = ObjectConfigStore.getStore().getItemByObjectId(item.getInteger(Configuration.CONFIG_OBJECT));

            ConfigConstants.removeGarbageFields(object);

            if (object.containsKey(AIOpsObject.OBJECT_TAGS))
            {
                object.put(AIOpsObject.OBJECT_TAGS, TagConfigStore.getStore().getItems(object.getJsonArray(AIOpsObject.OBJECT_TAGS)));
            }

            item.mergeIn(object);
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
        finally
        {
            CommonUtil.removeSensitiveFields(item, true);
        }
    }

    private void updateStatus(RoutingContext routingContext)
    {
        try
        {
            var id = CommonUtil.getLong(routingContext.request().getParam(GlobalConstants.ID));

            var status = routingContext.body().asJsonObject().getString(CONFIG_MANAGEMENT_STATUS);

            if (status.equals(YES) || status.equals(NO))
            {
                Bootstrap.vertx().eventBus().<JsonObject>request(EventBusConstants.EVENT_CONFIG_MANAGE,
                        new JsonObject().put(ID, id).put(CONFIG_MANAGEMENT_STATUS, status).put(USER_NAME, routingContext.user().principal().getString(User.USER_NAME)).put(REMOTE_ADDRESS, routingContext.request().remoteAddress().host()),
                        reply ->
                        {
                            var result = reply.result().body();

                            if (result.getString(GlobalConstants.STATUS).equalsIgnoreCase(GlobalConstants.STATUS_SUCCEED))
                            {
                                this.send(routingContext, result.put(RESPONSE_CODE, HttpStatus.SC_OK));
                            }
                            else
                            {
                                this.send(routingContext, result.put(RESPONSE_CODE, HttpStatus.SC_BAD_REQUEST));
                            }
                        });
            }
            else
            {
                this.send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_BAD_REQUEST)
                        .put(GlobalConstants.STATUS, GlobalConstants.STATUS_FAIL)
                        .put(GlobalConstants.MESSAGE, ErrorMessageConstants.API_INVALID_INPUT_PARAMETERS));
            }
        }
        catch (Exception exception)
        {
            APIUtil.sendResponse(exception, routingContext);
        }
    }

    private void assignBaseline(RoutingContext routingContext)
    {
        try
        {
            var id = CommonUtil.getLong(routingContext.request().getParam(GlobalConstants.ID));

            var item = ConfigurationConfigStore.getStore().getItem(id);

            var version = routingContext.body().asJsonObject().getInteger(CONFIG_BACKUP_FILE_BASELINE_VERSION);

            if (item != null && item.containsKey(Configuration.CONFIG_BACKUP_FILE_RUNNING_CURRENT_VERSION) && version != null)
            {
                var baselineVersion = item.containsKey(Configuration.CONFIG_BACKUP_FILE_BASELINE_VERSION) && item.getInteger(Configuration.CONFIG_BACKUP_FILE_BASELINE_VERSION) != DUMMY_NUMERIC_VALUE ? item.getInteger(Configuration.CONFIG_BACKUP_FILE_BASELINE_VERSION) : DUMMY_NUMERIC_VALUE;

                // Checking conflict between current running version and assigned baseline version
                Bootstrap.configDBService().get(String.format(DBConstants.TBL_CONFIG_RUNNING_RESULT, version),
                        new JsonObject().put(DBConstants.FIELD_NAME, Configuration.CONFIG_OBJECT).put(VALUE, item.getInteger(Configuration.CONFIG_OBJECT)),
                        asyncResult ->
                        {
                            if (asyncResult.succeeded())
                            {
                                var baselineRunningFileContent = CodecUtil.toString(asyncResult.result().getJsonObject(0).getBinary(CONFIG_BACKUP_FILE_CONTENT));

                                var runningConfigFileContent = CodecUtil.toString(ConfigurationCacheStore.getStore().getRecentRunningBackupRecords(item.getInteger(Configuration.CONFIG_OBJECT)));

                                Bootstrap.configDBService().update(DBConstants.TBL_CONFIGURATION,
                                        new JsonObject().put(DBConstants.FIELD_NAME, GlobalConstants.ID).put(VALUE, id),
                                        new JsonObject().put(Configuration.CONFIG_BACKUP_FILE_BASELINE_VERSION, version)
                                                .put(Configuration.CONFIG_BACKUP_FILE_RUNNING_WITH_BASELINE_CONFLICT_STATUS, ConfigConstants.detectConflicts(runningConfigFileContent, baselineRunningFileContent)),
                                        routingContext.user().principal().getString(User.USER_NAME),
                                        routingContext.request().remoteAddress().host(),
                                        future ->
                                        {
                                            if (future.succeeded())
                                            {
                                                ConfigurationConfigStore.getStore().updateItem(id).onComplete(result ->
                                                {
                                                    ConfigurationCacheStore.getStore().updateRecentBaselineBackupRecords(ConfigurationConfigStore.getStore().getObjectId(id), asyncResult.result().getJsonObject(0).getBinary(CONFIG_BACKUP_FILE_CONTENT));

                                                    if (baselineVersion != DUMMY_NUMERIC_VALUE && baselineVersion < item.getInteger(Configuration.CONFIG_BACKUP_FILE_RUNNING_MIN_VERSION))
                                                    {
                                                        deleteBaseline(item, baselineVersion);
                                                    }

                                                    this.send(routingContext, new JsonObject().put(GlobalConstants.STATUS, STATUS_SUCCEED).put(RESPONSE_CODE, HttpStatus.SC_OK)
                                                            .put(GlobalConstants.MESSAGE, InfoMessageConstants.CONFIG_BASELINE_ASSIGNED_SUCCESSFULLY));
                                                });
                                            }
                                            else
                                            {
                                                this.send(routingContext, new JsonObject().put(GlobalConstants.STATUS, GlobalConstants.STATUS_FAIL).put(RESPONSE_CODE, HttpStatus.SC_BAD_REQUEST)
                                                        .put(GlobalConstants.ERROR, future.failed() ? CommonUtil.formatStackTrace(future.cause().getStackTrace()) : UNKNOWN)
                                                        .put(ERROR_CODE, ErrorCodes.ERROR_CODE_INTERNAL_ERROR)
                                                        .put(GlobalConstants.MESSAGE, String.format(ErrorMessageConstants.CONFIG_BASELINE_ASSIGN_FAILED, future.cause().getMessage())));
                                            }
                                        });
                            }
                            else
                            {
                                this.send(routingContext, new JsonObject().put(GlobalConstants.STATUS, GlobalConstants.STATUS_FAIL).put(RESPONSE_CODE, HttpStatus.SC_BAD_REQUEST)
                                        .put(GlobalConstants.ERROR, asyncResult.failed() ? CommonUtil.formatStackTrace(asyncResult.cause().getStackTrace()) : UNKNOWN)
                                        .put(ERROR_CODE, ErrorCodes.ERROR_CODE_INTERNAL_ERROR)
                                        .put(GlobalConstants.MESSAGE, String.format(ErrorMessageConstants.CONFIG_BASELINE_ASSIGN_FAILED, asyncResult.cause().getMessage())));
                            }
                        });
            }
            else
            {
                this.send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_BAD_REQUEST)
                        .put(GlobalConstants.STATUS, GlobalConstants.STATUS_FAIL)
                        .put(GlobalConstants.MESSAGE, ErrorMessageConstants.API_INVALID_INPUT_PARAMETERS));
            }
        }
        catch (Exception exception)
        {
            APIUtil.sendResponse(exception, routingContext);
        }
    }

    private void removeBaseline(RoutingContext routingContext)
    {
        try
        {
            var id = CommonUtil.getLong(routingContext.request().getParam(GlobalConstants.ID));

            var item = ConfigurationConfigStore.getStore().getItem(id);

            if (item != null && item.containsKey(Configuration.CONFIG_BACKUP_FILE_BASELINE_VERSION) && item.getInteger(Configuration.CONFIG_BACKUP_FILE_BASELINE_VERSION) != DUMMY_NUMERIC_VALUE)
            {
                var version = item.getInteger(Configuration.CONFIG_BACKUP_FILE_BASELINE_VERSION);

                Bootstrap.configDBService().update(DBConstants.TBL_CONFIGURATION,
                        new JsonObject().put(DBConstants.FIELD_NAME, GlobalConstants.ID).put(VALUE, id),
                        new JsonObject().put(Configuration.CONFIG_BACKUP_FILE_BASELINE_VERSION, DUMMY_NUMERIC_VALUE).put(Configuration.CONFIG_BACKUP_FILE_RUNNING_WITH_BASELINE_CONFLICT_STATUS, ConfigConstants.ConfigConflictStatus.NA.getName()),
                        routingContext.user().principal().getString(User.USER_NAME),
                        routingContext.request().remoteAddress().host(),
                        future ->
                        {
                            if (future.succeeded())
                            {
                                ConfigurationConfigStore.getStore().updateItem(id).onComplete(result ->
                                {

                                    // Deleting baseline record from config cache as well
                                    ConfigurationCacheStore.getStore().deleteBaselineRecords(ConfigurationConfigStore.getStore().getObjectId(id));

                                    if (item.containsKey(Configuration.CONFIG_BACKUP_FILE_RUNNING_MIN_VERSION) && item.getInteger(Configuration.CONFIG_BACKUP_FILE_RUNNING_MIN_VERSION) > version)
                                    {
                                        // delete baseline version...
                                        deleteBaseline(item, version);
                                    }

                                    this.send(routingContext, new JsonObject().put(GlobalConstants.STATUS, STATUS_SUCCEED).put(RESPONSE_CODE, HttpStatus.SC_OK)
                                            .put(GlobalConstants.MESSAGE, InfoMessageConstants.CONFIG_BASELINE_REMOVED_SUCCESSFULLY));
                                });
                            }
                            else
                            {
                                this.send(routingContext, new JsonObject().put(GlobalConstants.STATUS, GlobalConstants.STATUS_FAIL).put(RESPONSE_CODE, HttpStatus.SC_BAD_REQUEST)
                                        .put(GlobalConstants.ERROR, future.failed() ? CommonUtil.formatStackTrace(future.cause().getStackTrace()) : UNKNOWN)
                                        .put(ERROR_CODE, ErrorCodes.ERROR_CODE_INTERNAL_ERROR)
                                        .put(GlobalConstants.MESSAGE, String.format(ErrorMessageConstants.CONFIG_BASELINE_REMOVE_FAILED, future.cause().getMessage())));
                            }
                        });
            }
            else
            {
                this.send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_BAD_REQUEST)
                        .put(GlobalConstants.STATUS, GlobalConstants.STATUS_FAIL)
                        .put(GlobalConstants.MESSAGE, ErrorMessageConstants.API_INVALID_INPUT_PARAMETERS));
            }
        }
        catch (Exception exception)
        {
            APIUtil.sendResponse(exception, routingContext);
        }
    }

    private void getFileContent(RoutingContext routingContext)
    {
        try
        {
            var id = CommonUtil.getLong(routingContext.request().getParam(GlobalConstants.ID));

            var version = CommonUtil.getInteger(routingContext.request().getParam(GlobalConstants.VERSION));

            var fileType = CommonUtil.getString(routingContext.request().getParam(ConfigConstants.BACKUP_FILE_TYPE));

            var item = ConfigurationConfigStore.getStore().getItem(id);

            if (item != null)
            {
                if (ConfigConstants.ConfigBackupType.valueOfName(fileType) != null)
                {
                    Bootstrap.configDBService().get(String.format(fileType.equalsIgnoreCase(ConfigConstants.ConfigBackupType.RUNNING.getName()) ? DBConstants.TBL_CONFIG_RUNNING_RESULT : DBConstants.TBL_CONFIG_STARTUP_RESULT, version),
                            new JsonObject().put(DBConstants.FIELD_NAME, Configuration.CONFIG_OBJECT).put(VALUE, item.getInteger(Configuration.CONFIG_OBJECT)),
                            future ->
                            {
                                if (future.succeeded())
                                {
                                    var backupResult = future.result().getJsonObject(0);

                                    APIUtil.removeDefaultParameters(backupResult);

                                    this.send(routingContext, new JsonObject().mergeIn(backupResult).put(GlobalConstants.STATUS, STATUS_SUCCEED).put(RESPONSE_CODE, HttpStatus.SC_OK)
                                            .put(GlobalConstants.MESSAGE, InfoMessageConstants.CONFIG_BACKUP_FILE_CONTENT_RETRIEVED_SUCCESSFULLY));
                                }
                                else
                                {
                                    this.send(routingContext, new JsonObject().put(GlobalConstants.STATUS, GlobalConstants.STATUS_FAIL).put(RESPONSE_CODE, HttpStatus.SC_BAD_REQUEST)
                                            .put(GlobalConstants.ERROR, future.failed() ? CommonUtil.formatStackTrace(future.cause().getStackTrace()) : UNKNOWN)
                                            .put(ERROR_CODE, ErrorCodes.ERROR_CODE_INTERNAL_ERROR)
                                            .put(GlobalConstants.MESSAGE, String.format(ErrorMessageConstants.CONFIG_BACKUP_RESULT_ERROR, future.cause().getMessage())));
                                }
                            });
                }
                else
                {
                    this.send(routingContext, new JsonObject().put(GlobalConstants.STATUS, GlobalConstants.STATUS_FAIL).put(RESPONSE_CODE, HttpStatus.SC_BAD_REQUEST).
                            put(ERROR_CODE, ErrorCodes.ERROR_CODE_BAD_RESPONSE).
                            put(MESSAGE, String.format(ErrorMessageConstants.CONFIG_BACKUP_RESULT_ERROR, " Not a valid file type")).
                            put(ERROR, String.format(ErrorMessageConstants.CONFIG_BACKUP_RESULT_ERROR, " Not a valid file type")));
                }
            }
            else
            {
                this.send(routingContext, new JsonObject().put(STATUS, STATUS_FAIL)
                        .put(ERROR_CODE, ErrorCodes.ERROR_CODE_NO_ITEM_FOUND)
                        .put(RESPONSE_CODE, SC_BAD_REQUEST).put(MESSAGE, ErrorMessageConstants.CONFIG_OBJECT_NOT_FOUND));
            }
        }
        catch (Exception exception)
        {
            APIUtil.sendResponse(exception, routingContext);
        }
    }

    private void execute(RoutingContext routingContext)
    {
        try
        {
            var operation = CommonUtil.getString(routingContext.body().asJsonObject().getString(ConfigConstants.CONFIG_OPERATION));

            if (CommonUtil.isNotNullOrEmpty(operation))
            {
                var eventType = switch (ConfigConstants.ConfigOperation.valueOfName(operation))
                {
                    case DISCOVERY -> EventBusConstants.EVENT_CONFIG_DISCOVERY;
                    case BACKUP -> EventBusConstants.EVENT_CONFIG_BACKUP;
                    case SYNC -> EventBusConstants.EVENT_CONFIG_SYNC;
                    case RESTORE -> EventBusConstants.EVENT_CONFIG_RESTORE;
                    case UPGRADE -> EventBusConstants.EVENT_CONFIG_UPGRADE;
                    case INFO -> EventBusConstants.EVENT_CONFIG_INFO;
                    case RUNBOOK -> EventBusConstants.EVENT_CONFIG_RUNBOOK;
                    default -> null;
                };

                if (CommonUtil.isNotNullOrEmpty(eventType))
                {
                    Bootstrap.vertx().eventBus().send(eventType, routingContext.body().asJsonObject().put(ConfigConstants.CONFIG_OPERATION_ID, CommonUtil.newId()).put(USER_NAME, routingContext.user().principal().getString(User.USER_NAME)).put(REMOTE_ADDRESS, routingContext.request().remoteAddress().host()));

                    this.send(routingContext, new JsonObject().put(GlobalConstants.STATUS, STATUS_SUCCEED).put(RESPONSE_CODE, HttpStatus.SC_OK)
                            .put(GlobalConstants.MESSAGE, String.format(InfoMessageConstants.CONFIG_REQUEST_QUEUED, operation)));
                }
                else
                {
                    this.send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_BAD_REQUEST)
                            .put(GlobalConstants.STATUS, GlobalConstants.STATUS_FAIL)
                            .put(GlobalConstants.MESSAGE, ErrorMessageConstants.API_INVALID_INPUT_PARAMETERS));
                }
            }
            else
            {
                this.send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_BAD_REQUEST)
                        .put(GlobalConstants.STATUS, GlobalConstants.STATUS_FAIL)
                        .put(GlobalConstants.MESSAGE, ErrorMessageConstants.API_INVALID_INPUT_PARAMETERS));
            }
        }
        catch (Exception exception)
        {
            APIUtil.sendResponse(exception, routingContext);
        }
    }

    private void updateAll(RoutingContext routingContext)
    {
        try
        {
            var requestBody = routingContext.body().asJsonObject();

            var ids = requestBody.getJsonArray(REQUEST_PARAM_IDS);

            requestBody.remove(REQUEST_PARAM_IDS);

            Bootstrap.configDBService().updateAll(DBConstants.TBL_CONFIGURATION,
                    new JsonObject().put(DBConstants.FIELD_NAME, ID).put(VALUE, ids),
                    requestBody,
                    routingContext.user().principal().getString(USER_NAME),
                    routingContext.request().remoteAddress().host(),
                    result ->
                    {
                        if (result.succeeded() && !result.result().isEmpty())
                        {
                            ConfigurationConfigStore.getStore().updateItems(ids).onComplete(asyncResult ->
                                    this.send(routingContext, new JsonObject().put(GlobalConstants.STATUS, STATUS_SUCCEED).put(RESPONSE_CODE, HttpStatus.SC_OK)
                                            .put(REQUEST_PARAM_IDS, ids)
                                            .put(GlobalConstants.MESSAGE, String.format(InfoMessageConstants.ENTITY_UPDATED, Entity.CONFIGURATION.getName()))));
                        }
                        else
                        {
                            this.send(routingContext, new JsonObject().put(GlobalConstants.STATUS, GlobalConstants.STATUS_FAIL).put(RESPONSE_CODE, HttpStatus.SC_BAD_REQUEST)
                                    .put(GlobalConstants.ERROR, result.failed() ? CommonUtil.formatStackTrace(result.cause().getStackTrace()) : UNKNOWN)
                                    .put(ERROR_CODE, ErrorCodes.ERROR_CODE_INTERNAL_ERROR)
                                    .put(GlobalConstants.MESSAGE, String.format(ErrorMessageConstants.ENTITY_UPDATE_FAILED, Entity.CONFIGURATION.getName(), result.failed() ? result.cause().getMessage() : UNKNOWN)));
                        }
                    });
        }
        catch (Exception exception)
        {
            APIUtil.sendResponse(exception, routingContext);
        }
    }

    private void deleteBaseline(JsonObject event, int version)
    {
        Bootstrap.configDBService().delete(String.format(DBConstants.TBL_CONFIG_RUNNING_RESULT, version),
                new JsonObject().put(DBConstants.FIELD_NAME, Configuration.CONFIG_OBJECT).put(VALUE, event.getInteger(Configuration.CONFIG_OBJECT)),
                event.containsKey(USER_NAME) ? event.getString(USER_NAME) : DEFAULT_USER,
                event.containsKey(REMOTE_ADDRESS) ? event.getString(REMOTE_ADDRESS) : SYSTEM_REMOTE_ADDRESS, result ->
                {
                    if (result.succeeded())
                    {
                        LOGGER.info(String.format("Configuration backup result deleted successfully, object : %s, file type : %s, version : %s", event.getString(AIOpsObject.OBJECT_NAME), ConfigConstants.ConfigBackupType.RUNNING.getName(), version));
                    }
                    else
                    {
                        LOGGER.warn(String.format(ErrorMessageConstants.CONFIG_CONFIG_BACKUP_RESULT_DELETE_FAILED, event.getString(AIOpsObject.OBJECT_NAME), ConfigConstants.ConfigBackupType.RUNNING.getName(), version, result.cause().getMessage()));
                    }
                });
    }
}
