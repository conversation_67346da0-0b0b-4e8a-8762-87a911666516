/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

package com.mindarray.api;

import com.mindarray.nms.SNMPTrapListener;
import com.mindarray.store.SNMPTrapListenerConfigStore;
import com.mindarray.util.Logger;
import io.vertx.core.Future;
import io.vertx.core.json.JsonObject;
import io.vertx.ext.web.RoutingContext;

import static com.mindarray.GlobalConstants.MOTADATA_API;

public class SNMPTrapListenerConfiguration extends AbstractAPI
{
    public static final String SNMP_COMMUNITY = "snmp.community";
    public static final String SNMP_V3_SECURITY_USERNAME = "snmp.security.user.name";
    public static final String SNMP_V3_AUTHENTICATION_PROTOCOL = "snmp.authentication.protocol";
    public static final String SNMP_V3_PRIVACY_PROTOCOL = "snmp.privacy.protocol";
    public static final String SNMP_V3_SECURITY_LEVEL = "snmp.security.level";
    public static final String SNMP_V3_AUTHENTICATION_PASSWORD = "snmp.authentication.password";
    public static final String SNMP_V3_PRIVATE_PASSWORD = "snmp.private.password";
    public static final String AUTHENTICATION_NO_PRIVACY = "Authentication No Privacy";
    public static final String NO_AUTHENTICATION_NO_PRIVACY = "No Authentication No Privacy";
    public static final String AUTHENTICATION_PRIVACY = "Authentication Privacy";
    public static final String DES = "DES";
    public static final String DES_3 = "3DES";
    public static final String AES_128 = "AES128";
    public static final String AES_256 = "AES256";
    public static final String AES_192 = "AES192";
    public static final String AES_192C = "AES192C";
    public static final String AES_192G = "AES192G";
    public static final String AES_256C = "AES256C";
    public static final String AES_256G = "AES256G";
    public static final String MD5 = "MD5";
    public static final String SHA = "SHA";
    public static final String SHA224 = "SHA224";
    public static final String SHA256 = "SHA256";
    public static final String SHA384 = "SHA384";
    public static final String SHA512 = "SHA512";
    public static final String SNMP_TRAP_LISTENER_V1_V2_PORT = "snmp.trap.listener.v1.v2.port";
    public static final String SNMP_TRAP_LISTENER_V1_V2_STATUS = "snmp.trap.listener.v1.v2.status";
    public static final String SNMP_TRAP_LISTENER_V3_PORT = "snmp.trap.listener.v3.port";
    public static final String SNMP_TRAP_LISTENER_V3_STATUS = "snmp.trap.listener.v3.status";
    public static final Short SNMP_V3_SECURITY_LEVEL1 = 1;
    public static final Short SNMP_V3_SECURITY_LEVEL2 = 2;
    public static final Short SNMP_V3_SECURITY_LEVEL3 = 3;
    private static final Logger LOGGER = new Logger(SNMPTrapListenerConfiguration.class, MOTADATA_API, "SNMP Trap Listener Configuration API");

    public SNMPTrapListenerConfiguration()
    {
        super("snmp-trap-listener-configuration", SNMPTrapListenerConfigStore.getStore(), LOGGER);
    }

    @Override
    protected Future<JsonObject> afterUpdate(JsonObject response, RoutingContext routingContext)
    {
        this.send(routingContext, response);

        SNMPTrapListener.stop();

        SNMPTrapListener.start();

        return Future.succeededFuture();
    }
}
