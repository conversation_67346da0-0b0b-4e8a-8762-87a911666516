/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

/*
 *  Change Logs:
 *  Date			Author			Notes
 *  6-Feb-2025		<PERSON><PERSON>		MOTADATA-4878: On Backup profile update operation sending notification to all the datastores
 *
 */


package com.mindarray.api;

import com.mindarray.GlobalConstants;
import com.mindarray.datastore.DatastoreConstants;
import com.mindarray.job.JobScheduler;
import com.mindarray.nms.NMSConstants;
import com.mindarray.store.*;
import com.mindarray.util.CommonUtil;
import com.mindarray.util.Logger;
import io.vertx.core.Future;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import io.vertx.ext.web.Router;
import io.vertx.ext.web.RoutingContext;
import org.apache.http.HttpStatus;

import java.util.Arrays;
import java.util.Collections;
import java.util.Map;
import java.util.stream.Collectors;

import static com.mindarray.GlobalConstants.*;
import static com.mindarray.api.APIConstants.RESPONSE_CODE;

public class BackupProfile extends AbstractAPI
{

    public static final Logger LOGGER = new Logger(BackupProfile.class, GlobalConstants.MOTADATA_API, "Backup Profile API");

    public static final String BACKUP_PROFILE_NAME = "backup.profile.name";
    public static final String BACKUP_STORAGE_PROFILE = "backup.storage.profile";
    public static final String BACKUP_PROFILE_TYPE = "backup.profile.type";
    public static final String BACKUP_PROFILE_CONTEXT = "backup.profile.context";

    public BackupProfile()
    {
        super("backup-profiles", BackupProfileConfigStore.getStore(), LOGGER);
    }

    @Override
    public void init(Router router)
    {
        try
        {
            super.init(router);

        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }

    @Override
    protected void getAll(RoutingContext routingContext)
    {
        try
        {
            var items = new JsonArray();

            var entities = BackupProfileConfigStore.getStore().getItems();                      // for backup screen

            var schedulers = SchedulerConfigStore.getStore().getItemsByValue(Scheduler.SCHEDULER_JOB_TYPE, JobScheduler.JobType.DATABASE_BACKUP.getName());

            for (var i = 0; i < entities.size(); i++)
            {
                var entity = entities.getJsonObject(i);

                for (var j = 0; j < schedulers.size(); j++)
                {
                    var scheduler = schedulers.getJsonObject(j);

                    if (scheduler.getJsonObject(Scheduler.SCHEDULER_CONTEXT).getJsonArray(NMSConstants.OBJECTS).getLong(0).equals(entity.getLong(ID)))
                    {
                        entity.put(NMSConstants.STATE, SchedulerCacheStore.getStore().getSchedulerEvents(scheduler.getLong(ID)) != null ? NMSConstants.STATE_RUNNING : NMSConstants.STATE_NOT_RUNNING);

                        entity.put("scheduler.id", scheduler.remove(ID));

                        entity.mergeIn(scheduler);
                    }
                }

                entity.put(StorageProfile.STORAGE_PROFILE_PROTOCOL, StorageProfileConfigStore.getStore().getItem(entity.getLong(BACKUP_STORAGE_PROFILE)).getString(StorageProfile.STORAGE_PROFILE_PROTOCOL));

                items.add(CommonUtil.removeSensitiveFields(entity, false));
            }

            this.send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_OK).put(GlobalConstants.STATUS, STATUS_SUCCEED).put(RESULT, items));
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);

            APIUtil.sendResponse(exception, routingContext);
        }
    }

    @Override
    protected Future<JsonObject> afterUpdate(JsonObject entity, RoutingContext routingContext)
    {
        this.send(routingContext, entity);

        try
        {
            var item = this.configStore.getItem(entity.getLong(ID));

            if (item.getString(BACKUP_PROFILE_TYPE).equalsIgnoreCase(BackupProfileType.REPORT_DB.getName()))
            {
                DatastoreConstants.notify(new JsonObject().put(DatastoreConstants.DATASTORE_TYPES, item.getJsonObject(BACKUP_PROFILE_CONTEXT).getJsonArray(DatastoreConstants.DATASTORE_TYPES)),
                        DatastoreConstants.OperationType.MODIFY_BACKUP_PROFILE.ordinal(), RemoteEventProcessorConfigStore.getStore().getIds());
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }

        return Future.succeededFuture();
    }

    public enum BackupProfileType
    {
        CONFIG_DB("Config DB"),
        REPORT_DB("Report DB");

        private static final Map<String, BackupProfile.BackupProfileType> VALUES = Collections.unmodifiableMap(Arrays.stream(values())
                .collect(Collectors.toMap(BackupProfile.BackupProfileType::getName, entity -> entity)));
        private final String name;

        BackupProfileType(String name)
        {
            this.name = name;
        }

        public static BackupProfile.BackupProfileType valueOfName(String name)
        {
            return VALUES.get(name);
        }

        public String getName()
        {
            return name;
        }
    }
}