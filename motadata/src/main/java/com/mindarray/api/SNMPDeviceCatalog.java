/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

/*  Change Logs:
 *  Date			Author		    Notes
 *  23-Apr-2025		sankalp		    MOTADATA-4883 : Added data security based on user group for reference entities
 */

package com.mindarray.api;

import com.mindarray.*;
import com.mindarray.db.DBConstants;
import com.mindarray.eventbus.EventBusConstants;
import com.mindarray.nms.NMSConstants;
import com.mindarray.store.*;
import com.mindarray.util.CommonUtil;
import com.mindarray.util.DateTimeUtil;
import com.mindarray.util.Logger;
import io.vertx.core.Future;
import io.vertx.core.Promise;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import io.vertx.ext.web.Router;
import io.vertx.ext.web.RoutingContext;
import org.apache.http.HttpStatus;

import java.util.ArrayList;

import static com.mindarray.GlobalConstants.*;
import static com.mindarray.api.APIConstants.RESPONSE_CODE;
import static com.mindarray.api.User.USER_ID;
import static com.mindarray.api.User.USER_NAME;

public class SNMPDeviceCatalog extends AbstractAPI
{
    public static final String SNMP_DEVICE_CATALOG_ID = "snmp.device.catalog.id";
    public static final String SNMP_DEVICE_CATALOG_OID = "snmp.device.catalog.oid";
    public static final String SNMP_DEVICE_CATALOG_MODEL = "snmp.device.catalog.model";
    public static final String SNMP_DEVICE_CATALOG_TYPE = "snmp.device.catalog.type";
    public static final String SNMP_DEVICE_CATALOG_VENDOR = "snmp.device.catalog.vendor";
    private static final Logger LOGGER = new Logger(SNMPDeviceCatalog.class, MOTADATA_API, "SNMP Device Catalog API");

    public SNMPDeviceCatalog()
    {
        super("snmp-device-catalogs", SNMPDeviceCatalogConfigStore.getStore(), LOGGER);
    }

    @Override
    public void init(Router router)
    {
        try
        {
            router.get("/" + endpoint + "/vendors").handler(this::getVendors);

            super.init(router);

            router.get("/" + endpoint + "/:id/references").handler(this::getReferences);

            router.post("/" + endpoint + "/:id/assign").handler(this::assign);
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }

    @Override
    protected Future<Void> afterDelete(JsonObject entity, RoutingContext routingContext)
    {
        try
        {
            //list down all metrics that matches deleted snmp device catalog id in metric.context and unprovision/delete them
            var items = MetricConfigStore.getStore().getItemsByMapMultiValueField(Metric.METRIC_CONTEXT, APIConstants.ENTITY_ID,
                    SNMPOIDGroupConfigStore.getStore().flatItems(SNMP_DEVICE_CATALOG_ID, CommonUtil.getLong(routingContext.request().getParam(GlobalConstants.ID)), ID));

            if (!items.isEmpty())
            {
                var ids = new JsonArray();

                var metrics = new ArrayList<JsonObject>();

                for (var index = 0; index < items.size(); index++)
                {
                    var item = items.getJsonObject(index);

                    if (item.containsKey(Metric.METRIC_CONTEXT) && item.getJsonObject(Metric.METRIC_CONTEXT).containsKey(APIConstants.ENTITY_ID))
                    {
                        ids.add(item.getJsonObject(Metric.METRIC_CONTEXT).getLong(APIConstants.ENTITY_ID));

                        metrics.add(new JsonObject()
                                .put(REMOTE_ADDRESS, routingContext.request().remoteAddress().host())
                                .put(USER_NAME, routingContext.user().principal().getString(USER_NAME))
                                .put(ID, item.getLong(ID)));
                    }
                }

                if (!metrics.isEmpty())
                {
                    NMSConstants.updateMetrics(metrics, EventBusConstants.EVENT_METRIC_UNPROVISION);

                    Bootstrap.configDBService().deleteAll(DBConstants.TBL_SNMP_OID_GROUP,
                            new JsonObject().put(DBConstants.FIELD_NAME, ID).put(VALUE, ids),
                            routingContext.user().principal().getString(USER_NAME),
                            routingContext.request().remoteAddress().host(),
                            result ->
                            {
                                if (result.succeeded())
                                {
                                    SNMPOIDGroupConfigStore.getStore().deleteItems(ids);
                                }
                            });
                }
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }

        return super.afterDelete(entity, routingContext);
    }

    private void assign(RoutingContext routingContext)
    {
        try
        {
            var schema = CommonUtil.getEntitySchema(endpoint);

            if (schema != null)
            {
                var id = CommonUtil.getLong(routingContext.request().getParam(GlobalConstants.ID));

                var snmpDeviceCatalog = SNMPDeviceCatalogConfigStore.getStore().getItem(id);

                if (snmpDeviceCatalog != null)
                {
                    var objects = routingContext.body().asJsonObject().getJsonArray(APIConstants.REQUEST_PARAM_IDS);

                    Bootstrap.configDBService().updateAll(DBConstants.TBL_OBJECT,
                            new JsonObject().put(DBConstants.FIELD_NAME, ID).put(VALUE, objects),
                            new JsonObject().put(AIOpsObject.OBJECT_SNMP_DEVICE_CATALOG, id).put(AIOpsObject.OBJECT_VENDOR, snmpDeviceCatalog.getString(SNMP_DEVICE_CATALOG_VENDOR)).put(AIOpsObject.OBJECT_MAKE_MODEL, snmpDeviceCatalog.getString(SNMP_DEVICE_CATALOG_MODEL)).put(AIOpsObject.OBJECT_SYSTEM_OID, snmpDeviceCatalog.getString(SNMP_DEVICE_CATALOG_OID)).put(AIOpsObject.OBJECT_TYPE, snmpDeviceCatalog.getString(SNMP_DEVICE_CATALOG_TYPE)).put(AIOpsObject.OBJECT_MODIFICATION_TIME, DateTimeUtil.timestamp()),
                            routingContext.user().principal().getString(USER_NAME), routingContext.request().remoteAddress().host(), result ->
                            {
                                if (result.succeeded() && !result.result().isEmpty())
                                {
                                    this.send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_OK).put(GlobalConstants.STATUS, STATUS_SUCCEED)
                                            .put(GlobalConstants.MESSAGE, InfoMessageConstants.SNMP_DEVICE_CATALOG_ASSIGN_SUCCEEDED).put(ID, id));

                                    Bootstrap.vertx().executeBlocking(promise ->
                                    {

                                        var context = new JsonObject().put(NMSConstants.OBJECTS, objects).put(SNMP_DEVICE_CATALOG_ID, id).put(APIConstants.REQUEST, APIConstants.REQUEST_CREATE)
                                                .put(DBConstants.FIELD_TYPE, DBConstants.ENTITY_TYPE_USER);

                                        var oidGroups = SNMPOIDGroupConfigStore.getStore().getItemsByValue(SNMP_DEVICE_CATALOG_ID, id);

                                        if (!oidGroups.isEmpty())
                                        {
                                            for (var index = 0; index < oidGroups.size(); index++)
                                            {
                                                NMSConstants.sendSNMPMetricProvisionEvent(context.put(NMSConstants.SNMP_OID_GROUP, oidGroups.getJsonObject(index)));
                                            }
                                        }

                                        else
                                        {
                                            var items = MetricConfigStore.getStore().getItemsByValues(Metric.METRIC_OBJECT, objects);

                                            var metrics = new ArrayList<JsonObject>();

                                            for (var index = 0; index < items.size(); index++)
                                            {
                                                var item = items.getJsonObject(index);

                                                if ((item.getString(Metric.METRIC_PLUGIN).equalsIgnoreCase(NMSConstants.MetricPlugin.SNMP_SCALAR_METRIC.getName())
                                                        || item.getString(Metric.METRIC_PLUGIN).equalsIgnoreCase(NMSConstants.MetricPlugin.SNMP_TABULAR_METRIC.getName()))
                                                        && item.getJsonObject(Metric.METRIC_CONTEXT).containsKey(DBConstants.FIELD_TYPE)
                                                        && item.getJsonObject(Metric.METRIC_CONTEXT).getString(DBConstants.FIELD_TYPE).equalsIgnoreCase(DBConstants.ENTITY_TYPE_USER))
                                                {
                                                    metrics.add(new JsonObject().put(ID, item.getLong(ID)));
                                                }
                                            }

                                            NMSConstants.updateMetrics(metrics, EventBusConstants.EVENT_METRIC_UNPROVISION);
                                        }

                                        ObjectConfigStore.getStore().updateItems(objects).onComplete(asyncResult -> promise.complete());

                                    }, false, asyncResult ->
                                    {
                                    });
                                }

                                else
                                {
                                    this.send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_INTERNAL_SERVER_ERROR).put(GlobalConstants.STATUS, STATUS_FAIL)
                                            .put(ERROR, result.failed() ? CommonUtil.formatStackTrace(result.cause().getStackTrace()) : UNKNOWN)
                                            .put(ERROR_CODE, ErrorCodes.ERROR_CODE_INTERNAL_ERROR)
                                            .put(GlobalConstants.MESSAGE, String.format(ErrorMessageConstants.SNMP_DEVICE_CATALOG_ASSIGN_FAILED, result.failed() ? result.cause().getMessage() : UNKNOWN)));
                                }
                            });
                }

                else
                {
                    this.send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_BAD_REQUEST).put(ERROR_CODE, ErrorCodes.ERROR_CODE_NO_ITEM_FOUND).put(GlobalConstants.STATUS, GlobalConstants.STATUS_FAIL)
                            .put(GlobalConstants.MESSAGE, String.format(ErrorMessageConstants.SNMP_DEVICE_CATALOG_ASSIGN_FAILED, String.format(ErrorMessageConstants.ITEM_NOT_FOUND_IN_STORE, APIConstants.Entity.OBJECT.getName()))));
                }
            }

            else
            {
                this.send(routingContext, new JsonObject().put(ERROR_CODE, ErrorCodes.ERROR_CODE_INTERNAL_ERROR).put(RESPONSE_CODE, HttpStatus.SC_BAD_REQUEST).put(GlobalConstants.STATUS, GlobalConstants.STATUS_FAIL).put(GlobalConstants.MESSAGE, String.format(ErrorMessageConstants.SCHEMA_FILE_NOT_FOUND, endpoint)));
            }
        }

        catch (Exception exception)
        {
            APIUtil.sendResponse(exception, routingContext);
        }
    }

    private void getVendors(RoutingContext routingContext)
    {
        try
        {
            this.send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_OK).put(GlobalConstants.STATUS, STATUS_SUCCEED)
                    .put(GlobalConstants.RESULT, SNMPDeviceCatalogConfigStore.getStore().flatItems(SNMPDeviceCatalog.SNMP_DEVICE_CATALOG_VENDOR)));
        }
        catch (Exception exception)
        {
            APIUtil.sendResponse(exception, routingContext);
        }
    }

    @Override
    protected Future<JsonObject> getReferencesPreHook(RoutingContext routingContext, JsonObject response)
    {

        try
        {
            var userGroups = UserConfigStore.getStore().getUserGroupsById(routingContext.user().principal().getLong(ID));

            if (response.containsKey(APIConstants.Entity.OBJECT.getName()))
            {
                var objects = response.getJsonArray(APIConstants.Entity.OBJECT.getName());

                var entities = new JsonArray();

                for (var index = 0; index < objects.size(); index++)
                {
                    entities.add(objects.getJsonObject(index).getLong(ID));
                }
                response.put(APIConstants.Entity.OBJECT.getName(), ObjectConfigStore.getStore().getItems(ObjectConfigStore.getStore().getIdsByGroups(userGroups, entities)));
            }

        }
        catch (Exception ignored)
        {

        }
        return Future.succeededFuture(response);
    }

    protected Future<JsonObject> getEntityCountPreHook(JsonObject response)
    {
        var promise = Promise.<JsonObject>promise();

        try
        {
            var userGroups = new JsonArray();

            if (response.containsKey(USER_ID))
            {
                userGroups = UserConfigStore.getStore().getUserGroupsById(response.getLong(USER_ID));

                response.remove(USER_ID);
            }

            var futures = new ArrayList<Future<Void>>();

            for (var catalogue : response)
            {
                futures.add(getQualifiedEntities(Long.parseLong(catalogue.getKey()), userGroups, response));
            }

            Future.join(futures).onComplete(result ->
                    promise.complete(response));

        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
        return promise.future();
    }

    private Future<Void> getQualifiedEntities(Long id, JsonArray userGroups, JsonObject response)
    {
        var promise = Promise.<Void>promise();

        this.configStore.getReferenceEntities(id).onComplete(result ->
        {
            try
            {
                if (result.succeeded())
                {
                    if (result.result().containsKey(APIConstants.Entity.OBJECT.getName()))
                    {
                        var objects = result.result().getJsonArray(APIConstants.Entity.OBJECT.getName());

                        var entities = new JsonArray();

                        for (var index = 0; index < objects.size(); index++)
                        {
                            entities.add(objects.getJsonObject(index).getLong(ID));
                        }
                        response.put(id.toString(), ObjectConfigStore.getStore().getIdsByGroups(userGroups, entities).size());
                    }

                    promise.complete();
                }
                else
                {
                    promise.fail(result.cause());
                }
            }
            catch (Exception exception)
            {
                LOGGER.error(exception);

                promise.fail(exception);
            }

        });

        return promise.future();
    }
}
