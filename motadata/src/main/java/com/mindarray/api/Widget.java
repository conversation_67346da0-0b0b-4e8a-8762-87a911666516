/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

/*
 *	Change Logs:
 *	Date			Author			    Notes
 *  28-Feb-2025		Darshan Parmar		MOTADATA-5215: SonarQube Suggestions Resolution
 */
package com.mindarray.api;

import com.mindarray.Bootstrap;
import com.mindarray.GlobalConstants;
import com.mindarray.eventbus.EventBusConstants;
import com.mindarray.store.DashboardConfigStore;
import com.mindarray.store.TemplateConfigStore;
import com.mindarray.store.WidgetConfigStore;
import com.mindarray.util.CommonUtil;
import com.mindarray.util.Logger;
import io.vertx.core.Future;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import io.vertx.ext.web.RoutingContext;
import org.apache.http.HttpStatus;

import static com.mindarray.GlobalConstants.ID;
import static com.mindarray.GlobalConstants.MOTADATA_API;

public class Widget extends AbstractAPI
{
    private static final Logger LOGGER = new Logger(Widget.class, MOTADATA_API, "Widget API");

    public Widget()
    {
        super("widgets", WidgetConfigStore.getStore(), LOGGER);
    }

    @Override
    protected void getAll(RoutingContext routingContext)
    {
        try
        {
            super.send(routingContext, new JsonObject().put(APIConstants.RESPONSE_CODE, HttpStatus.SC_OK)
                    .put(GlobalConstants.STATUS, GlobalConstants.STATUS_SUCCEED).put(GlobalConstants.RESULT, WidgetConfigStore.getStore().getItems()));
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }

    @Override
    protected Future<Void> afterCreate(JsonObject entity, RoutingContext routingContext)
    {
        this.send(routingContext, entity);

        Bootstrap.vertx().eventBus().publish(EventBusConstants.EVENT_CHANGE_NOTIFICATION, new JsonObject()
                .put(EventBusConstants.CHANGE_NOTIFICATION_TYPE, EventBusConstants.ChangeNotificationType.ADD_WIDGET.name()).put(EventBusConstants.EVENT_CONTEXT, entity));

        return Future.succeededFuture();
    }

    //after successfully dashboard create and if dashboard is default than this dashboard id store in user preference
    @Override
    protected Future<JsonObject> afterUpdate(JsonObject entity, RoutingContext routingContext)
    {
        this.send(routingContext, entity);

        Bootstrap.vertx().eventBus().publish(EventBusConstants.EVENT_CHANGE_NOTIFICATION, new JsonObject()
                .put(EventBusConstants.CHANGE_NOTIFICATION_TYPE, EventBusConstants.ChangeNotificationType.UPDATE_WIDGET.name()).put(EventBusConstants.EVENT_CONTEXT, entity));

        return Future.succeededFuture();
    }

    @Override
    protected Future<Void> afterDelete(JsonObject response, RoutingContext routingContext)
    {
        Bootstrap.vertx().eventBus().publish(EventBusConstants.EVENT_CHANGE_NOTIFICATION, new JsonObject()
                .put(EventBusConstants.CHANGE_NOTIFICATION_TYPE, EventBusConstants.ChangeNotificationType.DELETE_WIDGET.name()).put(EventBusConstants.EVENT_CONTEXT, response));

        return super.afterDelete(response, routingContext);
    }

    @Override
    protected Future<JsonObject> getReferencesPreHook(RoutingContext routingContext, JsonObject response)
    {
        try
        {
            response = new JsonObject();

            var widgetId = CommonUtil.getLong(routingContext.request().getParam(ID));

            var dashboardIds = DashboardConfigStore.getStore().getIds();

            var items = TemplateConfigStore.getStore().getItems();

            var dashboards = new JsonArray();

            var templates = new JsonArray();

            // in case of widget created in dashboard, we need to check for the reference for dashboard
            for (var index = 0; index < dashboardIds.size(); index++)
            {
                var context = DashboardConfigStore.getStore().getItem(dashboardIds.getLong(index)).getJsonObject(Dashboard.DASHBOARD_CONTEXT);

                if (context != null)
                {
                    var widgets = context.getJsonArray(Dashboard.DASHBOARD_WIDGETS);

                    for (var widget = 0; widget < widgets.size(); widget++)
                    {
                        var item = widgets.getJsonObject(widget);

                        if (item != null && !item.isEmpty() && item.containsKey(ID) && item.getLong(ID).equals(widgetId))
                        {
                            dashboards.add(DashboardConfigStore.getStore().getItem(dashboardIds.getLong(index)));

                            break;
                        }
                    }
                }
            }

            // in case of widget created in custom tab, we need to check for the reference for template
            for (var index = 0; index < items.size(); index++)
            {
                var widgets = items.getJsonObject(index).getJsonArray("template.widgets", new JsonArray());

                if (!widgets.isEmpty())
                {
                    for (var widget = 0; widget < widgets.size(); widget++)
                    {
                        var item = widgets.getJsonObject(widget);

                        if (item != null && !item.isEmpty() && item.containsKey(ID) && item.getLong(ID).equals(widgetId))
                        {
                            templates.add(items.getJsonObject(index));

                            break;
                        }

                    }
                }
            }

            if (!dashboards.isEmpty())
            {
                response.put(APIConstants.Entity.DASHBOARD.getName(), dashboards);
            }
            if (!templates.isEmpty())
            {
                response.put(APIConstants.Entity.TEMPLATE.getName(), templates);
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
        return Future.succeededFuture(response);
    }
}
