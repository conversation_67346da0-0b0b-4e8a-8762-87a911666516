/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

/*
 *  Change Logs:
 *  Date			Author			Notes
 *  6-Feb-2025		Ya<PERSON> T<PERSON>wari		MOTADATA-4878: On data retention policy update operation sending notification to all the datastores

 */

package com.mindarray.api;

import com.mindarray.store.DataRetentionPolicyConfigStore;
import com.mindarray.store.RemoteEventProcessorConfigStore;
import com.mindarray.util.Logger;
import com.mindarray.visualization.VisualizationConstants;
import io.vertx.core.Future;
import io.vertx.core.json.JsonObject;
import io.vertx.ext.web.RoutingContext;

import static com.mindarray.GlobalConstants.MOTADATA_API;

public class DataRetentionPolicy extends AbstractAPI
{
    public static final String DATA_RETENTION_POLICY_CONTEXT = "data.retention.policy.context";

    private static final Logger LOGGER = new Logger(DataRetentionPolicy.class, MOTADATA_API, "Data Retention Policy");

    public DataRetentionPolicy()
    {
        super("data-retention-policy", DataRetentionPolicyConfigStore.getStore(), new Logger(DataRetentionPolicyConfigStore.class, MOTADATA_API, "Data Retention Policy API"));
    }

    @Override
    protected Future<JsonObject> afterUpdate(JsonObject entity, RoutingContext routingContext)
    {
        this.send(routingContext, entity);

        VisualizationConstants.send(LOGGER, RemoteEventProcessorConfigStore.getStore().getIds());//need to update data retention settings to DB

        return Future.succeededFuture();
    }
}
