/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

/*
 * Change Logs:
 *  Date			Author			         Notes
 *  28-Feb-2025     Smit Prajapati           MOTADATA-4956: Rule Based Tagging
 */

package com.mindarray.api;

import com.mindarray.Bootstrap;
import com.mindarray.GlobalConstants;
import com.mindarray.db.DBConstants;
import com.mindarray.store.TagConfigStore;
import com.mindarray.store.TagRuleConfigStore;
import com.mindarray.util.CommonUtil;
import com.mindarray.util.Logger;
import io.vertx.core.Future;
import io.vertx.core.Promise;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import io.vertx.ext.web.Router;
import io.vertx.ext.web.RoutingContext;
import org.apache.http.HttpStatus;

import java.util.ArrayList;
import java.util.regex.Pattern;

import static com.mindarray.GlobalConstants.*;
import static com.mindarray.api.APIConstants.ENTITY_PROPERTY_COUNT;
import static com.mindarray.api.APIConstants.RESPONSE_CODE;
import static com.mindarray.eventbus.EventBusConstants.EVENT_TAG_RULE_RUN;

public class TagRule extends AbstractAPI
{
    public static final String TAG_RULE_NAME = "tag.rule.name";
    public static final String TAG_RULE_TYPE = "tag.rule.type";
    public static final String TAG_RULE_OPERATION = "tag.rule.operation";
    public static final String TAG_RULE_TAGS = "tag.rule.tags";
    public static final String TAG_RULE_DYNAMIC_TAGS = "tag.rule.dynamic.tags";
    public static final String TAG_RULE_CONDITIONS = "tag.rule.conditions";
    public static final String TAG_RULE_TRIGGERED_TIME = "tag.rule.triggered.time";
    public static final String TAG_RULE_INCLUSIVE_CONDITION = "tag.rule.inclusive.condition";
    public static final String TAG_RULE_TEST = "tag.rule.test";
    public static final String TAG_RULE_OPERATION_ASSIGN = "Assign";
    private static final Logger LOGGER = new Logger(TagRule.class, MOTADATA_API, "Tag Rule API");
    private static final Pattern PATTERN = Pattern.compile("(.*):\\{(\\S+)}");

    public TagRule()
    {
        super("tag-rules", TagRuleConfigStore.getStore(), LOGGER);
    }

    @Override
    public void init(Router router)
    {
        try
        {
            super.init(router);

            router.get("/" + endpoint + "/:id/references").handler(this::getReferences);
        }

        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }


    /*
     *  Rule can be created with 2 type of tags,
     *  Static tags and Dynamic tags.
     *   - Static tags are tags which are directly added to the rule.
     *   - Dynamic tags are tags which are added to the rule with a pattern, Dynamic tags are resolved at runtime.(example: host:{object.host})
     *
     *  Before Create we will separate this tags and store them in different fields.
     * */
    @Override
    protected Future<JsonObject> beforeCreate(RoutingContext routingContext)
    {
        var promise = Promise.<JsonObject>promise();

        try
        {
            var dynamicTags = new JsonObject();

            var staticTags = new JsonArray();

            var requestBody = routingContext.body().asJsonObject();

            var tags = requestBody.getJsonArray(TAG_RULE_TAGS);

            if (!tags.isEmpty())
            {
                for (var index = 0; index < tags.size(); index++)
                {
                    var tag = tags.getString(index);

                    var matcher = PATTERN.matcher(tag);

                    if (matcher.find() && matcher.groupCount() == 2)
                    {
                        if (matcher.group(1) != null && !matcher.group(1).isEmpty() && matcher.group(2) != null && !matcher.group(2).isEmpty())
                        {
                            dynamicTags.put(matcher.group(1), matcher.group(2));
                        }
                    }
                    else
                    {
                        staticTags.add(tag);
                    }
                }
            }

            requestBody.put(TAG_RULE_TAGS, TagConfigStore.getStore().addItems(staticTags, Tag.TagType.OBJECT.getName(), DBConstants.ENTITY_TYPE_USER));

            requestBody.put(TAG_RULE_DYNAMIC_TAGS, dynamicTags);

            promise.complete(requestBody);
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);

            promise.fail(exception);
        }

        return promise.future();
    }

    /*
     *  Will return the count of entities tagged by the rule.
     * */
    @Override
    protected Future<JsonObject> getEntityCountPreHook(JsonObject response)
    {
        var future = Promise.<JsonObject>promise();

        try
        {
            var items = TagRuleConfigStore.getStore().getItems();

            var futures = new ArrayList<Future<String>>();

            for (var index = 0; index < items.size(); index++)
            {
                var item = items.getJsonObject(index);

                var promise = Promise.<String>promise();

                futures.add(promise.future());

                Bootstrap.vertx().eventBus().<JsonObject>request(EVENT_TAG_RULE_RUN, item.put(TagRule.TAG_RULE_TEST, true), reply ->
                {
                    try
                    {
                        if (reply.succeeded())
                        {
                            var result = reply.result().body();

                            if (result.containsKey(STATUS) && result.getString(STATUS).equalsIgnoreCase(STATUS_SUCCEED))
                            {
                                promise.complete(item.getLong(ID) + HASH_SEPARATOR + result.getJsonArray(RESULT).size());
                            }
                            else
                            {
                                promise.complete(item.getLong(ID) + HASH_SEPARATOR + 0);
                            }
                        }
                        else
                        {
                            promise.complete(item.getLong(ID) + HASH_SEPARATOR + 0);
                        }
                    }
                    catch (Exception exception)
                    {
                        LOGGER.error(exception);

                        promise.complete(item.getLong(ID) + HASH_SEPARATOR + 0);
                    }
                });
            }

            Future.all(futures).onComplete(asyncResult ->
            {
                if (asyncResult.succeeded())
                {
                    var result = new JsonObject();

                    for (var object : asyncResult.result().list())
                    {
                        var tokens = CommonUtil.getString(object).split(HASH_SEPARATOR);

                        result.put(tokens[0], CommonUtil.getInteger(tokens[1]));
                    }

                    future.complete(result);
                }
                else
                {
                    LOGGER.error(asyncResult.cause());
                }
            });
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }

        return future.future();
    }

    @Override
    protected Future<Void> afterGetAll(JsonArray items, RoutingContext routingContext)
    {
        try
        {
            for (var index = 0; index < items.size(); index++)
            {
                resolveTags(items.getJsonObject(index));
            }

            this.send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_OK).put(GlobalConstants.STATUS, STATUS_SUCCEED).put(GlobalConstants.RESULT, items));
        }
        catch (Exception exception)
        {
            APIUtil.sendResponse(exception, routingContext);
        }

        return Future.succeededFuture();
    }

    @Override
    protected Future<Void> afterGet(JsonObject item, RoutingContext routingContext)
    {
        try
        {
            resolveTags(item);

            Bootstrap.vertx().eventBus().<JsonObject>request(EVENT_TAG_RULE_RUN, item.put(TagRule.TAG_RULE_TEST, true), reply ->
            {
                try
                {
                    if (reply.succeeded())
                    {
                        var result = reply.result().body();

                        if (result.containsKey(STATUS) && result.getString(STATUS).equalsIgnoreCase(STATUS_SUCCEED))
                        {
                            this.send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_OK).put(STATUS, STATUS_SUCCEED).put(GlobalConstants.RESULT, item.put(ENTITY_PROPERTY_COUNT, result.getJsonArray(RESULT).size())));
                        }
                        else
                        {
                            this.send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_OK).put(STATUS, STATUS_SUCCEED).put(GlobalConstants.RESULT, item.put(ENTITY_PROPERTY_COUNT, 0)));
                        }
                    }
                    else
                    {
                        this.send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_OK).put(STATUS, STATUS_SUCCEED).put(GlobalConstants.RESULT, item.put(ENTITY_PROPERTY_COUNT, 0)));
                    }
                }
                catch (Exception exception)
                {
                    LOGGER.error(exception);

                    this.send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_OK).put(STATUS, STATUS_SUCCEED).put(GlobalConstants.RESULT, item.put(ENTITY_PROPERTY_COUNT, 0)));
                }
            });
        }

        catch (Exception exception)
        {
            APIUtil.sendResponse(exception, routingContext);
        }

        return Future.succeededFuture();
    }

    //resolve tags from hashcode to actual tags
    private void resolveTags(JsonObject item)
    {
        //resolve static tags
        var items = TagConfigStore.getStore().getItems(item.getJsonArray(TAG_RULE_TAGS));

        //resolve dynamic tags
        if (item.containsKey(TAG_RULE_DYNAMIC_TAGS) && !item.getJsonObject(TAG_RULE_DYNAMIC_TAGS).isEmpty())
        {
            for (var key : item.getJsonObject(TAG_RULE_DYNAMIC_TAGS).getMap().entrySet())
            {
                items.add(key.getKey() + ":{" + key.getValue() + "}");
            }
        }

        item.put(TAG_RULE_TAGS, items);
    }
}
