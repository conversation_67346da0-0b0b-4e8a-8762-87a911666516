/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

/*
 *  Change Logs:
 *  Date            Author          Notes
 *  6-Feb-2025      <PERSON><PERSON>     MOTADATA-4878: refactored code to add datastore write event to availability datastore instead of metric datastore
 *  19-Mar-2025     Umang Sharma    LocalConsumer for EVENT_STATUS_FLAP_DATASTORE_DUMP added
 */
package com.mindarray.nms;

import com.mindarray.GlobalConstants;
import com.mindarray.api.AIOpsObject;
import com.mindarray.api.APIConstants;
import com.mindarray.datastore.DatastoreConstants;
import com.mindarray.eventbus.EventBusConstants;
import com.mindarray.ha.HAConstants;
import com.mindarray.store.MetricConfigStore;
import com.mindarray.store.ObjectConfigStore;
import com.mindarray.store.ObjectStatusCacheStore;
import com.mindarray.util.*;
import com.mindarray.visualization.VisualizationConstants;
import io.vertx.core.AbstractVerticle;
import io.vertx.core.Promise;
import io.vertx.core.buffer.Buffer;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;

import java.io.File;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.util.HashSet;
import java.util.Set;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;

import static com.mindarray.GlobalConstants.*;
import static com.mindarray.eventbus.EventBusConstants.CHANGE_NOTIFICATION_TYPE;
import static com.mindarray.nms.NMSConstants.*;

/**
 * ObjectStatusCalculator is responsible for tracking, calculating, and persisting the status of monitored objects
 * and their instances. It manages status flaps (changes in status), tracks durations of different statuses,
 * and provides status information to other components of the system.
 * <p>
 * Key responsibilities:
 * - Tracking object and instance status durations
 * - Recording status changes (flaps) and their history
 * - Persisting status information to the datastore
 * - Providing status duration information to other components
 * - Handling object deletion and cache updates
 * - Managing column mappers for the datastore
 * <p>
 * This class operates as a Vert.x verticle and uses the event bus for communication with other components.
 */
public class ObjectStatusCalculator extends AbstractVerticle
{
    // Logger for this class
    private static final Logger LOGGER = new Logger(ObjectStatusCalculator.class, GlobalConstants.MOTADATA_NMS, "Object Status Calculator");

    // Metrics used for availability calculations
    private static final JsonArray AVAILABILITY_METRICS = new JsonArray().add("seconds").add("percent");

    // Timer constants from configuration
    private static final int STATUS_FLAP_DURATION_DUMP_TIMER_SECONDS = MotadataConfigUtil.getStatusFlapDurationDumpTimerSeconds();

    private static final int INSTANCE_STATUS_FLUSH_TIMER_SECONDS = MotadataConfigUtil.getInstanceStatusFlushTimerSeconds();

    private static final int OBJECT_STATUS_FLUSH_TIMER_SECONDS = MotadataConfigUtil.getObjectStatusFlushTimerSeconds();
    /**
     * Set of instance types that should be considered for status flap tracking
     * Loaded from configuration via MotadataConfigUtil
     */
    private static final Set<String> INSTANCES = MotadataConfigUtil.getStatusFlapInstances();
    // StringBuilder for building column mapper strings
    private final StringBuilder builder = new StringBuilder(0);
    // Stores cumulative durations for objects and instances (persistent across restarts)
    private final JsonObject cumulativeDurations = new JsonObject();
    // Stores hourly durations for objects and instances (resets periodically)
    private final JsonObject hourlyDurations = new JsonObject();
    // Flag to track if durations have been updated
    private final AtomicBoolean updated = new AtomicBoolean();
    // Flag to track if cache files need to be synchronized
    private boolean dirty = false;
    // Set of column mappers to avoid duplicates
    private Set<String> columnMappers;

    /**
     * Initializes the ObjectStatusCalculator verticle.
     * <p>
     * This method:
     * 1. Sets up event bus consumers for status updates and duration queries
     * 2. Loads status flap durations from a backup file if available
     * 3. Starts periodic timers for:
     * - Flushing status flap information to disk
     * - Updating object status information in the datastore
     * - Updating instance status information in the datastore
     * 4. Sets up a consumer for change notifications (object deletion, cache updates)
     *
     * @param promise Promise to be completed when initialization is done
     * @throws Exception If an error occurs during initialization
     */
    @Override
    public void start(Promise<Void> promise) throws Exception
    {
        try
        {
            // Initialize status flap dump timer
            // This timer controls when status flap data is dumped to the datastore
            var hourlyFlapDurationFlushTimer = new AtomicInteger(3600); //1 hour

            // Align timer with the nearest minute boundary for consistent scheduling
            hourlyFlapDurationFlushTimer.set((((59 - LocalDateTime.now().getMinute()) * 60 + (60 - LocalDateTime.now().getSecond())) / 10) * 10);

            // Register event bus consumer for object status updates
            // This handles status changes for objects and processes flaps
            vertx.eventBus().<JsonObject>localConsumer(EventBusConstants.EVENT_OBJECT_STATUS_UPDATE,
                    message -> processFlap(message.body(), true)).exceptionHandler(LOGGER::error);

            // Register event bus consumer for duration queries
            // This provides status duration information to other components
            vertx.eventBus().<JsonObject>localConsumer(EventBusConstants.EVENT_OBJECT_STATUS_DURATION_QUERY,
                    message -> message.reply(fetch(message.body()))).exceptionHandler(LOGGER::error);

            // Initialize set to track column mappers and avoid duplicates
            columnMappers = new HashSet<>();

            // Initialize timer for dumping status flap data to disk
            var statusFlapDurationDumpTimer = new AtomicInteger(STATUS_FLAP_DURATION_DUMP_TIMER_SECONDS);

            // Initialize timer for flushing object status data to datastore
            var objectStatusFlushTimer = new AtomicInteger(OBJECT_STATUS_FLUSH_TIMER_SECONDS);

            // Initialize timer for flushing instance status data to datastore
            var instanceStatusFlushTimer = new AtomicInteger(INSTANCE_STATUS_FLUSH_TIMER_SECONDS);

            // Load duration data from persistent storage
            // These files contain historical status duration information
            LOGGER.info("Loading status duration data from persistent storage...");

            var file = new File(GlobalConstants.CURRENT_DIR + GlobalConstants.PATH_SEPARATOR +
                    GlobalConstants.CONFIG_DIR + GlobalConstants.PATH_SEPARATOR + "status-flap-durations");

            if (file.exists())
            {
                var buffer = vertx.fileSystem().readFileBlocking(file.getPath());

                if (buffer != null && buffer.getBytes().length > 0)
                {
                    new JsonObject(Buffer.buffer(CodecUtil.toBytes(buffer.getBytes())))
                            .forEach(entry -> cumulativeDurations.put(entry.getKey(), JsonObject.mapFrom(entry.getValue())));

                    LOGGER.info(String.format("%s loaded from the backup file...", "status-flap-durations"));
                }

                file.delete();
            }
            else
            {
                load(CUMULATIVE_STATUS_FLAP_DURATIONS, cumulativeDurations);
            }

            load(HOURLY_STATUS_FLAP_DURATIONS, hourlyDurations);

            // Set up main periodic timer (runs every 10 seconds)
            // This timer handles:
            // 1. Updating object and instance durations
            // 2. Processing object and instance statuses
            // 3. Dumping status flap data to datastore
            // 4. Saving duration data to disk
            LOGGER.info("Starting main periodic timer for status processing...");

            vertx.setPeriodic(10 * 1000L, timer ->
            {
                try
                {
                    // Decrement all timers by 10 seconds
                    statusFlapDurationDumpTimer.set(statusFlapDurationDumpTimer.get() - 10);

                    hourlyFlapDurationFlushTimer.set(hourlyFlapDurationFlushTimer.get() - 10);

                    objectStatusFlushTimer.set(objectStatusFlushTimer.get() - 10);

                    instanceStatusFlushTimer.set(instanceStatusFlushTimer.get() - 10);


                    // Get current timestamp for status records
                    var timestamp = DateTimeUtil.currentSeconds();

                    // Process each object in the status cache
                    for (var entry : ObjectStatusCacheStore.getStore().getItems().entrySet())
                    {
                        // Get object configuration
                        var item = ObjectConfigStore.getStore().getItem(entry.getKey(), false);

                        if (item == null)
                        {
                            // Skip if object configuration is not available
                            continue;
                        }

                        // Extract object identifiers
                        var id = CommonUtil.getString(entry.getKey());

                        var objectId = item.getInteger(AIOpsObject.OBJECT_ID);

                        // Update duration counters for this object
                        updateObjectDurations(id);

                        // Process overall object status if the timer has expired
                        if (objectStatusFlushTimer.get() <= 0)
                        {
                            LOGGER.debug("Processing object status for object ID: " + id);

                            processObjectStatus(timestamp, item, objectId, entry.getValue());
                        }

                        // Process individual instance statuses for this object
                        processInstanceStatuses(timestamp, item, id, objectId, entry.getKey(), instanceStatusFlushTimer.get());
                    }

                    // Check if it's time to dump status flap data to datastore
                    if (hourlyFlapDurationFlushTimer.get() <= 0)
                    {
                        LOGGER.info("Status flap dump timer expired, dumping data to datastore...");

                        dump();

                        hourlyFlapDurationFlushTimer.set(3600); // 1 hour
                    }

                    // Check if it's time to flush status flap durations to disk
                    if (statusFlapDurationDumpTimer.get() <= 0)
                    {
                        // Only save if there's data or if durations have been updated
                        if (!cumulativeDurations.isEmpty() || updated.get())
                        {
                            LOGGER.info("Saving status flap durations to disk...");

                            save(CUMULATIVE_STATUS_FLAP_DURATIONS, cumulativeDurations);

                            save(HOURLY_STATUS_FLAP_DURATIONS, hourlyDurations);

                            dirty = true;
                        }

                        // Reset update flag and timer
                        updated.set(false);

                        statusFlapDurationDumpTimer.set(STATUS_FLAP_DURATION_DUMP_TIMER_SECONDS);
                    }

                    // Reset object status flush timer if expired
                    if (objectStatusFlushTimer.get() <= 0)
                    {
                        objectStatusFlushTimer.set(OBJECT_STATUS_FLUSH_TIMER_SECONDS);
                    }

                    // Reset instance status flush timer if expired
                    if (instanceStatusFlushTimer.get() <= 0)
                    {
                        instanceStatusFlushTimer.set(INSTANCE_STATUS_FLUSH_TIMER_SECONDS);
                    }
                }
                catch (Exception exception)
                {
                    LOGGER.error(exception);
                }
            });

            //24349
            vertx.eventBus().<JsonObject>localConsumer(EventBusConstants.EVENT_CHANGE_NOTIFICATION
                    , message ->
                    {
                        var event = message.body();

                        if (EventBusConstants.ChangeNotificationType.valueOf(event.getString(EventBusConstants.CHANGE_NOTIFICATION_TYPE)) == EventBusConstants.ChangeNotificationType.DELETE_OBJECT)
                        {
                            var id = event.getLong(ID);

                            var existingObjects = ObjectStatusCacheStore.getStore().getInstanceItems().get(id);

                            if (event.getJsonArray(NMSConstants.METRIC_INSTANCES) != null && !event.getJsonArray(NMSConstants.METRIC_INSTANCES).isEmpty())
                            {

                                if (existingObjects != null && !existingObjects.isEmpty())
                                {
                                    var objects = event.getJsonArray(NMSConstants.METRIC_INSTANCES);

                                    var iterator = existingObjects.entrySet().iterator();

                                    while (iterator.hasNext() && !objects.isEmpty())
                                    {
                                        var entry = iterator.next();

                                        if (!entry.getKey().equalsIgnoreCase(EMPTY_VALUE) && objects.contains(entry.getKey().split(INSTANCE_SEPARATOR)[1]))
                                        {
                                            iterator.remove();

                                            objects.remove(entry.getKey().split(INSTANCE_SEPARATOR)[1]);

                                            cumulativeDurations.getJsonObject(CommonUtil.getString(id)).remove(entry.getKey());

                                            hourlyDurations.getJsonObject(CommonUtil.getString(id)).remove(entry.getKey());
                                        }
                                    }


                                }
                            }
                            else
                            {
                                ObjectStatusCacheStore.getStore().deleteItem(id);

                                cumulativeDurations.remove(CommonUtil.getString(id));

                                hourlyDurations.remove(CommonUtil.getString(id));

                                updated.set(true);

                            }
                        }

                        // Handle cache update events for high availability synchronization
                        else if (EventBusConstants.ChangeNotificationType.valueOf(event.getString(CHANGE_NOTIFICATION_TYPE)) == EventBusConstants.ChangeNotificationType.UPDATE_CACHE)
                        {
                            // Synchronize status flap durations cache files if they've been modified
                            if (dirty)
                            {
                                LOGGER.info("Synchronizing status flap duration cache files for HA...");

                                dirty = false;

                                // Notify observers about cumulative durations
                                HAConstants.notifyObserver(new JsonObject()
                                        .put(HAConstants.CACHE_NAME, CUMULATIVE_STATUS_FLAP_DURATIONS)
                                        .put(RESULT, vertx.fileSystem().readFileBlocking(GlobalConstants.CURRENT_DIR +
                                                GlobalConstants.PATH_SEPARATOR + GlobalConstants.CONFIG_DIR +
                                                GlobalConstants.PATH_SEPARATOR + CUMULATIVE_STATUS_FLAP_DURATIONS)));

                                // Notify observers about hourly durations
                                HAConstants.notifyObserver(new JsonObject()
                                        .put(HAConstants.CACHE_NAME, HOURLY_STATUS_FLAP_DURATIONS)
                                        .put(RESULT, vertx.fileSystem().readFileBlocking(GlobalConstants.CURRENT_DIR +
                                                GlobalConstants.PATH_SEPARATOR + GlobalConstants.CONFIG_DIR +
                                                GlobalConstants.PATH_SEPARATOR + HOURLY_STATUS_FLAP_DURATIONS)));
                            }

                            // Synchronize status flaps cache file if it's been modified
                            if (ObjectStatusCacheStore.getStore().dirty())
                            {
                                LOGGER.info("Synchronizing status flaps cache file for HA...");

                                ObjectStatusCacheStore.getStore().setDirty(false);

                                // Notify observers about status flaps
                                HAConstants.notifyObserver(new JsonObject()
                                        .put(HAConstants.CACHE_NAME, STATUS_FLAPS)
                                        .put(RESULT, vertx.fileSystem().readFileBlocking(GlobalConstants.CURRENT_DIR +
                                                GlobalConstants.PATH_SEPARATOR + GlobalConstants.CONFIG_DIR +
                                                GlobalConstants.PATH_SEPARATOR + STATUS_FLAPS)));
                            }
                        }

                    }).exceptionHandler(LOGGER::error);

            promise.complete();
        }
        catch (Exception exception)
        {
            promise.fail(exception.getCause());

            LOGGER.error(exception);
        }
    }

    /**
     * Dumps hourly status flap data to the datastore.
     * <p>
     * This method is called periodically to write accumulated hourly status duration data
     * to the datastore. It processes each object and instance in the hourlyDurations map,
     * creates a flap context for each, and sends it for processing. This ensures that
     * status duration data is regularly persisted for reporting and analysis.
     */
    private void dump()
    {
        LOGGER.info("Status Flap Datastore Dump...");

        var timestamp = DateTimeUtil.currentSeconds();

        for (var duration : hourlyDurations.getMap().entrySet())
        {
            var id = CommonUtil.getLong(duration.getKey());

            for (var entry : ((JsonObject) duration.getValue()).getMap().entrySet())
            {
                var key = entry.getKey();

                // Skip processing if not empty value or not in status flap instances
                if (!key.equalsIgnoreCase(EMPTY_VALUE) &&
                        !INSTANCES.contains(key.split(INSTANCE_SEPARATOR)[0]))
                {
                    continue;
                }

                processFlap(createFlapContext(id, key, timestamp, CommonUtil.getLong(entry.getValue())), false);
            }
        }
    }

    /**
     * Creates a context object for flap processing.
     * <p>
     * This method builds a JsonObject containing all the necessary information for
     * processing a status flap event. It includes the object ID, instance key, timestamp,
     * duration, and status information. This context is used by the processFlap method
     * to handle status changes and update the datastore.
     *
     * @param id        Object ID
     * @param key       Instance key (can be EMPTY_VALUE for object-level status)
     * @param timestamp The timestamp for this flap event
     * @param duration  The duration in seconds for this status
     * @return JsonObject with complete flap context information
     */
    private JsonObject createFlapContext(long id, String key, long timestamp, long duration)
    {
        return new JsonObject()
                .put(ID, id)
                .put(NMSConstants.INSTANCE_NAME, key)
                .put(PREVIOUS_FLAP_TIMESTAMP, timestamp)
                .put(DURATION, duration)
                .put(VisualizationConstants.VisualizationDataSource.HOURLY_OBJECT_STATUS_FLAP.getName(), GlobalConstants.YES)
                .put(PREVIOUS_FLAP_STATUS, ObjectStatusCacheStore.getStore().getItem(id));
    }

    /**
     * Fetches cumulative duration data for the specified entities.
     * <p>
     * This method retrieves the cumulative status durations for a list of object IDs
     * provided in the context. It's used to respond to duration queries from other
     * components of the system.
     *
     * @param context JsonObject containing a list of entity IDs in the ENTITIES array
     * @return JsonObject containing cumulative duration data for each requested entity
     */
    private JsonObject fetch(JsonObject context)
    {
        var result = new JsonObject();

        try
        {
            if (!cumulativeDurations.isEmpty())
            {
                for (var i = 0; i < context.getJsonArray(ENTITIES).size(); i++)
                {
                    var id = CommonUtil.getString(context.getJsonArray(ENTITIES).getLong(i));

                    result.put(id, cumulativeDurations.getJsonObject(id));
                }
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }

        return result;
    }

    /**
     * Processes metric status flap events for both instance and object level monitors.
     * <p>
     * This method handles the processing of status flap events, updating the appropriate
     * duration counters and status records. It retrieves the object configuration,
     * validates the instance data, and processes the status change. The method handles
     * both object-level and instance-level status changes, updating the datastore
     * accordingly.
     *
     * @param context JsonObject containing flap context with object ID, instance name, and other details
     * @param flap    boolean indicating if this is a flap event that should be processed
     */
    private void processFlap(JsonObject context, boolean flap)
    {
        try
        {
            var item = ObjectConfigStore.getStore().getItem(context.getLong(ID));

            if (item == null)
            {
                return;
            }

            var objectId = item.getInteger(AIOpsObject.OBJECT_ID);

            var instance = context.getString(NMSConstants.INSTANCE_NAME);

            var id = CommonUtil.getString(context.getLong(ID));

            // Skip if duration data is not available
            if (!hourlyDurations.containsKey(id) || !hourlyDurations.getJsonObject(id).containsKey(instance))
            {
                return;
            }

            var object = CommonUtil.getString(context.getValue(NMSConstants.INSTANCE_NAME))
                    .equalsIgnoreCase(GlobalConstants.EMPTY_VALUE);

            var duration = context.getLong(DURATION, hourlyDurations.getJsonObject(id).getLong(instance));

            context.put(PREVIOUS_FLAP_TIMESTAMP, context.getLong(PREVIOUS_FLAP_TIMESTAMP) - duration);

            if (flap && CommonUtil.traceEnabled())
            {
                LOGGER.trace("Flapping object with name " + ObjectConfigStore.getStore().getItem(ObjectConfigStore.getStore().getIdByObjectId(objectId)).getString(AIOpsObject.OBJECT_NAME) + " and instance " + instance + " and timestamp is " + context.getLong(PREVIOUS_FLAP_TIMESTAMP) + " and duration " + duration);
            }

            if (object)
            {
                processObjectFlap(context, item, objectId, duration);
            }
            else if (context.getString(VisualizationConstants.VisualizationDataSource.HOURLY_OBJECT_STATUS_FLAP.getName()).equalsIgnoreCase(YES))
            {
                processInstanceFlap(context, item, objectId, instance, duration);
            }

            resetDurations(id, instance, flap);
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }

    /**
     * Process flap for monitor availability
     */
    private void processObjectFlap(JsonObject context, JsonObject object, int objectId, long duration)
    {
        var buffer = Buffer.buffer();

        appendBytes(context.put(AIOpsObject.OBJECT_ID, objectId).put(PLUGIN_ID, NMSConstants.AVAILABILITY_PLUGIN_ID), buffer);

        updateScalarBatch("status.flap.history", context.getString(PREVIOUS_FLAP_STATUS).toUpperCase(),
                NMSConstants.AVAILABILITY_PLUGIN_ID, buffer, false, false);

        updateScalarBatch(REASON, ObjectStatusCacheStore.getStore().getItemByReason(object.getLong(ID), EMPTY_VALUE),
                NMSConstants.AVAILABILITY_PLUGIN_ID, buffer, false, false);

        updateScalarBatch(DURATION, CommonUtil.getString(duration),
                NMSConstants.AVAILABILITY_PLUGIN_ID, buffer, false, false);

        vertx.eventBus().send(EventBusConstants.EVENT_DATASTORE_WRITE + "." +
                DatastoreConstants.DatastoreCategory.METRIC.getName(), buffer.getBytes());
        // Add mappers for monitor duration
        addColumnMapper(DatastoreConstants.DataCategory.NUMERIC.getName(), NMSConstants.AVAILABILITY_PLUGIN_ID,
                "monitor.duration", NO);

        // Add mappers for monitor status flap history
        addColumnMapper(DatastoreConstants.DataCategory.STRING.getName(), NMSConstants.AVAILABILITY_PLUGIN_ID,
                "monitor.status.flap.history", NO);
    }

    /**
     * Process flap for instance
     */
    private void processInstanceFlap(JsonObject context, JsonObject object, int objectId, String instance, long duration)
    {
        var buffer = Buffer.buffer();

        var tokens = instance.split(INSTANCE_SEPARATOR);

        var pluginId = CommonUtil.getInteger(tokens[2]);

        var instancePrefix = tokens[0] + INSTANCE_SEPARATOR;

        appendBytes(context.put(INSTANCE_NAME, tokens[1])
                .put(AIOpsObject.OBJECT_ID, objectId)
                .put(PLUGIN_ID, pluginId), buffer);

        updateScalarBatch(instancePrefix + "status.flap.history",
                context.getString(PREVIOUS_FLAP_STATUS).toUpperCase(), pluginId, buffer, false, false);

        updateScalarBatch(REASON, ObjectStatusCacheStore.getStore().getItemByReason(object.getLong(ID), instance),
                pluginId, buffer, false, false);

        updateScalarBatch(instancePrefix + DURATION,
                CommonUtil.getString(duration), pluginId, buffer, false, false);

        updateScalarBatch(instancePrefix + INSTANCE_NAME, tokens[1], pluginId, buffer, false, false);

        vertx.eventBus().send(EventBusConstants.EVENT_DATASTORE_WRITE + "." +
                DatastoreConstants.DatastoreCategory.METRIC.getName(), buffer.getBytes());

        // Add mappers for instance duration
        addColumnMapper(DatastoreConstants.DataCategory.NUMERIC.getName(), pluginId,
                instancePrefix + DURATION, NO);

        // Add mappers for instance status flap history
        addColumnMapper(DatastoreConstants.DataCategory.STRING.getName(), pluginId,
                instancePrefix + "status.flap.history", NO);
    }

    /**
     * Reset duration counters
     */
    /**
     * Resets duration counters for a specific object instance.
     * <p>
     * This method resets the hourly duration counter for the specified object instance.
     * If the flap parameter is true, it also resets the cumulative duration counter.
     * This is typically called after processing a status flap event to start tracking
     * the new status duration from zero.
     *
     * @param id       The object ID as a string
     * @param instance The instance name
     * @param flap     Boolean flag indicating if this is a flap event that should reset cumulative counters
     */
    private void resetDurations(String id, String instance, boolean flap)
    {
        hourlyDurations.getJsonObject(id).put(instance, 0);

        if (flap)
        {
            cumulativeDurations.getJsonObject(id).put(instance, 0);
        }
    }

    /**
     * Add column mapper and publish update event if needed
     */
    private void addColumnMapper(byte category, int pluginId, String column, String aggregated)
    {
        builder.setLength(0);

        builder.append(category).append(COLUMN_SEPARATOR)
                .append(pluginId).append(COLUMN_SEPARATOR)
                .append(column).append(COLUMN_SEPARATOR)
                .append(aggregated).append(COLUMN_SEPARATOR)
                .append(GlobalConstants.YES);

        if (!columnMappers.contains(builder.toString()))
        {
            columnMappers.add(builder.toString());

            vertx.eventBus().publish(EventBusConstants.EVENT_COLUMN_MAPPER_UPDATE,
                    new JsonObject()
                            .put(CHANGE_NOTIFICATION_TYPE, EventBusConstants.ChangeNotificationType.UPDATE_METRIC_COLUMN.name())
                            .put(DatastoreConstants.MAPPER, builder.toString()));
        }

        builder.setLength(0);
    }

    /**
     * Appends the event data to the buffer in a specific format for availability timestamp and percentage
     *
     * @param buffer    Buffer to append data to
     * @param timestamp Timestamp value
     * @param pluginId  Plugin ID
     * @param objectId  Object ID
     */
    private void appendBytes(Buffer buffer, long timestamp, int pluginId, int objectId)
    {
        buffer.setLongLE(0, timestamp);

        appendPluginInfo(buffer, pluginId);

        buffer.appendByte(DatastoreConstants.DatastoreFormat.VERTICAL.getName().byteValue());

        buffer.appendByte(CommonUtil.getByteValue(DatastoreConstants.DatastoreType.OBJECT_STATUS_METRIC.ordinal()));

        buffer.appendIntLE(objectId);
    }

    /**
     * Appends the event data to the buffer in a specific format for flap history
     *
     * @param event  the JsonObject containing event data
     * @param buffer the Buffer to append the data to
     */
    private void appendBytes(JsonObject event, Buffer buffer)
    {
        buffer.setLongLE(0, event.getLong(PREVIOUS_FLAP_TIMESTAMP));

        appendPluginInfo(buffer, event.getInteger(PLUGIN_ID));

        buffer.appendByte(DatastoreConstants.DatastoreFormat.VERTICAL.getName().byteValue());

        buffer.appendByte(CommonUtil.getByteValue(DatastoreConstants.DatastoreType.OBJECT_STATUS_FLAP_METRIC.ordinal()));

        buffer.appendIntLE(event.getInteger(AIOpsObject.OBJECT_ID));

        appendInstanceInfo(buffer, event.getString(INSTANCE_NAME, EMPTY_VALUE));
    }

    /**
     * Append plugin information to buffer
     *
     * @param buffer   Buffer to append to
     * @param pluginId Plugin ID
     */
    private void appendPluginInfo(Buffer buffer, int pluginId)
    {
        var pluginName = pluginId + DASH_SEPARATOR + MetricConfigStore.getStore().getMetricName(pluginId);

        buffer.appendIntLE(pluginName.length());

        buffer.appendString(pluginName);
    }

    /**
     * Append instance information to buffer
     *
     * @param buffer       Buffer to append to
     * @param instanceName Instance name
     */
    private void appendInstanceInfo(Buffer buffer, String instanceName)
    {
        var instanceBytes = CommonUtil.getString(instanceName).getBytes(StandardCharsets.UTF_8);

        buffer.appendIntLE(instanceBytes.length);

        buffer.appendBytes(instanceBytes);
    }

    /**
     * Updates scalar batch with column data and optionally creates mappers
     *
     * @param column           Column name
     * @param value            Value to set
     * @param pluginId         Plugin ID
     * @param buffer           Buffer to append to
     * @param create           Whether to create mapper
     * @param useValueAsColumn Whether to use value as mapper column
     */
    private void updateScalarBatch(String column, String value, int pluginId, Buffer buffer, boolean create, boolean useValueAsColumn)
    {
        try
        {
            // Determine data category
            var category = getCategory(column);

            // Append column info
            appendColumnInfo(buffer, category, column);

            // Append value based on category
            appendValue(buffer, category, value);

            // Create mappers if needed
            if (create)
            {
                createAvailabilityMappers(pluginId, column, value, useValueAsColumn);
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }

    /**
     * Determine data category based on column name
     *
     * @param column Column name
     * @return Data category byte value
     */
    private byte getCategory(String column)
    {
        return column.equalsIgnoreCase(AIOpsObject.OBJECT_ID) || column.contains(DURATION)
                ? DatastoreConstants.DataCategory.NUMERIC.getName()
                : DatastoreConstants.DataCategory.STRING.getName();
    }

    /**
     * Append column information to buffer
     *
     * @param buffer   Buffer to append to
     * @param category Data category
     * @param column   Column name
     */
    private void appendColumnInfo(Buffer buffer, byte category, String column)
    {
        buffer.appendByte(category);

        buffer.appendIntLE(column.length());

        buffer.appendString(column);
    }

    /**
     * Append value to buffer based on data category
     *
     * @param buffer   Buffer to append to
     * @param category Data category
     * @param value    Value to append
     */
    private void appendValue(Buffer buffer, byte category, String value)
    {
        if (category == DatastoreConstants.DataCategory.NUMERIC.getName())
        {
            // Convert to integer if it has decimal number
            buffer.appendLongLE(Math.round(CommonUtil.getDouble(value)));
        }

        else
        {
            buffer.appendIntLE(value.length());

            buffer.appendString(value);
        }
    }

    /**
     * Create availability mappers for metrics
     *
     * @param pluginId         Plugin ID
     * @param column           Column name
     * @param value            Value
     * @param useValueAsColumn Whether to use value as column name
     */
    private void createAvailabilityMappers(int pluginId, String column, String value, boolean useValueAsColumn)
    {
        column = useValueAsColumn ? value : column;

        for (var index = 0; index < AVAILABILITY_METRICS.size(); index++)
        {
            addColumnMapper(DatastoreConstants.DataCategory.NUMERIC.getName(),
                    pluginId, column + "." + AVAILABILITY_METRICS.getString(index), YES);
        }
    }

    /**
     * Update object durations for tracking
     *
     * @param id Object ID as string
     */
    private void updateObjectDurations(String id)
    {
        // Initialize cumulativeDuration if not present
        if (!cumulativeDurations.containsKey(id))
        {
            cumulativeDurations.getMap().computeIfAbsent(id, value -> new JsonObject().put(EMPTY_VALUE, 0));
        }

        var cumulativeDuration = cumulativeDurations.getJsonObject(id);
        // Update cumulativeDuration counter
        cumulativeDuration.put(EMPTY_VALUE, cumulativeDuration.getInteger(EMPTY_VALUE) + 10);

        // Initialize hourlyDuration if not present
        if (!hourlyDurations.containsKey(id))
        {
            hourlyDurations.getMap().computeIfAbsent(id, value -> new JsonObject().put(EMPTY_VALUE, 0));
        }

        var hourlyDuration = hourlyDurations.getJsonObject(id);
        // Update hourlyDuration counter
        hourlyDuration.put(EMPTY_VALUE, hourlyDuration.getInteger(EMPTY_VALUE) + 10);
    }

    /**
     * Process object status and write to datastore
     *
     * @param timestamp Current timestamp
     * @param object    Object data
     * @param objectId  Object ID
     * @param status    Object status
     */
    private void processObjectStatus(long timestamp, JsonObject object, int objectId, String status)
    {
        var buffer = Buffer.buffer();

        var pluginId = NMSConstants.AVAILABILITY_PLUGIN_ID;

        appendBytes(buffer, timestamp, pluginId, objectId);

        buffer.appendIntLE(EMPTY_VALUE.length());

        buffer.appendString(EMPTY_VALUE);

        updateScalarBatch(METRIC, APIConstants.Entity.OBJECT.getName().toLowerCase() + "." +
                status.toLowerCase() + TIME, pluginId, buffer, true, true);

        updateScalarBatch(STATUS, status.toUpperCase(), pluginId, buffer, false, false);

        updateScalarBatch(REASON, ObjectStatusCacheStore.getStore().getItemByReason(object.getLong(ID), EMPTY_VALUE),
                pluginId, buffer, false, false);

        updateScalarBatch(DURATION, CommonUtil.getString(OBJECT_STATUS_FLUSH_TIMER_SECONDS),
                pluginId, buffer, false, false);

        vertx.eventBus().send(EventBusConstants.EVENT_DATASTORE_WRITE + "." +
                DatastoreConstants.DatastoreCategory.AVAILABILITY.getName(), buffer.getBytes());
    }

    /**
     * Process instance statuses and write to datastore
     *
     * @param timestamp  Current timestamp
     * @param object     Object data
     * @param id         Object ID as string
     * @param objectId   Object ID as integer
     * @param key        Item key
     * @param timerValue Instance status flush timer value
     */
    private void processInstanceStatuses(long timestamp, JsonObject object, String id, int objectId,
                                         long key, int timerValue)
    {
        var items = ObjectStatusCacheStore.getStore().getInstanceItems(key, false);

        if (items == null)
        {
            return;
        }

        for (var entry : items.entrySet())
        {
            var instanceKey = entry.getKey();

            var instanceValue = entry.getValue();

            updateInstanceDurations(id, instanceKey);

            // Process instance status if timer expired
            if (timerValue <= 0)
            {
                processInstanceStatus(timestamp, object, objectId, instanceKey, instanceValue);
            }
        }
    }

    /**
     * Update instance durations for tracking
     *
     * @param id          Object ID as string
     * @param instanceKey Instance key
     */
    private void updateInstanceDurations(String id, String instanceKey)
    {
        var valid = MotadataConfigUtil.getStatusFlapInstances()
                .contains(instanceKey.split(INSTANCE_SEPARATOR)[0]);

        var hourlyDuration = hourlyDurations.getJsonObject(id);

        var cumulativeDuration = cumulativeDurations.getJsonObject(id);

        // Update hourlyDuration for valid instances
        if (!hourlyDuration.containsKey(instanceKey) && valid)
        {
            hourlyDuration.getMap().putIfAbsent(instanceKey, 0);
        }

        if (valid)
        {
            hourlyDuration.put(instanceKey, hourlyDuration.getInteger(instanceKey) + 10);
        }

        // Update cumulativeDuration for all instances
        if (!cumulativeDuration.containsKey(instanceKey))
        {
            cumulativeDuration.getMap().putIfAbsent(instanceKey, 0);
        }

        cumulativeDuration.put(instanceKey, cumulativeDuration.getInteger(instanceKey) + 10);
    }

    /**
     * Process instance status and write to datastore
     *
     * @param timestamp     Current timestamp
     * @param object        Object data
     * @param objectId      Object ID
     * @param instanceKey   Instance key
     * @param instanceValue Instance value
     */
    private void processInstanceStatus(long timestamp, JsonObject object, int objectId,
                                       String instanceKey, String instanceValue)
    {
        var buffer = Buffer.buffer();

        var tokens = instanceKey.split(INSTANCE_SEPARATOR);

        var pluginId = CommonUtil.getInteger(tokens[2]);

        var instancePrefix = tokens[0] + INSTANCE_SEPARATOR;

        appendBytes(buffer, timestamp, pluginId, objectId);

        buffer.appendIntLE(tokens[1].length());

        buffer.appendString(tokens[1]);

        updateScalarBatch(METRIC, instancePrefix + instanceValue.toLowerCase() + TIME,
                pluginId, buffer, true, true);

        updateScalarBatch(STATUS, instanceValue.toUpperCase(), pluginId, buffer, false, false);

        updateScalarBatch(REASON, ObjectStatusCacheStore.getStore().getItemByReason(object.getLong(ID), instanceKey),
                pluginId, buffer, false, false);

        updateScalarBatch(DURATION, CommonUtil.getString(INSTANCE_STATUS_FLUSH_TIMER_SECONDS),
                pluginId, buffer, false, false);

        vertx.eventBus().send(EventBusConstants.EVENT_DATASTORE_WRITE + "." +
                DatastoreConstants.DatastoreCategory.AVAILABILITY.getName(), buffer.getBytes());
    }

    /**
     * Load durations data from file
     *
     * @param fileName Name of the file to load
     * @param object   JsonObject to load data into
     */
    private void load(String fileName, JsonObject object)
    {
        var file = new File(GlobalConstants.CURRENT_DIR + GlobalConstants.PATH_SEPARATOR +
                GlobalConstants.CONFIG_DIR + GlobalConstants.PATH_SEPARATOR + fileName);

        if (file.exists())
        {
            var buffer = vertx.fileSystem().readFileBlocking(file.getPath());

            if (buffer != null && buffer.getBytes().length > 0)
            {
                new JsonObject(Buffer.buffer(CodecUtil.toBytes(buffer.getBytes())))
                        .forEach(entry -> object.put(entry.getKey(), JsonObject.mapFrom(entry.getValue())));

                LOGGER.info(String.format("%s loaded from the backup file...", fileName));
            }
        }
        else
        {
            vertx.fileSystem().createFileBlocking(file.getPath());
        }
    }

    /**
     * Save durations data to file if not empty
     *
     * @param fileName Name of the file to save to
     * @param object   JsonObject containing data to save
     */
    private void save(String fileName, JsonObject object)
    {
        if (!object.isEmpty())
        {
            vertx.fileSystem().writeFileBlocking(GlobalConstants.CURRENT_DIR + GlobalConstants.PATH_SEPARATOR +
                            GlobalConstants.CONFIG_DIR + GlobalConstants.PATH_SEPARATOR + fileName,
                    Buffer.buffer(CodecUtil.compress(new JsonObject().mergeIn(object).encode().getBytes())));
        }
    }

    /**
     * Stops the ObjectStatusCalculator verticle.
     * Saves all duration data to disk before shutting down.
     *
     * @param promise Promise to be completed when shutdown is done
     */
    @Override
    public void stop(Promise<Void> promise)
    {
        LOGGER.info("Undeploying verticle ObjectStatusCalculator...");

        // Save cumulative durations to persistent storage
        LOGGER.info("Saving cumulative status flap durations before shutdown...");
        save(CUMULATIVE_STATUS_FLAP_DURATIONS, cumulativeDurations);

        // Save hourly durations to persistent storage
        LOGGER.info("Saving hourly status flap durations before shutdown...");
        save(HOURLY_STATUS_FLAP_DURATIONS, hourlyDurations);

        LOGGER.info("ObjectStatusCalculator shutdown complete.");
        promise.complete();
    }
}
