/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

/*
	Change Logs:
	Date			Author			    Notes
	25-Feb-2025		Darshan Parmar		MOTADATA-5215: SonarQube Suggestions Resolution
*/
package com.mindarray.nms;

import com.mindarray.Bootstrap;
import com.mindarray.api.Metric;
import com.mindarray.api.User;
import com.mindarray.eventbus.EventBusConstants;
import com.mindarray.store.AgentConfigStore;
import com.mindarray.store.ObjectConfigStore;
import com.mindarray.store.RemoteEventProcessorConfigStore;
import com.mindarray.util.CommonUtil;
import com.mindarray.util.HealthUtil;
import com.mindarray.util.Logger;
import com.mindarray.util.MotadataConfigUtil;
import com.mindarray.visualization.VisualizationConstants;
import io.vertx.core.AbstractVerticle;
import io.vertx.core.Promise;
import io.vertx.core.buffer.Buffer;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;

import java.util.Collections;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.atomic.AtomicInteger;

import static com.mindarray.GlobalConstants.*;
import static com.mindarray.api.Agent.AGENT_UUID;
import static com.mindarray.api.RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_UUID;
import static com.mindarray.eventbus.EventBusConstants.*;

/**
 * AutoScaler is responsible for dynamically scaling the metric polling system based on load and resource availability.
 * <p>
 * This class monitors the load on the metric polling system by tracking pending events by category,
 * checks system resource usage (CPU and memory), and allocates additional worker executors when needed.
 * It helps prevent system overload while ensuring efficient resource utilization.
 * <p>
 * Key responsibilities:
 * - Monitoring pending events by category to detect overload
 * - Querying system resource usage (CPU and memory)
 * - Determining how many additional worker executors can be allocated
 * - Triggering auto-scaling for the most overloaded categories
 * - Preventing system overload by limiting scaling when resources are constrained
 * <p>
 * This class operates as a Vert.x verticle and uses the event bus for communication with other components.
 */
public class AutoScaler extends AbstractVerticle
{
    private static final Logger LOGGER = new Logger(AutoScaler.class, MOTADATA_NMS, "Auto Scaler");

    private final Map<String, Long> pendingEventsByCategory = new HashMap<>();

    private final Map<String, int[]> pendingEventHelpers = new HashMap<>();

    /**
     * Initializes the AutoScaler verticle.
     * <p>
     * This method sets up:
     * 1. Data structures to track pending events by category
     * 2. A consumer for ENGINE_STATS_RESPONSE events to monitor pending events
     * 3. A periodic timer to check for overload and query system resources
     * 4. A consumer for MOTADATA_STATS_QUERY events to process resource usage data and trigger auto-scaling
     * <p>
     * The auto-scaling logic works as follows:
     * - Track pending events by category to detect overload
     * - When overload is detected, query system resource usage (CPU and memory)
     * - Based on resource availability, determine how many additional worker executors can be allocated
     * - Send auto-scale events to the metric poller to add more workers for the most overloaded categories
     *
     * @param promise Promise to be completed when initialization is done
     */
    @Override
    public void start(Promise<Void> promise)
    {
        // Initialize tracking arrays for each category
        // Each category gets an array to track [pending events, pending batch events]
        for (var category : NMSConstants.Category.values())
        {
            pendingEventHelpers.put(category.getName(), new int[2]);
        }

        // Track the maximum number of workers that can be added
        var maxWorkers = new AtomicInteger(MotadataConfigUtil.getMaxPollerWorkers());

        // Set up a consumer to monitor engine statistics and track pending events by category
        vertx.eventBus().<JsonObject>localConsumer(EventBusConstants.EVENT_ENGINE_STATS_RESPONSE, message ->
        {
            try
            {
                var event = message.body();

                if (event.getString(ENGINE_TYPE).equalsIgnoreCase(EVENT_METRIC_POLL) && Bootstrap.getRegistrationId().equalsIgnoreCase(event.getString(REMOTE_EVENT_PROCESSOR_UUID)))
                {
                    var stats = event.getJsonObject(HealthUtil.HEALTH_STATS);

                    if (CommonUtil.traceEnabled())
                    {
                        LOGGER.trace(String.format("current pending events : %s, pending batch event: %s", stats.getInteger("pending.events"), stats.getInteger("pending.batch.events")));
                    }

                    for (var index = 0; index < stats.getJsonArray(Metric.METRIC_CATEGORY).size(); index++)
                    {
                        var item = stats.getJsonArray(Metric.METRIC_CATEGORY).getJsonObject(index);

                        if (item.containsKey("metric.category.pending.events") && item.containsKey("metric.category.pending.batch.events"))
                        {
                            if (item.getInteger("metric.category.pending.events") == 0 && item.getInteger("metric.category.pending.batch.events") == 0)
                            {
                                pendingEventsByCategory.put(item.getString(Metric.METRIC_CATEGORY), 0L);
                            }
                            else if (item.getInteger("metric.category.pending.events") > pendingEventHelpers.get(item.getString(Metric.METRIC_CATEGORY))[0] ||
                                    item.getInteger("metric.category.pending.batch.events") > pendingEventHelpers.get(item.getString(Metric.METRIC_CATEGORY))[1])
                            {
                                pendingEventsByCategory.put(item.getString(Metric.METRIC_CATEGORY), pendingEventsByCategory.getOrDefault(item.getString(Metric.METRIC_CATEGORY), 0L) + 1);
                            }

                            pendingEventHelpers.get(item.getString(Metric.METRIC_CATEGORY))[0] = item.getInteger("metric.category.pending.events");

                            pendingEventHelpers.get(item.getString(Metric.METRIC_CATEGORY))[1] = item.getInteger("metric.category.pending.batch.events");
                        }
                    }
                }
            }
            catch (Exception exception)
            {
                LOGGER.error(exception);
            }
        });

        // Set up a periodic timer to check for overload and query system resources
        vertx.setPeriodic(MotadataConfigUtil.getPollerHealthInspectionTimerSeconds(), timer ->
        {
            // Sort categories by number of pending events (highest first)
            var categories = pendingEventsByCategory.entrySet().stream()
                    .sorted(Collections.reverseOrder(Map.Entry.comparingByValue())).map(Map.Entry::getKey).toList();

            if (CommonUtil.traceEnabled())
            {
                LOGGER.trace(String.format("after %s min, pending events %s", MotadataConfigUtil.getPollerHealthInspectionTimerSeconds(), pendingEventsByCategory));
            }

            // If the category with the most pending events has any, we have an overload situation
            if (pendingEventsByCategory.get(categories.getFirst()) > 0) // Having overload, fetch CPU utilization
            {
                var items = AgentConfigStore.getStore().getItems();

                for (var index = 0; index < items.size(); index++)
                {
                    var item = items.getJsonObject(index);

                    if (RemoteEventProcessorConfigStore.getStore().getItemByValue(REMOTE_EVENT_PROCESSOR_UUID, item.getString(AGENT_UUID)) != null)
                    {
                        var query = new JsonObject("{\"ui.event.uuid\":\"e9891ee0-e238-4bff-b784-23b49234ceda\",\"_type\":\"0\",\"id\":10000000001208,\"visualization.name\":\"CPU (%)\",\"visualization.description\":\"CPU Utilization Server Category\",\"visualization.timeline\":{\"relative.timeline\":\"-15m\"},\"visualization.category\":\"Grid\",\"visualization.type\":\"KPI Gauge\",\"visualization.data.sources\":[{\"type\":\"metric\",\"filters\":{\"data.filter\":{},\"result.filter\":{}},\"data.points\":[{\"data.point\":\"system.cpu.percent\",\"aggregator\":\"avg\",\"entity.type\":\"Monitor\",\"entities\":{},\"filter.keys\":[],\"plugins\":[]},{\"data.point\":\"system.cpu.user.percent\",\"aggregator\":\"avg\",\"entity.type\":\"Monitor\",\"entities\":{},\"filter.keys\":[],\"plugins\":[]},{\"data.point\":\"system.cpu.interrupt.percent\",\"aggregator\":\"avg\",\"entity.type\":\"Monitor\",\"entities\":{},\"filter.keys\":[],\"plugins\":[]},{\"data.point\":\"system.memory.used.percent\",\"aggregator\":\"avg\",\"entity.type\":\"Monitor\",\"entities\":{},\"filter.keys\":[],\"plugins\":[]}],\"correlated.data.points\":[{\"type\":\"metric\",\"data.point\":\"system.cpu.percent\"},{\"type\":\"metric\",\"data.point\":\"system.cpu.idle.percent\"},{\"type\":\"metric\",\"data.point\":\"system.cpu.interrupt.percent\"},{\"type\":\"metric\",\"data.point\":\"system.cpu.system.percent\"},{\"type\":\"metric\",\"data.point\":\"system.cpu.user.percent\"},{\"type\":\"metric\",\"data.point\":\"system.memory.used.percent\"}],\"entities\":{},\"group.by\":\"\",\"status\":[],\"instance.type\":\"\",\"visualization.result.by\":[\"monitor\"],\"plugins\":[]}],\"visualization.properties\":{},\"entity.type\":\"Monitor\",\"container.type\":\"Template\"}");

                        query.put(ENTITIES, new JsonArray().add(ObjectConfigStore.getStore().getItemByAgentId(item.getLong(ID)).getLong(ID)));

                        if (CommonUtil.traceEnabled())
                        {
                            LOGGER.trace("widget sending to visualization....." + query);
                        }

                        vertx.eventBus().send(EVENT_VISUALIZATION, query.put(User.USER_NAME, DEFAULT_USER).put(EVENT_TYPE, EVENT_MOTADATA_STATS_QUERY));
                    }
                }
            }
            else
            {
                if (CommonUtil.traceEnabled())
                {
                    LOGGER.trace("No Overhead found, resuming poller...");
                }
            }

        });

        vertx.eventBus().<JsonObject>localConsumer(EVENT_MOTADATA_STATS_QUERY, message ->
        {

            var categories = new JsonArray();

            var rows = VisualizationConstants.unpack(Buffer.buffer(message.body().getBinary(RESULT)), LOGGER, false, null, true, true).getJsonArray(RESULT);

            if (rows != null && rows.getJsonObject(0) != null)
            {
                // Extract CPU and memory usage from the query results
                var cpu = rows.getJsonObject(0).getInteger("system.cpu.percent^avg");
                var memory = rows.getJsonObject(0).getInteger("system.memory.used.percent^avg");

                if (CommonUtil.traceEnabled())
                {
                    LOGGER.trace(String.format("system memory : %s and cpu : %s", memory, cpu));
                }

                // Determine how many workers to add based on resource availability
                var workers = 0;

                // Scale based on resource usage:
                // - Low resource usage (< 50%): Add more workers (3)
                // - Medium resource usage (< 80%): Add fewer workers (2)
                // - High resource usage (< 95%): Add minimal workers (1)
                // - Very high resource usage (≥ 95%): Add no workers (0)
                if (cpu < 50 && memory < 50)  // Low resource usage
                {
                    workers = 3; // Add 3 workers (scales to 3, 6, 9, or 15 depending on deployment type)
                }
                else if (cpu < 80 && memory < 80)  // Medium resource usage
                {
                    workers = 2; // Add 2 workers (scales to 2, 4, 6, or 10 depending on deployment type)
                }
                else if (cpu < 95 && memory < 95)  // High resource usage
                {
                    workers = 1; // Add 1 worker (scales to 1, 2, 3, or 5 depending on deployment type)
                }

                // Ensure we don't exceed the maximum allowed workers
                workers = (maxWorkers.get() - workers) >= 0 ? workers : maxWorkers.get();

                if (CommonUtil.traceEnabled())
                {
                    LOGGER.trace(String.format("executors qualified : %d", workers));
                }

                // Only proceed if we have workers to allocate and haven't exceeded the maximum
                if (workers > 0 && (maxWorkers.get() - workers) >= 0)
                {
                    // Decrement the available worker count
                    maxWorkers.set(maxWorkers.get() - workers);

                    // Identify the categories with pending events, sorted by most overloaded first
                    for (var category : pendingEventsByCategory.entrySet().stream()
                            .sorted(Collections.reverseOrder(Map.Entry.comparingByValue())).map(Map.Entry::getKey).toList())
                    {
                        // Only include categories that actually have pending events
                        if (pendingEventsByCategory.get(category) > 0)
                        {
                            categories.add(category);

                            if (CommonUtil.traceEnabled())
                            {
                                LOGGER.trace(String.format("category qualified : %s", category));
                            }
                        }
                    }

                    // If we have categories that need scaling, send the auto-scale event
                    if (!categories.isEmpty())
                    {
                        if (CommonUtil.traceEnabled())
                        {
                            LOGGER.trace(String.format("sending event metric poller context: %s executors : %d ", categories, workers));
                        }

                        // Send the auto-scale event to the metric poller with the categories and worker count
                        vertx.eventBus().send(EVENT_POLLER_AUTO_SCALE, new JsonObject().put(EVENT_CONTEXT, categories).put("workers", workers));
                    }
                }
                else
                {
                    // We can't allocate more workers due to resource constraints
                    LOGGER.warn("cpu/memory over utilization need to upgrade hardware...");
                }
            }
        });

        promise.complete();
    }

}
