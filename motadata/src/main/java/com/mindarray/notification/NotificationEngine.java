/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

package com.mindarray.notification;

import com.mindarray.Bootstrap;
import com.mindarray.ErrorCodes;
import com.mindarray.GlobalConstants;
import com.mindarray.eventbus.EventBusConstants;
import com.mindarray.eventbus.EventEngine;
import com.mindarray.util.CommonUtil;
import com.mindarray.util.Logger;
import io.vertx.core.AbstractVerticle;
import io.vertx.core.Promise;
import io.vertx.core.datagram.DatagramSocket;
import io.vertx.core.json.JsonObject;
import org.snmp4j.Snmp;
import org.snmp4j.mp.MPv1;
import org.snmp4j.mp.MPv2c;
import org.snmp4j.mp.MPv3;
import org.snmp4j.transport.DefaultUdpTransportMapping;

import static com.mindarray.GlobalConstants.DEFAULT_USER;
import static com.mindarray.GlobalConstants.ERROR_CODE;
import static com.mindarray.api.User.USER_NAME;

/**
 * The NotificationEngine is a Vert.x verticle that orchestrates the delivery of notifications
 * across various channels (email, SMS, push, etc.) in the Motadata system.
 * <p>
 * This engine:
 * <ul>
 *   <li>Listens for notification events on the Vert.x event bus</li>
 *   <li>Routes notifications to the appropriate handler based on notification type</li>
 *   <li>Tracks notification delivery status</li>
 *   <li>Reports success or failure of notification delivery back to the event bus</li>
 * </ul>
 * <p>
 * The engine supports multiple notification channels including:
 * <ul>
 *   <li>Email</li>
 *   <li>SMS</li>
 *   <li>Push notifications</li>
 *   <li>SNMP traps</li>
 *   <li>Syslog messages</li>
 *   <li>Webhooks</li>
 *   <li>Sound alerts</li>
 *   <li>Microsoft Teams messages</li>
 * </ul>
 * <p>
 * Each notification type is handled by a dedicated handler class that extends the
 * {@link Notification} abstract class.
 */
public class NotificationEngine extends AbstractVerticle
{

    /**
     * Logger for the notification engine
     */
    private static final Logger LOGGER = new Logger(NotificationEngine.class, GlobalConstants.MOTADATA_NOTIFICATION, "Notification Engine");

    /**
     * Socket for sending syslog messages
     */
    private final DatagramSocket socket = Bootstrap.vertx().createDatagramSocket();

    /**
     * Event engine for handling notification events
     */
    private EventEngine eventEngine;

    /**
     * SNMP client for sending trap notifications
     */
    private Snmp snmp = null;

    /**
     * Initializes and starts the notification engine.
     * <p>
     * This method:
     * <ul>
     *   <li>Initializes the SNMP client with message processing models</li>
     *   <li>Creates instances of all notification handlers</li>
     *   <li>Sets up the event engine to process notification events</li>
     *   <li>Configures event handling logic for routing notifications</li>
     * </ul>
     *
     * @param promise Promise to be completed when initialization is done
     * @throws Exception If an error occurs during initialization
     */
    @Override
    public void start(Promise<Void> promise) throws Exception
    {
        // Define the topic for notification reply messages
        var replyTopic = EventBusConstants.EVENT_NOTIFICATION + EventBusConstants.EVENT_REPLY;

        // Initialize SNMP client for trap notifications
        snmp = new Snmp(new DefaultUdpTransportMapping());

        // Add support for SNMP v1
        snmp.getMessageDispatcher().addMessageProcessingModel(new MPv1());

        // Add support for SNMP v2c
        snmp.getMessageDispatcher().addMessageProcessingModel(new MPv2c());

        // Add support for SNMP v3
        snmp.getMessageDispatcher().addMessageProcessingModel(new MPv3());

        // Initialize notification handlers for each notification type
        var emailNotification = new EmailNotification();
        var pushNotification = new PushNotification();
        var smsNotification = new SMSNotification();
        var syslogNotification = new SyslogNotification(socket);
        var trapNotification = new SNMPTrapNotification(snmp);
        var webHookNotification = new WebHookNotification();
        var soundNotification = new SoundNotification();
        var teamsNotification = new MicrosoftTeamsNotification();

        // Configure and start the event engine to handle notification events
        eventEngine = new EventEngine().setEventType(EventBusConstants.EVENT_NOTIFICATION)
                .setBlockingEvent(true).setLogger(LOGGER).setEventHandler(event ->
                {
                    try
                    {
                        // Extract the event ID from the notification event
                        var eventId = event.getLong(EventBusConstants.EVENT_ID);

                        // Record the notification event in the event system with QUEUED state
                        vertx.eventBus().send(EventBusConstants.EVENT_ADD, new JsonObject().put(EventBusConstants.EVENT_ID, eventId)
                                .put(EventBusConstants.EVENT_TYPE, EventBusConstants.EVENT_NOTIFICATION)
                                .put(EventBusConstants.EVENT_STATE, EventBusConstants.EVENT_STATE_QUEUED)
                                .put(USER_NAME, event.containsKey(USER_NAME) ? event.getString(USER_NAME) : DEFAULT_USER)
                                .put(EventBusConstants.EVENT_CONTEXT, event));

                        // Route the notification to the appropriate handler based on notification type
                        var result = switch (Notification.NotificationType.valueOfName(event.getString(Notification.NOTIFICATION_TYPE)))
                        {
                            case EMAIL -> emailNotification.notify(event);
                            case PUSH -> pushNotification.notify(event);
                            case SMS -> smsNotification.notify(event);
                            case SYSLOG -> syslogNotification.notify(event);
                            case SNMP_TRAP -> trapNotification.notify(event);
                            case WEBHOOK -> webHookNotification.notify(event);
                            case SOUND -> soundNotification.notify(event);
                            case MICROSOFT_TEAMS -> teamsNotification.notify(event);
                        };

                        // Handle case where notification type is invalid or not supported
                        if (result == null)
                        {
                            // Send reply to the original notification request
                            vertx.eventBus().send(replyTopic, event.put(EventBusConstants.EVENT_ID, eventId));

                            // Mark the notification as failed
                            vertx.eventBus().send(EventBusConstants.EVENT_FAIL, event.put(GlobalConstants.MESSAGE, "invalid notification type..."));
                        }
                        else
                        {
                            // Process the result of the notification attempt when it completes
                            result.future().onComplete(asyncResult ->
                            {
                                // If result contains data, merge it into the event
                                if (asyncResult.result() != null)
                                {
                                    event.mergeIn(asyncResult.result());
                                }

                                // Send reply to the original notification request
                                vertx.eventBus().send(replyTopic, event.put(EventBusConstants.EVENT_ID, eventId));

                                if (asyncResult.succeeded())
                                {
                                    // Check if the result contains a status field
                                    if (asyncResult.result().containsKey(GlobalConstants.STATUS))
                                    {
                                        // If status is "succeed", mark the notification as successful
                                        if (asyncResult.result().getString(GlobalConstants.STATUS).equalsIgnoreCase(GlobalConstants.STATUS_SUCCEED))
                                        {
                                            vertx.eventBus().send(EventBusConstants.EVENT_SUCCEED, event);
                                        }
                                        // Otherwise, mark the notification as failed
                                        else
                                        {
                                            vertx.eventBus().send(EventBusConstants.EVENT_FAIL, event);
                                        }
                                    }
                                    // If no status field is present, mark the notification as failed
                                    else
                                    {
                                        vertx.eventBus().send(EventBusConstants.EVENT_FAIL, event);
                                    }
                                }
                                // If the notification attempt failed with an exception
                                else
                                {
                                    // Mark the notification as failed with error details
                                    vertx.eventBus().send(EventBusConstants.EVENT_FAIL, event.put(GlobalConstants.STATUS, GlobalConstants.STATUS_FAIL)
                                            .put(GlobalConstants.MESSAGE, asyncResult.cause().getMessage())
                                            .put(ERROR_CODE, ErrorCodes.ERROR_CODE_INTERNAL_ERROR)
                                            .put(GlobalConstants.ERROR, CommonUtil.formatStackTrace(asyncResult.cause().getStackTrace())));
                                }
                            });
                        }
                    }
                    // Handle any exceptions that occur during notification processing
                    catch (Exception exception)
                    {
                        LOGGER.error(exception);

                        // Send reply to the original notification request
                        vertx.eventBus().send(replyTopic, event);
                    }
                }).start(vertx, promise);
    }


    /**
     * Stops the notification engine and releases resources.
     * <p>
     * This method:
     * <ul>
     *   <li>Closes the SNMP client if it was initialized</li>
     *   <li>Closes the datagram socket used for syslog messages</li>
     *   <li>Stops the event engine</li>
     * </ul>
     *
     * @param promise Promise to be completed when shutdown is done
     * @throws Exception If an error occurs during shutdown
     */
    @Override
    public void stop(Promise<Void> promise) throws Exception
    {
        if (snmp != null)
        {
            snmp.close();
        }
        socket.close();

        eventEngine.stop(vertx, promise);
    }


}
