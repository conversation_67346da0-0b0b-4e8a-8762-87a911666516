[{"_type": "0", "id": 10000000000511, "visualization.name": "Sessions", "visualization.description": "MSSQL Sessions", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "mssql.connected.sessions", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "mssql.active.sessions", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "mssql.blocked.sessions", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "mssql.connected.sessions"}, {"type": "metric", "data.point": "mssql.active.sessions"}, {"type": "metric", "data.point": "mssql.blocked.sessions"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "color.data.point": "mssql.connected.sessions", "icon": {"name": "sessions", "placement": "prefix"}}, "header": {"title": "Sessions", "style": {"font.size": "medium"}, "data.points": [{"label": "Connected", "value": "mssql.connected.sessions.last"}]}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "Active", "value": "mssql.active.sessions.last"}, {"label": "Blocked", "value": "mssql.blocked.sessions.last"}]}}}}, {"_type": "0", "id": 10000000000512, "visualization.name": "Memory", "visualization.description": "MSSQL Memory", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "mssql.connection.memory.bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "mssql.server.memory.provisioned.bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "mssql.target.server.memory.bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "mssql.connection.memory.bytes"}, {"type": "metric", "data.point": "mssql.server.memory.provisioned.bytes"}, {"type": "metric", "data.point": "mssql.target.server.memory.bytes"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "color.data.point": "mssql.connection.memory.bytes", "icon": {"name": "memory", "placement": "prefix"}}, "header": {"title": "Memory", "style": {"font.size": "medium"}, "data.points": [{"label": "Connection Memory", "value": "mssql.connection.memory.bytes.last"}]}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "Total", "value": "mssql.server.memory.provisioned.bytes.last"}, {"label": "Target", "value": "mssql.target.server.memory.bytes.last"}]}}}}, {"_type": "0", "id": 10000000000513, "visualization.name": "<PERSON><PERSON>", "visualization.description": "MSSQL Cache", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "mssql.cache.hit.ratio.percent", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "mssql.cache.hit.ratio.percent"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "color.data.point": "mssql.cache.hit.ratio.percent", "icon": {"name": "database", "placement": "prefix"}}, "header": {"title": "<PERSON><PERSON>", "style": {"font.size": "medium"}, "data.points": []}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "", "value": "mssql.cache.hit.ratio.percent.last", "type": "gauge"}]}}}}, {"_type": "0", "id": 10000000000514, "visualization.name": "Buffer Manager", "visualization.description": "MSSQL Buffer Manager", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "mssql.buffer.cache.hit.ratio.percent", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "mssql.buffer.cache.hit.ratio.percent"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "color.data.point": "mssql.buffer.cache.hit.ratio.percent", "icon": {"name": "buffer", "placement": "prefix"}}, "header": {"title": "Buffer Manager", "style": {"font.size": "medium"}, "data.points": []}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "", "value": "mssql.buffer.cache.hit.ratio.percent.last", "type": "gauge"}]}}}}, {"_type": "0", "id": 10000000000515, "visualization.name": "Locks", "visualization.description": "MSSQL Locks", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "mssql.deadlocks.per.sec", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "mssql.lock.requests.per.sec", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "mssql.lock.waits.per.sec", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "mssql.deadlocks.per.sec"}, {"type": "metric", "data.point": "mssql.lock.requests.per.sec"}, {"type": "metric", "data.point": "mssql.lock.waits.per.sec"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "color.data.point": "mssql.deadlocks.per.sec", "icon": {"name": "locks", "placement": "prefix"}}, "header": {"title": "Locks", "style": {"font.size": "medium"}, "data.points": [{"label": "Deadlocks Per Sec", "value": "mssql.deadlocks.per.sec.last"}]}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "Requests", "value": "mssql.lock.requests.per.sec.last"}, {"label": "Waits", "value": "mssql.lock.waits.per.sec.last"}]}}}}, {"_type": "0", "id": 10000000000516, "visualization.name": "Pages", "visualization.description": "MSSQL Pages", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "mssql.checkpoint.pages.per.sec", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "mssql.page.splits.per.sec", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "mssql.page.lookups.per.sec", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "mssql.checkpoint.pages.per.sec"}, {"type": "metric", "data.point": "mssql.page.splits.per.sec"}, {"type": "metric", "data.point": "mssql.page.lookups.per.sec"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "color.data.point": "mssql.checkpoint.pages.per.sec", "icon": {"name": "pages", "placement": "prefix"}}, "header": {"title": "Pages", "style": {"font.size": "medium"}, "data.points": [{"label": "Checkpoint Pages", "value": "mssql.checkpoint.pages.per.sec.last"}]}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "Splits", "value": "mssql.page.splits.per.sec.last"}, {"label": "Lookups", "value": "mssql.page.lookups.per.sec.last"}]}}}}, {"_type": "0", "id": 10000000000517, "visualization.name": "Packet Details", "visualization.description": "MSSQL Packet Details", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "mssql.received.packets.per.sec", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "mssql.sent.packets.per.sec", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "mssql.error.packets", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"_type": "0", "id": 10000000000518, "visualization.name": "Availability Statistics", "visualization.category": "Grid", "visualization.type": "Application Availability Time Series", "visualization.timeline": {"relative.timeline": "today"}, "visualization.data.sources": [{"type": "availability", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "monitor.uptime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.downtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.suspendtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.unknowntime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.maintenancetime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.disabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.unreachabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}], "visualization.timeline": {"relative.timeline": "-1d"}}, {"type": "availability", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "monitor.uptime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.downtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.suspendtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.unknowntime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.maintenancetime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.disabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.unreachabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}], "visualization.timeline": {"relative.timeline": "-7d"}}, {"type": "availability", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "monitor.uptime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.downtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.suspendtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.unknowntime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.maintenancetime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.disabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.unreachabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}], "visualization.timeline": {"relative.timeline": "-15d"}}], "visualization.properties": {"chart": {"rotation.angle": 0, "chart.legend": "yes", "chart.label": "no", "axis.titles": {}, "highchart.settings": {}, "sorting": {"limit": 10, "order": "desc", "column": "monitor.uptime.percent.avg"}}}}, {"_type": "0", "id": 10000000000519, "visualization.name": "Today's Availability", "visualization.category": "Custom", "visualization.type": "Application Availability", "visualization.timeline": {"relative.timeline": "today"}, "visualization.result.by": ["monitor"], "visualization.data.sources": [{"visualization.result.by": ["monitor"], "type": "availability", "join.type": "all", "join.result": "availability", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "monitor.uptime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.downtime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.suspendtime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.unknowntime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.maintenancetime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.disabletime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.unreachabletime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}]}, {"visualization.result.by": ["monitor"], "type": "availability", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "monitor.uptime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.downtime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.suspendtime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.unknowntime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.maintenancetime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.disabletime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.unreachabletime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "no", "chart.label": "no", "axis.titles": {}, "highchart.settings": {"plotOptions": {"pie": {"innerSize": "70%"}}}}}, "join.type": "all", "join.columns": ["monitor"], "entity.type": "Monitor", "container.type": "Template", "entities": []}, {"_type": "0", "id": 10000000000520, "visualization.name": "Query Details", "visualization.description": "MSSQL Query Details", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "mssql.sql.compilations.per.sec", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "mssql.sql.recompilations.per.sec", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"_type": "0", "id": 10000000000521, "visualization.name": "Database Memory", "visualization.description": "MSSQL Database Memory", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "mssql.server.memory.provisioned.bytes", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "mssql.lock.memory.bytes", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "mssql.sql.cache.memory.bytes", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "mssql.optimizer.memory.bytes", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"_type": "0", "id": 10000000000522, "visualization.name": "Lock Details", "visualization.description": "MSSQL Lock Details", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "mssql.deadlocks.per.sec", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "mssql.lock.waits.per.sec", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "mssql.lock.requests.per.sec", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"_type": "0", "id": 10000000000523, "visualization.name": "Active Transaction", "visualization.description": "MSSQL Active Transaction", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Area", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "mssql.active.transactions", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "no", "chart.label": "no", "highchart.settings": {}}}}, {"_type": "0", "id": 10000000000524, "visualization.name": "Buffer <PERSON><PERSON>", "visualization.description": "MSSQL Buffer <PERSON><PERSON> Hit Ratio", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Area", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "mssql.buffer.cache.hit.ratio.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "no", "chart.label": "no", "highchart.settings": {}}}}, {"_type": "0", "id": 10000000000525, "visualization.name": "Latch Waits", "visualization.description": "MSSQL Latch Waits", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Area", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "mssql.latch.waits.per.sec", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "no", "chart.label": "no", "highchart.settings": {}}}}, {"_type": "0", "id": 10000000000526, "visualization.name": "Access Method Details", "visualization.description": "MSSQL Access Method Details", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "mssql.probe.scans.per.sec", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "mssql.full.scans.per.sec", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "mssql.range.scans.per.sec", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"_type": "0", "id": 10000000000527, "visualization.name": "Page Details", "visualization.description": "MSSQL Page Details", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "mssql.page.reads.per.sec", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "mssql.page.writes.per.sec", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "mssql.page.life.expectancy", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "mssql.pages", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"_type": "0", "id": 10000000000528, "visualization.name": "Connection Stats", "visualization.description": "MSSQL Connection Stats", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "mssql.user.connections", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "mssql.idle.sessions", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"_type": "0", "id": 10000000000529, "visualization.name": "Blocked Processes", "visualization.description": "MSSQL Blocked Processes", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Area", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "mssql.blocked.processes", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "no", "chart.label": "no", "highchart.settings": {}}}}, {"_type": "0", "id": 10000000000530, "visualization.name": "Batch Request", "visualization.description": "MSSQL Batch Request", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Area", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "mssql.batch.requests.per.sec", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"_type": "0", "id": 10000000000531, "visualization.name": "Session Details", "visualization.description": "MSSQL Session Details", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Area", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "mssql.idle.sessions", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "mssql.connected.sessions", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "mssql.blocked.sessions", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "mssql.active.sessions", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"_type": "0", "id": 10000000000532, "visualization.name": "CPU Busy", "visualization.description": "MSSQL CPU Busy", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Area", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "mssql.cpu.busy.seconds", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "no", "chart.label": "no", "highchart.settings": {}}}}, {"_type": "0", "id": 10000000000533, "visualization.name": "IO Busy", "visualization.description": "MSSQL IO Busy", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Area", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "mssql.io.busy.seconds", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "no", "chart.label": "no", "highchart.settings": {}}}}, {"_type": "0", "id": 10000000000534, "visualization.name": "Server Idle", "visualization.description": "MSSQL Server Idle", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Area", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "mssql.idle.seconds", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "no", "chart.label": "no", "highchart.settings": {}}}}, {"_type": "0", "id": 10000000000535, "visualization.name": "Background Process Details", "visualization.description": "MSSQL Background Process Details", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Grid", "visualization.type": "Grid", "visualization.data.sources": [{"type": "correlated.metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "mssql.process.kernel.id", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "mssql.process.status", "aggregator": "last", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "medium", "css.classes": ["stripped-grid"], "row.height.px": 50}, "columns": [{"name": "monitor", "show": "no", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 1, "style": {}}, {"name": "mssql.process", "title": " ", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 2, "style": {"classes": ["font-bold"]}}, {"name": "mssql.process.kernel.id.last", "title": "KPID", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 3, "style": {"classes": ["font-bold"], "icon": {"name": "question", "placement": "prefix", "classes": ["text-neutral-light"]}}}, {"name": "mssql.process.status.last", "title": "Status", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 4, "style": {"classes": ["font-bold"], "icon": {"name": "circle", "placement": "prefix", "classes": ["text-secondary-yellow"]}}}]}}}, {"_type": "0", "id": 10000000000536, "visualization.name": "MSSQL Job Details", "visualization.description": "MSSQL MSSQL Job Details", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "Grid", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "mssql.job~current.status", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "mssql.job~retry.attempts", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "mssql.job~server", "aggregator": "last", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "medium"}, "columns": [{"name": "monitor", "show": "no", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 1, "style": {}}, {"name": "mssql.job", "title": "Job", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 2, "style": {}}, {"name": "mssql.job~current.status.last", "title": "Status", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 3, "style": {}}, {"name": "mssql.job~retry.attempts.last", "title": "Retry Attempts", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 4, "style": {}}, {"name": "mssql.job~server.last", "title": "Server", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 5, "style": {}}]}}}, {"_type": "0", "id": 10000000000537, "visualization.name": "Database Details", "visualization.description": "MSSQL Database Details", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "Grid", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "mssql.database~status", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "mssql.database~active.transactions", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "mssql.database~database.size.bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "mssql.database~creation.time", "aggregator": "last", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "medium"}, "columns": [{"name": "monitor", "show": "no", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "mssql.database", "title": "Database", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "mssql.database~status.last", "title": "Status", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "mssql.database~active.transactions.last", "title": "Active Transaction", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "mssql.database~database.size.bytes.last", "title": "Database Size(MB)", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "mssql.database~creation.time.last", "title": "Creation Time", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}]}}}, {"_type": "0", "id": 10000000000538, "visualization.name": "Database Mirroring Details", "visualization.description": "MSSQL Database Mirroring Details", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Grid", "visualization.type": "Grid", "visualization.data.sources": [{"type": "correlated.metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "mssql.mirroring.partner.instance.name", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "mssql.mirroring.role", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "mssql.mirroring.state", "aggregator": "last", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "medium"}, "columns": [{"name": "mssql.mirroring.partner.instance.name.last", "title": "Server Instance", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "mssql.mirroring.role.last", "title": "Current Role", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "mssql.mirroring.state.last", "title": "Mirroring State", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}]}}}, {"_type": "0", "id": 10000000000539, "visualization.name": "Log Shipping Details", "visualization.description": "MSSQL Log Shipping Details", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Grid", "visualization.type": "Grid", "visualization.data.sources": [{"type": "correlated.metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "mssql.log.shipping.database.type", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "mssql.log.shipping.database.name", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "mssql.log.shipping.last.backup.duration.seconds", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "mssql.log.shipping.last.backedup.file", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "mssql.log.shipping.last.copy.duration.seconds", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "mssql.log.shipping.last.copied.file", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "mssql.log.shipping.last.restore.duration.seconds", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "mssql.log.shipping.last.restored.file", "aggregator": "last", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "medium"}, "columns": [{"name": "mssql.log.shipping.database.type.last", "title": "Database Type", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "mssql.log.shipping.database.name.last", "title": "Database Name", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "mssql.log.shipping.last.backup.duration.seconds.last", "title": "Backup Duration", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "mssql.log.shipping.last.backedup.file.last", "title": "Backuped File", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "mssql.log.shipping.last.copy.duration.seconds.last", "title": "Copy Duration", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "mssql.log.shipping.last.copied.file.last", "title": "Copied File", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "mssql.log.shipping.last.restore.duration.seconds.last", "title": "Restore Du<PERSON>", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "mssql.log.shipping.last.restored.file.last", "title": "Restored File", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}]}}}, {"_type": "0", "id": 10000000000540, "visualization.name": "High Availability", "visualization.description": "MSSQL High Availability", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Grid", "visualization.type": "Grid", "visualization.data.sources": [{"type": "correlated.metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "mssql.alwayson.replica.name", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "mssql.alwayson.connected.state", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "mssql.alwayson.role", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "mssql.alwayson.operational.state", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "mssql.alwayson.availability.mode", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "mssql.alwayson.failover.mode", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "mssql.alwayson.endpoint.url", "aggregator": "last", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "medium"}, "columns": [{"name": "mssql.alwayson.replica.name.last", "title": "Replica Server Name", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "mssql.alwayson.connected.state.last", "title": "Connected State", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "mssql.alwayson.role.last", "title": "Role", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "mssql.alwayson.operational.state.last", "title": "Operational State", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "mssql.alwayson.availability.mode.last", "title": "Availability Mode", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "mssql.alwayson.failover.mode.last", "title": "Failover Mode", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "mssql.alwayson.endpoint.url.last", "title": "Endpoint URL", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}]}}}, {"_type": "0", "id": 10000000000541, "visualization.name": "Cluster Details", "visualization.description": "MSSQL Cluster Details", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Grid", "visualization.type": "Grid", "visualization.data.sources": [{"type": "correlated.metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "mssql.cluster.node.name", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "mssql.cluster.node.state", "aggregator": "last", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "medium"}, "columns": [{"name": "mssql.cluster.node.name.last", "title": "Cluster Name", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "mssql.cluster.node.state.last", "title": "State", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}]}}}, {"_type": "0", "id": 10000000000542, "visualization.name": "Used Indexes", "visualization.description": "MSSQL Used Indexes", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Grid", "visualization.type": "Grid", "visualization.data.sources": [{"type": "correlated.metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "mssql.index.database.name", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "mssql.index.schema.name", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "mssql.index.table.name", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "mssql.index.user.seeks", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "mssql.index.user.scans", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "mssql.index.user.lookups", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "mssql.index.user.updates", "aggregator": "last", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "medium"}, "columns": [{"name": "mssql.index.database.name.last", "title": "Database Name", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "mssql.index.schema.name.last", "title": "Schema Name", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "mssql.index.table.name,last", "title": "Table Name", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "mssql.index.user.seeks.last", "title": "User Seeks", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "mssql.index.user.scans.last", "title": "User <PERSON>", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "mssql.index.user.lookups.last", "title": "User Lookups", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "mssql.index.user.updates.last", "title": "User Updates", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}]}}}, {"_type": "0", "id": 10000000000543, "visualization.name": "Unused Indexes", "visualization.description": "MSSQL Unused Indexes", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Grid", "visualization.type": "Grid", "visualization.data.sources": [{"type": "correlated.metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "mssql.unused.index.database.name", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "mssql.unused.index.schema.name", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "mssql.unused.index.table.name", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "mssql.unused.index.id", "aggregator": "last", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "medium"}, "columns": [{"name": "mssql.unused.index.database.name.last", "title": "Database Name", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "mssql.unused.index.schema.name.last", "title": "Schema Name", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "mssql.unused.index.table.name.last", "title": "Table Name", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "mssql.unused.index.id.last", "title": "Index ID", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}]}}}, {"_type": "0", "id": 10000000000544, "visualization.name": "Missing Indexes Details", "visualization.description": "MSSQL Missing Indexes Details", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Grid", "visualization.type": "Grid", "visualization.data.sources": [{"type": "correlated.metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "mssql.missing.index.group", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "mssql.missing.index.database.name", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "mssql.missing.index.table.name", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "mssql.missing.index.column.id", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "mssql.missing.index.column.name", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "mssql.missing.index.column.usage", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "mssql.missing.index.user.seeks", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "mssql.missing.index.user.scans", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "mssql.missing.index.user.cost", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "mssql.missing.index.user.percent", "aggregator": "last", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "medium"}, "columns": [{"name": "mssql.missing.index.last", "title": "Index", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "mssql.missing.index.group.last", "title": "Index Group", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "mssql.missing.index.database.name.last", "title": "Database Name", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "mssql.missing.index.table.name.last", "title": "Table Name", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "mssql.missing.index.column.id.last", "title": "Column ID", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "mssql.missing.index.column.name.last", "title": "Column Name", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "mssql.missing.index.column.usage.last", "title": "Column <PERSON>age", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "mssql.missing.index.user.seeks.last", "title": "User Seeks", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "mssql.missing.index.user.scans.last", "title": "User <PERSON>", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "mssql.missing.index.user.cost.last", "title": "User Cost", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "mssql.missing.index.user.percent.last", "title": "User Percent", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}]}}}, {"_type": "0", "id": 10000000000545, "visualization.name": "Backup Details", "visualization.description": "MSSQL Backup Details", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Grid", "visualization.type": "Grid", "visualization.data.sources": [{"type": "correlated.metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "mssql.backup", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "mssql.backup.server.name", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "mssql.backup.database.name", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "mssql.backup.start.time", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "mssql.backup.end.time", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "mssql.backup.type", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "mssql.backup.bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "medium"}, "columns": [{"name": "mssql.backup.last", "title": "Backup", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "mssql.backup.server.name.last", "title": "Server", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "mssql.backup.database.name.last", "title": "Database Name", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "mssql.backup.start.time.last", "title": "Backup Start Date", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "mssql.backup.end.time.last", "title": "Backup Finish Date", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "mssql.backup.type.last", "title": "Backup Type", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "mssql.backup.bytes.last", "title": "Backup Size", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}]}}}, {"_type": "0", "id": 10000000000546, "visualization.name": "Slow Query Details", "visualization.description": "MSSQL Slow Query Details", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Grid", "visualization.type": "Grid", "visualization.data.sources": [{"type": "correlated.metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "mssql.query", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "mssql.query.creation.time", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "mssql.query.cpu.time.seconds", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "mssql.query.avg.elapsed.time.seconds", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "mssql.query.execution.count", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "mssql.query.last.execution.time", "aggregator": "last", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "medium", "row.height.px": 50}, "columns": [{"name": "monitor", "show": "no", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "mssql.query.last.execution.time.last", "title": "Last Query Time", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {"classes": ["font-bold"], "icon": {"name": "file-search", "placement": "prefix", "classes": ["text-secondary-yellow"]}}}, {"name": "mssql.query.cpu.time.seconds.last", "title": "CPU", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {"classes": ["font-bold"], "icon": {"name": "cpu", "placement": "prefix", "classes": ["text-secondary-green"]}}}, {"name": "mssql.query.avg.elapsed.time.seconds.last", "title": "Elapsed Time", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {"classes": ["font-bold"], "icon": {"name": "stopwatch", "placement": "prefix", "classes": ["text-secondary-orange"]}}}, {"name": "mssql.query.execution.count.last", "title": "Execution Count", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {"classes": ["font-bold"], "icon": {"name": "tachometer-fast", "placement": "prefix", "classes": ["text-secondary-green"]}}}, {"name": "mssql.query.creation.time.last", "title": "Creation Time", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {"classes": ["font-bold"], "icon": {"name": "", "placement": "prefix", "classes": ["text-primary"]}}}, {"name": "mssql.query", "title": "Query", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "type": "query", "style": {"classes": ["font-bold"], "icon": {"name": "expand", "placement": "prefix", "classes": ["text-primary"]}}}]}}}, {"_type": "0", "id": 10000000000547, "visualization.name": "Top Queries By CPU", "visualization.description": "MSSQL Top Queries By CPU", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Grid", "visualization.type": "Grid", "visualization.data.sources": [{"type": "correlated.metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "mssql.query", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "mssql.query.creation.time", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "mssql.query.avg.elapsed.time.seconds", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "mssql.query.cpu.time.seconds", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "mssql.query.execution.count", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "mssql.query.last.execution.time", "aggregator": "last", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "medium", "css.classes": ["stripped-grid"], "row.height.px": 50}, "columns": [{"name": "mssql.query.creation.time.last", "title": "Creation Time", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "mssql.query.avg.elapsed.time.seconds.last", "title": "Avg Elapsed Time", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "mssql.query.cpu.time.seconds.last", "title": "CPU", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "mssql.query.execution.count.last", "title": "Execution Count", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "mssql.query.last.execution.time.last", "title": "Last Execution Time", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "mssql.query", "title": "Query", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "type": "query", "style": {}}]}}}, {"_type": "0", "id": 10000000000548, "visualization.name": "Top Queries By I/O", "visualization.description": "MSSQL Top Queries By Elapsed Time", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Grid", "visualization.type": "Grid", "visualization.data.sources": [{"type": "correlated.metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "mssql.query", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "mssql.query.creation.time", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "mssql.query.avg.elapsed.time.seconds", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "mssql.query.execution.count", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "mssql.query.last.execution.time", "aggregator": "last", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "medium", "css.classes": ["stripped-grid"], "row.height.px": 50}, "columns": [{"name": "mssql.query.creation.time.last", "title": "Creation Time", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "mssql.query.avg.elapsed.time.seconds.last", "title": "Avg Elapsed Time", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "mssql.query.execution.count.last", "title": "Execution Count", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "mssql.query.last.execution.time.last", "title": "Last Execution Time", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "mssql.query", "title": "Query", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "type": "query", "style": {}}]}}}, {"_type": "0", "id": 10000000000549, "visualization.name": "Session Details", "visualization.description": "MSSQL Session Details", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Grid", "visualization.type": "Grid", "visualization.data.sources": [{"type": "correlated.metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "mssql.session.login.name", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "mssql.session.status", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "mssql.session.writes", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "mssql.session.reads", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "mssql.session.cpu.time.ms", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "mssql.session.used.memory.bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "mssql.session.application", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "mssql.session.failed.logons", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "mssql.session.domain", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "mssql.session.domain.user", "aggregator": "last", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "medium"}, "columns": [{"name": "mssql.session", "title": "PID", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "mssql.session.login.name.last", "title": "Login Name", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "mssql.session.status.last", "title": "Status", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "mssql.session.writes.last", "title": "Writes", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "mssql.session.reads.last", "title": "Reads", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "mssql.session.cpu.time.ms.last", "title": "CPU Time", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "mssql.session.used.memory.bytes.last", "title": "Memory Used", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "mssql.session.application.last", "title": "Application", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "mssql.session.failed.logons.last", "title": "Failed Logons", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "mssql.session.domain.last", "title": "Domain", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "mssql.session.domain.user.last", "title": "Domain User", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}]}}}, {"_type": "0", "id": 10000000000550, "visualization.name": "Waiting Session Details", "visualization.description": "MSSQL Waiting Session Details", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Grid", "visualization.type": "Grid", "visualization.data.sources": [{"type": "correlated.metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "mssql.waiting.session.login.name", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "mssql.waiting.session.domain", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "mssql.waiting.session.domain.user", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "mssql.waiting.session.wait.type", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "mssql.waiting.session.wait.duration.ms", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "mssql.waiting.session.program.name", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "mssql.waiting.session.remote.client", "aggregator": "last", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "medium"}, "columns": [{"name": "mssql.waiting.session", "title": "PID", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "mssql.waiting.session.login.name.last", "title": "Login Name", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "mssql.waiting.session.domain.last", "title": "Domain", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "mssql.waiting.session.domain.user.last", "title": "Domain User", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "mssql.waiting.session.wait.type.last", "title": "Wait Type", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "mssql.waiting.session.wait.duration.ms.last", "title": "Wait Duration (ms)", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "mssql.waiting.session.program.name.last", "title": "Program Name", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "mssql.waiting.session.remote.client.last", "title": "Remote Client", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}]}}}, {"_type": "0", "id": 10000000000551, "visualization.name": "Blocked Session Details", "visualization.description": "MSSQL Blocked Session Details", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Grid", "visualization.type": "Grid", "visualization.data.sources": [{"type": "correlated.metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "mssql.blocked.session.domain", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "mssql.blocked.session.domain.user", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "mssql.blocked.session.user", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "mssql.blocked.session.database", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "mssql.blocked.session.remote.client", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "mssql.blocked.session.program.name", "aggregator": "last", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "medium"}, "columns": [{"name": "mssql.blocked.session", "title": "PID", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "mssql.blocked.session.domain.last", "title": "Domain", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "mssql.blocked.session.domain.user.last", "title": "Domain User", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "mssql.blocked.session.user.last", "title": "Session User", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "mssql.blocked.session.database.last", "title": "Database", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "mssql.blocked.session.remote.client.last", "title": "Remote Client", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "mssql.blocked.session.program.name.last", "title": "Program Name", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}]}}}, {"_type": "0", "id": 10000000000552, "visualization.name": "Blocking Session Details", "visualization.description": "MSSQL Blocking Session Details", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Grid", "visualization.type": "Grid", "visualization.data.sources": [{"type": "correlated.metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "mssql.blocking.session.domain", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "mssql.blocking.session.domain.user", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "mssql.blocking.session.user", "aggregator": "last", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "medium"}, "columns": [{"name": "mssql.blocking.session", "title": "PID", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "mssql.blocking.session.domain.last", "title": "Domain", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "mssql.blocking.session.domain.user.last", "title": "Domain User", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "mssql.blocking.session.user.last", "title": "Session User", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}]}}}]