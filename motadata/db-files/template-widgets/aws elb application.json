[{"_type": "0", "id": 10000000001409, "visualization.name": "Connections", "visualization.description": "ELB Connections", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "aws.elb.active.connections", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "aws.elb.rejected.connections", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "aws.elb.active.connections"}, {"type": "metric", "data.point": "aws.elb.rejected.connections"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "color.data.point": "aws.elb.active.connections", "icon": {"name": "active-connections", "placement": "prefix"}}, "header": {"title": "Connections", "style": {"font.size": "medium"}, "data.points": []}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "Active", "value": "aws.elb.active.connections.last"}, {"label": "Rejected", "value": "aws.elb.rejected.connections.last"}]}}}}, {"_type": "0", "id": 10000000001410, "visualization.name": "Request Counts", "visualization.description": "ELB Request Counts", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "aws.elb.requests", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "aws.elb.requests"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "color.data.point": "aws.elb.requests", "icon": {"name": "requests", "placement": "prefix"}}, "header": {"title": "Request Count", "style": {"font.size": "medium"}, "data.points": []}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "", "value": "aws.elb.requests.last"}]}}}}, {"_type": "0", "id": 10000000001411, "visualization.name": "Response Time", "visualization.description": "ELB Response Time", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "aws.elb.target.response.time.ms", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "aws.elb.target.response.time.ms"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "color.data.point": "aws.elb.target.response.time.ms", "icon": {"name": "response-time", "placement": "prefix"}}, "header": {"title": "Response Time", "style": {"font.size": "medium"}, "data.points": []}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "", "value": "aws.elb.target.response.time.ms.last"}]}}}}, {"_type": "0", "id": 10000000001412, "visualization.name": "Connection Errors", "visualization.description": "ELB Connection Errors", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "aws.elb.target.connection.errors", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "aws.elb.target.connection.errors"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "color.data.point": "aws.elb.target.connection.errors", "icon": {"name": "error-connections", "placement": "prefix"}}, "header": {"title": "Connection Error", "style": {"font.size": "medium"}, "data.points": []}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "", "value": "aws.elb.target.connection.errors.last"}]}}}}, {"_type": "0", "id": 10000000001413, "visualization.name": "Hosts", "visualization.description": "ELB Hosts", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "aws.elb.healthy.hosts", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "aws.elb.unhealthy.hosts", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "aws.elb.healthy.hosts"}, {"type": "metric", "data.point": "aws.elb.unhealthy.hosts"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "color.data.point": "aws.elb.healthy.hosts", "icon": {"name": "host", "placement": "prefix"}}, "header": {"title": "Hosts", "style": {"font.size": "medium"}, "data.points": []}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "Healthy", "value": "aws.elb.healthy.hosts.last"}, {"label": "Unhealthy", "value": "aws.elb.unhealthy.hosts.last"}]}}}}, {"_type": "0", "id": 10000000001414, "visualization.name": "Consumed Lcus", "visualization.description": "ELB Consumed Lcus", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "aws.elb.consumed.lcus", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "aws.elb.consumed.lcus"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "color.data.point": "aws.elb.consumed.lcus", "icon": {"name": "consumed-lcus", "placement": "prefix"}}, "header": {"title": "Consumed LCUs", "style": {"font.size": "medium"}, "data.points": []}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "", "value": "aws.elb.consumed.lcus.last"}]}}}}, {"_type": "0", "id": 10000000001415, "visualization.name": "Healthy/Unhealthy Host Count", "visualization.description": "ELB Healthy/Unhealthy Host Count", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "aws.elb.healthy.hosts", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "aws.elb.unhealthy.hosts", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"_type": "0", "id": 10000000001416, "visualization.name": "ELB HTTP Error Count", "visualization.description": "ELB HTTP Error Count", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "aws.elb.3xx.responses", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "aws.elb.4xx.responses", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "aws.elb.5xx.responses", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"_type": "0", "id": 10000000001417, "visualization.name": "Connection Details", "visualization.description": "ELB Connection Details", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "aws.elb.active.connections", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "aws.elb.rejected.connections", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"_type": "0", "id": 10000000001418, "visualization.name": "Processed Bytes", "visualization.description": "ELB Processed Bytes", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Area", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "aws.elb.processed.bytes", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "no", "chart.label": "no", "highchart.settings": {}}}}, {"_type": "0", "id": 10000000001419, "visualization.name": "New Connections", "visualization.description": "ELB New Connections", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Area", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "aws.elb.new.connections", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "no", "chart.label": "no", "highchart.settings": {}}}}, {"_type": "0", "id": 10000000001420, "visualization.name": "Request", "visualization.description": "ELB Request", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Area", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "aws.elb.requests", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "no", "chart.label": "no", "highchart.settings": {}}}}, {"_type": "0", "id": 10000000001421, "visualization.name": "ELB Response Time", "visualization.description": "ELB Response Time", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Area", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "aws.elb.target.response.time.ms", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "no", "chart.label": "no", "highchart.settings": {}}}}, {"_type": "0", "id": 10000000001422, "visualization.name": "Target Connection Errors", "visualization.description": "ELB Target Connection Errors", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Area", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "aws.elb.target.connection.errors", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "no", "chart.label": "no", "highchart.settings": {}}}}, {"_type": "0", "id": 10000000001423, "visualization.name": "ELB Consumed LCUs", "visualization.description": "ELB Consumed Lcus", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Area", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "aws.elb.consumed.lcus", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "no", "chart.label": "no", "highchart.settings": {}}}}]