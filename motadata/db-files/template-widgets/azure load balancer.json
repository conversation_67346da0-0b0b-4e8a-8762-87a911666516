[{"_type": "0", "id": 10000000000264, "visualization.name": "Availability", "visualization.description": "LoadBalancer Availability", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "azure.loadbalancer.availability", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "azure.loadbalancer.availability"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "color.data.point": "azure.loadbalancer.availability", "icon": {"name": "availability", "placement": "prefix"}}, "header": {"title": "Availability", "style": {"font.size": "medium"}, "data.points": []}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "", "value": "azure.loadbalancer.availability.last"}]}}}}, {"_type": "0", "id": 10000000000265, "visualization.name": "SNAT Ports", "visualization.description": "LoadBalancer SNAT Ports", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "azure.loadbalancer.snat.ports", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "azure.loadbalancer.used.snat.ports", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "azure.loadbalancer.snat.ports"}, {"type": "metric", "data.point": "azure.loadbalancer.used.snat.ports"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "color.data.point": "azure.loadbalancer.snat.ports", "icon": {"name": "interface", "placement": "prefix"}}, "header": {"title": "SNAT Ports", "style": {"font.size": "medium"}, "data.points": []}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "Allocated", "value": "azure.loadbalancer.snat.ports.last"}, {"label": "Used", "value": "azure.loadbalancer.used.snat.ports.last"}]}}}}, {"_type": "0", "id": 10000000000266, "visualization.name": "Outbound Connections", "visualization.description": "LoadBalancer Outbound Connections", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "azure.loadbalancer.snat.connections", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "azure.loadbalancer.snat.connections"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "color.data.point": "azure.loadbalancer.snat.connections", "icon": {"name": "active-connections", "placement": "prefix"}}, "header": {"title": "Outbound Connections", "style": {"font.size": "medium"}, "data.points": []}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "", "value": "azure.loadbalancer.snat.connections.last"}]}}}}, {"_type": "0", "id": 10000000000267, "visualization.name": "TCP SYN Packets", "visualization.description": "LoadBalancer TCP SYN Packets", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "azure.loadbalancer.syn.packets", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "azure.loadbalancer.syn.packets"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "color.data.point": "azure.loadbalancer.syn.packets", "icon": {"name": "synchronization", "placement": "prefix"}}, "header": {"title": "TCP SYN Packets", "style": {"font.size": "medium"}, "data.points": []}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "", "value": "azure.loadbalancer.syn.packets.last"}]}}}}, {"_type": "0", "id": 10000000000268, "visualization.name": "Frontend Packets Count", "visualization.description": "LoadBalancer Frontend Packets Count", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "azure.loadbalancer.packets", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "azure.loadbalancer.packets"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "color.data.point": "azure.loadbalancer.packets", "icon": {"name": "inno-db", "placement": "prefix"}}, "header": {"title": "Frontend Packets Count", "style": {"font.size": "medium"}, "data.points": []}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "", "value": "azure.loadbalancer.packets.last"}]}}}}, {"_type": "0", "id": 10000000000269, "visualization.name": "Total Bytes Transmitted", "visualization.description": "LoadBalancer Total Bytes Transmitted", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "azure.loadbalancer.bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "azure.loadbalancer.bytes"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "color.data.point": "azure.loadbalancer.bytes", "icon": {"name": "transactions", "placement": "prefix"}}, "header": {"title": "Total Bytes Transmitted", "style": {"font.size": "medium"}, "data.points": []}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "", "value": "azure.loadbalancer.bytes.last"}]}}}}, {"_type": "0", "id": 10000000000270, "visualization.name": "Availability Details", "visualization.description": "LoadBalancer Availability Details", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "azure.loadbalancer.availability", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "azure.loadbalancer.dip.availability", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"_type": "0", "id": 10000000000271, "visualization.name": "SNAT Port Usage", "visualization.description": "LoadBalancer SNAT Port Usage", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "azure.loadbalancer.snat.ports", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "azure.loadbalancer.used.snat.ports", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"_type": "0", "id": 10000000000272, "visualization.name": "Outbound Connection", "visualization.description": "LoadBalancer Outbound Connection", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "azure.loadbalancer.snat.connections", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "no", "chart.label": "no", "highchart.settings": {}}}}, {"_type": "0", "id": 10000000000273, "visualization.name": "Bytes Transmitted", "visualization.description": "LoadBalancer Bytes Transmitted", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Area", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "azure.loadbalancer.bytes", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "no", "chart.label": "no", "highchart.settings": {}}}}, {"_type": "0", "id": 10000000000274, "visualization.name": "TCP SYN Packets", "visualization.description": "LoadBalancer TCP SYN Packets Chart", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Area", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "azure.loadbalancer.syn.packets", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "no", "chart.label": "no", "highchart.settings": {}}}}, {"_type": "0", "id": 10000000000275, "visualization.name": "Frontend Packets Rate", "visualization.description": "LoadBalancer Frontend Packets Rate", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Area", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "azure.loadbalancer.packets", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "no", "chart.label": "no", "highchart.settings": {}}}}]