[{"id": 10000000100013, "container.type": "dashboard", "visualization.name": "Network Flow", "visualization.granularity": "5 m", "visualization.timeline": {"relative.timeline": "today", "visualization.time.range.inclusive": "no"}, "visualization.category": "Chart", "visualization.type": "Area", "visualization.data.sources": [{"type": "flow", "category": "flow", "filters": {"data.filter": {}, "result.filter": {}, "drill.down.filter": {}}, "visualization.result.by": [], "data.points": [{"data.point": "flows", "aggregator": "sum", "entity.type": "event.source", "entities": []}]}], "visualization.properties": {"chart": {"rotation.angle": 0, "chart.legend": "no", "vertical.legend": "no", "line.width": 2, "chart.label": "no", "highchart.settings": {}, "sorting": {"limit": 10, "order": "desc"}}}, "visualization.result.by": [], "granularity": {"value": 5, "unit": "m"}}, {"id": 10000000100014, "container.type": "dashboard", "visualization.name": "Flow Summary", "visualization.timeline": {"relative.timeline": "today", "visualization.time.range.inclusive": "no"}, "visualization.category": "Grid", "visualization.type": "Grid", "visualization.data.sources": [{"type": "flow", "category": "flow", "filters": {"data.filter": {}, "result.filter": {}, "drill.down.filter": {}}, "visualization.result.by": ["source.ip", "destination.ip"], "data.points": [{"data.point": "flows", "aggregator": "count", "entity.type": "event.source", "entities": []}]}], "visualization.properties": {"grid": {"visualization.grid.properties.required": "no", "searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "small"}, "columns": [{"name": "source.ip", "title": "SOURCE IP", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 1, "style": {}}, {"name": "destination.ip", "title": "DESTINATION IP", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 2, "style": {}}, {"name": "flows.count", "title": "FLOW COUNT", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 3, "style": {}}]}}, "visualization.result.by": ["source.ip", "destination.ip"]}, {"id": 10000000100015, "container.type": "dashboard", "visualization.name": "Conversation Details", "visualization.timeline": {"relative.timeline": "today", "visualization.time.range.inclusive": "no"}, "visualization.category": "<PERSON><PERSON>", "visualization.type": "Grid", "visualization.data.sources": [{"type": "flow", "category": "flow", "filters": {"data.filter": {}, "result.filter": {}, "drill.down.filter": {}}, "visualization.result.by": ["source.ip", "application", "protocol", "destination.ip"], "data.points": [{"data.point": "volume.bytes", "aggregator": "sum", "entity.type": "event.source", "entities": []}]}], "visualization.properties": {"grid": {"visualization.grid.properties.required": "no", "searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "medium"}, "columns": [], "sorting": {"limit": 10, "order": "desc", "column": "volume.bytes.sum"}}}, "visualization.result.by": ["source.ip", "application", "protocol", "destination.ip"]}, {"id": 10000000100016, "container.type": "dashboard", "visualization.name": "Conversation Insights", "visualization.timeline": {"relative.timeline": "today", "visualization.time.range.inclusive": "no"}, "visualization.category": "Grid", "visualization.type": "Grid", "visualization.data.sources": [{"type": "flow", "category": "flow", "filters": {"data.filter": {}, "result.filter": {}, "drill.down.filter": {}}, "visualization.result.by": ["source.ip", "destination.ip", "protocol", "application"], "data.points": [{"data.point": "volume.bytes", "aggregator": "sum", "entity.type": "event.source", "entities": []}]}], "visualization.properties": {"grid": {"visualization.grid.properties.required": "no", "searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "small"}, "columns": [{"name": "source.ip", "title": "SOURCE IP", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 1, "style": {}}, {"name": "destination.ip", "title": "DESTINATION IP", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 2, "style": {}}, {"name": "application", "title": "APPLICATION", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 3, "style": {}}, {"name": "protocol", "title": "PROTOCOL", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 4, "style": {}}, {"name": "volume.bytes.sum", "title": "VOLUME BYTES", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 5, "style": {}}]}}, "visualization.result.by": ["source.ip", "destination.ip", "protocol", "application"]}, {"id": 10000000100017, "container.type": "dashboard", "visualization.name": "Top Sources By Traffic", "visualization.timeline": {"relative.timeline": "today", "visualization.time.range.inclusive": "no"}, "visualization.category": "TopN", "visualization.type": "Pie", "visualization.data.sources": [{"type": "flow", "category": "flow", "filters": {"data.filter": {}, "result.filter": {}, "drill.down.filter": {}}, "visualization.result.by": ["source.ip"], "data.points": [{"data.point": "volume.bytes", "aggregator": "sum", "entity.type": "event.source", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "vertical.legend": "no", "line.width": 2, "chart.label": "no", "highchart.settings": {}, "sorting": {"limit": 10, "order": "desc", "column": "volume.bytes.sum"}}}, "visualization.result.by": ["source.ip"], "granularity": {"value": 5, "unit": "m"}}, {"id": 10000000100018, "container.type": "dashboard", "visualization.name": "Top Sources By Traffic Summary", "visualization.timeline": {"relative.timeline": "today", "visualization.time.range.inclusive": "no"}, "visualization.category": "Grid", "visualization.type": "Grid", "visualization.data.sources": [{"type": "flow", "category": "flow", "filters": {"data.filter": {}, "result.filter": {}, "drill.down.filter": {}}, "visualization.result.by": ["source.ip"], "data.points": [{"data.point": "volume.bytes", "aggregator": "sum", "entity.type": "event.source", "entities": []}, {"data.point": "ingress.volume.bytes", "aggregator": "sum", "entity.type": "event.source", "entities": []}, {"data.point": "egress.volume.bytes", "aggregator": "sum", "entity.type": "event.source", "entities": []}]}], "visualization.properties": {"grid": {"visualization.grid.properties.required": "no", "searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "small"}, "columns": [{"name": "source.ip", "title": "SOURCE IP", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 1, "style": {}}, {"name": "egress.volume.bytes.sum", "title": "EGRESS VOLUME BYTE SUM", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 3, "style": {}}, {"name": "volume.bytes.sum", "title": "VOLUME BYTES", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 4, "style": {}}, {"name": "ingress.volume.bytes.sum", "title": "INGRESS VOLUME BYTE SUM", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 3, "style": {}}]}}, "visualization.result.by": ["source.ip"]}, {"id": 10000000100019, "container.type": "dashboard", "visualization.name": "Top Destinations By Traffic", "visualization.timeline": {"relative.timeline": "today", "visualization.time.range.inclusive": "no"}, "visualization.category": "TopN", "visualization.type": "Pie", "visualization.data.sources": [{"type": "flow", "category": "flow", "filters": {"data.filter": {}, "result.filter": {}, "drill.down.filter": {}}, "visualization.result.by": ["destination.ip"], "data.points": [{"data.point": "volume.bytes", "aggregator": "sum", "entity.type": "event.source", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "vertical.legend": "no", "line.width": 2, "chart.label": "no", "highchart.settings": {}, "sorting": {"limit": 10, "order": "desc", "column": "volume.bytes.sum"}}}, "visualization.result.by": ["destination.ip"], "granularity": {"value": 5, "unit": "m"}}, {"id": 10000000100020, "container.type": "dashboard", "visualization.name": "Top Destinations By Traffic Summary", "visualization.timeline": {"relative.timeline": "today", "visualization.time.range.inclusive": "no"}, "visualization.category": "Grid", "visualization.type": "Grid", "visualization.data.sources": [{"type": "flow", "category": "flow", "filters": {"data.filter": {}, "result.filter": {}, "drill.down.filter": {}}, "visualization.result.by": ["destination.ip"], "data.points": [{"data.point": "volume.bytes", "aggregator": "sum", "entity.type": "event.source", "entities": []}, {"data.point": "ingress.volume.bytes", "aggregator": "sum", "entity.type": "event.source", "entities": []}, {"data.point": "egress.volume.bytes", "aggregator": "sum", "entity.type": "event.source", "entities": []}]}], "visualization.properties": {"grid": {"visualization.grid.properties.required": "no", "searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "small"}, "columns": [{"name": "destination.ip", "title": "DESTINATION IP", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 1, "style": {}}, {"name": "egress.volume.bytes.sum", "title": "EGRESS VOLUME BYTES", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 3, "style": {}}, {"name": "volume.bytes.sum", "title": "VOLUME BYTES", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 4, "style": {}}, {"name": "ingress.volume.bytes.sum", "title": "INGRESS VOLUME BYTES", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 2, "style": {}}]}}, "visualization.result.by": ["destination.ip"]}, {"id": 10000000100021, "container.type": "dashboard", "visualization.name": "Source Interfaces By Traffic", "visualization.granularity": "5 m", "visualization.timeline": {"relative.timeline": "today", "visualization.time.range.inclusive": "no"}, "visualization.category": "Chart", "visualization.type": "Area", "visualization.data.sources": [{"type": "flow", "category": "flow", "filters": {"data.filter": {}, "result.filter": {}, "drill.down.filter": {}}, "visualization.result.by": ["source.if.index"], "data.points": [{"data.point": "volume.bytes", "aggregator": "sum", "entity.type": "event.source", "entities": []}]}], "visualization.properties": {"chart": {"rotation.angle": 0, "chart.legend": "yes", "vertical.legend": "no", "line.width": 2, "chart.label": "no", "highchart.settings": {}, "sorting": {"limit": 10, "order": "desc"}}}, "visualization.result.by": ["source.if.index"], "granularity": {"value": 5, "unit": "m"}}, {"id": 10000000100022, "container.type": "dashboard", "visualization.name": "Destination Interfaces By Traffic", "visualization.granularity": "5 m", "visualization.timeline": {"relative.timeline": "today", "visualization.time.range.inclusive": "no"}, "visualization.category": "Chart", "visualization.type": "Area", "visualization.data.sources": [{"type": "flow", "category": "flow", "filters": {"data.filter": {}, "result.filter": {}, "drill.down.filter": {}}, "visualization.result.by": ["destination.if.index"], "data.points": [{"data.point": "volume.bytes", "aggregator": "sum", "entity.type": "event.source", "entities": []}]}], "visualization.properties": {"chart": {"rotation.angle": 0, "chart.legend": "yes", "vertical.legend": "no", "line.width": 2, "chart.label": "no", "highchart.settings": {}, "sorting": {"limit": 10, "order": "desc"}}}, "visualization.result.by": ["destination.if.index"], "granularity": {"value": 5, "unit": "m"}}, {"id": 10000000100023, "container.type": "dashboard", "visualization.name": "Interface Details", "visualization.timeline": {"relative.timeline": "today", "visualization.time.range.inclusive": "no"}, "visualization.category": "Grid", "visualization.type": "Grid", "visualization.data.sources": [{"type": "flow", "category": "flow", "filters": {"data.filter": {}, "result.filter": {}, "drill.down.filter": {}}, "visualization.result.by": ["source.ip", "destination.ip", "source.if.index", "destination.if.index"], "data.points": [{"data.point": "volume.bytes", "aggregator": "sum", "entity.type": "event.source", "entities": []}]}], "visualization.properties": {"grid": {"visualization.grid.properties.required": "no", "searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "small"}, "columns": [{"name": "source.ip", "title": "SOURCE IP", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 1, "style": {}}, {"name": "destination.ip", "title": "DESTINATION IP", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 2, "style": {}}, {"name": "source.if.index", "title": "SOURCE IF INDEX", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 3, "style": {}}, {"name": "destination.if.index", "title": "DESTINATION IF INDEX", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 4, "style": {}}, {"name": "volume.bytes.sum", "title": "VOLUME BYTES", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 5, "style": {}}]}}, "visualization.result.by": ["source.ip", "destination.ip", "source.if.index", "destination.if.index"]}, {"id": 10000000100024, "container.type": "dashboard", "visualization.name": "Top Application By Traffic", "visualization.timeline": {"relative.timeline": "today", "visualization.time.range.inclusive": "no"}, "visualization.category": "TopN", "visualization.type": "Pie", "visualization.data.sources": [{"type": "flow", "category": "flow", "filters": {"data.filter": {}, "result.filter": {}, "drill.down.filter": {}}, "visualization.result.by": ["application"], "data.points": [{"data.point": "volume.bytes", "aggregator": "sum", "entity.type": "event.source", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "vertical.legend": "no", "line.width": 2, "chart.label": "no", "highchart.settings": {}, "sorting": {"limit": 10, "order": "desc", "column": "volume.bytes.sum"}}}, "visualization.result.by": ["application"], "granularity": {"value": 5, "unit": "m"}}, {"id": 10000000100025, "container.type": "dashboard", "visualization.name": "Top Application By Traffic Summary", "visualization.timeline": {"relative.timeline": "today", "visualization.time.range.inclusive": "no"}, "visualization.category": "Grid", "visualization.type": "Grid", "visualization.data.sources": [{"type": "flow", "category": "flow", "filters": {"data.filter": {}, "result.filter": {}, "drill.down.filter": {}}, "visualization.result.by": ["source.ip", "destination.ip", "application"], "data.points": [{"data.point": "volume.bytes", "aggregator": "sum", "entity.type": "event.source", "entities": []}, {"data.point": "ingress.volume.bytes", "aggregator": "sum", "entity.type": "event.source", "entities": []}, {"data.point": "egress.volume.bytes", "aggregator": "sum", "entity.type": "event.source", "entities": []}]}], "visualization.properties": {"grid": {"visualization.grid.properties.required": "no", "searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "small"}, "columns": []}}, "visualization.result.by": ["source.ip", "destination.ip", "application"]}, {"id": 10000000100026, "container.type": "dashboard", "visualization.name": "Top Protocols By Traffic", "visualization.timeline": {"relative.timeline": "today", "visualization.time.range.inclusive": "no"}, "visualization.category": "TopN", "visualization.type": "Pie", "visualization.data.sources": [{"type": "flow", "category": "flow", "filters": {"data.filter": {}, "result.filter": {}, "drill.down.filter": {}}, "visualization.result.by": ["protocol"], "data.points": [{"data.point": "volume.bytes", "aggregator": "sum", "entity.type": "event.source", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "vertical.legend": "no", "line.width": 2, "chart.label": "no", "highchart.settings": {}, "sorting": {"limit": 10, "order": "desc", "column": "volume.bytes.sum"}}}, "visualization.result.by": ["protocol"], "granularity": {"value": 5, "unit": "m"}}, {"id": 10000000100027, "container.type": "dashboard", "visualization.name": "Top Protocols By Traffic Summary", "visualization.timeline": {"relative.timeline": "today", "visualization.time.range.inclusive": "no"}, "visualization.category": "Grid", "visualization.type": "Grid", "visualization.data.sources": [{"type": "flow", "category": "flow", "filters": {"data.filter": {}, "result.filter": {}, "drill.down.filter": {}}, "visualization.result.by": ["source.ip", "destination.ip", "protocol"], "data.points": [{"data.point": "volume.bytes", "aggregator": "sum", "entity.type": "event.source", "entities": []}, {"data.point": "ingress.volume.bytes", "aggregator": "sum", "entity.type": "event.source", "entities": []}, {"data.point": "egress.volume.bytes", "aggregator": "sum", "entity.type": "event.source", "entities": []}]}], "visualization.properties": {"grid": {"visualization.grid.properties.required": "no", "searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "small"}, "columns": []}}, "visualization.result.by": ["source.ip", "destination.ip", "protocol"]}, {"id": 10000000100028, "container.type": "dashboard", "visualization.name": "Destination AS By Traffic", "visualization.timeline": {"relative.timeline": "today", "visualization.time.range.inclusive": "no"}, "visualization.category": "TopN", "visualization.type": "VerticalBar", "visualization.data.sources": [{"type": "flow", "category": "flow", "filters": {"data.filter": {}, "result.filter": {}, "drill.down.filter": {}}, "visualization.result.by": ["destination.aso"], "data.points": [{"data.point": "volume.bytes", "aggregator": "sum", "entity.type": "event.source", "entities": []}, {"data.point": "ingress.volume.bytes", "aggregator": "sum", "entity.type": "event.source", "entities": []}, {"data.point": "egress.volume.bytes", "aggregator": "sum", "entity.type": "event.source", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "vertical.legend": "no", "line.width": 2, "chart.label": "no", "axis.titles": {"x": "Destination ASO", "y": "Sum Volume Bytes"}, "highchart.settings": {}, "sorting": {"limit": 10, "order": "desc", "column": "volume.bytes.sum"}}}, "visualization.result.by": ["destination.aso"]}, {"id": 10000000100029, "container.type": "dashboard", "visualization.name": "AS Details", "visualization.timeline": {"relative.timeline": "today", "visualization.time.range.inclusive": "no"}, "visualization.category": "Grid", "visualization.type": "Grid", "visualization.data.sources": [{"type": "flow", "category": "flow", "filters": {"data.filter": {}, "result.filter": {}, "drill.down.filter": {}}, "visualization.result.by": ["source.aso", "destination.aso"], "data.points": [{"data.point": "volume.bytes", "aggregator": "sum", "entity.type": "event.source", "entities": []}, {"data.point": "ingress.volume.bytes", "aggregator": "sum", "entity.type": "event.source", "entities": []}, {"data.point": "egress.volume.bytes", "aggregator": "sum", "entity.type": "event.source", "entities": []}]}], "visualization.properties": {"grid": {"visualization.grid.properties.required": "no", "searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "small"}, "columns": [{"name": "source.aso", "title": "SOURCE ASO", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 1, "style": {}}, {"name": "destination.aso", "title": "DESTINATION ASO", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 2, "style": {}}, {"name": "egress.volume.bytes.sum", "title": "EGRESS VOLUME BYTES", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 4, "style": {}}, {"name": "volume.bytes.sum", "title": "VOLUME BYTES", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 5, "style": {}}, {"name": "ingress.volume.bytes.sum", "title": "INGRESS VOLUME BYTES", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 5, "style": {}}]}}, "visualization.result.by": ["source.aso", "destination.aso"]}, {"id": 10000000100030, "container.type": "dashboard", "visualization.name": "Top Domain By Traffic", "visualization.timeline": {"relative.timeline": "today", "visualization.time.range.inclusive": "no"}, "visualization.category": "TopN", "visualization.type": "Pie", "visualization.data.sources": [{"type": "flow", "category": "flow", "filters": {"data.filter": {}, "result.filter": {}, "drill.down.filter": {}}, "visualization.result.by": ["source.domain"], "data.points": [{"data.point": "volume.bytes", "aggregator": "sum", "entity.type": "event.source", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "vertical.legend": "no", "line.width": 2, "chart.label": "no", "highchart.settings": {}, "sorting": {"limit": 10, "order": "desc", "column": "volume.bytes.sum"}}}, "visualization.result.by": ["source.domain"]}, {"id": 10000000100031, "container.type": "dashboard", "visualization.name": "Top Domains By Traffic Summary", "visualization.timeline": {"relative.timeline": "today", "visualization.time.range.inclusive": "no"}, "visualization.category": "Grid", "visualization.type": "Grid", "visualization.data.sources": [{"type": "flow", "category": "flow", "filters": {"data.filter": {}, "result.filter": {}, "drill.down.filter": {}}, "visualization.result.by": ["source.domain", "destination.domain"], "data.points": [{"data.point": "volume.bytes", "aggregator": "sum", "entity.type": "event.source", "entities": []}]}], "visualization.properties": {"grid": {"visualization.grid.properties.required": "no", "searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "small"}, "columns": [{"name": "source.domain", "title": "SOURCE DOMAIN", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 1, "style": {}}, {"name": "destination.domain", "title": "DESTINATION DOMAIN", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 2, "style": {}}, {"name": "volume.bytes.sum", "title": "VOLUME BYTES", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 3, "style": {}}]}}, "visualization.result.by": ["source.domain", "destination.domain"], "granularity": {"value": 5, "unit": "m"}}, {"id": 10000000100032, "container.type": "dashboard", "visualization.name": "Traffic from Source to Destination Country", "visualization.timeline": {"relative.timeline": "today", "visualization.time.range.inclusive": "no"}, "visualization.category": "Map", "visualization.type": "Map", "visualization.data.sources": [{"type": "flow", "category": "flow", "filters": {"data.filter": {}, "result.filter": {}, "drill.down.filter": {}}, "visualization.result.by": ["destination.country", "source.country"], "data.points": [{"data.point": "volume.bytes", "aggregator": "sum", "entity.type": "event.source", "entities": []}]}], "visualization.properties": {}, "visualization.result.by": ["destination.country", "source.country"]}, {"id": 10000000100033, "container.type": "dashboard", "visualization.name": "Traffic from Source to Destination Country Summary", "visualization.timeline": {"relative.timeline": "today", "visualization.time.range.inclusive": "no"}, "visualization.category": "Grid", "visualization.type": "Grid", "visualization.data.sources": [{"type": "flow", "category": "flow", "filters": {"data.filter": {}, "result.filter": {}, "drill.down.filter": {}}, "visualization.result.by": ["source.country", "destination.country"], "data.points": [{"data.point": "volume.bytes", "aggregator": "sum", "entity.type": "event.source", "entities": []}]}], "visualization.properties": {"grid": {"visualization.grid.properties.required": "no", "searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "small"}, "columns": [{"name": "source.country", "title": "SOURCE COUNTRY", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 1, "style": {}}, {"name": "destination.country", "title": "DESTINATION COUNTRY", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 2, "style": {}}, {"name": "volume.bytes.sum", "title": "VOLUME BYTES", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 3, "style": {}}]}}, "visualization.result.by": ["source.country", "destination.country"]}, {"id": 10000000100034, "container.type": "dashboard", "visualization.name": "Traffic by Source Country", "visualization.timeline": {"relative.timeline": "today", "visualization.time.range.inclusive": "no"}, "visualization.category": "Map", "visualization.type": "Map", "visualization.data.sources": [{"type": "flow", "category": "flow", "filters": {"data.filter": {}, "result.filter": {}, "drill.down.filter": {}}, "visualization.result.by": ["source.country"], "data.points": [{"data.point": "volume.bytes", "aggregator": "sum", "entity.type": "event.source", "entities": []}]}], "visualization.properties": {}, "visualization.result.by": ["source.country"]}, {"id": 10000000100035, "container.type": "dashboard", "visualization.name": "Traffic by Destination Country", "visualization.timeline": {"relative.timeline": "today", "visualization.time.range.inclusive": "no"}, "visualization.category": "Map", "visualization.type": "Map", "visualization.data.sources": [{"type": "flow", "category": "flow", "filters": {"data.filter": {}, "result.filter": {}, "drill.down.filter": {}}, "visualization.result.by": ["destination.country"], "data.points": [{"data.point": "volume.bytes", "aggregator": "sum", "entity.type": "event.source", "entities": []}]}], "visualization.properties": {}, "visualization.result.by": ["destination.country"]}, {"id": 10000000100036, "container.type": "dashboard", "visualization.name": "ToS Details", "visualization.timeline": {"relative.timeline": "today", "visualization.time.range.inclusive": "no"}, "visualization.category": "Grid", "visualization.type": "Grid", "visualization.data.sources": [{"type": "flow", "category": "flow", "filters": {"data.filter": {}, "result.filter": {}, "drill.down.filter": {}}, "visualization.result.by": ["tos"], "data.points": [{"data.point": "volume.bytes", "aggregator": "sum", "entity.type": "event.source", "entities": []}]}], "visualization.properties": {"grid": {"visualization.grid.properties.required": "no", "searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "small"}, "columns": [{"name": "tos", "title": "TOS", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 1, "style": {}}, {"name": "volume.bytes.sum", "title": "VOLUME BYTES", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 3, "style": {}}]}}, "visualization.result.by": ["tos"]}, {"id": 10000000100037, "container.type": "dashboard", "visualization.name": "Top Users By Traffic", "visualization.timeline": {"relative.timeline": "today", "visualization.time.range.inclusive": "no"}, "visualization.category": "TopN", "visualization.type": "Pie", "visualization.data.sources": [{"type": "flow", "category": "flow", "filters": {"data.filter": {}, "result.filter": {}, "drill.down.filter": {}}, "visualization.result.by": ["user"], "data.points": [{"data.point": "volume.bytes", "aggregator": "sum", "entity.type": "event.source", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "vertical.legend": "no", "line.width": 2, "chart.label": "no", "highchart.settings": {}, "sorting": {"limit": 10, "order": "desc", "column": "volume.bytes.sum"}}}, "visualization.result.by": ["user"]}, {"id": 10000000100038, "container.type": "dashboard", "visualization.name": "Top Users By Traffic Summary", "visualization.timeline": {"relative.timeline": "today", "visualization.time.range.inclusive": "no"}, "visualization.category": "Grid", "visualization.type": "Grid", "visualization.data.sources": [{"type": "flow", "category": "flow", "filters": {"data.filter": {}, "result.filter": {}, "drill.down.filter": {}}, "visualization.result.by": ["user"], "data.points": [{"data.point": "volume.bytes", "aggregator": "sum", "entity.type": "event.source", "entities": []}, {"data.point": "ingress.volume.bytes", "aggregator": "sum", "entity.type": "event.source", "entities": []}, {"data.point": "egress.volume.bytes", "aggregator": "sum", "entity.type": "event.source", "entities": []}]}], "visualization.properties": {"grid": {"visualization.grid.properties.required": "no", "searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "small"}, "columns": [{"name": "user", "title": "USER", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 1, "style": {}}, {"name": "egress.volume.bytes.sum", "title": "EGRESS VOLUME BYTES", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 3, "style": {}}, {"name": "volume.bytes.sum", "title": "VOLUME BYTES", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 4, "style": {}}, {"name": "ingress.volume.bytes.sum", "title": "INGRESS VOLUME BYTES", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 4, "style": {}}]}}, "visualization.result.by": ["user"]}]