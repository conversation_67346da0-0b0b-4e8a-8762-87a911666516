[{"_type": "0", "id": 10000000000474, "visualization.name": "Connections", "visualization.description": "IIS Connections", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "iis.connection.attempts", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "iis.active.connections", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "iis.logon.attempts", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "iis.anonymous.users.per.sec"}, {"type": "metric", "data.point": "iis.non.anonymous.users.per.sec"}, {"type": "metric", "data.point": "iis.active.non.anonymous.users"}, {"type": "metric", "data.point": "iis.connection.attempts"}, {"type": "metric", "data.point": "iis.active.connections"}, {"type": "metric", "data.point": "iis.logon.attempts"}]}], "visualization.properties": {"gauge": {"style": {"font.size": "medium", "color.data.point": "iis.active.connections", "icon": {"name": "active-connections", "placement": "prefix"}}, "header": {"title": "Connection", "style": {"font.size": "medium"}, "data.points": [{"label": "Connection Attempts", "value": "iis.connection.attempts.last"}]}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "Active", "value": "iis.active.connections.last"}, {"label": "Attempt", "value": "iis.logon.attempts.last"}]}}}}, {"id": 10000000000475, "_type": "0", "visualization.name": "Requests", "visualization.description": "IIS Requests", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "iis.request.execution.time.ms", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "iis.current.requests", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "iis.rejected.requests", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "iis.get.requests.per.sec"}, {"type": "metric", "data.point": "iis.post.requests.per.sec"}, {"type": "metric", "data.point": "iis.head.requests.per.sec"}, {"type": "metric", "data.point": "iis.put.requests.per.sec"}, {"type": "metric", "data.point": "iis.delete.requests.per.sec"}, {"type": "metric", "data.point": "iis.request.execution.time.ms"}, {"type": "metric", "data.point": "iis.current.requests"}, {"type": "metric", "data.point": "iis.rejected.requests"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "medium", "color.data.point": "iis.request.execution.time.ms", "icon": {"name": "requests", "placement": "prefix"}}, "header": {"title": "Requests", "style": {"font.size": "medium"}, "data.points": [{"label": "Execution Time", "value": "iis.request.execution.time.ms.last"}]}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "Current", "value": "iis.current.requests.last"}, {"label": "Rejected", "value": "iis.rejected.requests.last"}]}}}}, {"id": 10000000000476, "_type": "0", "visualization.name": "Files", "visualization.description": "IIS Files", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "iis.received.files", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "iis.sent.files", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "iis.transferred.files", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "iis.sent.files.per.sec"}, {"type": "metric", "data.point": "iis.received.files.per.sec'"}, {"type": "metric", "data.point": "iis.file.transfers.per.sec"}, {"type": "metric", "data.point": "iis.received.files"}, {"type": "metric", "data.point": "iis.sent.files"}, {"type": "metric", "data.point": "iis.transferred.files"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "medium", "color.data.point": "iis.transferred.files", "icon": {"name": "files", "placement": "prefix"}}, "header": {"title": "Files", "style": {"font.size": "medium"}, "data.points": [{"label": "Total Received", "value": "iis.received.files.last"}]}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "<PERSON><PERSON>", "value": "iis.sent.files.last"}, {"label": "Transferred", "value": "iis.transferred.files.last"}]}}}}, {"id": 10000000000477, "_type": "0", "visualization.name": "Errors", "visualization.description": "IIS Errors", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "iis.404.errors", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "iis.locked.errors", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "iis.404.errors.per.sec"}, {"type": "metric", "data.point": "iis.locked.errors.per.sec"}, {"type": "metric", "data.point": "iis.404.errors"}, {"type": "metric", "data.point": "iis.locked.errors"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "medium", "color.data.point": "iis.locked.errors", "icon": {"name": "errors", "placement": "prefix"}}, "header": {"title": "Errors", "style": {"font.size": "medium"}, "data.points": []}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "404", "value": "iis.404.errors.last"}, {"label": "Locked", "value": "iis.locked.errors.last"}]}}}}, {"id": 10000000000478, "_type": "0", "visualization.name": "Network Statistics", "visualization.description": "IIS Network Statistics", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "iis.traffic.volume.bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "iis.sent.bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "iis.received.bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "iis.sent.bytes.per.sec"}, {"type": "metric", "data.point": "iis.received.bytes.per.sec"}, {"type": "metric", "data.point": "iis.bytes.per.sec"}, {"type": "metric", "data.point": "iis.traffic.volume.bytes"}, {"type": "metric", "data.point": "iis.sent.bytes"}, {"type": "metric", "data.point": "iis.received.bytes"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "medium", "color.data.point": "iis.traffic.volume.bytes", "icon": {"name": "network", "placement": "prefix"}}, "header": {"title": "Network Statistics", "style": {"font.size": "medium"}, "data.points": [{"label": "Traffic Volume", "value": "iis.traffic.volume.bytes.last"}]}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "<PERSON><PERSON>", "value": "iis.sent.bytes.last"}, {"label": "Received", "value": "iis.received.bytes.last"}]}}}}, {"_type": "0", "id": 10000000000479, "visualization.name": "IIS File Cache", "visualization.description": "IIS File Cache <PERSON>", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "iis.file.cache.hit.ratio.percent", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "iis.file.cache.used.memory.bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "iis.file.cache.hit.ratio.percent"}, {"type": "metric", "data.point": "iis.file.cache.used.memory.bytes"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "medium", "color.data.point": "iis.file.cache.hit.ratio.percent", "icon": {"name": "cache", "placement": "prefix"}}, "header": {"title": "File Cache", "style": {"font.size": "medium"}, "data.points": [{"label": "File Cache Used Memory", "value": "iis.file.cache.used.memory.bytes.last"}]}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "", "value": "iis.file.cache.hit.ratio.percent.last", "type": "gauge"}]}}}}, {"_type": "0", "id": 10000000000480, "visualization.name": "Today's Availability", "visualization.category": "Custom", "visualization.type": "Application Availability", "visualization.timeline": {"relative.timeline": "today"}, "visualization.result.by": ["monitor"], "visualization.data.sources": [{"visualization.result.by": ["monitor"], "type": "availability", "join.type": "all", "join.result": "availability", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "system.process~uptime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "system.process~downtime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "system.process~suspendtime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "system.process~unknowntime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "system.process~maintenancetime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "system.process~disabletime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "system.process~unreachabletime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}]}, {"visualization.result.by": ["monitor"], "type": "availability", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "system.process~uptime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "system.process~downtime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "system.process~suspendtime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "system.process~unknowntime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "system.process~maintenancetime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "system.process~disabletime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "system.process~unreachabletime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "no", "chart.label": "no", "axis.titles": {}, "highchart.settings": {"plotOptions": {"pie": {"innerSize": "70%"}}}}}, "join.type": "all", "join.columns": ["monitor"], "entity.type": "Monitor", "container.type": "Template", "entities": []}, {"_type": "0", "id": 10000000000481, "visualization.name": "Availability Statistics", "visualization.category": "Grid", "visualization.type": "Application Availability Time Series", "visualization.timeline": {"relative.timeline": "today"}, "visualization.data.sources": [{"type": "availability", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "system.process~uptime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~downtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~unreachabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~maintenancetime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~suspendtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~disabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~unknowntime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}], "visualization.timeline": {"relative.timeline": "-1d"}}, {"type": "availability", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "system.process~uptime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~downtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~unreachabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~maintenancetime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~suspendtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~disabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~unknowntime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}], "visualization.timeline": {"relative.timeline": "-7d"}}, {"type": "availability", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "system.process~uptime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~downtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~unreachabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~maintenancetime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~suspendtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~disabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~unknowntime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}], "visualization.timeline": {"relative.timeline": "-15d"}}], "visualization.properties": {"chart": {"rotation.angle": 0, "chart.legend": "yes", "chart.label": "no", "axis.titles": {}, "highchart.settings": {}, "sorting": {"limit": 10, "order": "desc", "column": "system.process~uptime.percent.avg"}}}}, {"id": 10000000000482, "_type": "0", "visualization.name": "Application", "visualization.description": "IIS Application", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "Grid", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "iis.app~pool", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "iis.app~anonymous.requests", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "iis.app~errors", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "iis.app~protocol", "aggregator": "last", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "medium"}, "columns": [{"name": "monitor", "show": "no", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "iis.app", "title": "App", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "iis.app~pool.last", "title": "Pool", "show": "Pool", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "iis.app~anonymous.requests.last", "title": "Requests", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "iis.app~errors.last", "title": "Errors", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "iis.app~protocol.last", "title": "Protocol", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}]}}}, {"id": 10000000000483, "_type": "0", "visualization.name": "Connections Stats", "visualization.description": "IIS Connections Stats", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "iis.active.connections", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "iis.connection.attempts", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "iis.logon.attempts", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"id": 10000000000484, "_type": "0", "visualization.name": "Requests Stats", "visualization.description": "IIS Requests Stats", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "iis.current.requests", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "iis.queued.requests", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "iis.rejected.requests", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"id": 10000000000485, "_type": "0", "visualization.name": "Files Details", "visualization.description": "IIS Files Details", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "iis.sent.files", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "iis.transferred.files", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "iis.total.received.files", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"id": 10000000000486, "_type": "0", "visualization.name": "Anonymous Activity", "visualization.description": "IIS Anonymous Activity", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "iis.active.anonymous.users", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "no", "chart.label": "no", "highchart.settings": {}}}}, {"id": 10000000000487, "_type": "0", "visualization.name": "CGI Request", "visualization.description": "IIS CGI Request", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "iis.cgi.requests.per.sec", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "no", "chart.label": "no", "highchart.settings": {}}}}, {"id": 10000000000488, "_type": "0", "visualization.name": "Worker Processor", "visualization.description": "IIS Worker Processor", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "iis.worker.processes", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "no", "chart.label": "no", "highchart.settings": {}}}}, {"id": 10000000000489, "_type": "0", "visualization.name": "Network Statistics", "visualization.description": "IIS Network Statistics Chart", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "iis.sent.bytes", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "iis.received.bytes", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "iis.traffic.volume.bytes", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"id": 10000000000490, "_type": "0", "visualization.name": "ASP.NET", "visualization.description": "IIS ASP.NET", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "iis.asp.net.applications.running", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "iis.asp.net.current.requests", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "iis.asp.net.rejected.requests", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "iis.asp.net.active.sessions", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"id": 10000000000491, "_type": "0", "visualization.name": "<PERSON><PERSON>", "visualization.description": "IIS <PERSON><PERSON>", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "iis.file.cache.hit.ratio.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "iis.metadata.cache.hit.ratio.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "iis.uri.cache.hit.ratio.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"id": 10000000000492, "_type": "0", "visualization.name": "IIS Application Pool Details", "visualization.description": "IIS Application Pool Details", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "Grid", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "iis.app.pool~state", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "iis.app.pool~worker.processes", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "iis.app.pool~worker.process.failures", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "iis.app.pool~process.shutdown.failures", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "iis.app.pool~cpu.percent", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "iis.app.pool~memory.bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "medium"}, "columns": [{"name": "monitor", "show": "no", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "iis.app.pool", "title": "Pool", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "iis.app.pool~state.last", "title": "State", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "iis.app.pool~worker.processes.last", "title": "Worker Processes", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "iis.app.pool~worker.process.failures.last", "title": "Worker Process Failures", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "iis.app.pool~process.shutdown.failures.last", "title": "Process Shutdown Failures", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "iis.app.pool~cpu.percent.last", "title": "CPU", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {"inline.chart": {"type": "gauge"}}}, {"name": "iis.app.pool~memory.bytes.last", "title": "Memory", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}]}}}]