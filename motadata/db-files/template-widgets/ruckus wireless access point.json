[{"_type": "0", "id": 10000000002323, "visualization.name": "Uptime", "visualization.description": "Uptime Ruckus Wireless Access Point", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "ruckus.wireless.access.point~started.time.seconds", "aggregator": "last", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"gauge": {"header": {"title": "Uptime", "data.points": [], "style": {"font.size": "medium"}}, "footer": {"style": {"font.size": "large"}, "data.points": [{"value": "ruckus.wireless.access.point~started.time.seconds.last"}]}, "style": {"icon": {"name": "stopwatch"}, "color.data.point": "ruckus.wireless.access.point~started.time.seconds"}}}}, {"_type": "0", "id": 10000000002322, "visualization.name": "Clients", "visualization.description": "Clients Ruckus Wireless Access Point", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "ruckus.wireless.access.point~clients", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "ruckus.wireless.access.point~clients"}]}], "visualization.properties": {"gauge": {"header": {"title": "Clients", "data.points": [], "style": {"font.size": "medium"}}, "footer": {"style": {"font.size": "large"}, "data.points": [{"value": "ruckus.wireless.access.point~clients.last"}]}, "style": {"icon": {"name": "users"}, "color.data.point": "ruckus.wireless.access.point~clients"}}}}, {"_type": "0", "id": 10000000002331, "visualization.name": "Assigned Vlan", "visualization.description": "Assigned Vlan Ruckus Wireless Access Point", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "ruckus.wireless.access.point~vlan", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "ruckus.wireless.access.point~vlan"}]}], "visualization.properties": {"gauge": {"header": {"title": "Assigned Vlan", "data.points": [], "style": {"font.size": "medium"}}, "footer": {"style": {"font.size": "large"}, "data.points": [{"value": "ruckus.wireless.access.point~vlan.last"}]}, "style": {"icon": {"name": "info-circle"}, "color.data.point": "ruckus.wireless.access.point~vlan"}}}}, {"_type": "0", "id": 10000000002320, "visualization.name": "AP Traffic", "visualization.description": "AP Traffic Ruckus Wireless Access Point", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "ruckus.wireless.access.point~sent.bytes.per.sec", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "ruckus.wireless.access.point~received.bytes.per.sec", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "ruckus.wireless.access.point~sent.bytes.per.sec"}, {"type": "metric", "data.point": "ruckus.wireless.access.point~received.bytes.per.sec"}]}], "visualization.properties": {"gauge": {"header": {"title": "AP Traffic", "data.points": [], "style": {"font.size": "medium"}}, "footer": {"style": {"font.size": "large"}, "data.points": [{"label": "<PERSON><PERSON>", "value": "ruckus.wireless.access.point~sent.bytes.per.sec.last"}, {"label": "Received", "value": "ruckus.wireless.access.point~received.bytes.per.sec.last"}]}, "style": {"icon": {"name": "traffic"}, "color.data.point": "ruckus.wireless.access.point~received.bytes.per.sec"}}}}, {"_type": "0", "id": 10000000002319, "visualization.name": "CPU Utilization", "visualization.description": "CPU Utilization Ruckus Wireless Access Point", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "ruckus.wireless.access.point~cpu.percent", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "ruckus.wireless.access.point~cpu.percent"}]}], "visualization.properties": {"gauge": {"header": {"title": "CPU Utilization", "data.points": [], "style": {"font.size": "medium"}}, "footer": {"style": {"font.size": "large"}, "data.points": [{"value": "ruckus.wireless.access.point~cpu.percent.last", "type": "gauge"}]}, "style": {"icon": {"name": "cpu"}, "color.data.point": "ruckus.wireless.access.point~cpu.percent"}}}}, {"_type": "0", "id": 10000000002321, "visualization.name": "Memory Utilization", "visualization.description": "Memory Utilization Ruckus Wireless Access Point", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "ruckus.wireless.access.point~free.memory.bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "ruckus.wireless.access.point~memory.installed.bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "ruckus.wireless.access.point~free.memory.bytes"}, {"type": "metric", "data.point": "ruckus.wireless.access.point~memory.installed.bytes"}]}], "visualization.properties": {"gauge": {"header": {"title": "Memory Utilization", "data.points": [], "style": {"font.size": "medium"}}, "footer": {"style": {"font.size": "large"}, "data.points": [{"label": "Free", "value": "ruckus.wireless.access.point~free.memory.bytes.last"}, {"label": "Total", "value": "ruckus.wireless.access.point~memory.installed.bytes.last"}]}, "style": {"icon": {"name": "memory"}, "color.data.point": "ruckus.wireless.access.point~memory.installed.bytes"}}}}, {"_type": "0", "id": 10000000002324, "visualization.name": "Today's Availability", "visualization.category": "Custom", "visualization.type": "Pie", "visualization.timeline": {"relative.timeline": "today"}, "visualization.result.by": ["monitor"], "visualization.data.sources": [{"visualization.result.by": ["monitor"], "type": "availability", "join.type": "all", "join.result": "availability", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "ruckus.wireless.access.point~uptime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "ruckus.wireless.access.point~downtime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "ruckus.wireless.access.point~suspendtime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "ruckus.wireless.access.point~unknowntime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "ruckus.wireless.access.point~maintenancetime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "ruckus.wireless.access.point~disabletime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "ruckus.wireless.access.point~unreachabletime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}]}, {"visualization.result.by": ["monitor"], "type": "availability", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "ruckus.wireless.access.point~uptime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "ruckus.wireless.access.point~downtime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "ruckus.wireless.access.point~suspendtime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "ruckus.wireless.access.point~unknowntime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "ruckus.wireless.access.point~maintenancetime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "ruckus.wireless.access.point~disabletime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "ruckus.wireless.access.point~unreachabletime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "no", "chart.label": "no", "axis.titles": {}, "highchart.settings": {"plotOptions": {"pie": {"innerSize": "70%"}}}}}, "join.type": "all", "join.columns": ["monitor"], "entity.type": "Monitor", "container.type": "Template", "entities": []}, {"_type": "0", "id": 10000000002325, "visualization.name": "Availability Statistics", "visualization.category": "Grid", "visualization.type": "Availability Time Series", "visualization.timeline": {"relative.timeline": "today"}, "visualization.data.sources": [{"type": "availability", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "ruckus.wireless.access.point~uptime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "ruckus.wireless.access.point~downtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "ruckus.wireless.access.point~suspendtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "ruckus.wireless.access.point~unknowntime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "ruckus.wireless.access.point~maintenancetime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "ruckus.wireless.access.point~disabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "ruckus.wireless.access.point~unreachabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}], "visualization.timeline": {"relative.timeline": "-1d"}}, {"type": "availability", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "ruckus.wireless.access.point~uptime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "ruckus.wireless.access.point~downtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "ruckus.wireless.access.point~suspendtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "ruckus.wireless.access.point~unknowntime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "ruckus.wireless.access.point~maintenancetime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "ruckus.wireless.access.point~disabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "ruckus.wireless.access.point~unreachabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}], "visualization.timeline": {"relative.timeline": "-7d"}}, {"type": "availability", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "ruckus.wireless.access.point~uptime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "ruckus.wireless.access.point~downtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "ruckus.wireless.access.point~suspendtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "ruckus.wireless.access.point~unknowntime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "ruckus.wireless.access.point~maintenancetime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "ruckus.wireless.access.point~disabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "ruckus.wireless.access.point~unreachabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}], "visualization.timeline": {"relative.timeline": "-15d"}}], "visualization.properties": {"chart": {"rotation.angle": 0, "chart.legend": "yes", "chart.label": "no", "axis.titles": {}, "highchart.settings": {}, "sorting": {"limit": 10, "order": "desc", "column": "ruckus.wireless.access.point~uptime.percent.avg"}}}}, {"_type": "0", "id": 10000000002328, "visualization.name": "Wireless Signal Strength", "visualization.description": "Wireless Signal Strength Ruckus Wireless Access Point", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "Wireless Signal Strength", "instance.type.filter": {"ruckus.wireless.access.point": "ruckus.wireless.client~ap"}, "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "ruckus.wireless.client~signal.strength.dbm", "aggregator": "last", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "small"}, "columns": [{"name": "monitor", "show": "no", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "ruckus.wireless.client", "alias": "wireless.client", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "ruckus.wireless.client~signal.strength.dbm.last", "alias": "wireless.client.signal.strength.dbm.last", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}]}}}, {"_type": "0", "id": 10000000002327, "visualization.name": "Client Details", "visualization.description": "Client Details Ruckus Wireless Access Point", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "Grid", "instance.type.filter": {"ruckus.wireless.access.point": "ruckus.wireless.client~ap"}, "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "ruckus.wireless.client~status", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "ruckus.wireless.client~ap", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "ruckus.wireless.client~snr", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "ruckus.wireless.client~ip.address", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "ruckus.wireless.client~traffic.sent.bytes.per.sec", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "ruckus.wireless.client~traffic.received.bytes.per.sec", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "ruckus.wireless.client~channel", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "ruckus.wireless.client~started.time", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "ruckus.wireless.client~signal.strength.dbm", "aggregator": "last", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "medium", "css.classes": ["stripped-grid"], "row.height.px": 50}, "columns": [{"name": "monitor", "show": "no", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 1, "style": {}}, {"name": "ruckus.wireless.client", "title": "Name", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 2, "style": {"classes": ["font-bold", "text-primary"]}}, {"name": "ruckus.wireless.client~status.last", "title": "Status", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "type": "status", "position": 3}, {"name": "ruckus.wireless.client~ap.last", "title": "AP Name", "show": "no", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "type": "status", "position": 4, "style": {"classes": ["font-bold"]}}, {"name": "ruckus.wireless.client~snr.last", "title": "SNR", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "type": "status", "position": 5, "style": {"classes": ["font-bold"]}}, {"name": "ruckus.wireless.client~ip.address.last", "title": "IP Address", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 6, "style": {"classes": ["font-bold"], "icon": {"name": "map-marker-alt", "placement": "prefix", "classes": ["text-neutral-light"]}}}, {"name": "Traffic", "title": "Traffic", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "computed": "yes", "position": 7, "formula": {"operation": "combine", "columns": ["ruckus.wireless.client~traffic.sent.bytes.per.sec.last", "ruckus.wireless.client~traffic.received.bytes.per.sec.last"]}, "style": {"classes": ["font-bold"], "icon": {"name": "traffic", "placement": "prefix", "classes": ["text-neutral-light"]}}}, {"name": "ruckus.wireless.client~traffic.sent.bytes.per.sec.last", "title": "<PERSON><PERSON>", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 8, "style": {"icon": {"name": "long-arrow-left", "placement": "prefix", "classes": ["text-neutral-light"]}, "classes": ["font-bold"]}}, {"name": "ruckus.wireless.client~traffic.received.bytes.per.sec.last", "title": "Received Bytes", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 9, "style": {"icon": {"name": "long-arrow-right", "classes": ["text-neutral-light"]}, "classes": ["font-bold"]}}, {"name": "ruckus.wireless.client~channel.last", "title": "Channel", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 10, "type": "label", "style": {"classes": ["font-bold"], "icon": {"name": "network", "placement": "prefix", "classes": ["text-secondary-yellow"]}}}, {"name": "ruckus.wireless.client~signal.strength.dbm.last", "title": "Signal Strength", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 11, "type": "label", "style": {}}, {"name": "ruckus.wireless.client~started.time.last", "title": "Uptime", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 12, "style": {"classes": ["font-bold"], "icon": {"name": "calendar-alt", "placement": "prefix", "classes": ["text-secondary-orange"]}}}]}}}]