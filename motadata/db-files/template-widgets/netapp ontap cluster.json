[{"_type": "0", "id": 10000000019985, "visualization.name": "Capacity", "visualization.description": "Cluster Storage capacity", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "netapp.ontap.cluster.storage.used.percent", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "netapp.ontap.cluster.storage.capacity.bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "netapp.ontap.cluster.storage.free.bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "netapp.ontap.cluster.storage.used.percent"}, {"type": "metric", "data.point": "netapp.ontap.cluster.storage.capacity.bytes"}, {"type": "metric", "data.point": "netapp.ontap.cluster.storage.free.bytes"}]}], "visualization.properties": {"gauge": {"header": {"title": "Capacity", "style": {"font.size": "medium"}, "data.points": [{"label": "Total", "value": "netapp.ontap.cluster.storage.capacity.bytes.last"}, {"label": "Free", "value": "netapp.ontap.cluster.storage.free.bytes.last"}]}, "footer": {"style": {"font.size": "large"}, "data.points": [{"label": "", "value": "netapp.ontap.cluster.storage.used.percent.last", "type": "gauge"}]}, "style": {"icon": {"name": "disk"}, "color.data.point": "netapp.ontap.cluster.storage.used.percent"}}}}, {"_type": "0", "id": 10000000019986, "visualization.name": "Node", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "netapp.ontap.cluster.nodes", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "netapp.ontap.cluster.nodes"}]}], "visualization.properties": {"gauge": {"style": {"icon": {"name": "node-count"}, "font.size": "medium", "color.data.point": "netapp.ontap.cluster.nodes"}, "header": {"title": "Node", "style": {"font.size": "medium"}}, "footer": {"style": {"font.size": "large"}, "data.points": [{"label": " ", "value": "netapp.ontap.cluster.nodes.last"}]}}}}, {"_type": "0", "id": 10000000019987, "visualization.name": "SVM", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "netapp.ontap.cluster.svms", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "netapp.ontap.cluster.svms"}]}], "visualization.properties": {"gauge": {"style": {"icon": {"name": "vm"}, "font.size": "medium", "color.data.point": "netapp.ontap.cluster.svms"}, "header": {"title": "SVM", "style": {"font.size": "medium"}}, "footer": {"style": {"font.size": "large"}, "data.points": [{"label": " ", "value": "netapp.ontap.cluster.svms.last"}]}}}}, {"_type": "0", "id": 10000000019988, "visualization.name": "Disk", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "netapp.ontap.cluster.disks", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "netapp.ontap.cluster.disks"}]}], "visualization.properties": {"gauge": {"style": {"icon": {"name": "disk", "placement": "prefix"}, "font.size": "medium", "color.data.point": "netapp.ontap.cluster.disks"}, "header": {"title": "Disk", "style": {"font.size": "medium"}}, "footer": {"style": {"font.size": "large"}, "data.points": [{"label": " ", "value": "netapp.ontap.cluster.disks.last"}]}}}}, {"_type": "0", "id": 10000000019989, "visualization.name": "volume", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "netapp.ontap.cluster.volumes", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "netapp.ontap.cluster.volumes"}]}], "visualization.properties": {"gauge": {"style": {"icon": {"name": "database"}, "font.size": "medium", "color.data.point": "netapp.ontap.cluster.volumes"}, "header": {"title": "Volume", "style": {"font.size": "medium"}}, "footer": {"style": {"font.size": "large"}, "data.points": [{"label": " ", "value": "netapp.ontap.cluster.volumes.last"}]}}}}, {"_type": "0", "id": 10000000019990, "visualization.name": "LUNs", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "netapp.ontap.cluster.luns", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "netapp.ontap.cluster.luns"}]}], "visualization.properties": {"gauge": {"style": {"icon": {"name": "processor"}, "font.size": "medium", "color.data.point": "netapp.ontap.cluster.luns"}, "header": {"title": "LUNs", "style": {"font.size": "medium"}}, "footer": {"style": {"font.size": "large"}, "data.points": [{"label": " ", "value": "netapp.ontap.cluster.luns.last"}]}}}}, {"_type": "0", "id": 10000000019991, "visualization.name": "Today's Availability", "visualization.category": "Custom", "visualization.type": "Pie", "visualization.timeline": {"relative.timeline": "today"}, "visualization.result.by": ["monitor"], "visualization.data.sources": [{"visualization.result.by": ["monitor"], "type": "availability", "join.type": "all", "join.result": "availability", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "monitor.uptime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.downtime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.suspendtime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.unknowntime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.maintenancetime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.disabletime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.unreachabletime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}]}, {"visualization.result.by": ["monitor"], "type": "availability", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "monitor.uptime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.downtime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.suspendtime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.unknowntime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.maintenancetime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.disabletime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.unreachabletime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "no", "chart.label": "no", "axis.titles": {}, "highchart.settings": {"plotOptions": {"pie": {"innerSize": "70%"}}}}}, "join.type": "all", "join.columns": ["monitor"], "entity.type": "Monitor", "container.type": "Template", "entities": []}, {"_type": "0", "id": 10000000019992, "visualization.name": "Availability Statistics", "visualization.category": "Grid", "visualization.type": "Availability Time Series", "visualization.timeline": {"relative.timeline": "today"}, "visualization.data.sources": [{"type": "availability", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "monitor.uptime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.downtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.suspendtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.unknowntime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.maintenancetime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.disabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.unreachabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}], "visualization.timeline": {"relative.timeline": "-1d"}}, {"type": "availability", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "monitor.uptime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.downtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.suspendtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.unknowntime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.maintenancetime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.disabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.unreachabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}], "visualization.timeline": {"relative.timeline": "-7d"}}, {"type": "availability", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "monitor.uptime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.downtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.suspendtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.unknowntime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.maintenancetime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.disabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.unreachabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}], "visualization.timeline": {"relative.timeline": "-15d"}}], "visualization.properties": {"chart": {"rotation.angle": 0, "chart.legend": "yes", "chart.label": "no", "axis.titles": {}, "highchart.settings": {}, "sorting": {"limit": 10, "order": "desc", "column": "monitor.uptime.percent.avg"}}}}, {"_type": "0", "id": 10000000019993, "visualization.name": "Capacity Utilization", "visualization.description": "NetApp ONTAP Cluster Capacity Utilization", "visualization.category": "Chart", "visualization.type": "Line", "visualization.timeline": {"relative.timeline": "today"}, "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "netapp.ontap.cluster.storage.used.bytes", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "netapp.ontap.cluster.storage.free.bytes", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "netapp.ontap.cluster.storage.capacity.bytes", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"rotation.angle": 0, "chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"_type": "0", "id": 10000000019994, "visualization.name": "IOPS Details ", "visualization.description": "NetApp ONTAP Cluster IOps details", "visualization.category": "Chart", "visualization.type": "Line", "visualization.timeline": {"relative.timeline": "today"}, "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "netapp.ontap.cluster.io.read.ops.per.sec", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "netapp.ontap.cluster.io.write.ops.per.sec", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "netapp.ontap.cluster.io.ops.per.sec", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"rotation.angle": 0, "chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"_type": "0", "id": 10000000019995, "visualization.name": "Latency Details", "visualization.description": "NetApp ONTAP Cluster Latency details", "visualization.category": "Chart", "visualization.type": "Line", "visualization.timeline": {"relative.timeline": "today"}, "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "netapp.ontap.cluster.read.latency.ms", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "netapp.ontap.cluster.write.latency.ms", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "netapp.ontap.cluster.latency.ms", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"rotation.angle": 0, "chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"_type": "0", "id": 10000000019996, "visualization.name": "Throughput Details", "visualization.description": "NetApp ONTAP Cluster Throughput Details", "visualization.category": "Chart", "visualization.type": "Line", "visualization.timeline": {"relative.timeline": "today"}, "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "netapp.ontap.cluster.throughput.read.bytes.per.sec", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "netapp.ontap.cluster.throughput.write.bytes.per.sec", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "netapp.ontap.cluster.throughput.bytes.per.sec", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"rotation.angle": 0, "chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"_type": "0", "id": 10000000019997, "visualization.name": "<PERSON><PERSON>", "visualization.category": "Grid", "visualization.type": "<PERSON><PERSON>", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "netapp.ontap.cluster.shelf~name", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "netapp.ontap.cluster.shelf~model", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "netapp.ontap.cluster.shelf~disks", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "netapp.ontap.cluster.shelf~state", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "netapp.ontap.cluster.shelf~serial.number", "aggregator": "last", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "view": "vlan", "style": {"header.font.size": "medium", "css.classes": ["stripped-grid"], "row.height.px": 50}, "columns": [{"name": "monitor", "show": "no", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 1, "style": {}}, {"name": "netapp.ontap.cluster.shelf", "title": "uuid", "show": "no", "sortable": "yes", "disable": "yes", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 2}, {"name": "netapp.ontap.cluster.shelf~name.last", "title": "Name", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 3}, {"name": "netapp.ontap.cluster.shelf~model.last", "title": "Model", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 4}, {"name": "netapp.ontap.cluster.shelf~serial.number.last", "title": "Serial Number", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 5, "style": {}}, {"name": "netapp.ontap.cluster.shelf~state.last", "title": "State", "show": "yes", "sortable": "yes", "disable": "yes", "resizable": "yes", "selectable": "no", "orderable": "yes", "position": 5}, {"name": "netapp.ontap.cluster.shelf~disks.last", "title": "Disk Count", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 7}]}}}, {"_type": "0", "id": 10000000019998, "visualization.name": "Node Summary", "visualization.category": "Grid", "visualization.type": "node", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "netapp.ontap.cluster.node~name", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "netapp.ontap.cluster.node~started.time", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "netapp.ontap.cluster.node~model", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "netapp.ontap.cluster.node~state", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "netapp.ontap.cluster.node~serial.number", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "netapp.ontap.cluster.node~ports", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "netapp.ontap.cluster.node~disks", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "netapp.ontap.cluster.node~controller.board", "aggregator": "last", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "view": "node", "style": {"header.font.size": "medium", "css.classes": ["stripped-grid"], "row.height.px": 50}, "columns": [{"name": "monitor", "show": "no", "sortable": "no", "disable": "yes", "resizable": "no", "selectable": "no", "orderable": "no", "position": 1}, {"name": "netapp.ontap.cluster.node", "title": "uuid", "show": "no", "sortable": "yes", "disable": "yes", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 2}, {"name": "netapp.ontap.cluster.node~name.last", "title": "Name", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 3}, {"name": "netapp.ontap.cluster.node~model.last", "title": "Model", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 4}, {"name": "netapp.ontap.cluster.node~state.last", "title": "State", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 5, "type": "status", "style": {"classes": ["font-bold"]}}, {"name": "netapp.ontap.cluster.node~serial.number.last", "title": "Serial Number", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 6, "style": {}}, {"name": "netapp.ontap.cluster.node~ports.last", "title": "Port count", "show": "yes", "sortable": "yes", "disable": "yes", "resizable": "yes", "selectable": "no", "orderable": "yes", "position": 7, "type": "port"}, {"name": "netapp.ontap.cluster.node~disks.last", "title": "Disk count", "show": "yes", "sortable": "yes", "disable": "yes", "resizable": "yes", "selectable": "no", "orderable": "yes", "position": 8, "type": "port"}, {"name": "netapp.ontap.cluster.node~controller.board.last", "title": "Controller Board", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 9}, {"name": "netapp.ontap.cluster.node~started.time.last", "title": "Uptime", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 10}]}}}, {"_type": "0", "id": 10000000020002, "visualization.name": "Temperature Sensor Details", "visualization.category": "Grid", "visualization.type": "temperature sensor", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "netapp.ontap.cluster.temperature.sensor~name", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "netapp.ontap.cluster.temperature.sensor~celsius", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "netapp.ontap.cluster.temperature.sensor~node", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "netapp.ontap.cluster.temperature.sensor~status", "aggregator": "last", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "view": "temperature sensor", "style": {"header.font.size": "medium", "css.classes": ["stripped-grid"], "row.height.px": 50}, "columns": [{"name": "monitor", "show": "no", "sortable": "no", "disable": "yes", "resizable": "no", "selectable": "no", "orderable": "no", "position": 1}, {"name": "netapp.ontap.cluster.temperature.sensor", "title": "uuid", "show": "no", "sortable": "yes", "disable": "yes", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 2}, {"name": "netapp.ontap.cluster.temperature.sensor~name.last", "title": "Name", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 3}, {"name": "netapp.ontap.cluster.temperature.sensor~node.last", "title": "Node", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 4}, {"name": "netapp.ontap.cluster.temperature.sensor~status.last", "title": "Status", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 5}, {"name": "netapp.ontap.cluster.temperature.sensor~celsius.last", "title": "Temperature(celsius)", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 6, "style": {}}]}}}, {"_type": "0", "id": 10000000020000, "visualization.name": "Battery Details", "visualization.category": "Grid", "visualization.type": "battery", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "netapp.ontap.cluster.battery.sensor~name", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "netapp.ontap.cluster.battery.sensor~power", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "netapp.ontap.cluster.battery.sensor~node", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "netapp.ontap.cluster.battery.sensor~status", "aggregator": "last", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "view": "battery", "style": {"header.font.size": "medium", "css.classes": ["stripped-grid"], "row.height.px": 50}, "columns": [{"name": "monitor", "show": "no", "sortable": "no", "disable": "yes", "resizable": "no", "selectable": "no", "orderable": "no", "position": 1}, {"name": "netapp.ontap.cluster.battery.sensor", "title": "uuid", "show": "no", "sortable": "yes", "disable": "yes", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 2}, {"name": "netapp.ontap.cluster.battery.sensor~name.last", "title": "Name", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 3}, {"name": "netapp.ontap.cluster.battery.sensor~node.last", "title": "Node", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 4}, {"name": "netapp.ontap.cluster.battery.sensor~status.last", "title": "Status", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 5}, {"name": "netapp.ontap.cluster.battery.sensor~power.last", "title": "Power(mwh)", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 6, "style": {}}]}}}, {"_type": "0", "id": 10000000020001, "visualization.name": "Fan Details", "visualization.category": "Grid", "visualization.type": "fan", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "netapp.ontap.cluster.fan.sensor~name", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "netapp.ontap.cluster.fan.sensor~speed", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "netapp.ontap.cluster.fan.sensor~node", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "netapp.ontap.cluster.fan.sensor~status", "aggregator": "last", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "view": "fan", "style": {"header.font.size": "medium", "css.classes": ["stripped-grid"], "row.height.px": 50}, "columns": [{"name": "monitor", "show": "no", "sortable": "no", "disable": "yes", "resizable": "no", "selectable": "no", "orderable": "no", "position": 1}, {"name": "netapp.ontap.cluster.fan.sensor", "title": "uuid", "show": "no", "sortable": "yes", "disable": "yes", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 2}, {"name": "netapp.ontap.cluster.fan.sensor~name.last", "title": "Name", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 3}, {"name": "netapp.ontap.cluster.fan.sensor~node.last", "title": "Node", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 4}, {"name": "netapp.ontap.cluster.fan.sensor~status.last", "title": "Status", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 5}, {"name": "netapp.ontap.cluster.fan.sensor~speed.last", "title": "Speed(rpm)", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 6, "style": {}}]}}}, {"_type": "0", "id": 10000000019999, "visualization.name": "Power Supply Details", "visualization.category": "Grid", "visualization.type": "power supply", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "netapp.ontap.cluster.power.sensor~name", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "netapp.ontap.cluster.power.sensor~voltage", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "netapp.ontap.cluster.power.sensor~node", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "netapp.ontap.cluster.power.sensor~status", "aggregator": "last", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "view": "power supply", "style": {"header.font.size": "medium", "css.classes": ["stripped-grid"], "row.height.px": 50}, "columns": [{"name": "monitor", "show": "no", "sortable": "no", "disable": "yes", "resizable": "no", "selectable": "no", "orderable": "no", "position": 1}, {"name": "netapp.ontap.cluster.power.sensor", "title": "uuid", "show": "no", "sortable": "yes", "disable": "yes", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 2}, {"name": "netapp.ontap.cluster.power.sensor~name.last", "title": "Name", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 3}, {"name": "netapp.ontap.cluster.power.sensor~node.last", "title": "Node", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 4}, {"name": "netapp.ontap.cluster.power.sensor~status.last", "title": "Status", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 5}, {"name": "netapp.ontap.cluster.power.sensor~voltage.last", "title": "Voltage(mv)", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 6, "style": {}}]}}}, {"_type": "0", "id": 10000000020003, "visualization.name": "Aggregate By IOps", "visualization.description": "Aggregate By IOps", "visualization.category": "Chart", "visualization.type": "Line", "visualization.timeline": {"relative.timeline": "today"}, "visualization.result.by": ["netapp.ontap.cluster.aggregate"], "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "visualization.result.by": ["netapp.ontap.cluster.aggregate"], "data.points": [{"data.point": "netapp.ontap.cluster.aggregate~io.ops.per.sec", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"sorting": {"limit": 5, "order": "asc", "column": "cisco.vedge.tunnel~qoe.avg"}, "rotation.angle": 0, "chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"_type": "0", "id": 10000000020004, "visualization.name": "Aggregates By Latency", "visualization.description": "Aggregates By Latency", "visualization.category": "Chart", "visualization.type": "Line", "visualization.timeline": {"relative.timeline": "today"}, "visualization.result.by": ["netapp.ontap.cluster.aggregate"], "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "visualization.result.by": ["netapp.ontap.cluster.aggregate"], "data.points": [{"data.point": "netapp.ontap.cluster.aggregate~latency.ms", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"sorting": {"limit": 5, "order": "asc", "column": "cisco.vedge.tunnel~qoe.avg"}, "rotation.angle": 0, "chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"_type": "0", "id": 10000000020005, "visualization.name": "Aggregates by throughput", "visualization.description": "Aggregates by throughput", "visualization.category": "Chart", "visualization.type": "Line", "visualization.timeline": {"relative.timeline": "today"}, "visualization.result.by": ["netapp.ontap.cluster.aggregate"], "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "visualization.result.by": ["netapp.ontap.cluster.aggregate"], "data.points": [{"data.point": "netapp.ontap.cluster.aggregate~throughput.bytes.per.sec", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"sorting": {"limit": 5, "order": "asc", "column": "cisco.vedge.tunnel~qoe.avg"}, "rotation.angle": 0, "chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"_type": "0", "id": 10000000020006, "visualization.name": "Aggregate Summary", "visualization.category": "Grid", "visualization.type": "aggregate", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "netapp.ontap.cluster.aggregate~name", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "netapp.ontap.cluster.aggregate~type", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "netapp.ontap.cluster.aggregate~state", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "netapp.ontap.cluster.aggregate~capacity.bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "netapp.ontap.cluster.aggregate~used.bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "netapp.ontap.cluster.aggregate~free.bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "netapp.ontap.cluster.aggregate~disks", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "netapp.ontap.cluster.aggregate~volumes", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "netapp.ontap.cluster.aggregate~raid.type", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "netapp.ontap.cluster.aggregate~node", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "netapp.ontap.cluster.aggregate~io.ops.per.sec", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "netapp.ontap.cluster.aggregate~latency.ms", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "netapp.ontap.cluster.aggregate~throughput.bytes.per.sec", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "netapp.ontap.cluster.aggregate~created.time", "aggregator": "last", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "view": "aggregate", "style": {"header.font.size": "medium", "css.classes": ["stripped-grid"], "row.height.px": 50}, "columns": [{"name": "monitor", "show": "no", "sortable": "no", "disable": "yes", "resizable": "no", "selectable": "no", "orderable": "no", "position": 1}, {"name": "netapp.ontap.cluster.aggregate", "title": "uuid", "show": "no", "sortable": "yes", "disable": "yes", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 2}, {"name": "netapp.ontap.cluster.aggregate~name.last", "title": "Name", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 3}, {"name": "netapp.ontap.cluster.aggregate~type.last", "title": "Type", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 4}, {"name": "netapp.ontap.cluster.aggregate~state.last", "title": "State", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 5, "style": {"classes": ["font-bold"]}}, {"name": "netapp.ontap.cluster.aggregate~node.last", "title": "Node", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 6, "style": {}}, {"name": "netapp.ontap.cluster.aggregate~capacity.bytes.last", "title": "Total capacity", "show": "yes", "sortable": "yes", "disable": "yes", "resizable": "yes", "selectable": "no", "orderable": "yes", "position": 7, "type": "port"}, {"name": "netapp.ontap.cluster.aggregate~used.bytes.last", "title": "used capacity", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 8}, {"name": "netapp.ontap.cluster.aggregate~free.bytes.last", "title": "Free capacity", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 9}, {"name": "netapp.ontap.cluster.aggregate~disks.last", "title": "Disk count", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 10}, {"name": "netapp.ontap.cluster.aggregate~volumes.last", "title": "Volume Count", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 11}, {"name": "netapp.ontap.cluster.aggregate~raid.type.last", "title": "RAID TYPE", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 12}, {"name": "netapp.ontap.cluster.aggregate~io.ops.per.sec.last", "title": "AVG IOPS", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 13}, {"name": "netapp.ontap.cluster.aggregate~latency.ms.last", "title": " AVG Latency(ms)", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 14}, {"name": "netapp.ontap.cluster.aggregate~throughput.bytes.per.sec.last", "title": "AVG Throughput(MBPS)", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 15}, {"name": "netapp.ontap.cluster.aggregate~created.time.last", "title": "Built At", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 16}]}}}, {"_type": "0", "id": 10000000020007, "visualization.name": "Disk Summary", "visualization.category": "Grid", "visualization.type": "disk", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "netapp.ontap.cluster.disk~name", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "netapp.ontap.cluster.disk~aggregate", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "netapp.ontap.cluster.disk~state", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "netapp.ontap.cluster.disk~model", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "netapp.ontap.cluster.disk~type", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "netapp.ontap.cluster.disk~node", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "netapp.ontap.cluster.disk~serial.number", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "netapp.ontap.cluster.disk~usable.bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "view": "disk", "style": {"header.font.size": "medium", "css.classes": ["stripped-grid"], "row.height.px": 50}, "columns": [{"name": "monitor", "show": "no", "sortable": "no", "disable": "yes", "resizable": "no", "selectable": "no", "orderable": "no", "position": 1}, {"name": "netapp.ontap.cluster.disk", "title": "uuid", "show": "no", "sortable": "yes", "disable": "yes", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 2}, {"name": "netapp.ontap.cluster.disk~name.last", "title": "Name", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 3}, {"name": "netapp.ontap.cluster.disk~aggregate.last", "title": "Aggregate", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "type": "view_more_drawer", "position": 4}, {"name": "netapp.ontap.cluster.disk~model.last", "title": "Model", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 5}, {"name": "netapp.ontap.cluster.disk~type.last", "title": "Type", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 6, "style": {}}, {"name": "netapp.ontap.cluster.disk~state.last", "title": "State ", "show": "yes", "sortable": "yes", "disable": "yes", "resizable": "yes", "selectable": "no", "orderable": "yes", "position": 7, "style": {"classes": ["font-bold"]}}, {"name": "netapp.ontap.cluster.disk~node.last", "title": "Node", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 8}, {"name": "netapp.ontap.cluster.disk~serial.number.last", "title": "Serial Number", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 9}, {"name": "netapp.ontap.cluster.disk~usable.bytes.last", "title": "Usable size", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 10}]}}}, {"_type": "0", "id": 10000000020008, "visualization.name": "SVM Summary", "visualization.category": "Grid", "visualization.type": "SVM", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "netapp.ontap.cluster.svm~name", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "netapp.ontap.cluster.svm~subtype", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "netapp.ontap.cluster.svm~state", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "netapp.ontap.cluster.svm~ipspace", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "netapp.ontap.cluster.svm~max.volumes", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "netapp.ontap.cluster.svm~protocol", "aggregator": "last", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "view": "SVM", "style": {"header.font.size": "medium", "css.classes": ["stripped-grid"], "row.height.px": 50}, "columns": [{"name": "monitor", "show": "no", "sortable": "no", "disable": "yes", "resizable": "no", "selectable": "no", "orderable": "no", "position": 1}, {"name": "netapp.ontap.cluster.svm", "title": "uuid", "show": "no", "sortable": "yes", "disable": "yes", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 2}, {"name": "netapp.ontap.cluster.svm~name.last", "title": "Name", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 3}, {"name": "netapp.ontap.cluster.svm~state.last", "title": "State", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 4}, {"name": "netapp.ontap.cluster.svm~subtype.last", "title": "SubType", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 5, "style": {}}, {"name": "netapp.ontap.cluster.svm~ipspace.last", "title": "IPSPACE ", "show": "yes", "sortable": "yes", "disable": "yes", "resizable": "yes", "selectable": "no", "orderable": "yes", "position": 6}, {"name": "netapp.ontap.cluster.svm~protocol.last", "title": "Protocol", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 7}, {"name": "netapp.ontap.cluster.svm~max.volumes.last", "title": "Maximum Volumes", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 8}]}}}, {"_type": "0", "id": 10000000020009, "visualization.name": "Volume Summary", "visualization.category": "Grid", "visualization.type": "Volume", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "netapp.ontap.cluster.volume~name", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "netapp.ontap.cluster.volume~type", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "netapp.ontap.cluster.volume~state", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "netapp.ontap.cluster.volume~svm", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "netapp.ontap.cluster.volume~aggregates", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "netapp.ontap.cluster.volume~capacity.bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "netapp.ontap.cluster.volume~used.bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "netapp.ontap.cluster.volume~free.bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "netapp.ontap.cluster.volume~snapshots", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "netapp.ontap.cluster.volume~style", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "netapp.ontap.cluster.volume~avg.io.ops.per.sec", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "netapp.ontap.cluster.volume~avg.latency.ms", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "netapp.ontap.cluster.volume~avg.throughput.bytes.per.sec", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "netapp.ontap.cluster.volume~created.time", "aggregator": "last", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "view": "Volume", "style": {"header.font.size": "medium", "css.classes": ["stripped-grid"], "row.height.px": 50}, "columns": [{"name": "monitor", "show": "no", "sortable": "no", "disable": "yes", "resizable": "no", "selectable": "no", "orderable": "no", "position": 1}, {"name": "netapp.ontap.cluster.volume", "title": "uuid", "show": "no", "sortable": "yes", "disable": "yes", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 2}, {"name": "netapp.ontap.cluster.volume~name.last", "title": "Name", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 3}, {"name": "netapp.ontap.cluster.volume~state.last", "title": "State", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 4}, {"name": "netapp.ontap.cluster.volume~type.last", "title": "Type", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 5}, {"name": "netapp.ontap.cluster.volume~svm.last", "title": "SVM", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 6, "style": {}}, {"name": "netapp.ontap.cluster.volume~aggregates.last", "title": "Aggregate ", "show": "yes", "sortable": "yes", "disable": "yes", "resizable": "yes", "selectable": "no", "orderable": "yes", "type": "view_more_drawer", "position": 7}, {"name": "netapp.ontap.cluster.volume~capacity.bytes.last", "title": "Total Capacity", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 8}, {"name": "netapp.ontap.cluster.volume~used.bytes.last", "title": "Used Capacity", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 9}, {"name": "netapp.ontap.cluster.volume~free.bytes.last", "title": "Free Capacity", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 10}, {"name": "netapp.ontap.cluster.volume~snapshots.last", "title": "Snapshot count", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 11}, {"name": "netapp.ontap.cluster.volume~style.last", "title": "Style", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 12}, {"name": "netapp.ontap.cluster.volume~avg.io.ops.per.sec.last", "title": "Avg IOPS", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 13}, {"name": "netapp.ontap.cluster.volume~avg.latency.ms.last", "title": "Avg Latency(ms)", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 14}, {"name": "netapp.ontap.cluster.volume~avg.throughput.bytes.per.sec.last", "title": "Avg throughout(MBPS)", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 15}, {"name": "netapp.ontap.cluster.volume~created.time.last", "title": "Built at", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 16}]}}}, {"_type": "0", "id": 10000000020010, "visualization.name": "LUN Summary", "visualization.category": "Grid", "visualization.type": "LUN", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "netapp.ontap.cluster.lun~name", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "netapp.ontap.cluster.lun~type", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "netapp.ontap.cluster.lun~state", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "netapp.ontap.cluster.lun~svm", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "netapp.ontap.cluster.lun~volume", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "netapp.ontap.cluster.lun~capacity.bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "netapp.ontap.cluster.lun~used.bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "netapp.ontap.cluster.lun~free.bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "netapp.ontap.cluster.lun~serial.number", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "netapp.ontap.cluster.lun~avg.io.ops.per.sec", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "netapp.ontap.cluster.lun~avg.latency.ms", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "netapp.ontap.cluster.lun~avg.throughput.bytes.per.sec", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "netapp.ontap.cluster.lun~created.time", "aggregator": "last", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "view": "LUN", "style": {"header.font.size": "medium", "css.classes": ["stripped-grid"], "row.height.px": 50}, "columns": [{"name": "monitor", "show": "no", "sortable": "no", "disable": "yes", "resizable": "no", "selectable": "no", "orderable": "no", "position": 1}, {"name": "netapp.ontap.cluster.lun", "title": "uuid", "show": "no", "sortable": "yes", "disable": "yes", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 2}, {"name": "netapp.ontap.cluster.lun~name.last", "title": "Name", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 3}, {"name": "netapp.ontap.cluster.lun~state.last", "title": "State", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 4}, {"name": "netapp.ontap.cluster.lun~type.last", "title": "Type", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 5}, {"name": "netapp.ontap.cluster.lun~svm.last", "title": "SVM", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 6, "style": {}}, {"name": "netapp.ontap.cluster.lun~volume.last", "title": "Volume ", "show": "yes", "sortable": "yes", "disable": "yes", "resizable": "yes", "selectable": "no", "orderable": "yes", "position": 7}, {"name": "netapp.ontap.cluster.lun~capacity.bytes.last", "title": "Total Capacity", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 8}, {"name": "netapp.ontap.cluster.lun~used.bytes.last", "title": "Used Capacity", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 9}, {"name": "netapp.ontap.cluster.lun~free.bytes.last", "title": "Free Capacity", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 10}, {"name": "netapp.ontap.cluster.lun~serial.number.last", "title": "Serial Number", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 11}, {"name": "netapp.ontap.cluster.lun~avg.io.ops.per.sec.last", "title": "Avg IOPS", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 12}, {"name": "netapp.ontap.cluster.lun~avg.latency.ms.last", "title": "Avg Latency(ms)", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 13}, {"name": "netapp.ontap.cluster.lun~avg.throughput.bytes.per.sec.last", "title": "Avg throughout(MBPS)", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 14}, {"name": "netapp.ontap.cluster.lun~created.time.last", "title": "Built at", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 15}]}}}, {"_type": "0", "id": 10000000020011, "visualization.name": "Snapshot Summary", "visualization.category": "Grid", "visualization.type": "Snapshot", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "netapp.ontap.cluster.snapshot~name", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "netapp.ontap.cluster.snapshot~state", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "netapp.ontap.cluster.snapshot~svm", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "netapp.ontap.cluster.snapshot~volume", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "netapp.ontap.cluster.snapshot~size.bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "netapp.ontap.cluster.snapshot~expiry.time.bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "netapp.ontap.cluster.snapshot~created.time", "aggregator": "last", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "view": "Snapshot", "style": {"header.font.size": "medium", "css.classes": ["stripped-grid"], "row.height.px": 50}, "columns": [{"name": "monitor", "show": "no", "sortable": "no", "disable": "yes", "resizable": "no", "selectable": "no", "orderable": "no", "position": 1}, {"name": "netapp.ontap.cluster.snapshot", "title": "uuid", "show": "no", "sortable": "yes", "disable": "yes", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 2}, {"name": "netapp.ontap.cluster.snapshot~name.last", "title": "Name", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 3}, {"name": "netapp.ontap.cluster.snapshot~state.last", "title": "State", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 4}, {"name": "netapp.ontap.cluster.snapshot~svm.last", "title": "SVM", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 5, "style": {}}, {"name": "netapp.ontap.cluster.snapshot~volume.last", "title": "Volume ", "show": "yes", "sortable": "yes", "disable": "yes", "resizable": "yes", "selectable": "no", "orderable": "yes", "position": 6}, {"name": "netapp.ontap.cluster.snapshot~size.bytes.last", "title": "Size", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 7}, {"name": "netapp.ontap.cluster.snapshot~expiry.time.last", "title": "Expiry Time", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 8}, {"name": "netapp.ontap.cluster.snapshot~created.time.last", "title": "Built at", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 9}]}}}, {"_type": "0", "id": 10000000020012, "visualization.name": "Qtree Summary", "visualization.category": "Grid", "visualization.type": "Qtree", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "netapp.ontap.cluster.qtree~name", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "netapp.ontap.cluster.qtree~svm", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "netapp.ontap.cluster.qtree~volume", "aggregator": "last", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "view": "Qtree", "style": {"header.font.size": "medium", "css.classes": ["stripped-grid"], "row.height.px": 50}, "columns": [{"name": "monitor", "show": "no", "sortable": "no", "disable": "yes", "resizable": "no", "selectable": "no", "orderable": "no", "position": 1}, {"name": "netapp.ontap.cluster.qtree", "title": "uuid", "show": "no", "sortable": "yes", "disable": "yes", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 2}, {"name": "netapp.ontap.cluster.qtree~name.last", "title": "Name", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 3}, {"name": "netapp.ontap.cluster.qtree~svm.last", "title": "SVM", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 4, "style": {}}, {"name": "netapp.ontap.cluster.qtree~volume.last", "title": "Volume ", "show": "yes", "sortable": "yes", "disable": "yes", "resizable": "yes", "selectable": "no", "orderable": "yes", "position": 5}]}}}, {"_type": "0", "id": 10000000020013, "visualization.name": "Interface Summary", "visualization.category": "Grid", "visualization.type": "Summary", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "netapp.ontap.cluster.interface~name", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "netapp.ontap.cluster.interface~svm", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "netapp.ontap.cluster.interface~ip.space", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "netapp.ontap.cluster.interface~address", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "netapp.ontap.cluster.interface~current.node", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "netapp.ontap.cluster.interface~current.port", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "netapp.ontap.cluster.interface~port.set", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "netapp.ontap.cluster.interface~protocol", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "netapp.ontap.cluster.interface~status", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "netapp.ontap.cluster.interface~type", "aggregator": "last", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "view": "Summary", "style": {"header.font.size": "medium", "css.classes": ["stripped-grid"], "row.height.px": 50}, "columns": [{"name": "monitor", "show": "no", "sortable": "no", "disable": "yes", "resizable": "no", "selectable": "no", "orderable": "no", "position": 1}, {"name": "netapp.ontap.cluster.interface", "title": "uuid", "show": "no", "sortable": "yes", "disable": "yes", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 2}, {"name": "netapp.ontap.cluster.interface~name.last", "title": "Name", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 3}, {"name": "netapp.ontap.cluster.interface~status.last", "title": "status", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 4, "type": "status", "style": {}}, {"name": "netapp.ontap.cluster.interface~svm.last", "title": "SVM ", "show": "yes", "sortable": "yes", "disable": "yes", "resizable": "yes", "selectable": "no", "orderable": "yes", "position": 5}, {"name": "netapp.ontap.cluster.interface~ip.space.last", "title": "IPSPACE ", "show": "yes", "sortable": "yes", "disable": "yes", "resizable": "yes", "selectable": "no", "orderable": "yes", "position": 6}, {"name": "netapp.ontap.cluster.interface~address.last", "title": "Address ", "show": "yes", "sortable": "yes", "disable": "yes", "resizable": "yes", "selectable": "no", "orderable": "yes", "position": 7}, {"name": "netapp.ontap.cluster.interface~current.node.last", "title": "Current Node ", "show": "yes", "sortable": "yes", "disable": "yes", "resizable": "yes", "selectable": "no", "orderable": "yes", "position": 8}, {"name": "netapp.ontap.cluster.interface~current.port.last", "title": "Current port ", "show": "yes", "sortable": "yes", "disable": "yes", "resizable": "yes", "selectable": "no", "orderable": "yes", "position": 9}, {"name": "netapp.ontap.cluster.interface~port.set.last", "title": "Port set ", "show": "yes", "sortable": "yes", "disable": "yes", "resizable": "yes", "selectable": "no", "orderable": "yes", "position": 10}, {"name": "netapp.ontap.cluster.interface~protocol.last", "title": "Protocol ", "show": "yes", "sortable": "yes", "disable": "yes", "resizable": "yes", "selectable": "no", "orderable": "yes", "position": 11}, {"name": "netapp.ontap.cluster.interface~type.last", "title": "Type ", "show": "yes", "sortable": "yes", "disable": "yes", "resizable": "yes", "selectable": "no", "orderable": "yes", "position": 12}]}}}, {"_type": "0", "id": 10000000020014, "visualization.name": "Fc Port Summary", "visualization.category": "Grid", "visualization.type": "fc port", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "netapp.ontap.cluster.fc.port~name", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "netapp.ontap.cluster.fc.port~state", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "netapp.ontap.cluster.fc.port~wwpn", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "netapp.ontap.cluster.fc.port~node", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "netapp.ontap.cluster.fc.port~current.link.speed.bytes.per.sec", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "netapp.ontap.cluster.fc.port~address", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "netapp.ontap.cluster.fc.port~protocol", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "netapp.ontap.cluster.fc.port~throughput.bytes.per.sec", "aggregator": "last", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "view": "fc port", "style": {"header.font.size": "medium", "css.classes": ["stripped-grid"], "row.height.px": 50}, "columns": [{"name": "monitor", "show": "no", "sortable": "no", "disable": "yes", "resizable": "no", "selectable": "no", "orderable": "no", "position": 1}, {"name": "netapp.ontap.cluster.fc.port", "title": "uuid", "show": "no", "sortable": "yes", "disable": "yes", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 2}, {"name": "netapp.ontap.cluster.fc.port~name.last", "title": "Name", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 3}, {"name": "netapp.ontap.cluster.fc.port~state.last", "title": "State", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 4, "style": {}}, {"name": "netapp.ontap.cluster.fc.port~wwpn.last", "title": "WWPN ", "show": "yes", "sortable": "yes", "disable": "yes", "resizable": "yes", "selectable": "no", "orderable": "yes", "position": 5}, {"name": "netapp.ontap.cluster.fc.port~node.last", "title": "Node ", "show": "yes", "sortable": "yes", "disable": "yes", "resizable": "yes", "selectable": "no", "orderable": "yes", "position": 6}, {"name": "netapp.ontap.cluster.fc.port~current.link.speed.bytes.per.sec.last", "title": "Data Link Rate ", "show": "yes", "sortable": "yes", "disable": "yes", "resizable": "yes", "selectable": "no", "orderable": "yes", "position": 7}, {"name": "netapp.ontap.cluster.fc.port~address.last", "title": "Port Address", "show": "yes", "sortable": "yes", "disable": "yes", "resizable": "yes", "selectable": "no", "orderable": "yes", "position": 8}, {"name": "netapp.ontap.cluster.fc.port~protocol.last", "title": "Protocol", "show": "yes", "sortable": "yes", "disable": "yes", "resizable": "yes", "selectable": "no", "orderable": "yes", "position": 9}, {"name": "netapp.ontap.cluster.fc.port~throughput.bytes.per.sec.last", "title": "AVG Throughput(MBPS)", "show": "yes", "sortable": "yes", "disable": "yes", "resizable": "yes", "selectable": "no", "orderable": "yes", "position": 10}]}}}, {"_type": "0", "id": 10000000020015, "visualization.name": "Ethernet Port Summary", "visualization.category": "Grid", "visualization.type": "Ethernet", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "netapp.ontap.cluster.ethernet.port~name", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "netapp.ontap.cluster.ethernet.port~state", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "netapp.ontap.cluster.ethernet.port~node", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "netapp.ontap.cluster.ethernet.port~mac.address", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "netapp.ontap.cluster.ethernet.port~mtu.bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "netapp.ontap.cluster.ethernet.port~throughput.bytes.per.sec", "aggregator": "last", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "view": "Ethernet", "style": {"header.font.size": "medium", "css.classes": ["stripped-grid"], "row.height.px": 50}, "columns": [{"name": "monitor", "show": "no", "sortable": "no", "disable": "yes", "resizable": "no", "selectable": "no", "orderable": "no", "position": 1}, {"name": "netapp.ontap.cluster.ethernet.port", "title": "uuid", "show": "no", "sortable": "yes", "disable": "yes", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 2}, {"name": "netapp.ontap.cluster.ethernet.port~name.last", "title": "Name", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 3}, {"name": "netapp.ontap.cluster.ethernet.port~state.last", "title": "State", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 4, "type": "status", "style": {}}, {"name": "netapp.ontap.cluster.ethernet.port~node.last", "title": "Node ", "show": "yes", "sortable": "yes", "disable": "yes", "resizable": "yes", "selectable": "no", "orderable": "yes", "position": 5}, {"name": "netapp.ontap.cluster.ethernet.port~mac.address.last", "title": "MAC Address ", "show": "yes", "sortable": "yes", "disable": "yes", "resizable": "yes", "selectable": "no", "orderable": "yes", "position": 6}, {"name": "netapp.ontap.cluster.ethernet.port~mtu.bytes.last", "title": "MTU", "show": "yes", "sortable": "yes", "disable": "yes", "resizable": "yes", "selectable": "no", "orderable": "yes", "position": 7}, {"name": "netapp.ontap.cluster.ethernet.port~throughput.bytes.per.sec.last", "title": "AVG Throughput(MBPS)", "show": "yes", "sortable": "yes", "disable": "yes", "resizable": "yes", "selectable": "no", "orderable": "yes", "position": 8}]}}}]