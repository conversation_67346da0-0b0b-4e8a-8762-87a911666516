[{"_type": "0", "id": 10000000000039, "visualization.name": "Desired Capacity", "visualization.description": "AutoScaling Desired Capacity", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "aws.autoscaling.group.desired.capacity", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "aws.autoscaling.group.desired.capacity"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "color.data.point": "aws.autoscaling.group.desired.capacity", "icon": {"name": "desired-capacity", "placement": "prefix"}}, "header": {"title": "Desired Capacity", "style": {"font.size": "medium"}, "data.points": []}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "", "value": "aws.autoscaling.group.desired.capacity.last"}]}}}}, {"_type": "0", "id": 10000000000040, "visualization.name": "Group Size", "visualization.description": "AutoScaling Group Size", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "aws.autoscaling.group.min.size", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "aws.autoscaling.group.max.size", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "aws.autoscaling.group.min.size"}, {"type": "metric", "data.point": "aws.autoscaling.group.max.size"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "color.data.point": "aws.autoscaling.group.max.size", "icon": {"name": "buffer", "placement": "prefix"}}, "header": {"title": "Group Size", "style": {"font.size": "medium"}, "data.points": []}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "Min", "value": "aws.autoscaling.group.min.size.last"}, {"label": "Max", "value": "aws.autoscaling.group.max.size.last"}]}}}}, {"_type": "0", "id": 10000000000041, "visualization.name": "In-service", "visualization.description": "AutoScaling In-service", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "aws.autoscaling.group.in.service.instances", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "aws.autoscaling.group.in.service.instances"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "color.data.point": "aws.autoscaling.group.in.service.instances", "icon": {"name": "in-service-instances", "placement": "prefix"}}, "header": {"title": "In-service", "style": {"font.size": "medium"}, "data.points": []}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "", "value": "aws.autoscaling.group.in.service.instances.last"}]}}}}, {"_type": "0", "id": 10000000000042, "visualization.name": "Total Instances", "visualization.description": "AutoScaling Total Instances", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "aws.autoscaling.group.instances", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "aws.autoscaling.group.instances"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "color.data.point": "aws.autoscaling.group.instances", "icon": {"name": "instances", "placement": "prefix"}}, "header": {"title": "Total Instances", "style": {"font.size": "medium"}, "data.points": []}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "", "value": "aws.autoscaling.group.instances.last"}]}}}}, {"_type": "0", "id": 10000000000043, "visualization.name": "Pending Instances", "visualization.description": "AutoScaling Pending Instances", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "aws.autoscaling.group.pending.instances", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "aws.autoscaling.group.pending.instances"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "color.data.point": "aws.autoscaling.group.pending.instances", "icon": {"name": "pending-instances", "placement": "prefix"}}, "header": {"title": "Pending Instances", "style": {"font.size": "medium"}, "data.points": []}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "", "value": "aws.autoscaling.group.pending.instances.last"}]}}}}, {"_type": "0", "id": 10000000000044, "visualization.name": "Terminating Instances", "visualization.description": "AutoScaling Terminating Instances", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "aws.autoscaling.group.terminating.instances", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "aws.autoscaling.group.terminating.instances"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "color.data.point": "aws.autoscaling.group.terminating.instances", "icon": {"name": "stopped-instances", "placement": "prefix"}}, "header": {"title": "Terminating Instances", "style": {"font.size": "medium"}, "data.points": []}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "", "value": "aws.autoscaling.group.terminating.instances.last"}]}}}}, {"_type": "0", "id": 10000000000045, "visualization.name": "Min/Max Capacity", "visualization.description": "AutoScaling Min/Max Capacity", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "aws.autoscaling.group.min.size", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "aws.autoscaling.group.max.size", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"_type": "0", "id": 10000000000046, "visualization.name": "In-service/Pending Instances", "visualization.description": "AutoScaling In-service/Pending Instances", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "aws.autoscaling.group.in.service.instances", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "aws.autoscaling.group.pending.instances", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"_type": "0", "id": 10000000000047, "visualization.name": "Desired Capacity", "visualization.description": "AutoScaling Desired Capacity Chart", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "aws.autoscaling.group.desired.capacity", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "no", "chart.label": "no", "highchart.settings": {}}}}, {"_type": "0", "id": 10000000000048, "visualization.name": "Terminating Instances", "visualization.description": "AutoScaling Terminating Instances Chart", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "aws.autoscaling.group.terminating.instances", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "no", "chart.label": "no", "highchart.settings": {}}}}, {"_type": "0", "id": 10000000000049, "visualization.name": "Standby Instances", "visualization.description": "AutoScaling Standby Instances", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "aws.autoscaling.group.standby.instances", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "no", "chart.label": "no", "highchart.settings": {}}}}, {"_type": "0", "id": 10000000000050, "visualization.name": "Total Instances", "visualization.description": "AutoScaling Total Instances Chart", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "aws.autoscaling.group.instances", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "no", "chart.label": "no", "highchart.settings": {}}}}]