[{"_type": "0", "id": 10000000002217, "visualization.name": "Stack Details", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "cisco.stack.switch.members", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "cisco.stack.switch.members"}]}], "visualization.properties": {"gauge": {"style": {"icon": {"name": "cpu", "placement": "prefix"}, "font.size": "medium", "color.data.point": "cisco.stack.switch.members"}, "header": {"title": "Stack Details", "style": {"font.size": "medium"}}, "footer": {"style": {"font.size": "large"}, "data.points": [{"label": "Members", "value": "cisco.stack.switch.members.last"}]}}}}, {"_type": "0", "id": 10000000002218, "visualization.name": "<PERSON><PERSON>", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Gauge", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "interface~speed.bits.per.sec", "aggregator": "avg", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "interface~speed.bits.per.sec"}]}], "visualization.properties": {"gauge": {"style": {"icon": {"name": "memory", "placement": "prefix"}, "font.size": "medium", "color.data.point": "interface~speed.bits.per.sec"}, "header": {"title": "<PERSON><PERSON>", "style": {"font.size": "medium"}}, "footer": {"style": {"font.size": "large"}, "data.points": [{"label": "", "value": "interface~speed.bits.per.sec.avg"}]}}}}, {"_type": "0", "id": 10000000002219, "visualization.name": "Interface Status Count", "visualization.category": "Gauge", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "availability", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "interface~up.count", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "interface~down.count", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "interface~up.count"}, {"type": "metric", "data.point": "interface~down.count"}]}], "visualization.properties": {"gauge": {"header": {"title": "Interface", "style": {"font.size": "medium"}}, "footer": {"style": {"font.size": "large"}, "data.points": [{"label": "Up", "value": "up"}, {"label": "Down", "value": "down"}]}, "style": {"icon": {"name": "port"}, "color.data.point": "interface~up.count"}}}, "visualization.timeline": {"relative.timeline": "today"}}, {"_type": "0", "id": 10000000002220, "visualization.name": "Buffer <PERSON>", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "cisco.small.buffer.misses", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "cisco.large.buffer.misses", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "cisco.small.buffer.misses"}, {"type": "metric", "data.point": "cisco.large.buffer.misses"}]}], "visualization.properties": {"gauge": {"header": {"title": "Buffer <PERSON>", "style": {"font.size": "medium"}}, "footer": {"style": {"font.size": "large"}, "data.points": [{"label": "Small", "value": "cisco.small.buffer.misses.last"}, {"label": "Large", "value": "cisco.large.buffer.misses.last"}]}, "style": {"icon": {"name": "buffer-miss"}, "color.data.point": "cisco.small.buffer.misses"}}}}, {"_type": "0", "id": 10000000002221, "visualization.name": "Packet Lost", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "ping.packet.lost.percent", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "ping.packet.lost.percent"}]}], "visualization.properties": {"gauge": {"style": {"icon": {"name": "rejected-connections", "placement": "prefix"}, "font.size": "medium", "color.data.point": "ping.packet.lost.percent"}, "header": {"title": "Packet Lost", "style": {"font.size": "medium"}}, "footer": {"style": {"font.size": "large"}, "data.points": [{"label": "", "value": "ping.packet.lost.percent.last", "type": "gauge"}]}}}}, {"_type": "0", "id": 10000000002222, "visualization.name": "Response Time", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "ping.latency.ms", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "ping.lost.packets", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "ping.latency.ms"}, {"type": "metric", "data.point": "ping.max.latency.ms"}, {"type": "metric", "data.point": "ping.min.latency.ms"}, {"type": "metric", "data.point": "ping.lost.packets"}]}], "visualization.properties": {"gauge": {"header": {"title": "Response Time", "style": {"font.size": "medium"}, "data.points": [{"label": "Error Packets", "value": "ping.lost.packets.last"}]}, "footer": {"style": {"font.size": "large"}, "data.points": [{"label": "", "value": "ping.latency.ms.last"}]}, "style": {"icon": {"name": "response-time"}, "color.data.point": "ping.latency.ms.last"}}}}, {"_type": "0", "id": 10000000002223, "visualization.name": "Today's Availability", "visualization.category": "Custom", "visualization.type": "Pie", "visualization.timeline": {"relative.timeline": "today"}, "visualization.result.by": ["monitor"], "visualization.data.sources": [{"visualization.result.by": ["monitor"], "type": "availability", "join.type": "all", "join.result": "availability", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "monitor.uptime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.downtime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.suspendtime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.unknowntime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.maintenancetime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.disabletime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.unreachabletime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}]}, {"visualization.result.by": ["monitor"], "type": "availability", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "monitor.uptime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.downtime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.suspendtime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.unknowntime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.maintenancetime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.disabletime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.unreachabletime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "no", "chart.label": "no", "axis.titles": {}, "highchart.settings": {"plotOptions": {"pie": {"innerSize": "70%"}}}}}, "join.type": "all", "join.columns": ["monitor"], "entity.type": "Monitor", "container.type": "Template", "entities": []}, {"_type": "0", "id": 10000000002224, "visualization.name": "Availability Statistics", "visualization.category": "Grid", "visualization.type": "Availability Time Series", "visualization.timeline": {"relative.timeline": "today"}, "visualization.data.sources": [{"type": "availability", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "monitor.uptime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.downtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.suspendtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.unknowntime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.maintenancetime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.disabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.unreachabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}], "visualization.timeline": {"relative.timeline": "-1d"}}, {"type": "availability", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "monitor.uptime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.downtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.suspendtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.unknowntime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.maintenancetime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.disabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.unreachabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}], "visualization.timeline": {"relative.timeline": "-7d"}}, {"type": "availability", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "monitor.uptime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.downtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.suspendtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.unknowntime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.maintenancetime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.disabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.unreachabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}], "visualization.timeline": {"relative.timeline": "-15d"}}], "visualization.properties": {"chart": {"rotation.angle": 0, "chart.legend": "yes", "chart.label": "no", "axis.titles": {}, "highchart.settings": {}, "sorting": {"limit": 10, "order": "desc", "column": "monitor.uptime.percent.avg"}}}}, {"_type": "0", "id": 10000000002241, "visualization.name": "Response Time vs Packet Lost", "visualization.description": "Response Time vs Packet Lost <PERSON><PERSON> Stacked", "visualization.category": "Chart", "visualization.type": "Line", "visualization.timeline": {"relative.timeline": "today"}, "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "ping.packet.lost.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "ping.latency.ms", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"rotation.angle": 0, "chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"id": 10000000002226, "visualization.name": "CPU Utilization", "visualization.description": "CPU Utilization", "visualization.granularity": "5 m", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "system.cpu.percent", "aggregator": "avg", "entity.type": "Group"}]}], "visualization.properties": {"chart": {"rotation.angle": 0, "chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}, "granularity": {"value": 5, "unit": "m"}}, {"id": 10000000002227, "visualization.name": "Memory Utilization", "visualization.description": "Memory Utilization", "visualization.granularity": "5 m", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "system.memory.used.percent", "aggregator": "avg", "entity.type": "Group"}]}], "visualization.properties": {"chart": {"rotation.angle": 0, "chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}, "granularity": {"value": 5, "unit": "m"}}, {"_type": "0", "id": 10000000002228, "visualization.name": "Top 10 Interfaces By Average Traffic", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "TopN", "visualization.type": "Grid", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "interface~traffic.bits.per.sec", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "interface~in.traffic.utilization.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "interface~out.traffic.utilization.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "medium"}, "columns": [{"name": "monitor", "show": "no", "sortable": "yes", "disable": "yes", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "interface", "title": "Interface", "show": "yes", "sortable": "yes", "disable": "yes", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}, "type": "interface"}, {"name": "interface~traffic.bits.per.sec.avg", "title": "Total Traffic", "show": "yes", "sortable": "yes", "disable": "yes", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "interface~in.traffic.utilization.percent.avg", "title": "IN (%)", "show": "yes", "sortable": "yes", "disable": "yes", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {"inline.chart": {"type": "gauge", "classes": ["success"]}}}, {"name": "interface~out.traffic.utilization.percent.avg", "title": "OUT (%)", "show": "yes", "sortable": "yes", "disable": "yes", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {"inline.chart": {"type": "gauge"}}}], "sorting": {"limit": 10, "order": "desc", "column": "interface~traffic.bits.per.sec.avg"}}}}, {"_type": "0", "id": 10000000002229, "visualization.name": "Top 10 Interfaces By Average Error Packets", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "TopN", "visualization.type": "Grid", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "interface~error.packets", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "interface~error.packets", "aggregator": "sparkline", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "medium"}, "columns": [{"name": "monitor", "show": "no", "sortable": "yes", "disable": "yes", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 1}, {"name": "interface", "title": "Interface", "show": "yes", "sortable": "yes", "disable": "yes", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}, "type": "interface"}, {"name": "interface~error.packets.avg", "title": "Value", "show": "yes", "sortable": "yes", "disable": "yes", "resizable": "yes", "selectable": "yes", "orderable": "yes", "type": "number", "position": 2}, {"name": "interface~error.packets.sparkline", "title": " ", "show": "yes", "sortable": "yes", "disable": "yes", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 3, "style": {"inline.chart": {"type": "sparkline"}}}], "sorting": {"limit": 10, "order": "desc", "column": "interface~error.packets.avg"}}, "sparkline": "yes"}}, {"_type": "0", "id": 10000000002230, "visualization.name": "Top 10 Interfaces by Average Discard Packets", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "TopN", "visualization.type": "Grid", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "interface~discard.packets", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "interface~discard.packets", "aggregator": "sparkline", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "medium"}, "columns": [{"name": "monitor", "show": "no", "sortable": "yes", "disable": "yes", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "interface", "title": "Interface", "show": "yes", "sortable": "yes", "disable": "yes", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}, "type": "interface"}, {"name": "interface~discard.packets.avg", "title": "Value", "show": "yes", "sortable": "yes", "disable": "yes", "resizable": "yes", "selectable": "yes", "orderable": "yes", "type": "number", "style": {}}, {"name": "interface~discard.packets.sparkline", "title": " ", "show": "yes", "sortable": "yes", "disable": "yes", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {"inline.chart": {"type": "sparkline"}}}], "sorting": {"limit": 10, "order": "desc", "column": "interface~discard.packets.avg"}}, "sparkline": "yes"}}, {"_type": "0", "id": 10000000002231, "visualization.name": "VLAN Details", "visualization.category": "Grid", "visualization.type": "VLAN", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "vlan~name", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "vlan~port", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "vlan~ports", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "vlan~status", "aggregator": "last", "entity.type": "monitor", "entities": []}]}, {"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "interface~index", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "interface~in.traffic.bits.per.sec", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "interface~out.traffic.bits.per.sec", "aggregator": "last", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "no", "column.selection": "no", "header": "yes", "view": "vlan", "style": {"header.font.size": "medium", "css.classes": ["stripped-grid"], "row.height.px": 50}, "columns": [{"name": "monitor.last", "show": "no", "sortable": "no", "disable": "yes", "resizable": "no", "selectable": "no", "orderable": "no", "position": 1}, {"name": "vlan", "title": "VLAN", "show": "yes", "sortable": "yes", "disable": "yes", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 2, "style": {"classes": ["font-bold"], "width.percent": 5}}, {"name": "vlan~name.last", "title": "VLAN Name", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 3, "style": {"icon": {"name": "topology", "classes": ["text-neutral-light"]}, "classes": ["font-bold"]}}, {"name": "vlan~status.last", "title": "Status", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 4, "type": "status", "style": {"icon": {"placement": "prefix", "conditions": [{"value": "Active", "operator": "=", "icon": "check-circle"}, {"value": "Inactive", "operator": "=", "icon": "times-circle"}]}, "classes": ["font-bold"]}}, {"name": "vlan~ports.last", "title": "Port Count", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 5, "style": {}}, {"name": "vlan~port.last", "title": "Port", "show": "yes", "sortable": "yes", "disable": "yes", "resizable": "yes", "selectable": "no", "orderable": "yes", "position": 5, "type": "port", "style": {"classes": ["font-bold"]}}, {"name": "traffic", "title": "Traffic", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 7, "computed": "yes", "formula": {"operation": "combine", "columns": ["interface~in.traffic.bits.per.sec.last", "interface~out.traffic.bits.per.sec.last"]}, "style": {"icon": {"name": "traffic", "classes": ["text-neutral-light"]}, "classes": ["font-bold"]}}, {"name": "interface~in.traffic.bits.per.sec.last", "title": "Traffic", "show": "no", "sortable": "yes", "disable": "yes", "resizable": "yes", "selectable": "no", "orderable": "yes", "position": 8, "style": {"icon": {"name": "long-arrow-right", "placement": "prefix", "classes": ["text-neutral-light"]}}}, {"name": "interface~out.traffic.bits.per.sec.last", "title": "Traffic", "show": "no", "sortable": "yes", "disable": "yes", "resizable": "yes", "selectable": "no", "orderable": "yes", "position": 9, "style": {"icon": {"name": "long-arrow-left", "classes": ["text-neutral-light"]}}}, {"name": "monitor", "show": "no", "sortable": "no", "disable": "yes", "resizable": "no", "selectable": "no", "orderable": "no", "position": 10}]}}}, {"_type": "0", "id": 10000000002225, "visualization.name": "Hardware Sensor Details", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "Hardware Sensor", "publish.sub.query.progress": false, "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "temperature.sensor~status", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "temperature.sensor~reading.celsius", "aggregator": "last", "entity.type": "monitor", "entities": []}]}, {"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "fan.sensor~status", "aggregator": "last", "entity.type": "monitor", "entities": []}]}, {"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "power.supply.sensor~status", "aggregator": "last", "entity.type": "monitor", "entities": []}]}, {"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "voltage.sensor~status", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "voltage.sensor~reading.mill.volts", "aggregator": "last", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "medium"}, "columns": []}}}, {"_type": "0", "id": 10000000002237, "visualization.name": "Stack Data Ring", "visualization.category": "Grid", "visualization.type": "Stacked Switch View", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "cisco.stack.switch~role", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "cisco.stack.switch~port.status", "aggregator": "last", "entity.type": "monitor", "entities": []}]}, {"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "cisco.stack.bandwidth", "aggregator": "last", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "small"}, "columns": [{"name": "cisco.stack.switch", "title": "<PERSON>ack Switch", "show": "yes", "sortable": "yes", "disable": "yes", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 1}, {"name": "cisco.stack.switch~role", "title": "Role", "show": "yes", "sortable": "yes", "disable": "yes", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 2}, {"name": "cisco.stack.switch~port.status", "title": "Port Status", "show": "yes", "sortable": "yes", "disable": "yes", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 3}, {"name": "cisco.stack.bandwidth", "title": "Bandwidth", "show": "yes", "sortable": "yes", "disable": "yes", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 4}]}}}, {"_type": "0", "id": 10000000002238, "visualization.name": "Stack Switch Details", "visualization.description": "Cisco Stack Switch Details", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "Grid", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "cisco.stack.switch~software.priority", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "cisco.stack.switch~hardware.priority", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "cisco.stack.switch~role", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "cisco.stack.switch~mac.address", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "cisco.stack.switch~serial.no", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "cisco.stack.switch~model.no", "aggregator": "last", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "medium"}, "columns": [{"name": "monitor", "show": "no", "sortable": "yes", "disable": "yes", "resizable": "yes", "selectable": "no", "orderable": "yes", "position": 1, "style": {}}, {"name": "cisco.stack.switch.last", "title": "Switch Name", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 2, "style": {}}, {"name": "priority", "title": "Priority", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 3, "computed": "yes", "formula": {"operation": "combine", "columns": ["cisco.stack.switch~software.priority.last", "cisco.stack.switch~hardware.priority.last"]}, "style": {"icon": {"name": "traffic", "classes": ["text-neutral-light"]}, "classes": ["font-bold"]}}, {"name": "cisco.stack.switch~software.priority.last", "title": "Priority", "show": "no", "sortable": "yes", "disable": "yes", "resizable": "yes", "selectable": "no", "orderable": "yes", "position": 4, "style": {}}, {"name": "cisco.stack.switch~hardware.priority.last", "title": "Priority", "show": "no", "sortable": "yes", "disable": "yes", "resizable": "yes", "selectable": "no", "orderable": "yes", "position": 5, "style": {}}, {"name": "cisco.stack.switch~role.last", "title": "Role", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 6, "style": {"classes": ["font-bold"], "icon": {"name": "locks", "placement": "prefix", "classes": ["text-neutral-light"]}}}, {"name": "cisco.stack.switch~mac.address.last", "title": "MAC", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 7, "type": "macaddress", "style": {"classes": ["font-bold"], "icon": {"name": "ip", "placement": "prefix", "classes": ["text-neutral-light"]}}}, {"name": "cisco.stack.switch~serial.no.last", "title": "Serial No", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 8, "style": {"classes": ["font-bold"], "icon": {"name": "ip", "placement": "prefix", "classes": ["text-neutral-light"]}}}, {"name": "cisco.stack.switch~model.no.last", "title": "Model No", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 9, "style": {"icon": {"name": "port", "classes": ["text-secondary-orange"]}, "classes": ["font-bold"]}}]}}}, {"_type": "0", "id": 10000000002232, "visualization.name": "Interface Switch PortView", "visualization.category": "Grid", "visualization.type": "Port View", "join.type": "custom", "join.result": "port.view", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.data.sources": [{"type": "metric", "join.type": "custom", "filters": {"data.filter": {"operator": "and", "filter": "include", "groups": [{"filter": "include", "operator": "and", "conditions": [{"operand": "interface~type^last", "operator": "in", "value": ["ethernetCsmacd (6)", "gigabitEthernet (117)"]}]}]}, "result.filter": {}}, "data.points": [{"data.point": "interface~index", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "interface~name", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "interface~status", "aggregator": "last", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "small"}, "columns": []}}}, {"_type": "0", "id": 10000000002233, "visualization.name": "Interface Details", "visualization.category": "Grid", "visualization.type": "Interface", "publish.sub.query.progress": false, "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "interface~index", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "interface~name", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "interface~ip.address", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "interface~alias", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "interface~type", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "interface~status", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "interface~discard.packets", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "interface~error.packets", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "interface~traffic.utilization.percent", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "interface~out.traffic.utilization.percent", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "interface~in.traffic.utilization.percent", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "interface~speed.bits.per.sec", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "interface~description", "aggregator": "last", "entity.type": "monitor", "entities": []}]}, {"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "vlan~name", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "vlan~port", "aggregator": "last", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "yes", "header": "yes", "style": {"header.font.size": "medium", "css.classes": ["stripped-grid"], "row.height.px": 50}, "columns": [{"name": "monitor", "show": "no", "sortable": "yes", "disable": "yes", "resizable": "yes", "selectable": "no", "orderable": "yes", "position": 1}, {"name": "interface", "title": "Interface", "show": "yes", "sortable": "yes", "disable": "yes", "resizable": "yes", "selectable": "no", "orderable": "yes", "position": 2, "type": "interface", "style": {}}, {"name": "interface~alias.last", "title": "<PERSON><PERSON>", "show": "yes", "sortable": "yes", "disable": "yes", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 4, "style": {"classes": ["font-bold"]}}, {"name": "interface~ip.address.last", "title": "Interface IP", "show": "no", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {"classes": ["font-bold"]}}, {"name": "interface~status.last", "title": "Status", "show": "yes", "sortable": "yes", "disable": "yes", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 5, "type": "status", "formula": {"conditions": [{"result": "Up", "operator": "=", "value": "up"}, {"result": "Down", "operator": "=", "value": "down"}]}}, {"name": "interface~traffic.utilization.percent.last", "title": "Traffic Utilization", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 6, "style": {"classes": ["font-bold"], "inline.chart": {"type": "gauge"}}}, {"name": "interface~in.traffic.utilization.percent.last", "title": "IN (%)", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 7, "style": {"classes": ["font-bold"], "inline.chart": {"type": "gauge"}}}, {"name": "interface~out.traffic.utilization.percent.last", "title": "OUT (%)", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 8, "style": {"classes": ["font-bold"], "inline.chart": {"type": "gauge"}}}, {"name": "interface~type.last", "title": "Port Type", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 9, "style": {"classes": ["font-bold"], "icon": {"name": "port", "classes": ["text-secondary-orange"]}}}, {"name": "interface~error.packets.last", "title": "Error", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "type": "number", "position": 10, "style": {"classes": ["font-bold"], "icon": {"name": "times-circle", "classes": ["text-secondary-red"]}}}, {"name": "interface~discard.packets.last", "title": "Discarded", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "type": "number", "position": 11, "style": {"classes": ["font-bold"], "icon": {"name": "trash", "classes": ["text-secondary-orange"]}}}, {"name": "interface~index.last", "title": "Interface Index", "show": "no", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 12, "style": {}}, {"name": "interface~name.last", "title": "Interface Name", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 3}, {"name": "vlan", "title": "VLAN", "show": "no", "sortable": "yes", "disable": "yes", "resizable": "yes", "selectable": "no", "orderable": "yes", "position": 13, "type": "interface", "style": {}}, {"name": "vlan~name.last", "title": "Assigned VLAN", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 14, "style": {"classes": ["font-bold"], "icon": {"name": "topology", "classes": []}}}, {"name": "vlan~port.last", "title": "VLAN Port", "show": "no", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "no", "orderable": "no", "position": 15, "style": {}}, {"name": "interface~speed.bits.per.sec.last", "title": "Speed", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 16, "style": {}}, {"name": "interface~description.last", "title": "Description", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 17, "style": {}}]}}}, {"_type": "0", "id": **************, "visualization.name": "Routing", "visualization.description": "IP Routing Table (Route Next Hop)", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "Grid", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "ip.route~subnet.mask", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "ip.route~next.hop", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "ip.route~interface.index", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "ip.route~type", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "ip.route~protocol", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "ip.route~flaps.count", "aggregator": "last", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "medium", "css.classes": ["stripped-grid"], "row.height.px": 45}, "columns": [{"name": "monitor", "show": "no", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "no", "orderable": "yes", "position": 1}, {"name": "ip.route", "title": "Route", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "type": "ipaddress", "position": 2}, {"name": "ip.route~subnet.mask.last", "title": "Subnet Mask", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 3, "style": {"icon": {"name": "lock-alt", "placement": "prefix", "classes": ["text-neutral-light"]}}}, {"name": "ip.route~next.hop.last", "title": "Next Hop", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 4, "style": {"icon": {"name": "next-hop", "placement": "prefix", "classes": ["text-secondary-yellow"]}}}, {"name": "ip.route~interface.index.last", "title": "Interface", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 5, "style": {"icon": {"name": "desktop", "placement": "prefix", "classes": ["text-neutral-light"]}}}, {"name": "ip.route~flaps.count.last", "title": "Flaps", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 6, "style": {"icon": {"name": "flaps", "placement": "prefix", "classes": ["text-secondary-yellow"]}}}, {"name": "ip.route~protocol.last", "title": "Protocol", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 7, "style": {"icon": {"name": "file-alt", "placement": "prefix", "classes": ["text-primary"]}}}, {"name": "ip.route~type.last", "title": "Type", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 8, "style": {"icon": {"name": "port", "placement": "prefix", "classes": ["text-secondary-orange"]}}}]}}}, {"_type": "0", "id": 10000000002235, "visualization.name": "STP Port Details", "visualization.description": "STP Port Details", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "Grid", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "stp~protocol", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "stp~bridge.priority", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "stp~topology.change", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "stp~listening.ports", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "stp~blocking.ports", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "stp~disabled.ports", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "stp~port", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "stp~forwarding.ports", "aggregator": "last", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "yes", "header": "yes", "style": {"header.font.size": "medium", "css.classes": ["stripped-grid"], "row.height.px": 45}, "columns": [{"name": "monitor", "show": "no", "sortable": "yes", "disable": "yes", "resizable": "yes", "selectable": "no", "orderable": "yes", "position": 1, "style": {}}, {"name": "stp", "title": "STP", "show": "yes", "sortable": "yes", "disable": "yes", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 2, "style": {}}, {"name": "stp~protocol.last", "title": "Protocol", "show": "yes", "sortable": "yes", "disable": "yes", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 3, "style": {}}, {"name": "stp~bridge.priority.last", "title": "Bridge Priority", "show": "yes", "sortable": "yes", "disable": "yes", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 4, "style": {}}, {"name": "stp~topology.change.last", "title": "Topology Change", "show": "yes", "sortable": "yes", "disable": "yes", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 5, "style": {}}, {"name": "stp~listening.ports.last", "title": "Listening Ports", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 7, "style": {}}, {"name": "stp~blocking.ports.last", "title": "Blocking Ports", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 9, "style": {}}, {"name": "stp~disabled.ports.last", "title": "Disabled Ports", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 10, "style": {}}, {"name": "stp~forwarding.ports.last", "title": "Forwarding Ports", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 12, "style": {}}, {"name": "stp~port.last", "title": "Port", "show": "no", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 13, "type": "port"}]}}}, {"_type": "0", "id": 10000000002236, "visualization.name": "Switch Port Mapper", "visualization.category": "Grid", "visualization.type": "Switch Port Mapper", "publish.sub.query.progress": false, "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "interface~index", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "interface~name", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "interface~alias", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "interface~description", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "interface~ip.address", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "interface~type", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "interface~address", "aggregator": "last", "entity.type": "monitor", "entities": []}]}, {"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "client.mac.address~client.port", "aggregator": "last", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "yes", "header": "yes", "style": {"header.font.size": "medium", "css.classes": ["stripped-grid"], "row.height.px": 50}, "columns": [{"name": "monitor", "show": "no", "sortable": "yes", "disable": "yes", "resizable": "yes", "selectable": "no", "orderable": "yes", "position": 1}, {"name": "interface", "title": "Interface", "show": "yes", "sortable": "yes", "disable": "yes", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 2, "type": "interface"}, {"name": "interface~alias.last", "title": "<PERSON><PERSON>", "show": "yes", "sortable": "yes", "disable": "yes", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 4}, {"name": "interface~description.last", "title": "Description", "show": "yes", "sortable": "yes", "disable": "yes", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 5}, {"name": "interface~ip.address.last", "title": "IP", "show": "yes", "sortable": "yes", "disable": "yes", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 6, "type": "ipaddress"}, {"name": "interface~address.last", "title": "MAC Address", "show": "yes", "sortable": "yes", "disable": "yes", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 7}, {"name": "interface~type.last", "title": "Type", "show": "yes", "sortable": "yes", "disable": "yes", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 8, "style": {"icon": {"placement": "prefix", "name": "port", "classes": ["text-secondary-orange"]}}}, {"name": "interface~index.last", "title": "Interface Index", "show": "no", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 9}, {"name": "client.mac.address", "title": "Connected MAC Address", "show": "yes", "sortable": "yes", "disable": "yes", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 10, "type": "macaddress"}, {"name": "client.mac.address~client.port.last", "title": "Connected Port", "show": "yes", "sortable": "yes", "disable": "yes", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 11, "style": {"icon": {"placement": "prefix", "name": "port", "classes": ["text-secondary-orange"]}}}]}}}, {"_type": "0", "id": 10000000002809, "visualization.name": "WAN Link", "visualization.description": "WAN Link Inventory Summary", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Grid", "visualization.type": "Grid", "visualization.data.sources": [{"type": "metric", "join.type": "custom", "filters": {"data.filter": {}, "result.filter": {}}, "visualization.result.by": ["monitor", "ipsla"], "data.points": [{"data.point": "ipsla~operation.type", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "ipsla~status", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "ipsla~latency.ms", "aggregator": "last", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "medium"}, "columns": [{"name": "monitor", "title": "Monitor", "show": "no", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 1, "style": {}}, {"name": "ipsla", "title": "WAN Link Name", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 2, "style": {}}, {"name": "ipsla~operation.type.last", "title": "WAN Probe Type", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 3, "style": {}}, {"name": "source.ip.address", "title": "Source IP", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 4, "style": {}}, {"name": "destination.ip.address", "title": "Destination IP", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 5, "style": {}}, {"name": "source.interface.name", "title": "Source Interface", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 6, "style": {}}, {"name": "ipsla~status.last", "title": "Status", "show": "yes", "sortable": "yes", "disable": "yes", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 7, "type": "status", "formula": {"conditions": [{"result": "Up", "operator": "=", "value": "up"}, {"result": "Down", "operator": "=", "value": "down"}]}}, {"name": "ipsla~latency.ms.last", "title": "RTT", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 8, "style": {}}]}}, "visualization.tags": ["source.ip.address", "destination.ip.address", "source.interface.name"], "join.type": "custom", "join.result": "tag", "visualization.result.by": ["monitor", "ipsla"]}]