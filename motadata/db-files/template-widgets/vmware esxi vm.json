[{"_type": "0", "id": 10000000000867, "visualization.name": "VM CPU Utilization", "visualization.description": "CPU Utilization VM", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "esxi.vm~cpu.percent", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "esxi.vm~cpu.percent"}]}], "visualization.properties": {"gauge": {"header": {"title": "CPU", "style": {"font.size": "medium"}}, "footer": {"style": {"font.size": "large"}, "data.points": [{"value": "esxi.vm~cpu.percent.last", "type": "gauge"}]}, "style": {"icon": {"name": "cpu"}, "color.data.point": "esxi.vm~cpu.percent"}}}}, {"_type": "0", "id": 10000000000868, "visualization.name": "VM Memory Utilization", "visualization.description": "Memory Utilization VM", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "esxi.vm~memory.used.percent", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "esxi.vm~memory.used.percent"}]}], "visualization.properties": {"gauge": {"header": {"title": "Memory", "style": {"font.size": "medium"}}, "footer": {"style": {"font.size": "large"}, "data.points": [{"value": "esxi.vm~memory.used.percent.last", "type": "gauge"}]}, "style": {"icon": {"name": "memory"}, "color.data.point": "esxi.vm~memory.used.percent"}}}}, {"_type": "0", "id": 10000000000869, "visualization.name": "VM Disk Utilization", "visualization.description": "Disk Utilization VM", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "esxi.vm~disk.used.percent", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "esxi.vm~disk.used.percent"}]}], "visualization.properties": {"gauge": {"header": {"title": "Disk", "style": {"font.size": "medium"}}, "footer": {"style": {"font.size": "large"}, "data.points": [{"value": "esxi.vm~disk.used.percent.last", "type": "gauge"}]}, "style": {"icon": {"name": "disk"}, "color.data.point": "esxi.vm~disk.used.percent"}}}}, {"_type": "0", "id": 10000000000870, "visualization.name": "VM Swap Memory In/Out", "visualization.description": "Swap Memory In/Out VM", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "esxi.vm~swap.in.memory.bytes.per.sec", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "esxi.vm~swap.out.memory.bytes.per.sec", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "esxi.vm~swap.in.memory.bytes.per.sec"}, {"type": "metric", "data.point": "esxi.vm~swap.out.memory.bytes.per.sec"}]}], "visualization.properties": {"gauge": {"header": {"title": "<PERSON><PERSON><PERSON>", "style": {"font.size": "medium"}}, "footer": {"style": {"font.size": "large"}, "data.points": [{"label": "In", "value": "esxi.vm~swap.in.memory.bytes.per.sec.last"}, {"label": "Out", "value": "esxi.vm~swap.out.memory.bytes.per.sec.last"}]}, "style": {"icon": {"name": "swap"}, "color.data.point": "esxi.vm~swap.in.memory.bytes.per.sec"}}}}, {"_type": "0", "id": 10000000000871, "visualization.name": "VM Disk IO Average Latency", "visualization.description": "Disk IO Average Latency VM", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "esxi.vm~disk.io.read.ops.per.sec", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "esxi.vm~disk.io.write.ops.per.sec", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "esxi.vm~disk.io.read.ops.per.sec"}, {"type": "metric", "data.point": "esxi.vm~disk.io.write.ops.per.sec"}]}], "visualization.properties": {"gauge": {"header": {"title": "IOPS", "style": {"font.size": "medium"}}, "footer": {"style": {"font.size": "large"}, "data.points": [{"label": "Read", "value": "esxi.vm~disk.io.read.ops.per.sec.last"}, {"label": "Write", "value": "esxi.vm~disk.io.write.ops.per.sec.last"}]}, "style": {"icon": {"name": "disk"}, "color.data.point": "esxi.vm~disk.io.write.ops.per.sec"}}}}, {"_type": "0", "id": 10000000000872, "visualization.name": "VM Virtual Processor", "visualization.description": "Virtual Processor VM", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "esxi.vm~virtual.processors", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "esxi.vm~virtual.processors"}]}], "visualization.properties": {"gauge": {"header": {"title": "Processors", "style": {"font.size": "medium"}}, "footer": {"style": {"font.size": "large"}, "data.points": [{"label": "", "value": "esxi.vm~virtual.processors.last"}]}, "style": {"icon": {"name": "cube"}, "color.data.point": "esxi.vm~virtual.processors"}}}}, {"_type": "0", "id": 10000000000873, "visualization.name": "Today's Availability", "visualization.category": "Custom", "visualization.type": "Pie", "visualization.timeline": {"relative.timeline": "today"}, "visualization.result.by": ["monitor"], "visualization.data.sources": [{"type": "availability", "join.type": "all", "join.result": "availability", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "esxi.vm~uptime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "esxi.vm~downtime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "esxi.vm~unreachabletime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "esxi.vm~maintenancetime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "esxi.vm~disabletime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "esxi.vm~suspendtime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "esxi.vm~unknowntime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}]}, {"type": "availability", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "esxi.vm~uptime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "esxi.vm~downtime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "esxi.vm~unreachabletime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "esxi.vm~maintenancetime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "esxi.vm~disabletime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "esxi.vm~suspendtime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "esxi.vm~unknowntime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "no", "chart.label": "no", "axis.titles": {}, "highchart.settings": {"plotOptions": {"pie": {"innerSize": "70%"}}}}}, "join.type": "all", "join.columns": ["monitor"], "entity.type": "Monitor", "container.type": "Template", "entities": []}, {"_type": "0", "id": 10000000000874, "visualization.name": "Availability Statistics", "visualization.category": "Grid", "visualization.type": "Availability Time Series", "visualization.timeline": {"relative.timeline": "today"}, "visualization.data.sources": [{"type": "availability", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "esxi.vm~uptime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "esxi.vm~downtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "esxi.vm~unreachabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "esxi.vm~maintenancetime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "esxi.vm~suspendtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "esxi.vm~disabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "esxi.vm~unknowntime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}], "visualization.timeline": {"relative.timeline": "-1d"}}, {"type": "availability", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "esxi.vm~uptime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "esxi.vm~downtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "esxi.vm~unreachabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "esxi.vm~maintenancetime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "esxi.vm~suspendtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "esxi.vm~disabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "esxi.vm~unknowntime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}], "visualization.timeline": {"relative.timeline": "-7d"}}, {"type": "availability", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "esxi.vm~uptime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "esxi.vm~downtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "esxi.vm~unreachabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "esxi.vm~maintenancetime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "esxi.vm~suspendtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "esxi.vm~disabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "esxi.vm~unknowntime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}], "visualization.timeline": {"relative.timeline": "-15d"}}], "visualization.properties": {"chart": {"rotation.angle": 0, "chart.legend": "yes", "chart.label": "no", "axis.titles": {}, "highchart.settings": {}, "sorting": {"limit": 10, "order": "desc", "column": "esxi.vm~uptime.percent.avg"}}}}, {"_type": "0", "id": 10000000000875, "visualization.name": "CPU Utilization", "visualization.description": "CPU Utilization (TimeSeries) VM", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "esxi.vm~cpu.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"rotation.angle": 0, "chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"_type": "0", "id": 10000000000876, "visualization.name": "Memory Utilization", "visualization.description": "Memory Utilization (TimeSeries) VM", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "esxi.vm~memory.used.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"rotation.angle": 0, "chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"_type": "0", "id": 10000000000877, "visualization.name": "Disk Utilization", "visualization.description": "Disk Utilization (TimeSeries) VM", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "esxi.vm~disk.used.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"rotation.angle": 0, "chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"_type": "0", "id": 10000000000878, "visualization.name": "Swap Memory", "visualization.description": "Swap Memory (TimeSeries) VM", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "esxi.vm~swap.out.memory.bytes", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "esxi.vm~swap.in.memory.bytes", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"rotation.angle": 0, "chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"_type": "0", "id": 10000000000879, "visualization.name": "Disk IO Pending Read/Write", "visualization.description": "Disk IO Pending Read/Write VM", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "esxi.vm~disk.io.pending.reads", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "esxi.vm~disk.io.pending.writes", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"rotation.angle": 0, "chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"_type": "0", "id": 10000000000880, "visualization.name": "Disk IOPS", "visualization.description": "Disk IOPS (TimeSeries) VM", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "esxi.vm~disk.io.read.bytes.per.sec", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "esxi.vm~disk.io.write.bytes.per.sec", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"rotation.angle": 0, "chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"_type": "0", "id": 10000000000881, "visualization.name": "Disk Details", "visualization.description": "VM Disk Details", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "Grid", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "esxi.vm~disk.capacity.bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "esxi.vm~disk.used.bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "esxi.vm~disk.free.bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"view": "Storage Details", "searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "medium"}, "columns": [{"name": "monitor", "show": "no", "sortable": "yes", "disable": "yes", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "esxi.vm", "alias": "system.disk.volume", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "esxi.vm~disk.capacity.bytes.last", "alias": "system.disk.volume.capacity.bytes.last", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "esxi.vm~disk.used.bytes.last", "alias": "system.disk.volume.used.bytes.last", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "esxi.vm~disk.free.bytes.last", "alias": "system.disk.volume.free.bytes.last", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}]}}}]