[{"id": 10000000000349, "_type": "0", "visualization.name": "Queries", "visualization.description": "Bind9 Queries", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "bind9.nsstat.rejected.auth.queries", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "bind9.nsstat.successful.queries", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "bind9.nsstat.failed.queries", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "bind9.nsstat.answered.auth.queries"}, {"type": "metric", "data.point": "bind9.nsstat.empty.queries"}, {"type": "metric", "data.point": "bind9.nsstat.duplicate.queries"}, {"type": "metric", "data.point": "bind9.nsstat.dropped.queries"}, {"type": "metric", "data.point": "bind9.nsstat.domain.nx.queries"}, {"type": "metric", "data.point": "bind9.nsstat.failed.queries"}, {"type": "metric", "data.point": "bind9.nsstat.rejected.auth.queries"}, {"type": "metric", "data.point": "bind9.nsstat.successful.queries"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "medium", "color.data.point": "bind9.nsstat.successful.queries", "icon": {"name": "query", "placement": "prefix"}}, "header": {"title": "Queries", "style": {"font.size": "medium"}, "data.points": [{"label": "Rejected", "value": "bind9.nsstat.rejected.auth.queries.last"}]}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "Succesful", "value": "bind9.nsstat.successful.queries.last"}, {"label": "Failed", "value": "bind9.nsstat.failed.queries.last"}]}}}}, {"id": 10000000000350, "_type": "0", "visualization.name": "Recursive Queries", "visualization.description": "Bind9 Recursive Queries", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "bind9.nsstat.recursive.queries", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "bind9.nsstat.rejected.auth.queries", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "bind9.nsstat.recursive.clients"}, {"type": "metric", "data.point": "bind9.nsstat.recursive.queries"}, {"type": "metric", "data.point": "bind9.nsstat.rejected.auth.queries"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "medium", "color.data.point": "bind9.nsstat.rejected.auth.queries", "icon": {"name": "query", "placement": "prefix"}}, "header": {"title": "Recursive Queries", "style": {"font.size": "medium"}, "data.points": []}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "Recursive", "value": "bind9.nsstat.recursive.queries.last"}, {"label": "Rejected", "value": "bind9.nsstat.rejected.auth.queries.last"}]}}}}, {"id": 10000000000351, "_type": "0", "visualization.name": "Zone Transfer", "visualization.description": "Bind9 Zone Transfer", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "bind9.nsstat.rejected.zone.transfers", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "bind9.zonestat.successful.transfers", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "bind9.zonestat.failed.transfers", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "bind9.nsstat.completed.zone.transfers"}, {"type": "metric", "data.point": "bind9.zonestat.successful.transfers"}, {"type": "metric", "data.point": "bind9.zonestat.failed.transfers"}, {"type": "metric", "data.point": "bind9.zonestat.ipv4.axfr.requests"}, {"type": "metric", "data.point": "bind9.zonestat.ipv6.axfr.requests"}, {"type": "metric", "data.point": "bind9.nsstat.rejected.zone.transfers"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "medium", "color.data.point": "bind9.zonestat.successful.transfers", "icon": {"name": "location-pin", "placement": "prefix"}}, "header": {"title": "Zone Transfer", "style": {"font.size": "medium"}, "data.points": [{"label": "Rejected", "value": "bind9.nsstat.rejected.zone.transfers.last"}]}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "Success", "value": "bind9.zonestat.successful.transfers.last"}, {"label": "Failures", "value": "bind9.zonestat.failed.transfers.last"}]}}}}, {"id": 10000000000352, "_type": "0", "visualization.name": "Opcodes", "visualization.description": "Bind9 Opcodes", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "bind9.opcode.update.requests", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "bind9.opcode.incoming.requests.per.sec", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "bind9.opcode.outgoing.requests.per.sec", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "bind9.opcode.status.requests"}, {"type": "metric", "data.point": "bind9.opcode.notify.requests"}, {"type": "metric", "data.point": "bind9.opcode.update.requests"}, {"type": "metric", "data.point": "bind9.opcode.incoming.requests.per.sec"}, {"type": "metric", "data.point": "bind9.opcode.outgoing.requests.per.sec"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "medium", "color.data.point": "bind9.opcode.update.requests", "icon": {"name": "code", "placement": "prefix"}}, "header": {"title": "Opcodes", "style": {"font.size": "medium"}, "data.points": [{"label": "Update", "value": "bind9.opcode.update.requests.last"}]}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "Incoming", "value": "bind9.opcode.incoming.requests.per.sec.last"}, {"label": "Outgoing", "value": "bind9.opcode.outgoing.requests.per.sec.last"}]}}}}, {"id": 10000000000353, "_type": "0", "visualization.name": "Socket Errors", "visualization.description": "Bind9 Socket Errors", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "bind9.sockstat.failed.sockets", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "bind9.sockstat.sent.errors", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "bind9.sockstat.received.errors", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "bind9.sockstat.udp4.send.errors"}, {"type": "metric", "data.point": "bind9.sockstat.udp6.send.errors"}, {"type": "metric", "data.point": "bind9.sockstat.tcp4.send.errors"}, {"type": "metric", "data.point": "bind9.sockstat.tcp6.send.errors"}, {"type": "metric", "data.point": "bind9.sockstat.unix.send.errors"}, {"type": "metric", "data.point": "bind9.sockstat.failed.sockets"}, {"type": "metric", "data.point": "bind9.sockstat.sent.errors"}, {"type": "metric", "data.point": "bind9.sockstat.received.errors"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "medium", "data.point": "bind9.sockstat.failed.sockets", "icon": {"name": "error-connections", "placement": "prefix"}}, "header": {"title": "Socket Errors", "style": {"font.size": "medium"}, "data.points": [{"label": "Failed", "value": "bind9.sockstat.failed.sockets.last"}]}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "<PERSON><PERSON>", "value": "bind9.sockstat.sent.errors.last"}, {"label": "Received", "value": "bind9.sockstat.received.errors.last"}]}}}}, {"id": 10000000002634, "_type": "0", "visualization.name": "Requests", "visualization.description": "Bind9 Requests", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "bind9.opcode.status.requests", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "bind9.opcode.notify.requests", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "bind9.opcode.update.requests", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "bind9.opcode.status.requests"}, {"type": "metric", "data.point": "bind9.opcode.notify.requests"}, {"type": "metric", "data.point": "bind9.opcode.update.requests"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "medium", "data.point": "bind9.opcode.status.requests", "icon": {"name": "requests", "placement": "prefix"}}, "header": {"title": "Requests", "style": {"font.size": "medium"}, "data.points": [{"label": "Status Count", "value": "bind9.opcode.status.requests.last"}]}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "Notify", "value": "bind9.opcode.notify.requests.last"}, {"label": "Update", "value": "bind9.opcode.update.requests.last"}]}}}}, {"_type": "0", "id": 10000000000354, "visualization.name": "Today's Availability", "visualization.category": "Custom", "visualization.type": "Application Availability", "visualization.timeline": {"relative.timeline": "today"}, "visualization.result.by": ["monitor"], "visualization.data.sources": [{"visualization.result.by": ["monitor"], "type": "availability", "join.type": "all", "join.result": "availability", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "system.process~uptime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "system.process~downtime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "system.process~suspendtime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "system.process~unknowntime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "system.process~maintenancetime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "system.process~disabletime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "system.process~unreachabletime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}]}, {"visualization.result.by": ["monitor"], "type": "availability", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "system.process~uptime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "system.process~downtime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "system.process~suspendtime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "system.process~unknowntime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "system.process~maintenancetime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "system.process~disabletime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "system.process~unreachabletime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "no", "chart.label": "no", "axis.titles": {}, "highchart.settings": {"plotOptions": {"pie": {"innerSize": "70%"}}}}}, "join.type": "all", "join.columns": ["monitor"], "entity.type": "Monitor", "container.type": "Template", "entities": []}, {"_type": "0", "id": 10000000000355, "visualization.name": "Availability Statistics", "visualization.category": "Grid", "visualization.type": "Application Availability Time Series", "visualization.timeline": {"relative.timeline": "today"}, "visualization.data.sources": [{"type": "availability", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "system.process~uptime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~downtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~unreachabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~maintenancetime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~suspendtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~disabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~unknowntime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}], "visualization.timeline": {"relative.timeline": "-1d"}}, {"type": "availability", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "system.process~uptime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~downtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~unreachabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~maintenancetime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~suspendtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~disabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~unknowntime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}], "visualization.timeline": {"relative.timeline": "-7d"}}, {"type": "availability", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "system.process~uptime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~downtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~unreachabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~maintenancetime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~suspendtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~disabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~unknowntime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}], "visualization.timeline": {"relative.timeline": "-15d"}}], "visualization.properties": {"chart": {"rotation.angle": 0, "chart.legend": "yes", "chart.label": "no", "axis.titles": {}, "highchart.settings": {}, "sorting": {"limit": 10, "order": "desc", "column": "system.process~uptime.percent.avg"}}}}, {"id": 10000000000356, "_type": "0", "visualization.name": "Zone Transfer Notification", "visualization.description": "Bind9 Zone Transfer Notification", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "bind9.zonestat.ipv4.sent.notifications", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "bind9.zonestat.ipv4.received.notifications", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "bind9.zonestat.ipv6.sent.notifications", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "bind9.zonestat.ipv6.received.notifications", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"id": 10000000000357, "_type": "0", "visualization.name": "Query Details", "visualization.description": "Bind9 Query Details", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "bind9.nsstat.successful.queries", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "bind9.nsstat.failed.queries", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "bind9.nsstat.rejected.auth.queries", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"id": 10000000000358, "_type": "0", "visualization.name": "Request By IP Version", "visualization.description": "Bind9 Request By IP Version", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "bind9.nsstat.ipv4.requests", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "bind9.nsstat.ipv6.requests", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"id": 10000000000359, "_type": "0", "visualization.name": "Zone Transfers", "visualization.description": "Bind9 Zone Transfers", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "bind9.zonestat.successful.transfers", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "bind9.zonestat.failed.transfers", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"id": 10000000000360, "_type": "0", "visualization.name": "Recursive Queries", "visualization.description": "Bind9 Recursive Queries", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "bind9.nsstat.recursive.queries", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "bind9.nsstat.rejected.recursive.queries", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"id": 10000000000361, "_type": "0", "visualization.name": "Requests By Opcode", "visualization.description": "Bind9 Requests By Opcode", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "bind9.opcode.outgoing.requests.per.sec", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "bind9.opcode.incoming.requests.per.sec", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "bind9.opcode.update.requests", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"id": 10000000000362, "_type": "0", "visualization.name": "AXFR Requests By IP Versions", "visualization.description": "Bind9 AXFR Requests By IP Versions", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "bind9.zonestat.ipv4.axfr.requests", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "bind9.zonestat.ipv6.axfr.requests", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"id": 10000000000363, "_type": "0", "visualization.name": "Queries By IP Protocol (TCP, UDP)", "visualization.description": "Bind9 Queries By IP Protocol (TCP, UDP)", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "bind9.nsstat.tcp.queries.per.sec", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "bind9.nsstat.udp.queries.per.sec", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"id": 10000000000364, "_type": "0", "visualization.name": "Socket Errors", "visualization.description": "Bind9 Socket Errors", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "bind9.sockstat.sent.errors", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "bind9.sockstat.received.errors", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"id": 10000000000365, "_type": "0", "visualization.name": "IXFR Requests By IP Versions", "visualization.description": "Bind9 IXFR Requests By IP Versions", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "bind9.zonestat.ipv4.ixfr.requests", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "bind9.zonestat.ipv6.ixfr.requests", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}]