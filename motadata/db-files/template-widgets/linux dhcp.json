[{"_type": "0", "id": 10000000000463, "visualization.name": "DHCP Scopes", "visualization.description": "LinuxDHCP DHCP Scopes", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "dhcp.scopes", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "dhcp.scopes"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "color.data.point": "dhcp.scopes", "icon": {"name": "file-check", "placement": "prefix"}}, "header": {"title": "DHCP Scopes", "style": {"font.size": "medium"}, "data.points": []}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "", "value": "dhcp.scopes.last"}]}}}}, {"_type": "0", "id": 10000000000464, "visualization.name": "DHCP Total IPs", "visualization.description": "LinuxDHCP DHCP Total IPs", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "dhcp.ip.addresses", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "dhcp.ip.addresses"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "color.data.point": "dhcp.ip.addresses", "icon": {"name": "total-ip", "placement": "prefix"}}, "header": {"title": "DHCP Total IPs", "style": {"font.size": "medium"}, "data.points": []}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "", "value": "dhcp.ip.addresses.last"}]}}}}, {"_type": "0", "id": 10000000000465, "visualization.name": "DHCP Transient IPs", "visualization.description": "LinuxDHCP DHCP Transient IPs", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "dhcp.transient.ip.addresses", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "dhcp.transient.ip.addresses"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "color.data.point": "dhcp.transient.ip.addresses", "icon": {"name": "transient-ip", "placement": "prefix"}}, "header": {"title": "DHCP Transient IPs", "style": {"font.size": "medium"}, "data.points": []}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "", "value": "dhcp.transient.ip.addresses.last"}]}}}}, {"_type": "0", "id": 10000000000466, "visualization.name": "DHCP Leased IPs", "visualization.description": "LinuxDHCP DHCP Leased IPs", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "dhcp.leased.ip.addresses", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "dhcp.leased.ip.addresses"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "color.data.point": "dhcp.leased.ip.addresses", "icon": {"name": "lease-ip", "placement": "prefix"}}, "header": {"title": "DHCP Leased IPs", "style": {"font.size": "medium"}, "data.points": []}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "", "value": "dhcp.leased.ip.addresses.last"}]}}}}, {"_type": "0", "id": 10000000000467, "visualization.name": "IPs Available For Lease", "visualization.description": "LinuxDHCP IPs Available For Lease", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "dhcp.available.ip.addresses", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "dhcp.available.ip.addresses"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "color.data.point": "dhcp.available.ip.addresses", "icon": {"name": "available-ip", "placement": "prefix"}}, "header": {"title": "IPs Available For Lease", "style": {"font.size": "medium"}, "data.points": []}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "", "value": "dhcp.available.ip.addresses.last"}]}}}}, {"_type": "0", "id": 10000000000468, "visualization.name": "Today's Availability", "visualization.category": "Custom", "visualization.type": "Application Availability", "visualization.timeline": {"relative.timeline": "today"}, "visualization.result.by": ["monitor"], "visualization.data.sources": [{"visualization.result.by": ["monitor"], "type": "availability", "join.type": "all", "join.result": "availability", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "system.process~uptime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "system.process~downtime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "system.process~suspendtime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "system.process~unknowntime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "system.process~maintenancetime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "system.process~disabletime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "system.process~unreachabletime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}]}, {"visualization.result.by": ["monitor"], "type": "availability", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "system.process~uptime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "system.process~downtime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "system.process~suspendtime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "system.process~unknowntime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "system.process~maintenancetime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "system.process~disabletime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "system.process~unreachabletime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "no", "chart.label": "no", "axis.titles": {}, "highchart.settings": {"plotOptions": {"pie": {"innerSize": "70%"}}}}}, "join.type": "all", "join.columns": ["monitor"], "entity.type": "Monitor", "container.type": "Template", "entities": []}, {"_type": "0", "id": 10000000000469, "visualization.name": "Availability Statistics", "visualization.category": "Grid", "visualization.type": "Application Availability Time Series", "visualization.timeline": {"relative.timeline": "today"}, "visualization.data.sources": [{"type": "availability", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "system.process~uptime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~downtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~unreachabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~maintenancetime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~suspendtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~disabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~unknowntime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}], "visualization.timeline": {"relative.timeline": "-1d"}}, {"type": "availability", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "system.process~uptime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~downtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~unreachabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~maintenancetime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~suspendtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~disabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~unknowntime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}], "visualization.timeline": {"relative.timeline": "-7d"}}, {"type": "availability", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "system.process~uptime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~downtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~unreachabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~maintenancetime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~suspendtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~disabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~unknowntime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}], "visualization.timeline": {"relative.timeline": "-15d"}}], "visualization.properties": {"chart": {"rotation.angle": 0, "chart.legend": "yes", "chart.label": "no", "axis.titles": {}, "highchart.settings": {}, "sorting": {"limit": 10, "order": "desc", "column": "system.process~uptime.percent.avg"}}}}, {"_type": "0", "id": 10000000000470, "visualization.name": "Top Scope By IP Utilization", "visualization.description": "LinuxDHCP Top Scope By IP Utilization", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "Grid", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "dhcp.scope~available.ip.addresses", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "dhcp.scope~utilization.percent", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "dhcp.scope~active.ip.addresses", "aggregator": "last", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "medium"}, "columns": [{"name": "monitor", "show": "no", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 1, "style": {}}, {"name": "dhcp.scope", "title": "<PERSON><PERSON>", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 2, "style": {}}, {"name": "dhcp.scope~available.ip.addresses.last", "title": "Available IP Addresses", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 3, "style": {}}, {"name": "dhcp.scope~utilization.percent.last", "title": "Utilization", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 5, "style": {"inline.chart": {"type": "gauge"}}}, {"name": "dhcp.scope~active.ip.addresses.last", "title": "Active IP Addresses", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 4, "style": {}}]}}}, {"_type": "0", "id": 10000000000471, "visualization.name": "DHCP Scope Details", "visualization.description": "LinuxDHCP DHCP Scope Details", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "Grid", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "dhcp.scope~name", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "dhcp.scope~subnet.mask", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "dhcp.scope~ip.addresses", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "dhcp.scope~available.ip.addresses", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "dhcp.scope~active.ip.addresses", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "dhcp.scope~utilization.percent", "aggregator": "last", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "medium"}, "columns": [{"name": "monitor", "show": "no", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 1, "style": {}}, {"name": "dhcp.scope", "title": "<PERSON><PERSON>", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 2, "style": {}}, {"name": "dhcp.scope~name.last", "title": "Name", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 3, "style": {}}, {"name": "dhcp.scope~subnet.mask.last", "title": "Subnet Mask", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 4, "style": {}}, {"name": "dhcp.scope~ip.addresses.last", "title": "IP Addresses", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 5, "style": {}}, {"name": "dhcp.scope~available.ip.addresses.last", "title": "Available IP Addresses", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 6, "style": {}}, {"name": "dhcp.scope~active.ip.addresses.last", "title": "Active IP Addresses", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 7, "style": {}}, {"name": "dhcp.scope~utilization.percent.last", "title": "Utilization", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 8, "style": {"inline.chart": {"type": "gauge"}}}]}}}, {"_type": "0", "id": 10000000000472, "visualization.name": "DHCP Lease Details", "visualization.description": "LinuxDHCP DHCP Lease Details", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "Grid", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "dhcp.lease~client.hostname", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "dhcp.lease~binding.state", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "dhcp.lease~next.binding.state", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "dhcp.lease~rewind.binding.state", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "dhcp.lease~started.time.seconds", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "dhcp.lease~start.time", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "dhcp.lease~end.time", "aggregator": "last", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "medium"}, "columns": [{"name": "monitor", "show": "no", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 1, "style": {}}, {"name": "dhcp.lease", "title": "Lease", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 3, "style": {}}, {"name": "dhcp.lease~client.hostname.last", "title": "Hostname", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 2, "style": {}}, {"name": "dhcp.lease~binding.state.last", "title": "State", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 4, "style": {}}, {"name": "dhcp.lease~next.binding.state.last", "title": "Next Binding State", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 5, "style": {}}, {"name": "dhcp.lease~rewind.binding.state.last", "title": "Rewind Binding State", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 6, "style": {}}, {"name": "dhcp.lease~start.time.last", "title": "Start Time", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 8, "style": {}}, {"name": "dhcp.lease~end.time.last", "title": "End Time", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 9, "style": {}}, {"name": "dhcp.lease~started.time.seconds.last", "title": "Lease Time", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 7, "style": {}}]}}}, {"_type": "0", "id": 10000000000473, "visualization.name": "DHCP Pool Details", "visualization.description": "LinuxDHCP DHCP Pool Details", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "Grid", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "dhcp.pool~network", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "dhcp.pool~ip.addresses", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "dhcp.pool~active.ip.addresses", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "dhcp.pool~abandoned.ip.addresses", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "dhcp.pool~utilization.percent", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "dhcp.pool~transient.ip.addresses", "aggregator": "last", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "medium"}, "columns": [{"name": "monitor", "show": "no", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 1, "style": {}}, {"name": "dhcp.pool", "title": "Pool", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 2, "style": {}}, {"name": "dhcp.pool~network.last", "title": "Network", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 3, "style": {}}, {"name": "dhcp.pool~ip.addresses.last", "title": "IP Addresses", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 4, "style": {}}, {"name": "dhcp.pool~active.ip.addresses.last", "title": "Active IP Addresses", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 5, "style": {}}, {"name": "dhcp.pool~utilization.percent.last", "title": "Utilization", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 6, "style": {"inline.chart": {"type": "gauge"}}}, {"name": "dhcp.pool~abandoned.ip.addresses.last", "title": "Abandoned IP Addresses", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 7, "style": {}}, {"name": "dhcp.pool~transient.ip.addresses.last", "title": "Transient IP Addresses", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 8, "style": {}}]}}}]