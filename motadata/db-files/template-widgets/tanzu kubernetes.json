[{"_type": "0", "id": 10000000004095, "visualization.name": "CPU (%)", "visualization.description": "CPU Utilization", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "tanzu.kubernetes.cpu.percent", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "tanzu.kubernetes.cpu.percent"}]}], "visualization.properties": {"gauge": {"header": {"title": "CPU", "style": {"font.size": "medium"}}, "footer": {"style": {"font.size": "large"}, "data.points": [{"label": "", "value": "tanzu.kubernetes.cpu.percent.last", "type": "gauge"}]}, "style": {"icon": {"name": "cpu"}, "color.data.point": "tanzu.kubernetes.cpu.percent.last"}}}}, {"_type": "0", "id": 10000000004096, "visualization.name": "Memory (%)", "visualization.description": "Memory Utilization ", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "tanzu.kubernetes.memory.percent", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "tanzu.kubernetes.memory.capacity.bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "tanzu.kubernetes.memory.available.bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "tanzu.kubernetes.memory.percent"}, {"type": "metric", "data.point": "tanzu.kubernetes.memory.capacity.bytes"}, {"type": "metric", "data.point": "tanzu.kubernetes.memory.available.bytes"}]}], "visualization.properties": {"gauge": {"style": {"icon": {"name": "memory"}, "color.data.point": "tanzu.kubernetes.memory.percent.last"}, "header": {"title": "Memory", "style": {"font.size": "medium"}, "data.points": [{"label": "Total", "value": "tanzu.kubernetes.memory.capacity.bytes.last"}, {"label": "Free", "value": "tanzu.kubernetes.memory.available.bytes.last"}]}, "footer": {"style": {"font.size": "large"}, "data.points": [{"label": "", "value": "tanzu.kubernetes.memory.percent.last", "type": "gauge"}]}}}}, {"_type": "0", "id": 10000000004097, "visualization.name": "Namespace", "visualization.description": "Namespace", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "tanzu.kubernetes.namespaces", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "tanzu.kubernetes.namespaces"}]}], "visualization.properties": {"gauge": {"header": {"title": "Namespace", "style": {"font.size": "medium"}}, "footer": {"style": {"font.size": "large"}, "data.points": [{"label": "", "value": "tanzu.kubernetes.namespaces.last", "type": "gauge"}]}, "style": {"icon": {"name": "disk"}, "color.data.point": "tanzu.kubernetes.namespaces.last"}}}}, {"_type": "0", "id": 10000000004098, "visualization.name": "Nodes", "visualization.description": "Nodes", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "tanzu.kubernetes.nodes", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "tanzu.kubernetes.master.nodes", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "tanzu.kubernetes.worker.nodes", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "tanzu.kubernetes.nodes"}, {"type": "metric", "data.point": "tanzu.kubernetes.master.nodes"}, {"type": "metric", "data.point": "tanzu.kubernetes.worker.nodes"}]}], "visualization.properties": {"gauge": {"style": {"icon": {"name": "rtt"}, "color.data.point": "tanzu.kubernetes.nodes.last"}, "header": {"title": "Nodes", "style": {"font.size": "medium"}, "data.points": [{"label": "Master", "value": "tanzu.kubernetes.master.nodes.last"}, {"label": "Worker", "value": "tanzu.kubernetes.worker.nodes.last"}]}, "footer": {"style": {"font.size": "large"}, "data.points": [{"label": "", "value": "tanzu.kubernetes.nodes.last", "type": "gauge"}]}}}}, {"_type": "0", "id": 10000000004099, "visualization.name": "Pods", "visualization.description": "Pods", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "tanzu.kubernetes.pods", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "tanzu.kubernetes.pods"}]}], "visualization.properties": {"gauge": {"header": {"title": "Pods", "style": {"font.size": "medium"}}, "footer": {"style": {"font.size": "large"}, "data.points": [{"label": "", "value": "tanzu.kubernetes.pods.last", "type": "gauge"}]}, "style": {"icon": {"name": "vm"}, "color.data.point": "tanzu.kubernetes.pods.last"}}}}, {"_type": "0", "id": 10000000004100, "visualization.name": "Containers", "visualization.description": "Containers", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "tanzu.kubernetes.containers", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "tanzu.kubernetes.containers"}]}], "visualization.properties": {"gauge": {"header": {"title": "Containers", "style": {"font.size": "medium"}}, "footer": {"style": {"font.size": "large"}, "data.points": [{"label": "", "value": "tanzu.kubernetes.containers.last", "type": "gauge"}]}, "style": {"icon": {"name": "docker-container"}, "color.data.point": "tanzu.kubernetes.containers.last"}}}}, {"_type": "0", "id": 10000000004101, "visualization.name": "Today's Availability", "visualization.category": "Custom", "visualization.type": "Pie", "visualization.timeline": {"relative.timeline": "today"}, "visualization.result.by": ["monitor"], "visualization.data.sources": [{"visualization.result.by": ["monitor"], "type": "availability", "join.type": "all", "join.result": "availability", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "monitor.uptime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.downtime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.suspendtime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.unknowntime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.maintenancetime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.disabletime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.unreachabletime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}]}, {"visualization.result.by": ["monitor"], "type": "availability", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "monitor.uptime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.downtime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.suspendtime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.unknowntime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.maintenancetime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.disabletime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.unreachabletime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "no", "chart.label": "no", "axis.titles": {}, "highchart.settings": {"plotOptions": {"pie": {"innerSize": "70%"}}}}}, "join.type": "all", "join.columns": ["monitor"], "entity.type": "Monitor", "container.type": "Template", "entities": []}, {"_type": "0", "id": 10000000004102, "visualization.name": "Availability Statistics", "visualization.category": "Grid", "visualization.type": "Availability Time Series", "visualization.timeline": {"relative.timeline": "today"}, "visualization.data.sources": [{"type": "availability", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "monitor.uptime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.downtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.suspendtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.unknowntime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.maintenancetime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.disabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.unreachabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}], "visualization.timeline": {"relative.timeline": "-1d"}}, {"type": "availability", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "monitor.uptime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.downtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.suspendtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.unknowntime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.maintenancetime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.disabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.unreachabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}], "visualization.timeline": {"relative.timeline": "-7d"}}, {"type": "availability", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "monitor.uptime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.downtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.suspendtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.unknowntime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.maintenancetime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.disabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.unreachabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}], "visualization.timeline": {"relative.timeline": "-15d"}}], "visualization.properties": {"chart": {"rotation.angle": 0, "chart.legend": "yes", "chart.label": "no", "axis.titles": {}, "highchart.settings": {}, "sorting": {"limit": 10, "order": "desc", "column": "monitor.uptime.percent.avg"}}}}, {"id": 10000000004103, "_type": "0", "visualization.name": "CPU/Memory Utilization", "visualization.description": "CPU/Memory Utilization", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "tanzu.kubernetes.memory.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "tanzu.kubernetes.cpu.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"id": 10000000004104, "_type": "0", "visualization.name": "Cluster Details", "visualization.description": "Cluster Details", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Grid", "visualization.type": "Grid", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "tanzu.kubernetes.deployments", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "tanzu.kubernetes.services", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "tanzu.kubernetes.daemon.sets", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "tanzu.kubernetes.stateful.sets", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "tanzu.kubernetes.jobs", "aggregator": "last", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "no", "column.selection": "no", "header": "yes", "style": {"header.font.size": "medium"}, "columns": [{"name": "monitor", "show": "no", "sortable": "no", "disable": "yes", "resizable": "no", "selectable": "no", "orderable": "no"}, {"name": "tanzu.kubernetes.deployments.last", "title": "DEPLOYMENT", "show": "yes", "sortable": "yes", "disable": "yes", "resizable": "yes", "selectable": "no", "orderable": "no"}, {"name": "tanzu.kubernetes.services.last", "title": "SERVICE", "show": "yes", "sortable": "yes", "disable": "yes", "resizable": "yes", "selectable": "no", "orderable": "no"}, {"name": "tanzu.kubernetes.daemon.sets.last", "title": "DEAMONSET", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 2}, {"name": "tanzu.kubernetes.stateful.sets.last", "title": "STATEFULSET", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 3}, {"name": "tanzu.kubernetes.jobs.last", "title": "JOB", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 4}]}}}, {"id": 10000000004105, "_type": "0", "visualization.name": "Control Plane Components", "visualization.description": "Control Plane Components", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Grid", "visualization.type": "Grid", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "tanzu.kubernetes.component~status", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "tanzu.kubernetes.component~message", "aggregator": "last", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "no", "column.selection": "no", "header": "yes", "style": {"header.font.size": "medium"}, "columns": [{"name": "monitor", "show": "no", "sortable": "no", "disable": "yes", "resizable": "no", "selectable": "no", "orderable": "no"}, {"name": "tanzu.kubernetes.component", "title": "NAME", "show": "yes", "sortable": "yes", "disable": "yes", "resizable": "yes", "selectable": "no", "orderable": "no"}, {"name": "tanzu.kubernetes.component~status.last", "title": "STATUS", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 2}, {"name": "tanzu.kubernetes.component~message.last", "title": "MESSAGE", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 3}]}}}, {"id": 10000000004106, "_type": "0", "visualization.name": "Namespace Details", "visualization.description": "Namespace Details", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Grid", "visualization.type": "Grid", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "tanzu.kubernetes.namespace~status", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "tanzu.kubernetes.namespace~total.pods", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "tanzu.kubernetes.namespace~running.pods", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "tanzu.kubernetes.namespace~succeeded.pods", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "tanzu.kubernetes.namespace~pending.pods", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "tanzu.kubernetes.namespace~failed.pods", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "tanzu.kubernetes.namespace~unknown.pods", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "tanzu.kubernetes.namespace~creation.time", "aggregator": "last", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "no", "column.selection": "no", "header": "yes", "style": {"header.font.size": "medium"}, "columns": [{"name": "monitor", "show": "no", "sortable": "no", "disable": "yes", "resizable": "no", "selectable": "no", "orderable": "no"}, {"name": "tanzu.kubernetes.namespace", "title": "NAME", "show": "yes", "sortable": "yes", "disable": "yes", "resizable": "yes", "selectable": "no", "orderable": "no"}, {"name": "tanzu.kubernetes.namespace~status.last", "title": "STATUS", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 2}, {"name": "tanzu.kubernetes.namespace~total.pods.last", "title": "TOTAL PODS", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 3}, {"name": "tanzu.kubernetes.namespace~running.pods.last", "title": "RUNNING PODS", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 4}, {"name": "tanzu.kubernetes.namespace~succeeded.pods.last", "title": "SUCCEEDED PODS", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 5}, {"name": "tanzu.kubernetes.namespace~pending.pods.last", "title": "PENDING PODS", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 6}, {"name": "tanzu.kubernetes.namespace~failed.pods.last", "title": "FAILED PODS", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 7}, {"name": "tanzu.kubernetes.namespace~unknown.pods.last", "title": "UNKNOWN PODS", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 8}, {"name": "tanzu.kubernetes.namespace~creation.time.last", "title": "CREATED AT", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 9}]}}}, {"id": 10000000004107, "_type": "0", "visualization.name": "Daemonset Details", "visualization.description": "Daemonset Details", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Grid", "visualization.type": "Grid", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "tanzu.kubernetes.daemon.set~name", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "tanzu.kubernetes.daemon.set~namespace", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "tanzu.kubernetes.daemon.set~desired.replicas", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "tanzu.kubernetes.daemon.set~current.replicas", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "tanzu.kubernetes.daemon.set~running.replicas", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "tanzu.kubernetes.daemon.set~available.replicas", "aggregator": "last", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "no", "column.selection": "no", "header": "yes", "style": {"header.font.size": "medium"}, "columns": [{"name": "monitor", "show": "no", "sortable": "no", "disable": "yes", "resizable": "no", "selectable": "no", "orderable": "no"}, {"name": "tanzu.kubernetes.daemon.set", "title": "", "show": "no", "sortable": "yes", "disable": "yes", "resizable": "yes", "selectable": "no", "orderable": "no"}, {"name": "tanzu.kubernetes.daemon.set~name.last", "title": "NAME", "show": "yes", "sortable": "yes", "disable": "yes", "resizable": "yes", "selectable": "no", "orderable": "no", "position": 1}, {"name": "tanzu.kubernetes.daemon.set~namespace.last", "title": "NAMESPACE", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 2}, {"name": "tanzu.kubernetes.daemon.set~desired.replicas.last", "title": "DESIRED REPLICA", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 3}, {"name": "tanzu.kubernetes.daemon.set~current.replicas.last", "title": "CURRENT REPLICA", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 4}, {"name": "tanzu.kubernetes.daemon.set~ready.replicas.last", "title": "READY REPLICA", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 5}, {"name": "tanzu.kubernetes.daemon.set~available.replicas.last", "title": "AVAILABLE REPLICA", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 6}, {"name": "tanzu.kubernetes.daemon.set~updated.replicas.last", "title": "UP-TO-DATE REPLICA", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 7}, {"name": "tanzu.kubernetes.daemon.set~node.selector.last", "title": "NODE SELECTOR", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 8}, {"name": "tanzu.kubernetes.daemon.set~creation.time.last", "title": "CREATED AT", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 9}]}}}, {"id": 10000000004108, "_type": "0", "visualization.name": "Job Details", "visualization.description": "Job Details", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Grid", "visualization.type": "Grid", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "tanzu.kubernetes.job~name", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "tanzu.kubernetes.job~namespace", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "tanzu.kubernetes.job~status", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "tanzu.kubernetes.job~duration", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "tanzu.kubernetes.job~started.time", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "tanzu.kubernetes.job~completion.time", "aggregator": "last", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "no", "column.selection": "no", "header": "yes", "style": {"header.font.size": "medium"}, "columns": [{"name": "monitor", "show": "no", "sortable": "no", "disable": "yes", "resizable": "no", "selectable": "no", "orderable": "no"}, {"name": "tanzu.kubernetes.job", "title": "", "show": "no", "sortable": "yes", "disable": "yes", "resizable": "yes", "selectable": "no", "orderable": "no"}, {"name": "tanzu.kubernetes.job~name.last", "title": "NAME", "show": "yes", "sortable": "yes", "disable": "yes", "resizable": "yes", "selectable": "no", "orderable": "no", "position": 1}, {"name": "tanzu.kubernetes.job~namespace.last", "title": "NAMESPACE", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 2}, {"name": "tanzu.kubernetes.job~status.last", "title": "STATUS", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 3}, {"name": "tanzu.kubernetes.job~duration.last", "title": "DURATION", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 4}, {"name": "tanzu.kubernetes.job~started.time.last", "title": "STARTED AT", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 5}, {"name": "tanzu.kubernetes.job~completion.time.last", "title": "COMPLETED AT", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 6}]}}}, {"id": 10000000004109, "_type": "0", "visualization.name": "Statefulset Details", "visualization.description": "StatefulSet Details", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Grid", "visualization.type": "Grid", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "tanzu.kubernetes.stateful.set~name", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "tanzu.kubernetes.stateful.set~namespace", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "tanzu.kubernetes.stateful.set~replicas", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "tanzu.kubernetes.stateful.set~ready.replicas", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "tanzu.kubernetes.stateful.set~current.replicas", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "tanzu.kubernetes.stateful.set~updated.replicas", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "tanzu.kubernetes.stateful.set~service", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "tanzu.kubernetes.stateful.set~creation.time", "aggregator": "last", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "no", "column.selection": "no", "header": "yes", "style": {"header.font.size": "medium"}, "columns": [{"name": "monitor", "show": "no", "sortable": "no", "disable": "yes", "resizable": "no", "selectable": "no", "orderable": "no"}, {"name": "tanzu.kubernetes.stateful.set", "title": "", "show": "no", "sortable": "yes", "disable": "yes", "resizable": "yes", "selectable": "no", "orderable": "no"}, {"name": "tanzu.kubernetes.stateful.set~name.last", "title": "NAME", "show": "yes", "sortable": "yes", "disable": "yes", "resizable": "yes", "selectable": "no", "orderable": "no", "position": 1}, {"name": "tanzu.kubernetes.stateful.set~namespace.last", "title": "NAMESPACE", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 2}, {"name": "tanzu.kubernetes.stateful.set~replicas.last", "title": "TOTAL REPLICA", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 3}, {"name": "tanzu.kubernetes.stateful.set~ready.replicas.last", "title": "READY REPLICA", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 4}, {"name": "tanzu.kubernetes.stateful.set~current.replicas.last", "title": "CURRENT REPLICA", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 5}, {"name": "tanzu.kubernetes.stateful.set~updated.replicas.last", "title": "UPDATED REPLICA", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 6}, {"name": "tanzu.kubernetes.stateful.set~service.last", "title": "SERVICE", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 7}, {"name": "tanzu.kubernetes.stateful.set~creation.time.last", "title": "CREATED AT", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 8}]}}}, {"_type": "0", "id": 10000000004110, "visualization.name": "Top Nodes By Memory Details", "visualization.description": "Nodes By Memory Details", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "TopN", "visualization.type": "VerticalBar", "visualization.result.by": ["tanzu.kubernetes.node"], "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "tanzu.kubernetes.node~memory.limit.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "tanzu.kubernetes.node~memory.request.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "no", "chart.label": "no", "highchart.settings": {}, "sorting": {"limit": "5", "order": "desc", "column": "tanzu.kubernetes.node~memory.limit.percent.avg"}}}}, {"_type": "0", "id": 10000000004111, "visualization.name": "Top Nodes By CPU Details", "visualization.description": "Nodes By CPU Details", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "TopN", "visualization.type": "VerticalBar", "visualization.result.by": ["tanzu.kubernetes.node"], "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "tanzu.kubernetes.node~cpu.limit.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "tanzu.kubernetes.node~cpu.request.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "no", "chart.label": "no", "highchart.settings": {}, "sorting": {"limit": "5", "order": "desc", "column": "tanzu.kubernetes.node~cpu.request.percent.avg"}}}}, {"id": 10000000004112, "_type": "0", "visualization.name": "Node's Resources Summary", "visualization.description": "Node's Resource Details", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Grid", "visualization.type": "Grid", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "tanzu.kubernetes.node~allocatable.cpu.cores", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "tanzu.kubernetes.node~allocatable.memory.bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "tanzu.kubernetes.node~used.memory.bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "tanzu.kubernetes.node~available.memory.bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "tanzu.kubernetes.node~cpu.limit.percent", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "tanzu.kubernetes.node~cpu.request.percent", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "tanzu.kubernetes.node~memory.limit.percent", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "tanzu.kubernetes.node~memory.request.percent", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "tanzu.kubernetes.node~allocatable.ephemeral.storage.bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "no", "column.selection": "no", "header": "yes", "style": {"header.font.size": "medium"}, "columns": [{"name": "monitor", "show": "no", "sortable": "no", "disable": "yes", "resizable": "no", "selectable": "no", "orderable": "no"}, {"name": "tanzu.kubernetes.node", "title": "NAME", "show": "yes", "sortable": "yes", "disable": "yes", "resizable": "yes", "selectable": "no", "orderable": "no"}, {"name": "tanzu.kubernetes.node~allocatable.cpu.cores.last", "title": "ALLOCATABLE CPU CORES", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 2}, {"name": "tanzu.kubernetes.node~allocatable.memory.bytes.last", "title": "ALLOCATABLE MEMORY", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 5}, {"name": "tanzu.kubernetes.node~used.memory.bytes.last", "title": "USED MEMORY", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 6}, {"name": "tanzu.kubernetes.node~available.memory.bytes.last", "title": "AVAILABLE MEMORY", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 7}, {"name": "tanzu.kubernetes.node~cpu.limit.percent.last", "title": "CPU LIMIT %", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 8}, {"name": "tanzu.kubernetes.node~cpu.request.percent.last", "title": "CPU REQUEST %", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 9}, {"name": "tanzu.kubernetes.node~memory.limit.percent.last", "title": "MEMORY LIMIT %", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 10}, {"name": "tanzu.kubernetes.node~memory.request.percent.last", "title": "MEMORY REQUEST %", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 11}, {"name": "tanzu.kubernetes.node~allocatable.ephemeral.storage.bytes.last", "title": "ALLOCATABLE EPHEMERAL STORAGE", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 12}]}}}, {"id": 10000000004113, "_type": "0", "visualization.name": "Node Summary", "visualization.description": "Node Summary Details", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Grid", "visualization.type": "Grid", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "tanzu.kubernetes.node~ip", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "tanzu.kubernetes.node~status", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "tanzu.kubernetes.node~type", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "tanzu.kubernetes.node~allocatable.pods", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "tanzu.kubernetes.node~pods", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "tanzu.kubernetes.node~containers", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "tanzu.kubernetes.node~container.runtime.version", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "tanzu.kubernetes.node~creation.time", "aggregator": "last", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "no", "column.selection": "no", "header": "yes", "style": {"header.font.size": "medium"}, "columns": [{"name": "monitor", "show": "no", "sortable": "no", "disable": "yes", "resizable": "no", "selectable": "no", "orderable": "no"}, {"name": "tanzu.kubernetes.node", "title": "NAME", "show": "yes", "sortable": "yes", "disable": "yes", "resizable": "yes", "selectable": "no", "orderable": "no"}, {"name": "tanzu.kubernetes.node~ip.last", "title": "IP", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 2}, {"name": "tanzu.kubernetes.node~status.last", "title": "STATUS", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 3}, {"name": "tanzu.kubernetes.node~type.last", "title": "TYPE", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 4}, {"name": "tanzu.kubernetes.node~allocatable.pods.last", "title": "ALLOCATABLE PODS", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 5}, {"name": "tanzu.kubernetes.node~pods.last", "title": "PODS", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 6}, {"name": "tanzu.kubernetes.node~containers.last", "title": "CONTAINERS", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 7}, {"name": "tanzu.kubernetes.node~container.runtime.version.last", "title": "CONTAINER RUNTIME", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 8}, {"name": "tanzu.kubernetes.node~creation.time.last", "title": "CREATED AT", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 9}]}}}, {"_type": "0", "id": 10000000004114, "visualization.name": "Top Container By Memory details", "visualization.description": "Container By Memory details", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "TopN", "visualization.type": "VerticalBar", "visualization.result.by": ["tanzu.kubernetes.container"], "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "tanzu.kubernetes.container~memory.limit.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "tanzu.kubernetes.container~memory.request.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "no", "chart.label": "no", "highchart.settings": {}, "sorting": {"limit": "5", "order": "desc", "column": "tanzu.kubernetes.container~memory.request.percent.avg"}}}}, {"_type": "0", "id": 10000000004115, "visualization.name": "Top Containers By CPU details", "visualization.description": "Containers By CPU details", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "TopN", "visualization.type": "VerticalBar", "visualization.result.by": ["tanzu.kubernetes.container"], "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "tanzu.kubernetes.container~cpu.limit.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "tanzu.kubernetes.container~cpu.request.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "no", "chart.label": "no", "highchart.settings": {}, "sorting": {"limit": "5", "order": "desc", "column": "tanzu.kubernetes.container~cpu.request.percent.avg"}}}}, {"id": 10000000004116, "_type": "0", "visualization.name": "Container's Resource Summary", "visualization.description": "Container's Resource Summary", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Grid", "visualization.type": "Grid", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "tanzu.kubernetes.container~name", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "tanzu.kubernetes.container~cpu.percent", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "tanzu.kubernetes.container~memory.bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "tanzu.kubernetes.container~cpu.limit.percent", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "tanzu.kubernetes.container~cpu.request.percent", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "tanzu.kubernetes.container~memory.limit.percent", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "tanzu.kubernetes.container~memory.request.percent", "aggregator": "last", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "medium"}, "columns": [{"name": "monitor", "show": "no", "sortable": "no", "disable": "yes", "resizable": "no", "selectable": "no", "orderable": "no"}, {"name": "tanzu.kubernetes.container", "title": "", "show": "no", "sortable": "yes", "disable": "yes", "resizable": "yes", "selectable": "no", "orderable": "no"}, {"name": "tanzu.kubernetes.container~name.last", "title": "NAME", "show": "yes", "sortable": "yes", "disable": "yes", "resizable": "yes", "selectable": "no", "orderable": "no", "position": 1}, {"name": "tanzu.kubernetes.container~cpu.percent.last", "title": "CPU %", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 2}, {"name": "tanzu.kubernetes.container~memory.bytes.last", "title": "MEMORY", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 3}, {"name": "tanzu.kubernetes.container~cpu.limit.percent.last", "title": "CPU LIMIT %", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 4}, {"name": "tanzu.kubernetes.container~cpu.request.percent.last", "title": "CPU REQUEST %", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 5}, {"name": "tanzu.kubernetes.container~memory.limit.percent.last", "title": "MEMORY LIMIT %", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 6}, {"name": "tanzu.kubernetes.container~memory.request.percent.last", "title": "MEMORY REQUEST %", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 7}]}}}, {"id": 10000000004117, "_type": "0", "visualization.name": "Container Summary", "visualization.description": "Container Summary", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Grid", "visualization.type": "Grid", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "tanzu.kubernetes.container~name", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "tanzu.kubernetes.container~image", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "tanzu.kubernetes.container~status", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "tanzu.kubernetes.container~pod.name", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "tanzu.kubernetes.container~restarts", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "tanzu.kubernetes.container~ports", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "tanzu.kubernetes.container~volume.mounts", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "tanzu.kubernetes.container~creation.time", "aggregator": "last", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "medium"}, "columns": [{"name": "monitor", "show": "no", "sortable": "no", "disable": "yes", "resizable": "no", "selectable": "no", "orderable": "no"}, {"name": "tanzu.kubernetes.container", "title": "", "show": "no", "sortable": "yes", "disable": "yes", "resizable": "yes", "selectable": "no", "orderable": "no"}, {"name": "tanzu.kubernetes.container~name.last", "title": "NAME", "show": "yes", "sortable": "yes", "disable": "yes", "resizable": "yes", "selectable": "no", "orderable": "no", "position": 1}, {"name": "tanzu.kubernetes.container~image.last", "title": "IMAGE", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 2}, {"name": "tanzu.kubernetes.container~status.last", "title": "STATUS", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 3}, {"name": "tanzu.kubernetes.container~pod.name.last", "title": "POD", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 4}, {"name": "tanzu.kubernetes.container~restarts.last", "title": "RESTART COUNT", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 5}, {"name": "tanzu.kubernetes.container~ports.last", "title": "PORT", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "position": 6, "type": "port"}, {"name": "tanzu.kubernetes.container~volume.mounts.last", "title": "VOLUME MOUNT", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "position": 7, "type": "port"}, {"name": "tanzu.kubernetes.container~creation.time.last", "title": "STARTED AT", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 8}]}}}, {"id": 10000000004118, "_type": "0", "visualization.name": "Pod's Resource Summary", "visualization.description": "Pod's Resource Summary", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Grid", "visualization.type": "Grid", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "tanzu.kubernetes.pod~name", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "tanzu.kubernetes.pod~namespace", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "tanzu.kubernetes.pod~containers", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "tanzu.kubernetes.pod~cpu.limit.percent", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "tanzu.kubernetes.pod~cpu.request.percent", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "tanzu.kubernetes.pod~memory.limit.percent", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "tanzu.kubernetes.pod~memory.request.percent", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "tanzu.kubernetes.pod~creation.time", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "tanzu.kubernetes.pod~persistent.volume", "aggregator": "last", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "medium"}, "columns": [{"name": "monitor", "show": "no", "sortable": "no", "disable": "yes", "resizable": "no", "selectable": "no", "orderable": "no"}, {"name": "tanzu.kubernetes.pod", "title": "", "show": "no", "sortable": "yes", "disable": "yes", "resizable": "yes", "selectable": "no", "orderable": "no"}, {"name": "tanzu.kubernetes.pod~name.last", "title": "NAME", "show": "yes", "sortable": "yes", "disable": "yes", "resizable": "yes", "selectable": "no", "orderable": "no", "position": 1}, {"name": "tanzu.kubernetes.pod~namespace.last", "title": "NAMESPACE", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 2}, {"name": "tanzu.kubernetes.pod~containers.last", "title": "CONTAINER", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 3}, {"name": "tanzu.kubernetes.pod~cpu.limit.percent.last", "title": "CPU LIMIT %", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 4}, {"name": "tanzu.kubernetes.pod~cpu.request.percent.last", "title": "CPU REQUEST %", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 5}, {"name": "tanzu.kubernetes.pod~memory.limit.percent.last", "title": "MEMORY LIMIT %", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 6}, {"name": "tanzu.kubernetes.pod~memory.request.percent.last", "title": "MEMORY REQUEST %", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 7}, {"name": "tanzu.kubernetes.pod~creation.time.last", "title": "CREATED BY", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 8}, {"name": "tanzu.kubernetes.pod~persistent.volume.last", "title": "PERSISTENT VOLUME", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 9, "type": "port"}]}}}, {"id": 10000000004119, "_type": "0", "visualization.name": "Pod Summary", "visualization.description": "Pod Summary Overview", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Grid", "visualization.type": "Grid", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "tanzu.kubernetes.pod~name", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "tanzu.kubernetes.pod~namespace", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "tanzu.kubernetes.pod~node.name", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "tanzu.kubernetes.pod~status", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "tanzu.kubernetes.pod~ip.address", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "tanzu.kubernetes.pod~type", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "tanzu.kubernetes.pod~application", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "tanzu.kubernetes.pod~restarts", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "tanzu.kubernetes.pod~creation.time", "aggregator": "last", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "medium"}, "columns": [{"name": "monitor", "show": "no", "sortable": "no", "disable": "yes", "resizable": "no", "selectable": "no", "orderable": "no"}, {"name": "tanzu.kubernetes.pod", "title": "", "show": "no", "sortable": "yes", "disable": "yes", "resizable": "yes", "selectable": "no", "orderable": "no"}, {"name": "tanzu.kubernetes.pod~name.last", "title": "NAME", "show": "yes", "sortable": "yes", "disable": "yes", "resizable": "yes", "selectable": "no", "orderable": "no", "position": 1}, {"name": "tanzu.kubernetes.pod~namespace.last", "title": "NAMESPACE", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 2}, {"name": "tanzu.kubernetes.pod~node.name.last", "title": "NODE", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 3}, {"name": "tanzu.kubernetes.pod~status.last", "title": "STATUS", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 4}, {"name": "tanzu.kubernetes.pod~ip.address.last", "title": "IP", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 5, "type": "port"}, {"name": "tanzu.kubernetes.pod~type.last", "title": "TYPE", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 6}, {"name": "tanzu.kubernetes.pod~application.last", "title": "APPLICATION", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 7}, {"name": "tanzu.kubernetes.pod~restarts.last", "title": "RESTART COUNT", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 8}, {"name": "tanzu.kubernetes.pod~creation.time.last", "title": "CREATED AT", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 9}]}}}, {"id": 10000000004120, "_type": "0", "visualization.name": "Replica Set Details", "visualization.description": "Replica Set Details", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Grid", "visualization.type": "Grid", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "tanzu.kubernetes.replica.set~name", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "tanzu.kubernetes.replica.set~namespace", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "tanzu.kubernetes.replica.set~desired.replicas", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "tanzu.kubernetes.replica.set~current.replicas", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "tanzu.kubernetes.replica.set~ready.replicas", "aggregator": "last", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "medium"}, "columns": [{"name": "monitor", "show": "no", "sortable": "no", "disable": "yes", "resizable": "no", "selectable": "no", "orderable": "no"}, {"name": "tanzu.kubernetes.replica.set", "title": "", "show": "no", "sortable": "yes", "disable": "yes", "resizable": "yes", "selectable": "no", "orderable": "no"}, {"name": "tanzu.kubernetes.replica.set~name.last", "title": "NAME", "show": "yes", "sortable": "yes", "disable": "yes", "resizable": "yes", "selectable": "no", "orderable": "no", "position": 1}, {"name": "tanzu.kubernetes.replica.set~namespace.last", "title": "NAMESPACE", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 2}, {"name": "tanzu.kubernetes.replica.set~desired.replicas.last", "title": "DESIRED REPLICA", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 3}, {"name": "tanzu.kubernetes.replica.set~current.replicas.last", "title": "CURRENT REPLICA", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 4}, {"name": "tanzu.kubernetes.replica.set~ready.replicas.last", "title": "READY REPLICA", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 5}]}}}, {"id": 10000000004121, "_type": "0", "visualization.name": "Deployment Details", "visualization.description": "Deployment Details", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Grid", "visualization.type": "Grid", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "tanzu.kubernetes.deployment~name", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "tanzu.kubernetes.deployment~namespace", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "tanzu.kubernetes.deployment~total.replicas", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "tanzu.kubernetes.deployment~ready.replicas", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "tanzu.kubernetes.deployment~available.replicas", "aggregator": "last", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "medium"}, "columns": [{"name": "monitor", "show": "no", "sortable": "no", "disable": "yes", "resizable": "no", "selectable": "no", "orderable": "no"}, {"name": "tanzu.kubernetes.deployment", "title": "", "show": "no", "sortable": "yes", "disable": "yes", "resizable": "yes", "selectable": "no", "orderable": "no"}, {"name": "tanzu.kubernetes.deployment~name.last", "title": "NAME", "show": "yes", "sortable": "yes", "disable": "yes", "resizable": "yes", "selectable": "no", "orderable": "no", "position": 1}, {"name": "tanzu.kubernetes.deployment~namespace.last", "title": "NAMESPACE", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 2}, {"name": "tanzu.kubernetes.deployment~total.replicas.last", "title": "TOTAL REPLICA", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 3}, {"name": "tanzu.kubernetes.deployment~ready.replicas.last", "title": "READY REPLICA", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 4}, {"name": "tanzu.kubernetes.deployment~available.replicas.last", "title": "AVAILABLE REPLICA", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 5}]}}}, {"id": 10000000004122, "_type": "0", "visualization.name": "Service Details", "visualization.description": "Service Details", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Grid", "visualization.type": "Grid", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "tanzu.kubernetes.service~name", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "tanzu.kubernetes.service~namespace", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "tanzu.kubernetes.service~application", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "tanzu.kubernetes.service~type", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "tanzu.kubernetes.service~cluster.ip.address", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "tanzu.kubernetes.service~ports", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "tanzu.kubernetes.service~creation.time", "aggregator": "last", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "no", "column.selection": "no", "header": "yes", "style": {"header.font.size": "medium"}, "columns": [{"name": "monitor", "show": "no", "sortable": "no", "disable": "yes", "resizable": "no", "selectable": "no", "orderable": "no"}, {"name": "tanzu.kubernetes.service", "title": "", "show": "no", "sortable": "yes", "disable": "yes", "resizable": "yes", "selectable": "no", "orderable": "no"}, {"name": "tanzu.kubernetes.service~name.last", "title": "NAME", "show": "yes", "sortable": "yes", "disable": "yes", "resizable": "yes", "selectable": "no", "orderable": "no", "position": 1}, {"name": "tanzu.kubernetes.service~namespace.last", "title": "NAMESPACE", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 2}, {"name": "tanzu.kubernetes.service~application.last", "title": "APPLICATION", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 3}, {"name": "tanzu.kubernetes.service~type.last", "title": "SERVICE TYPE", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 4}, {"name": "tanzu.kubernetes.service~cluster.ip.address.last", "title": "CLUSTER IP", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 5}, {"name": "tanzu.kubernetes.service~ports.last", "title": "PORT", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 6, "type": "port"}, {"name": "tanzu.kubernetes.service~creation.time.last", "title": "CREATED AT", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 7}]}}}]