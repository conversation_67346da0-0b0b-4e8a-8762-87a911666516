[{"_type": "0", "id": 10000000001656, "visualization.name": "SQS Count", "visualization.description": "SQS Count", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "aws.sqs", "aggregator": "last", "entity.type": "Group"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "color.data.point": "aws.sqs", "icon": {"name": "sqs", "placement": "prefix"}}, "header": {"title": "SQS Count", "style": {"font.size": "medium"}, "data.points": []}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "", "value": "aws.sqs.last"}]}}}, "container.type": "dashboard"}, {"_type": "0", "id": 10000000001657, "visualization.name": "Total Messages Sent", "visualization.description": "Total Messages Sent", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Gauge", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "aws.sqs.sent.messages", "aggregator": "avg", "entity.type": "Group", "entities": []}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "color.data.point": "aws.sqs.sent.messages", "icon": {"name": "outgoing-messages", "placement": "prefix"}}, "header": {"title": "Total Messages Sent", "style": {"font.size": "medium"}, "data.points": []}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "", "value": "aws.sqs.sent.messages.avg"}]}}}, "container.type": "dashboard"}, {"_type": "0", "id": 10000000001658, "visualization.name": "Total Messages Received", "visualization.description": "Total Messages Received", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Gauge", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "aws.sqs.received.messages", "aggregator": "avg", "entity.type": "Group", "entities": []}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "color.data.point": "aws.sqs.received.messages", "icon": {"name": "incoming-messages", "placement": "prefix"}}, "header": {"title": "Total Messages Received", "style": {"font.size": "medium"}, "data.points": []}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "", "value": "aws.sqs.received.messages.avg"}]}}}, "container.type": "dashboard"}, {"_type": "0", "id": 10000000001659, "visualization.name": "Total Empty Messages", "visualization.description": "Total Empty Messages", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Gauge", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "aws.sqs.empty.messages", "aggregator": "avg", "entity.type": "Group", "entities": []}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "color.data.point": "aws.sqs.empty.messages", "icon": {"name": "empty-message", "placement": "prefix"}}, "header": {"title": "Total Empty Messages", "style": {"font.size": "medium"}, "data.points": []}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "", "value": "aws.sqs.empty.messages.avg"}]}}}, "container.type": "dashboard"}, {"_type": "0", "id": 10000000001660, "visualization.name": "Total Messages Deleted", "visualization.description": "Total Messages Deleted", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Gauge", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "aws.sqs.deleted.messages", "aggregator": "avg", "entity.type": "Group", "entities": []}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "color.data.point": "aws.sqs.deleted.messages", "icon": {"name": "delete-message", "placement": "prefix"}}, "header": {"title": "Total Messages Deleted", "style": {"font.size": "medium"}, "data.points": []}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "", "value": "aws.sqs.deleted.messages.avg"}]}}}, "container.type": "dashboard"}, {"_type": "0", "id": 10000000001661, "visualization.name": "Total Messages Delayed", "visualization.description": "Total Messages Delayed", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Gauge", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "aws.sqs.delayed.messages", "aggregator": "avg", "entity.type": "Group", "entities": []}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "color.data.point": "aws.sqs.delayed.messages", "icon": {"name": "delayed-message", "placement": "prefix"}}, "header": {"title": "Total Messages Delayed", "style": {"font.size": "medium"}, "data.points": []}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "", "value": "aws.sqs.delayed.messages.avg"}]}}}, "container.type": "dashboard"}, {"id": 10000000001662, "visualization.name": "Queue Details", "visualization.description": "Queue Details", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "Grid", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "aws.sqs.queues", "aggregator": "last", "entity.type": "Group", "entities": []}, {"data.point": "aws.sqs.received.messages", "aggregator": "last", "entity.type": "Group", "entities": []}, {"data.point": "aws.sqs.sent.messages", "aggregator": "last", "entity.type": "Group", "entities": []}, {"data.point": "aws.sqs.empty.messages", "aggregator": "last", "entity.type": "Group", "entities": []}, {"data.point": "aws.sqs.deleted.messages", "aggregator": "last", "entity.type": "Group", "entities": []}, {"data.point": "aws.sqs.delayed.messages", "aggregator": "last", "entity.type": "Group", "entities": []}, {"data.point": "aws.sqs.visible.messages", "aggregator": "last", "entity.type": "Group", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "small"}, "columns": [{"name": "aws.sqs.queues", "title": "Name", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 1, "style": {}}, {"name": "aws.sqs.received.messages", "title": "Messages", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 3, "style": {"icon": {"placement": "prefix", "text": "Received", "name": "file-search"}}}, {"name": "aws.sqs.sent.messages", "title": "Messages", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 3, "style": {"icon": {"placement": "prefix", "text": "<PERSON><PERSON>"}}}, {"name": "aws.sqs.empty.messages", "title": "Empty Receives", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 3, "style": {"icon": {"placement": "prefix", "name": "database"}}}, {"name": "aws.sqs.deleted.messages", "title": "Messages", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 3, "style": {"icon": {"placement": "prefix", "text": "Deleted", "name": "file-search"}}}, {"name": "aws.sqs.delayed.messages", "title": "Messages", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 3, "style": {"icon": {"placement": "prefix", "text": "Delayed"}}}, {"name": "aws.sqs.visible.messages", "title": "Messages Visible", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 3, "style": {"icon": {"placement": "prefix", "name": "flag"}}}]}}, "granularity": {"value": 5, "unit": "m"}, "container.type": "dashboard"}, {"id": 10000000001663, "visualization.name": "Top Queue By Sent Message", "visualization.description": "Top Queue By Sent Message", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "TopN", "visualization.type": "HorizontalBar", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "aws.sqs.sent.messages", "aggregator": "avg", "entity.type": "Group", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "no", "chart.label": "no", "highchart.settings": {}, "sorting": {"limit": 10, "order": "desc", "column": "aws.sqs.sent.messages.avg"}}}, "granularity": {"value": 5, "unit": "m"}, "container.type": "dashboard"}, {"id": 10000000001664, "visualization.name": "Top Queue By Received Message", "visualization.description": "Top Queue By Received Message", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "TopN", "visualization.type": "HorizontalBar", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "aws.sqs.received.messages", "aggregator": "avg", "entity.type": "Group", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "no", "chart.label": "no", "highchart.settings": {}, "sorting": {"limit": 10, "order": "desc", "column": "aws.sqs.received.messages.avg"}}}, "granularity": {"value": 5, "unit": "m"}, "container.type": "dashboard"}, {"id": 10000000001665, "visualization.name": "Approximate Age Of Oldest Message Per Queue", "visualization.description": "Approximate Age Of Oldest Message Per Queue", "visualization.granularity": "5 m", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.result.by": ["monitor"], "visualization.data.sources": [{"visualization.result.by": ["monitor"], "type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "aws.sqs.oldest.message.age.seconds", "aggregator": "avg", "entity.type": "Group", "entities": []}]}], "visualization.properties": {"chart": {"rotation.angle": 0, "chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}, "granularity": {"value": 5, "unit": "m"}, "container.type": "dashboard"}, {"id": 10000000001666, "visualization.name": "Approximate Number Of Messages Delayed Per Queue", "visualization.description": "Approximate Number Of Messages Delayed Per Queue", "visualization.granularity": "5 m", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.result.by": ["monitor"], "visualization.data.sources": [{"visualization.result.by": ["monitor"], "type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "aws.sqs.delayed.messages", "aggregator": "avg", "entity.type": "Group", "entities": []}]}], "visualization.properties": {"chart": {"rotation.angle": 0, "chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}, "granularity": {"value": 5, "unit": "m"}, "container.type": "dashboard"}, {"id": 10000000001667, "visualization.name": "Approximate Number Of Messages Not Visible Per Queue", "visualization.description": "Approximate Number Of Messages Not Visible Per Queue", "visualization.granularity": "5 m", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.result.by": ["monitor"], "visualization.data.sources": [{"visualization.result.by": ["monitor"], "type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "aws.sqs.hidden.messages", "aggregator": "avg", "entity.type": "Group", "entities": []}]}], "visualization.properties": {"chart": {"rotation.angle": 0, "chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}, "granularity": {"value": 5, "unit": "m"}, "container.type": "dashboard"}, {"id": 10000000001668, "visualization.name": "Published Bytes By Topics", "visualization.description": "Published Bytes By Topics", "visualization.granularity": "5 m", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.result.by": ["monitor"], "visualization.data.sources": [{"visualization.result.by": ["monitor"], "type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "aws.sqs.sent.bytes", "aggregator": "avg", "entity.type": "Group", "entities": []}]}], "visualization.properties": {"chart": {"rotation.angle": 0, "chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}, "granularity": {"value": 5, "unit": "m"}, "container.type": "dashboard"}, {"id": 10000000001669, "visualization.name": "SMS Success Rates By Topics", "visualization.description": "SMS Success Rates By Topics", "visualization.granularity": "5 m", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.result.by": ["monitor"], "visualization.data.sources": [{"visualization.result.by": ["monitor"], "type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "", "aggregator": "avg", "entity.type": "Group", "entities": []}]}], "visualization.properties": {"chart": {"rotation.angle": 0, "chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}, "granularity": {"value": 5, "unit": "m"}, "container.type": "dashboard"}, {"id": 10000000001670, "visualization.name": "SMS Usage Cost By Topics", "visualization.description": "SMS Usage Cost By Topics", "visualization.granularity": "5 m", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.result.by": ["monitor"], "visualization.data.sources": [{"visualization.result.by": ["monitor"], "type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "", "aggregator": "avg", "entity.type": "Group", "entities": []}]}], "visualization.properties": {"chart": {"rotation.angle": 0, "chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}, "granularity": {"value": 5, "unit": "m"}, "container.type": "dashboard"}]