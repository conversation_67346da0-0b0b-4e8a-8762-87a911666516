[{"oid.group.id": "44cb62ee-fc39-4e7a-bf23-fb14fb7b3219", "oid.group.device.type": "UPS", "oid.group.type": "scalar", "oid.group.name": "UPS Statistics", "oid.group.polling.timeout.sec": 60, "oid.group.polling.interval.sec": 600, "oid.group.oids": {"(.1.3.6.1.2.1.33.1.2.3.0*60)": "ups.backup.time.remaining.seconds", "(.1.3.6.1.4.1.935.1.1.1.3.2.1.0/10": "ups.input.voltage.volt", "(.1.3.6.1.4.1.935.1.1.1.3.2.4.0/10)": "ups.input.frequency.hz", "(.1.3.6.1.4.1.935.1.1.1.4.2.1.0/10)": "ups.output.voltage.volt", "(.1.3.6.1.4.1.935.1.1.1.4.2.2.0/10)": "ups.output.frequency.hz", ".1.3.6.1.2.1.33.1.1.1.0": "system.vendor", ".1.3.6.1.2.1.33.1.1.2.0": "system.model", ".1.3.6.1.2.1.33.1.1.3.0": "system.firmware.version", ".1.3.6.1.2.1.33.1.1.4.0": "system.software.version", ".1.3.6.1.2.1.33.1.3.3.1.3.1": "ups.input.voltage.volt", ".1.3.6.1.2.1.33.1.4.1.0": "ups.output.source", ".1.3.6.1.2.1.33.1.4.2.0": "ups.output.frequency.hz", ".1.3.6.1.2.1.33.1.4.4.1.2.1": "ups.output.voltage.volt", ".1.3.6.1.2.1.33.1.4.4.1.3.1": "ups.output.current.ampere", ".1.3.6.1.2.1.33.1.4.4.1.5.1": "ups.load.percent", ".1.3.6.1.2.1.33.1.6.1.0": "ups.active.alarms", ".1.3.6.1.4.1.2254.2.4.5.7.0": "ups.output.load.rate", ".1.3.6.1.4.1.318.1.1.1.1.1.1.0": "system.model", ".1.3.6.1.4.1.318.1.1.1.1.2.1.0": "system.firmware.version", ".1.3.6.1.4.1.318.1.1.1.2.2.6.0": "ups.bad.external.battery.packs", ".1.3.6.1.4.1.318.1.1.1.3.2.1.0": "ups.input.voltage.volt", ".1.3.6.1.4.1.318.1.1.1.3.2.4.0": "ups.input.frequency.hz", ".1.3.6.1.4.1.318.1.1.1.4.1.1.0": "ups.status", ".1.3.6.1.4.1.318.1.1.1.4.2.1.0": "ups.output.voltage.volt", ".1.3.6.1.4.1.318.1.1.1.4.2.2.0": "ups.output.frequency.hz", ".1.3.6.1.4.1.318.1.1.1.4.2.3.0": "ups.load.percent", ".1.3.6.1.4.1.318.1.1.1.4.2.4.0": "ups.output.current.ampere", ".1.3.6.1.4.1.318.1.1.1.7.2.3.0": "ups.last.self.test.result", ".1.3.6.1.4.1.318.1.1.1.7.2.4.0": "ups.last.self.test.date", ".1.3.6.1.4.1.4555.1.1.1.1.4.4.1.4.1": "ups.output.load", ".1.3.6.1.4.1.4555.1.1.1.1.4.4.1.4.2": "ups.output.load", ".1.3.6.1.4.1.4555.1.1.1.1.4.4.1.4.3": "ups.output.load", ".1.3.6.1.4.1.4555.1.1.3.1.4.3.0": "ups.output.load.rate", ".1.3.6.1.4.1.4555.1.1.3.1.4.4.0": "ups.output.load.rate", ".1.3.6.1.4.1.4555.1.1.3.1.4.5.0": "ups.output.load.rate", ".1.3.6.1.4.1.4555.1.1.5.1.4.3.0": "ups.output.load.rate", ".1.3.6.1.4.1.476.1.1.1.1.1.1.0": "system.vendor", ".1.3.6.1.4.1.476.1.1.1.1.1.2.0": "system.model", ".1.3.6.1.4.1.476.1.1.1.1.1.3.0": "system.software.version", ".1.3.6.1.4.1.476.1.1.1.1.13.1": "ups.bypass.state", ".1.3.6.1.4.1.476.1.1.1.1.3.1.0": "ups.input.frequency.hz", ".1.3.6.1.4.1.476.1.1.1.1.3.4.0": "ups.number.transients", ".1.3.6.1.4.1.476.1.1.1.1.3.6.1.2": "ups.input.voltage.volt", ".1.3.6.1.4.1.476.1.1.1.1.4.1.0": "ups.output.frequency.hz", ".1.3.6.1.4.1.476.1.1.1.1.4.2.0": "ups.load.percent", ".1.3.6.1.4.1.476.1.1.1.1.4.4.1.2": "ups.output.voltage.volt", ".1.3.6.1.4.1.476.1.1.1.1.4.4.1.3": "ups.output.current.ampere", ".1.3.6.1.4.1.476.1.1.1.1.4.4.1.4": "ups.output.voltage.volt", ".1.3.6.1.4.1.476.1.1.1.1.4.5": "ups.output.power", ".1.3.6.1.4.1.476.1.1.1.1.5.1": "ups.inverter.state", ".1.3.6.1.4.1.476.1.1.1.1.5.2": "ups.inverter.temperature", ".1.3.6.1.4.1.476.1.1.1.1.6.1.0": "ups.active.alarms", ".1.3.6.1.4.1.476.1.1.1.2.1.1.0": "system.firmware.version", ".1.3.6.1.4.1.476.1.1.1.2.13.2.0": "ups.bypass.frequency.hz", ".1.3.6.1.4.1.534.1.3.4.1.2": "ups.input.voltage.volt", ".1.3.6.1.4.1.534.1.3.5.0": "ups.input.source", ".1.3.6.1.4.1.534.1.4.1.0": "ups.load.percent", ".1.3.6.1.4.1.534.1.4.4.1.2": "ups.output.voltage.volt", ".1.3.6.1.4.1.534.1.4.4.1.3": "ups.output.current.ampere", ".1.3.6.1.4.1.534.1.4.5.0": "ups.output.source", ".1.3.6.1.4.1.534.1.6.1.0": "ups.temperature.celsius", ".1.3.6.1.4.1.705.1.1.2.0": "system.model", ".1.3.6.1.4.1.705.1.1.4.0": "system.firmware.version", ".1.3.6.1.4.1.705.1.1.7.0": "system.serial.number", ".1.3.6.1.4.1.705.1.12.12.0": "system.software.version", ".1.3.6.1.4.1.705.1.6.2.1.2.1": "ups.input.voltage.volt", ".1.3.6.1.4.1.705.1.6.2.1.3.1": "ups.input.frequency.hz", ".1.3.6.1.4.1.705.1.6.2.1.6.1": "ups.input.current.ampere", ".1.3.6.1.4.1.705.1.7.2.1.2.1": "ups.output.voltage.volt", ".1.3.6.1.4.1.705.1.7.2.1.3.1": "ups.output.frequency.hz", ".1.3.6.1.4.1.705.1.7.2.1.4": "ups.load", ".1.3.6.1.4.1.705.1.7.2.1.5.1": "ups.output.current.ampere", ".1.3.6.1.4.1.850.1.1.2.4.0": "ups.charge", ".1.3.6.1.4.1.850.1.1.4.5.1.5": "ups.load.percent", ".1.3.6.1.4.1.935.1.1.1.1.1.1.0": "system.model", ".1.3.6.1.4.1.935.1.1.1.1.2.1.0": "system.firmware.version", ".1.3.6.1.4.1.935.1.1.1.1.2.3.0": "system.serial.number", ".1.3.6.1.4.1.935.1.1.1.4.2.3.0": "ups.load.percent"}, "oid.group.converters": {".1.3.6.1.4.1.318.1.1.1.4.1.1.0": {"1": "Unknown", "2": "Online", "3": "On Battery", "4": "On SmartBoost", "5": "Timed Sleeping", "6": "Software Bypass", "7": "Offline", "8": "rebooting", "9": "Switched Bypass", "10": "Hardware Failure", "11": "Sleeping", "12": "On SmartTrim"}, ".1.3.6.1.4.1.318.1.1.1.3.2.5.0": {"1": "No Transfer", "2": "High line Voltage", "3": "<PERSON> Out", "4": "Black Out", "5": "Small Momentary Sag", "6": "Deep Momentary Sag", "7": "Small Momentary Spike", "8": "Large Momentary Spike", "9": "Self Test", "10": "Voltage Rate Change"}, ".1.3.6.1.4.1.534.1.3.5.0": {"1": "Other", "2": "None", "3": "Primary Utility", "4": "Bypass Feed", "5": "Secondary Utility", "6": "Generator", "7": "Flywheel", "8": "Fuel Cell"}, ".1.3.6.1.4.1.476.1.1.1.1.5.1": {"1": "Unknown", "2": "ON", "3": "OFF"}, "1.3.6.1.4.1.476.1.1.1.1.13.1": {"1": "Unknown", "2": "Yes", "3": "No", "4": "Maintenance"}}}, {"oid.group.id": "44cb62ee-fc39-4e7a-bf23-fb14fb7e3120", "oid.group.device.type": "UPS", "oid.group.type": "scalar", "oid.group.name": "UPS Battery Statistics", "oid.group.polling.timeout.sec": 60, "oid.group.polling.interval.sec": 600, "oid.group.oids": {".1.3.6.1.2.1.33.1.2.5.0": "ups.battery.voltage.volt", ".1.3.6.1.2.1.33.1.2.6.0": "ups.battery.current.ampere", "(.1.3.6.1.4.1.2254.2.4.7.6.0/10)": "ups.battery.voltage", "(.1.3.6.1.4.1.4555.1.1.1.1.2.5.0/10)": "ups.battery.voltage", "(.1.3.6.1.4.1.4555.1.1.3.1.2.4.0)/10": "ups.battery.positive.voltage", "(.1.3.6.1.4.1.4555.1.1.3.1.2.5.0/10)": "ups.battery.negative.voltage", "(.1.3.6.1.4.1.4555.1.1.5.1.2.5.0/10)": "ups.battery.voltage", "(.1.3.6.1.4.1.935.1.1.1.2.2.3.0/10)": "ups.battery.temperature.celsius", "(60*.1.3.6.1.4.1.476.1.1.1.1.2.1)": "ups.battery.time.remaining.seconds", ".1.3.6.1.2.1.33.1.2.1.0": "ups.battery.status", ".1.3.6.1.2.1.33.1.2.2.0": "ups.battery.runtime.elapsed.seconds", ".1.3.6.1.2.1.33.1.2.4.0": "ups.battery.remaining.percent", ".1.3.6.1.2.1.33.1.2.7.0": "ups.battery.temperature.celsius", ".1.3.6.1.4.1.2254.2.4.7.8.0": "ups.battery.capacity", ".1.3.6.1.4.1.318.1.1.1.2.1.1.0": "ups.battery.status", ".1.3.6.1.4.1.318.1.1.1.2.1.2.0": "ups.battery.runtime.elapsed.seconds", ".1.3.6.1.4.1.318.1.1.1.2.1.3.0": "ups.battery.last.replace.date", ".1.3.6.1.4.1.318.1.1.1.2.2.1.0": "ups.battery.capacity.percent", ".1.3.6.1.4.1.318.1.1.1.2.2.2.0": "ups.battery.temperature.celsius", ".1.3.6.1.4.1.318.1.1.1.2.2.3.0": "ups.battery.runtime.remaining.seconds", ".1.3.6.1.4.1.318.1.1.1.2.2.4.0": "ups.battery.replacement.status", ".1.3.6.1.4.1.318.1.1.1.3.2.5.0": "ups.battery.transfer.reason", ".1.3.6.1.4.1.4555.1.1.1.1.2.4.0": "ups.battery.capacity", ".1.3.6.1.4.1.4555.1.1.3.1.2.3.0": "ups.battery.capacity", ".1.3.6.1.4.1.4555.1.1.5.1.2.4.0": "ups.battery.capacity", ".1.3.6.1.4.1.476.1.1.1.1.2.10": "ups.battery.used.power.kWh", ".1.3.6.1.4.1.476.1.1.1.1.2.2.0": "ups.battery.temperature.celsius", ".1.3.6.1.4.1.476.1.1.1.1.2.3.0": "ups.battery.voltage.volt", ".1.3.6.1.4.1.476.1.1.1.1.2.4.0": "ups.battery.current.ampere", ".1.3.6.1.4.1.534.1.2.1.0": "ups.battery.runtime.remaining.seconds", ".1.3.6.1.4.1.534.1.2.3.0": "ups.battery.current.ampere", ".1.3.6.1.4.1.534.1.2.4.0": "ups.battery.capacity.percent", ".1.3.6.1.4.1.705.1.4.1.0": "ups.battery.installed", ".1.3.6.1.4.1.705.1.4.11.0": "ups.battery.sys.shutdown.duration", ".1.3.6.1.4.1.705.1.5.1.0": "ups.battery.runtime.remaining.seconds", ".1.3.6.1.4.1.705.1.5.11.0": "ups.battery.replacement.status", ".1.3.6.1.4.1.705.1.5.2.0": "ups.battery.capacity.percent", ".1.3.6.1.4.1.705.1.5.5.0": "ups.battery.voltage.volt", ".1.3.6.1.4.1.705.1.8.1.0": "ups.battery.temperature.celsius", ".1.3.6.1.4.1.935.1.1.1.2.1.1.0": "ups.battery.status", ".1.3.6.1.4.1.935.1.1.1.2.2.1.0": "ups.battery.capacity.percent", ".1.3.6.1.4.1.935.1.1.1.2.2.2.0": "ups.battery.voltage.volt", ".1.3.6.1.4.1.935.1.1.1.2.2.3.0": "ups.battery.temperature.celsius", ".1.3.6.1.4.1.935.1.1.1.2.2.4.0": "ups.battery.runtime.remaining.seconds", ".1.3.6.1.4.1.935.1.1.1.2.2.5.0": "ups.battery.replacement.status", ".1.3.6.1.4.1.935.1.1.1.8.1.1.0": "ups.battery.voltage.volt"}, "oid.group.converters": {".1.3.6.1.4.1.705.1.4.1.0": {"1": "Yes", "2": "No"}, ".1.3.6.1.4.1.318.1.1.1.2.1.1.0": {"1": "Unknown", "2": "Normal", "3": "Low"}, ".1.3.6.1.4.1.935.1.1.1.2.1.1.0": {"1": "Unknown", "2": "Normal", "3": "Low"}, ".1.3.6.1.4.1.705.1.5.11.0": {"1": "Yes", "2": "No"}, ".1.3.6.1.4.1.935.1.1.1.2.2.5.0": {"1": "Yes", "2": "No"}, ".1.3.6.1.2.1.33.1.4.1.0": {"1": "Other", "2": "None", "3": "Normal", "4": "Bypass", "5": "Battery", "6": "<PERSON><PERSON><PERSON>", "7": "Reducer"}, ".1.3.6.1.4.1.318.1.1.1.2.2.4.0": {"1": "Not Required", "2": "Required"}, ".1.3.6.1.4.1.534.1.4.5.0": {"1": "Other", "2": "None", "3": "Normal", "4": "Bypass", "5": "Battery", "6": "<PERSON><PERSON><PERSON>", "7": "Reducer", "8": "Parallel Capacity", "9": "<PERSON>lle<PERSON> Redundant", "10": "High-Efficiency Mode", "11": "Maintenance Bypass"}, ".1.3.6.1.2.1.33.1.2.1.0": {"1": "Unknown", "2": "Normal", "3": "Low", "4": "Depleted"}}}]