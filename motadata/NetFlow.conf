plugins: print[NetFlow]
nfacctd_port:2055
logfile: /home/<USER>/Workspace/java-workspace/Motadata-NR/motadata/logs/flow/NetFlow_1746797607876.log
loglevel: 7
snaplen: 65535
use_ip_next_hop: true
classifier_num_roots: 10240
aggregate[NetFlow]: src_host, dst_host, peer_src_ip, peer_dst_ip, in_iface, out_iface, src_port, dst_port, proto, sampling_rate, flows, tcpflags, tag, export_proto_version, tos, class
print_output_file[NetFlow]:/home/<USER>/Workspace/java-workspace/Motadata-NR/motadata/flow-cache/%Y%m%d%H%M
print_output[NetFlow]: json
print_history[NetFlow]: 3m
print_history_roundoff[NetFlow]: m
print_refresh_time[NetFlow]: 180
print_output_file_append: true
timestamps_secs: true
plugin_pipe_zmq: true
print_num_protos: true
pre_tag_map: /home/<USER>/Workspace/java-workspace/Motadata-NR/motadata/tag.rule
print_cache_entries[NetFlow]: 16411
nfacctd_time_new: true
daemonize: true
pidfile: /home/<USER>/Workspace/java-workspace/Motadata-NR/motadata/NetFlow
